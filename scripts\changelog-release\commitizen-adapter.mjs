/**
 * 自定义 Commitizen 适配器 (ES 模块版本)
 *
 * 该适配器为项目提供交互式提交信息创建功能，确保：
 * - 遵循 Conventional Commits 规范
 * - 包含必需的 Issue-ID 和 Applies-To 字段
 * - 提供友好的中文提示界面
 * - 与 changelog.ts 使用一致的提交类型配置
 */

import { generateCommitizenChoices } from './types.js';
import { listExtensions } from '../extension-config-manager/utils.js';

export default {
  // 提示用户输入提交信息的各个部分
  prompter(cz, commit) {
    const availableExtensions = listExtensions();

    cz.prompt([
      {
        type: 'list',
        name: 'type',
        message: '选择提交类型:',
        choices: generateCommitizenChoices(),
        default: 'feat',
      },
      {
        type: 'input',
        name: 'scope',
        message: '输入变更范围 (可选):',
        validate: (input) => {
          if (!input || input.trim().length === 0) {
            return '描述不能为空';
          }
          return true;
        },
      },
      {
        type: 'input',
        name: 'subject',
        message: '输入简短描述:',
        validate: (input) => {
          if (!input || input.trim().length === 0) {
            return '描述不能为空';
          }
          if (input.length > 100) {
            return '描述不能超过100个字符';
          }
          return true;
        },
      },
      {
        type: 'input',
        name: 'body',
        message: '输入详细描述 (可选):',
      },
      {
        type: 'input',
        name: 'issueId',
        message: '输入 Issue-ID (可选，格式: 1#20250101-01):',
        validate: (input) => {
          if (input && input.trim().length > 0) {
            const formatRegex = /^\d+#\d{8}-\d{2}$/;
            if (!formatRegex.test(input.trim())) {
              return '格式不正确，应为: 数字#YYYYMMDD-序号 (例如: 1#20250101-01)';
            }
          }
          return true;
        },
      },
      {
        type: 'checkbox',
        name: 'appliesTo',
        message: '选择受影响的插件:',
        choices:
          availableExtensions.length > 0
            ? availableExtensions.map((plugin) => ({ name: plugin, value: plugin }))
            : [{ name: '未找到插件目录，请手动输入', value: '', disabled: true }],
        validate: (input) => {
          if (!input || input.length === 0) {
            return '必须选择至少一个插件';
          }
          return true;
        },
        when: () => availableExtensions.length > 0,
      },
      {
        type: 'input',
        name: 'appliesToManual',
        message: '输入受影响的插件 (用逗号分隔):',
        when: () => availableExtensions.length === 0,
        validate: (input) => {
          if (!input || input.trim().length === 0) {
            return '必须指定受影响的插件';
          }
          const plugins = input.split(',').map((p) => p.trim());
          const validPluginRegex = /^[a-zA-Z0-9_-]+$/;
          for (const plugin of plugins) {
            if (!validPluginRegex.test(plugin)) {
              return `插件名格式不正确: ${plugin}。只允许字母、数字、下划线和连字符`;
            }
          }
          return true;
        },
      },
      {
        type: 'confirm',
        name: 'isBreaking',
        message: '是否包含破坏性变更?',
        default: false,
      },
      {
        type: 'input',
        name: 'breaking',
        message: '描述破坏性变更:',
        when: (answers) => answers.isBreaking,
        validate: (input) => {
          if (!input || input.trim().length === 0) {
            return '必须描述破坏性变更';
          }
          return true;
        },
      },
    ]).then((answers) => {
      // 构建提交信息
      const scope = answers.scope ? `(${answers.scope})` : '';
      const head = `${answers.type}${scope}: ${answers.subject}`;

      let body = answers.body || '';

      // 添加破坏性变更信息
      if (answers.isBreaking && answers.breaking) {
        body += (body ? '\n\n' : '') + `BREAKING CHANGE: ${answers.breaking}`;
      }

      // 添加 Issue-ID
      if (answers.issueId && answers.issueId.trim()) {
        body += (body ? '\n\n' : '') + `Issue-ID: ${answers.issueId.trim()}`;
      }

      // 添加 Applies-To
      const appliesTo =
        answers.appliesTo ||
        (answers.appliesToManual ? answers.appliesToManual.split(',').map((p) => p.trim()) : []);
      if (appliesTo.length > 0) {
        body += (body ? '\n' : '') + `Applies-To: ${appliesTo.join(', ')}`;
      }

      const fullCommitMessage = head + (body ? '\n\n' + body : '');

      commit(fullCommitMessage);
    });
  },
};
