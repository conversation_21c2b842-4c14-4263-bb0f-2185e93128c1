import { isEqual } from 'lodash-es';
import path from 'node:path';
import { Logger } from '../helpers/logger.js';
import { backupFile, getExistingLocales, readJson, writeJson } from './helpers.js';
import { NestedObject, TransformFunction, UpdateI18nOptions } from './types.js';

Logger.setVerbose(true);
const logger = Logger.create('I18nUpdater');

/**
 * 使用转换函数批量更新所有 i18n JSON 文件
 *
 * 功能说明：
 * 1. 扫描指定目录下的所有 JSON 语言包文件
 * 2. 对每个文件的顶层键值对应用转换函数
 * 3. 支持文件备份、语言过滤等选项
 * 4. 提供详细的处理统计和错误报告
 *
 * @param localesDir - 包含源 .json 文件的目录
 * @param transform - 应用于每个顶层键值对的转换函数
 * @param options - 批量更新操作的选项配置
 * @param options.backup - 是否备份原文件，默认为 false
 * @param options.backupSuffix - 备份文件后缀，默认为 '.bak'
 * @param options.includeLocales - 包含的语言列表，为空表示处理所有语言
 * @param options.excludeLocales - 排除的语言列表
 * @throws 如果处理过程中出现错误，会抛出异常
 */
export async function updateI18n(
  localesDir: string,
  transform: TransformFunction,
  options: UpdateI18nOptions = {},
): Promise<void> {
  const startTime = Date.now();
  let hasErrors = false;

  try {
    logger.info('开始批量更新 i18n 文件');

    // 设置默认选项
    const opts = {
      backup: false,
      backupSuffix: '.bak',
      includeLocales: [],
      excludeLocales: [],
      ...options,
    };

    // 获取所有现有的语言文件
    const allLocales = await getExistingLocales(localesDir);

    if (allLocales.length === 0) {
      throw new Error('没有找到任何 JSON 语言包文件');
    }

    // 应用语言过滤
    let locales = allLocales;
    if (opts.includeLocales.length > 0) {
      locales = locales.filter((locale) => opts.includeLocales.includes(locale));
    }
    if (opts.excludeLocales.length > 0) {
      locales = locales.filter((locale) => !opts.excludeLocales.includes(locale));
    }

    if (locales.length === 0) {
      throw new Error('过滤后没有需要处理的语言包文件');
    }

    logger.info(
      `发现 ${allLocales.length} 个语言包文件，将处理 ${locales.length} 个: ${locales.join(', ')}`,
    );

    let totalProcessed = 0;
    let totalModified = 0;
    let totalErrors = 0;

    // 处理每个语言文件
    for (const locale of locales) {
      const filePath = path.join(localesDir, `${locale}.json`);

      try {
        logger.verbose(`处理语言包: ${locale}`);

        // 备份文件（如果需要）
        if (opts.backup) {
          const backupSuccess = await backupFile(filePath, opts.backupSuffix);
          if (!backupSuccess) {
            logger.warn(`备份文件失败，跳过处理: ${locale}`);
            continue;
          }
        }

        // 读取原始数据
        const originalData = await readJson(filePath);
        if (!originalData) {
          logger.error(`无法读取文件: ${locale}`);
          totalErrors++;
          hasErrors = true;
          continue;
        }

        // 应用转换函数
        const transformResult = await transformJsonData(originalData, transform, locale);

        if (transformResult.error) {
          logger.error(`转换失败 ${locale}: ${transformResult.error}`);
          totalErrors++;
          hasErrors = true;
          continue;
        }

        const newData = transformResult.data!;
        totalProcessed += transformResult.processedCount;

        // 检查是否有变化
        if (isEqual(originalData, newData)) {
          logger.verbose(`${locale}: 无变化，跳过写入`);
          continue;
        }

        // 写入更新后的数据
        const writeSuccess = await writeJson(filePath, newData);
        if (writeSuccess) {
          totalModified++;
          logger.info(`${locale}: 已更新 (处理 ${transformResult.processedCount} 个条目)`);
        } else {
          logger.error(`写入文件失败: ${locale}`);
          totalErrors++;
          hasErrors = true;
        }
      } catch (error) {
        logger.error(
          `处理语言包失败 ${locale}: ${error instanceof Error ? error.message : String(error)}`,
        );
        totalErrors++;
        hasErrors = true;
      }
    }

    const duration = Date.now() - startTime;

    // 输出处理结果
    logger.success(`批量更新完成：`);
    logger.info(`- 处理语言: ${locales.length}`);
    logger.info(`- 修改文件: ${totalModified}`);
    logger.info(`- 处理条目: ${totalProcessed}`);
    logger.info(`- 错误数量: ${totalErrors}`);
    logger.info(`- 耗时: ${duration}ms`);
  } catch (error) {
    logger.error(`批量更新失败: ${error instanceof Error ? error.message : String(error)}`);
    hasErrors = true;
  }

  // 如果有错误，抛出异常
  if (hasErrors) {
    throw new Error('批量更新失败');
  }
}

/**
 * 对 JSON 数据的顶层键值对应用转换函数
 *
 * 功能说明：
 * 1. 遍历 JSON 对象的所有顶层键值对
 * 2. 对每个键值对应用转换函数
 * 3. 根据转换结果决定保留、修改或删除键
 * 4. 统计处理的条目数量
 *
 * @param data - 原始 JSON 数据对象
 * @param transform - 转换函数，返回新的键值对或 null（删除）
 * @param locale - 当前处理的语言代码
 * @returns 包含转换后数据、错误信息和处理统计的结果对象
 */
async function transformJsonData(
  data: NestedObject,
  transform: TransformFunction,
  locale: string,
): Promise<{
  data?: NestedObject;
  processedCount: number;
  error?: string;
}> {
  try {
    const newData: NestedObject = {};
    let processedCount = 0;

    // 遍历顶层的每个 key-value 对
    for (const [key, value] of Object.entries(data)) {
      try {
        // 应用转换函数
        const result = transform(key, value, locale);
        processedCount++;

        // 处理转换结果
        if (result === null || result === undefined) {
          // 删除该条目
          logger.verbose(`${locale}: 删除条目 "${key}"`);
        } else if (Array.isArray(result) && result.length === 2) {
          // 更新或重命名条目
          const [newKey, newValue] = result;

          if (newKey !== key) {
            logger.verbose(`${locale}: 重命名 "${key}" -> "${newKey}"`);
          }

          newData[newKey] = newValue;
        } else {
          logger.warn(`${locale}: 转换函数返回值格式无效，保持原样: "${key}"`);
          newData[key] = value;
        }
      } catch (error) {
        logger.error(
          `${locale}: 转换条目失败 "${key}": ${error instanceof Error ? error.message : String(error)}`,
        );
        // 保持原样
        newData[key] = value;
      }
    }

    return {
      data: newData,
      processedCount,
    };
  } catch (error) {
    return {
      processedCount: 0,
      error: error instanceof Error ? error.message : String(error),
    };
  }
}

/**
 * 常用转换函数的工厂对象
 *
 * 提供了一系列预定义的转换函数，用于常见的 i18n 文件操作：
 * - 重命名键前缀
 * - 删除匹配的键
 * - 映射键名
 * - 保留指定键
 * - 组合多个转换
 */
export const commonTransforms = {
  /**
   * 创建重命名键前缀的转换函数
   *
   * 示例：renameKeys('old_', 'new_') 会将 'old_key' 转换为 'new_key'
   *
   * @param oldPrefix - 要替换的旧前缀
   * @param newPrefix - 新的前缀
   * @returns 转换函数
   */
  renameKeys: (oldPrefix: string, newPrefix: string): TransformFunction => {
    return (key, value, _locale) => {
      if (key.startsWith(oldPrefix)) {
        const newKey = key.replace(oldPrefix, newPrefix);
        return [newKey, value];
      }
      return [key, value];
    };
  },

  /**
   * 创建删除匹配模式键的转换函数
   *
   * 示例：deleteKeys(/^temp_/) 会删除所有以 'temp_' 开头的键
   *
   * @param pattern - 匹配模式（正则表达式或字符串）
   * @returns 转换函数
   */
  deleteKeys: (pattern: string | RegExp): TransformFunction => {
    const regex = typeof pattern === 'string' ? new RegExp(pattern) : pattern;

    return (key, value, _locale) => {
      if (regex.test(key)) {
        return null; // 删除
      }
      return [key, value];
    };
  },

  /**
   * 批量更新多个键值的转换函数
   *
   * 示例：updateValues({ version: '2.0.0', name: '新名称' }) 会批量更新对应键的值
   *
   * @param valuesMap - 需要更新的键值对对象
   * @returns 转换函数
   */
  updateValues: (valuesMap: Record<string, unknown>): TransformFunction => {
    return (key, value, _locale) => {
      if (Object.prototype.hasOwnProperty.call(valuesMap, key)) {
        return [key, valuesMap[key]];
      }
      return [key, value];
    };
  },

  /**
   * 创建更新特定键值的转换函数
   *
   * 示例：updateValue('version', '2.0.0') 会将 'version' 键的值更新为 '2.0.0'
   *
   * @param targetKey - 目标键名
   * @param newValue - 新的值
   * @returns 转换函数
   */
  updateValue: (targetKey: string, newValue: unknown): TransformFunction => {
    return (key, value, _locale) => {
      if (key === targetKey) {
        return [key, newValue];
      }
      return [key, value];
    };
  },

  /**
   * 创建为字符串值添加前缀或后缀的转换函数
   *
   * 支持嵌套对象中的字符串值处理
   * 示例：addTextWrapper('[', ']') 会将 'hello' 转换为 '[hello]'
   *
   * @param prefix - 要添加的前缀，默认为空字符串
   * @param suffix - 要添加的后缀，默认为空字符串
   * @returns 转换函数
   */
  addTextWrapper: (prefix: string = '', suffix: string = ''): TransformFunction => {
    return (key, value, _locale) => {
      if (typeof value === 'string') {
        return [key, prefix + value + suffix];
      } else if (typeof value === 'object' && value !== null) {
        // 处理嵌套对象中的字符串
        const processNestedValue = (obj: unknown): unknown => {
          if (typeof obj === 'string') {
            return prefix + obj + suffix;
          } else if (typeof obj === 'object' && obj !== null) {
            const newObj: Record<string, unknown> = {};
            for (const [k, v] of Object.entries(obj)) {
              newObj[k] = processNestedValue(v);
            }
            return newObj;
          }
          return obj;
        };

        return [key, processNestedValue(value)];
      }
      return [key, value];
    };
  },

  /**
   * 创建根据映射表重命名键的转换函数
   *
   * 示例：mapKeys({ 'oldKey': 'newKey', 'foo': 'bar' }) 会重命名匹配的键
   *
   * @param keyMap - 键名映射表，格式为 { [oldKey]: newKey }
   * @returns 转换函数
   */
  mapKeys: (keyMap: Record<string, string>): TransformFunction => {
    return (key, value, _locale) => {
      const newKey = keyMap[key] || key;
      return [newKey, value];
    };
  },

  /**
   * 创建只保留指定键的转换函数
   *
   * 示例：keepOnlyKeys(['key1', 'key2']) 只会保留 'key1' 和 'key2'，删除其他所有键
   *
   * @param allowedKeys - 允许保留的键名列表
   * @returns 转换函数
   */
  keepOnlyKeys: (allowedKeys: string[]): TransformFunction => {
    const allowedSet = new Set(allowedKeys);
    return (key, value, _locale) => {
      if (allowedSet.has(key)) {
        return [key, value];
      }
      return null; // 删除
    };
  },

  /**
   * 创建组合多个转换函数的转换函数
   *
   * 按顺序执行所有转换函数，前一个的输出作为后一个的输入
   * 如果任何一个转换函数返回 null，则整个链条返回 null（删除键）
   *
   * 示例：compose(renameKeys('old_', 'new_'), updateValue('version', '2.0'))
   *
   * @param transforms - 要组合的转换函数数组
   * @returns 组合后的转换函数
   */
  compose: (...transforms: TransformFunction[]): TransformFunction => {
    return (key, value, locale) => {
      let currentKey = key;
      let currentValue = value;

      for (const transform of transforms) {
        const result = transform(currentKey, currentValue, locale);
        if (result === null || result === undefined) {
          return null; // 删除
        }
        [currentKey, currentValue] = result;
      }

      return [currentKey, currentValue];
    };
  },
};
