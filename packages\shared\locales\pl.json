{"EXTENSION_NAME": {"message": "TODO: EXTENSION_NAME"}, "EXTENSION_DESCRIPTION": {"message": "TODO: EXTENSION_DESCRIPTION"}, "1688_cross_border_hot_selling": {"message": "1688 Transgraniczne gorące miejsce sprzedaży"}, "1688_shi_li_ren_zheng": {"message": "Certyfikat wytrzym<PERSON>ł<PERSON>ci 1688"}, "1_jian_qi_pi": {"message": "MOQ: 1"}, "1year_yi_shang": {"message": "Ponad 1 rok"}, "24H": {"message": "24H"}, "24H_fa_huo": {"message": "Dostawa w ciągu 24 godzin"}, "24H_lan_shou_lv": {"message": "24-<PERSON><PERSON><PERSON> stawka za opakowanie"}, "30D_shang_xin": {"message": "Nowości miesięczne"}, "30d_sales": {"message": "Sprzedaż miesięczna:$amount$", "placeholders": {"amount": {"content": "$1"}}}, "3Min_xiang_ying_lv": {"message": "Odpowiedź w ciągu 3 minut."}, "3Min_xiang_ying_lv__desc": {"message": "Odsetek skutecznych odpowiedzi Wangwang na wiadomości z zapytaniami kupującego w ciągu 3 minut w ciągu ostatnich 30 dni"}, "48H": {"message": "48H"}, "48H_fa_huo": {"message": "Dostawa w ciągu 48 godzin"}, "48H_lan_shou_lv": {"message": "48-<PERSON><PERSON><PERSON> stawka za opakowanie"}, "48H_lan_shou_lv__desc": {"message": "Stosunek liczby zamówień odebranych w ciągu 48 godzin do całkowitej liczby zamówień"}, "48H_lv_yue_lv": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> 48-<PERSON><PERSON><PERSON>"}, "48H_lv_yue_lv__desc": {"message": "Stosunek liczby zamówień odebranych lub dostarczonych w ciągu 48 godzin do całkowitej liczby zamówień"}, "72H": {"message": "72H"}, "7D_shang_xin": {"message": "Nowości tygodniowe"}, "7D_wu_li_you": {"message": "7 dni bez opieki"}, "ABS_title_text": {"message": "Ta oferta zawiera historię marki"}, "AC_title_text": {"message": "Ta oferta ma odznakę Amazon's Choice"}, "A_title_text": {"message": "Ta oferta ma stronę z treścią A+"}, "BS_title_text": {"message": "Ta oferta jest klasyfikowana jako bestseller $num$ w kategorii $type$", "placeholders": {"type": {"content": "$1"}, "num": {"content": "$2"}}}, "LTD_title_text": {"message": "LTD (oferta ograniczona czasowo) oznacza, że ​​ta oferta jest częścią wydarzenia „7-dniowej promocji”"}, "NR_title_text": {"message": "Ta oferta jest klasyfikowana jako now<PERSON> $num$ w kategorii $type$", "placeholders": {"type": {"content": "$1"}, "num": {"content": "$2"}}}, "SBV_title_text": {"message": "Ta oferta zawiera reklamę wideo, rodzaj reklamy PPC, która zwykle pojawia się na środku wyników wyszukiwania"}, "SB_title_text": {"message": "Ta oferta zawiera reklamę marki, rodzaj reklamy PPC, która zwykle pojawia się na górze lub na dole wyników wyszukiwania"}, "SP_title_text": {"message": "Ta oferta zawiera reklamę sponsorowanego produktu"}, "V_title_text": {"message": "Ta oferta zawiera wprowadzenie wideo"}, "advanced_research": {"message": "<PERSON><PERSON>"}, "agent_ds1688___my_order": {"message": "Moje <PERSON>ów<PERSON>ia"}, "agent_ds1688__add_to_cart": {"message": "Zakup za granicą"}, "agent_ds1688__cart": {"message": "<PERSON><PERSON><PERSON><PERSON> s<PERSON>powy"}, "agent_ds1688__desc": {"message": "Dostarczane przez 1688. Obsługuje bezpośredni zakup z zagranicy, płat<PERSON>ść w USD i dostawę do magazynu tranzytowego w Chinach."}, "agent_ds1688__freight": {"message": "Kalkulator kosztów wysyłki"}, "agent_ds1688__help": {"message": "Pomoc"}, "agent_ds1688__packages": {"message": "List przewozowy"}, "agent_ds1688__profile": {"message": "Centrum osobiste"}, "agent_ds1688__warehouse": {"message": "<PERSON><PERSON><PERSON>"}, "ai_comment_analysis_advantage": {"message": "<PERSON><PERSON><PERSON>"}, "ai_comment_analysis_ai": {"message": "<PERSON><PERSON>za recenzji <PERSON>"}, "ai_comment_analysis_available": {"message": "Dostępne"}, "ai_comment_analysis_balance": {"message": "Za mało monet, doła<PERSON><PERSON>"}, "ai_comment_analysis_behavior": {"message": "Zachowanie"}, "ai_comment_analysis_characteristic": {"message": "Charakterystyka tłumu"}, "ai_comment_analysis_comment": {"message": "Produkt nie ma wystarczającej liczby recenzji, aby w<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> dokładne wnioski, wybierz produkt z większą liczbą recenzji."}, "ai_comment_analysis_consume": {"message": "Szacowane zużycie"}, "ai_comment_analysis_default": {"message": "Domyślne recenzje"}, "ai_comment_analysis_desire": {"message": "Oczekiwania klientów"}, "ai_comment_analysis_disadvantage": {"message": "Wady"}, "ai_comment_analysis_free": {"message": "Bezpłatne próby"}, "ai_comment_analysis_freeNum": {"message": "Zostanie wykorzystany 1 bezpłatny kredyt"}, "ai_comment_analysis_go_recharge": {"message": "Przejdź do doładowania"}, "ai_comment_analysis_intelligence": {"message": "Ana<PERSON>za inteligentnych recenzji"}, "ai_comment_analysis_location": {"message": "Lokalizacja"}, "ai_comment_analysis_motive": {"message": "Motywacja zakupu"}, "ai_comment_analysis_network_error": {"message": "<PERSON><PERSON><PERSON><PERSON>, spróbuj ponownie"}, "ai_comment_analysis_normal": {"message": "Recenzje ze zdjęciami"}, "ai_comment_analysis_number_reviews": {"message": "Liczba recenzji: $num$, Szacowane zużycie: $consume$", "placeholders": {"num": {"content": "$1"}, "consume": {"content": "$2"}}}, "ai_comment_analysis_ordinary": {"message": "Ko<PERSON><PERSON><PERSON> ogó<PERSON>"}, "ai_comment_analysis_percentage": {"message": "Procent"}, "ai_comment_analysis_problem": {"message": "Problemy z płatnością"}, "ai_comment_analysis_reanalysis": {"message": "Przean<PERSON><PERSON><PERSON>onown<PERSON>"}, "ai_comment_analysis_reason": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "ai_comment_analysis_recharge": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "ai_comment_analysis_recharged": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "ai_comment_analysis_retry": {"message": "Ponów próbę"}, "ai_comment_analysis_scene": {"message": "Scenariusz użycia"}, "ai_comment_analysis_start": {"message": "Rozpocznij analizę"}, "ai_comment_analysis_subject": {"message": "<PERSON><PERSON><PERSON>"}, "ai_comment_analysis_time": {"message": "Czas użytkowania"}, "ai_comment_analysis_tool": {"message": "Narzędzie AI"}, "ai_comment_analysis_user_portrait": {"message": "<PERSON><PERSON>"}, "ai_comment_analysis_welcome": {"message": "Witamy w analizie recenzji AI"}, "ai_comment_analysis_year": {"message": "Komentarze z ostatniego roku"}, "ai_listing_Exclude_keywords": {"message": "Z wyłączeniem słów kluczowych"}, "ai_listing_Login_the_feature": {"message": "<PERSON><PERSON> z tej funkcji, wymagane jest logowanie"}, "ai_listing_aI_generation": {"message": "Generacja sztucznej inteligencji"}, "ai_listing_add_automatic": {"message": "Automatyczny"}, "ai_listing_add_dictionary_new": {"message": "Utwórz nową bibliotekę"}, "ai_listing_add_enter_keywords": {"message": "Wprowadź słowa kluczowe"}, "ai_listing_add_inputkey_selling": {"message": "Wprowadź punkt sprzedaży i naciśnij $key$, aby zakończyć dodawanie", "placeholders": {"key": {"content": "$1"}}}, "ai_listing_add_inputkey_warning": {"message": "Limit <PERSON>, aż do $amount$ punktów sprzedaży", "placeholders": {"amount": {"content": "$1"}}}, "ai_listing_add_keywords": {"message": "Dodaj słowa kluczowe"}, "ai_listing_add_manually": {"message": "<PERSON><PERSON>j <PERSON>"}, "ai_listing_add_selling": {"message": "Dodaj punkty sprzedaży"}, "ai_listing_added_keywords": {"message": "Dodano słowa kluczowe"}, "ai_listing_added_successfully": {"message": "<PERSON><PERSON><PERSON>"}, "ai_listing_addexcluded_keywords": {"message": "Wpisz wykluczone słowa kluczowe i naciśnij Enter, aby zakończyć dodawanie."}, "ai_listing_adding_selling": {"message": "Dodano punkty sprzedaży"}, "ai_listing_addkeyword_enter": {"message": "Wpisz kluczowe słowa atrybutu i naciśnij klawisz Enter, aby zakończyć dodawanie"}, "ai_listing_ai_description": {"message": "Biblioteka słów opisu AI"}, "ai_listing_ai_dictionary": {"message": "Biblioteka słów tytułowych AI"}, "ai_listing_ai_title": {"message": "Tytuł AI"}, "ai_listing_aidescription_repeated": {"message": "Nazwa biblioteki słów opisu AI nie może się powtórzyć"}, "ai_listing_aititle_repeated": {"message": "Nazwa biblioteki słów tytułowych AI nie może się powtórzyć"}, "ai_listing_data_comes_from": {"message": "Dane te pochodzą z:"}, "ai_listing_deleted_successfully": {"message": "Skutecznie usunięte"}, "ai_listing_dictionary_name": {"message": "Nazwa biblioteki"}, "ai_listing_edit_dictionary": {"message": "Modyfikuj bibliotekę..."}, "ai_listing_edit_word_library": {"message": "Edytuj bibliotekę słów"}, "ai_listing_enter_keywords": {"message": "Wpisz słowa kluczowe i naciśnij $key$, aby zakończyć dodawanie", "placeholders": {"key": {"content": "$1"}}}, "ai_listing_exceeded_maximum": {"message": "Limit został p<PERSON>, maksymalna liczba słów kluczowych $amount$", "placeholders": {"amount": {"content": "$1"}}}, "ai_listing_excluded_word_library": {"message": "Wykluczona biblioteka słów"}, "ai_listing_generate_characters": {"message": "Generuj znaki"}, "ai_listing_generation_platform": {"message": "Platforma generacji"}, "ai_listing_help_optimize": {"message": "Pomóż mi zoptymalizować tytuł produktu. Tytuł oryginalny"}, "ai_listing_include_selling": {"message": "Inne punkty sprzedaży obejmowały:"}, "ai_listing_included_keyword": {"message": "Uwzględnione słowa kluczowe"}, "ai_listing_included_keywords": {"message": "Uwzględnione słowa kluczowe"}, "ai_listing_input_selling": {"message": "Wprowadź punkt sprzedaży"}, "ai_listing_input_selling_fit": {"message": "Wprowadź punkty sprzedaży pasujące do tytułu"}, "ai_listing_input_selling_please": {"message": "Proszę podać punkty sprzedaży"}, "ai_listing_intelligently_title": {"message": "Wprowadź wymaganą treść powyżej, aby inteligentnie wygenerować tytuł"}, "ai_listing_keyword_product_title": {"message": "Słowo kluczowe tytuł produktu"}, "ai_listing_keywords_repeated": {"message": "Słowa kluczowe nie mogą się powtarzać"}, "ai_listing_listed_selling_points": {"message": "Zawiera punkty sprzedaży"}, "ai_listing_long_title_1": {"message": "Zawiera podstawowe informacje, takie jak nazwa marki, typ produktu, cechy produktu itp."}, "ai_listing_long_title_2": {"message": "Na podstawie standardowego tytułu produktu dodawane są słowa kluczowe sprzyjające SEO."}, "ai_listing_long_title_3": {"message": "Oprócz nazwy marki, rod<PERSON><PERSON> produktu, cech produktu i słów kluczowych, uwzględniane są również słowa kluczowe z długim ogonem, aby uzyskać wyższą pozycję w przypadku określonych, podzielonych na segmenty zapytań."}, "ai_listing_longtail_keyword_product_title": {"message": "Tytuł produktu ze słowem kluczowym z długim ogonem"}, "ai_listing_manually_enter": {"message": "Ręcznie wpisz..."}, "ai_listing_network_not_working": {"message": "Internet nie jest dost<PERSON><PERSON><PERSON>, aby <PERSON><PERSON><PERSON> dostęp do ChatGPT, wymagana jest sieć VPN"}, "ai_listing_new_dictionary": {"message": "Utwórz nową bibliotekę słów..."}, "ai_listing_new_generate": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "ai_listing_optional_words": {"message": "Opcjonalne słowa"}, "ai_listing_original_title": {"message": "Oryginalny tytuł"}, "ai_listing_other_keywords_included": {"message": "Inne słowa kluczowe obejmowały:"}, "ai_listing_please_again": {"message": "Proszę spróbuj ponownie"}, "ai_listing_please_select": {"message": "Wygenerowano dla Ciebie następujące tytuły, wybierz:"}, "ai_listing_product_category": {"message": "Kategoria produktu"}, "ai_listing_product_category_is": {"message": "Kategoria produktu to"}, "ai_listing_product_category_to": {"message": "Do jaki<PERSON> kategorii należy produkt?"}, "ai_listing_random_keywords": {"message": "Losowe słowa kluczowe $amount$", "placeholders": {"amount": {"content": "$1"}}}, "ai_listing_random_selling": {"message": "Losowe $amount$ punktów sprzedaży", "placeholders": {"amount": {"content": "$1"}}}, "ai_listing_randomize_keywords": {"message": "Losuj z biblioteki słów"}, "ai_listing_search_selling": {"message": "Szukaj według punktu sprzedaży"}, "ai_listing_select_product_categories": {"message": "Automatycznie wybieraj kategorie produktów."}, "ai_listing_select_product_selling_points": {"message": "Automatycznie wybieraj punkty sprzedaży produktu"}, "ai_listing_select_word_library": {"message": "Wybierz bibliotekę słów"}, "ai_listing_selling": {"message": "Punkty sprzedaży"}, "ai_listing_selling_ask": {"message": "Jakie inne wymagania dotyczące punktów sprzedaży obowiązują w przypadku tytułu?"}, "ai_listing_selling_optional": {"message": "Opcjonalne punkty sprzedaży"}, "ai_listing_selling_repeat": {"message": "Punkty nie podlegają duplikacji"}, "ai_listing_set_excluded": {"message": "Ustaw jako wykluczoną bibliotekę słów"}, "ai_listing_set_include_selling_points": {"message": "Uwzględnij punkty sprzedaży"}, "ai_listing_set_included": {"message": "Ustaw jako dołączoną bibliotekę słów"}, "ai_listing_set_selling_dictionary": {"message": "Ustaw jako bibliotekę punktów sprzedaży"}, "ai_listing_standard_product_title": {"message": "Standardowy tytuł produktu"}, "ai_listing_translated_title": {"message": "Przetłumaczony tytuł"}, "ai_listing_visit_chatGPT": {"message": "Odwiedź ChatGPT"}, "ai_listing_what_other_keywords": {"message": "Jakie inne słowa kluczowe są wymagane w tytule?"}, "aliprice_coupons_apply_again": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "aliprice_coupons_apply_coupons": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "aliprice_coupons_apply_success": {"message": "Znaleziono kupon: zaoszczędź $amount$", "placeholders": {"amount": {"content": "$1"}}}, "aliprice_coupons_applying": {"message": "Testowanie kodów najlepszych ofert..."}, "aliprice_coupons_applying_desc": {"message": "Sprawdzanie: $code$", "placeholders": {"code": {"content": "$1"}}}, "aliprice_coupons_back_to_checkout": {"message": "Kont<PERSON><PERSON><PERSON><PERSON><PERSON> s<PERSON>wd<PERSON>"}, "aliprice_coupons_found_coupons": {"message": "Znaleźliśmy $amount$ kupony", "placeholders": {"amount": {"content": "$1"}}}, "aliprice_coupons_found_coupons_desc": {"message": "Gotowy do kasy? Zadbajmy o najlepszą cenę!"}, "aliprice_coupons_no_coupon_aviable": {"message": "Te kody nie d<PERSON>łały. <PERSON><PERSON> wielkiego — już otrzymujesz najlepszą cenę."}, "aliprice_coupons_toolbar_btn": {"message": "Zdobądź kupony"}, "aliww_translate": {"message": "Tłumacz czatów Aliwangwang"}, "aliww_translate_supports": {"message": "Wsparcie: 1688 i Taobao"}, "amazon_extended_keywords_Keywords": {"message": "Słowa kluczowe"}, "amazon_extended_keywords_copy_all": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "amazon_extended_keywords_more": {"message": "<PERSON><PERSON><PERSON><PERSON>j"}, "an_lei_ji_xiao_liang_pai_xu": {"message": "Sort<PERSON>j według skumulowanej sprzedaży"}, "an_lei_xing_cha_kan": {"message": "Widok według typu"}, "an_yue_dai_xiao_pai_xu": {"message": "Ranking sprz<PERSON><PERSON>y metodą dropshipping"}, "apra_btn__cat_name": {"message": "<PERSON><PERSON><PERSON> rece<PERSON>"}, "apra_chart__name": {"message": "Procent sprzedaży produktów według kraju"}, "apra_chart__update_at": {"message": "Czas aktualizacji $time$", "placeholders": {"time": {"content": "$1"}}}, "apra_name": {"message": "Statystyki sprzedaży krajów"}, "auto_opening": {"message": "Automatyczne otwarcie za $num$ sekund", "placeholders": {"num": {"content": "$1"}}}, "auto_page_next_x_pages": {"message": "Następne $autoPaging$ strony", "placeholders": {"autoPaging": {"content": "$1"}}}, "average_days_listed": {"message": "Średnia w dniach przechowywania"}, "average_hui_fu_lv": {"message": "Średni wskaźnik odpowiedzi"}, "average_ping_gong_ying_shang_deng_ji": {"message": "Przeciętny poziom dostawcy"}, "average_price": {"message": "Średnia cena"}, "average_qi_ding_liang": {"message": "Ś<PERSON>nie <PERSON>"}, "average_rating": {"message": "Średnia ocena"}, "average_ren_zheng_gong_ying_nian_chang": {"message": "Ś<PERSON>nie lata c<PERSON><PERSON>"}, "average_revenue": {"message": "Ś<PERSON><PERSON>"}, "average_revenue_per_product": {"message": "Całkowity przychód ÷ Liczba produktów"}, "average_sales": {"message": "Średnia sprzedaż"}, "average_sales_per_product": {"message": "Całkowita sprzedaż ÷ Liczba produktów"}, "bao_han": {"message": "Zawiera"}, "bao_zheng_jin": {"message": "Mar<PERSON><PERSON>"}, "bian_ti_shu": {"message": "Wariac<PERSON>"}, "biao_ti": {"message": "<PERSON><PERSON><PERSON>"}, "blacklist_add_blacklist": {"message": "Zablokuj ten sklep"}, "blacklist_address_incorrect": {"message": "Adres jest niepoprawny. Proszę sprawdź to."}, "blacklist_blacked_out": {"message": "Sklep został zablokowany"}, "blacklist_blacklist": {"message": "Czarna lista"}, "blacklist_no_records_yet": {"message": "Nie ma jeszcze rekordu!"}, "blue_rocket": {"message": "로켓배송"}, "brand_name": {"message": "<PERSON><PERSON>"}, "btn_aliprice_agent__daigou": {"message": "Pośrednik zakupu"}, "btn_aliprice_agent__dropshipping": {"message": "Dropshipping"}, "btn_have_a_try": {"message": "Spróbuj teraz"}, "btn_refresh": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "btn_try_it_now": {"message": "Spróbuj teraz"}, "btn_txt_view_on_aliprice": {"message": "Zobacz na AliPrice"}, "bu_bao_han": {"message": "<PERSON><PERSON>"}, "bulk_copy_links": {"message": "Linki do masowego kopiowania"}, "bulk_copy_products": {"message": "Produkty do masowego kopiowania"}, "cai_gou_zi_xun": {"message": "Obsługa klienta"}, "cai_gou_zi_xun__desc": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> reakcji sprzedawcy wynosząca trzy minuty"}, "can_ping_lei_xing": {"message": "<PERSON><PERSON>"}, "cao_zuo": {"message": "<PERSON><PERSON><PERSON>"}, "chan_pin_ID": {"message": "ID produktu"}, "chan_pin_e_wai_xin_xi": {"message": "Dodatkowe informacje o produkcie"}, "chan_pin_lian_jie": {"message": "Link do produktu"}, "cheng_li_shi_jian": {"message": "<PERSON>zas założenia"}, "city__aba": {"message": "Aba"}, "city__aksu": {"message": "<PERSON><PERSON><PERSON>"}, "city__alaer": {"message": "<PERSON><PERSON><PERSON>"}, "city__altai": {"message": "Altai"}, "city__alxaleague": {"message": "Alxa League"}, "city__ankang": {"message": "Ankan<PERSON>"}, "city__anqing": {"message": "Anqing"}, "city__anshan": {"message": "<PERSON><PERSON>"}, "city__anshun": {"message": "<PERSON><PERSON><PERSON>"}, "city__anyang": {"message": "Anyang"}, "city__baicheng": {"message": "Baicheng"}, "city__baise": {"message": "Baise"}, "city__baisha": {"message": "Baisha"}, "city__baishan": {"message": "Baishan"}, "city__baiyin": {"message": "<PERSON><PERSON><PERSON>"}, "city__baoding": {"message": "<PERSON>od<PERSON>"}, "city__baoji": {"message": "<PERSON><PERSON><PERSON>"}, "city__baoshan": {"message": "<PERSON><PERSON><PERSON>"}, "city__baoting": {"message": "Baoting"}, "city__baotou": {"message": "<PERSON><PERSON><PERSON>"}, "city__bayannur": {"message": "Bayannur"}, "city__bayinguoleng": {"message": "Bayinguoleng"}, "city__bazhong": {"message": "Bazhong"}, "city__beihai": {"message": "Beihai"}, "city__bengbu": {"message": "<PERSON><PERSON><PERSON>"}, "city__benxi": {"message": "Benxi"}, "city__bijie": {"message": "Bijie"}, "city__binzhou": {"message": "Binzhou"}, "city__bortala": {"message": "<PERSON><PERSON><PERSON>"}, "city__bozhou": {"message": "Bozhou"}, "city__cangzhou": {"message": "Cangzhou"}, "city__chamdo": {"message": "<PERSON><PERSON><PERSON>"}, "city__changchun": {"message": "<PERSON><PERSON><PERSON>"}, "city__changde": {"message": "<PERSON><PERSON>"}, "city__changhua": {"message": "Changhua"}, "city__changji": {"message": "Changji"}, "city__changjiang": {"message": "Changjiang"}, "city__changsha": {"message": "Changsha"}, "city__changzhi": {"message": "Changzhi"}, "city__changzhou": {"message": "Changzhou"}, "city__chaohu": {"message": "<PERSON><PERSON>"}, "city__chaoyang": {"message": "Chaoyang"}, "city__chaozhou": {"message": "Chaozhou"}, "city__chengde": {"message": "Chengde"}, "city__chengdu": {"message": "Chengdu"}, "city__chengmai": {"message": "<PERSON><PERSON><PERSON>"}, "city__chenzhou": {"message": "Chenzhou"}, "city__chiayi": {"message": "<PERSON><PERSON><PERSON>"}, "city__chifeng": {"message": "<PERSON><PERSON>"}, "city__chizhou": {"message": "Chizhou"}, "city__chongzuo": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__chuxiong": {"message": "Chuxiong"}, "city__chuzhou": {"message": "Chuzhou"}, "city__dali": {"message": "<PERSON><PERSON>"}, "city__dalian": {"message": "<PERSON><PERSON>"}, "city__dandong": {"message": "Dandong"}, "city__danzhou": {"message": "Danzhou"}, "city__daqing": {"message": "Daqing"}, "city__datong": {"message": "<PERSON><PERSON><PERSON>"}, "city__daxinganling": {"message": "<PERSON><PERSON>'<PERSON>ling"}, "city__dazhou": {"message": "Dazhou"}, "city__dehong": {"message": "<PERSON><PERSON>"}, "city__deyang": {"message": "Deyang"}, "city__dezhou": {"message": "Dezhou"}, "city__dingan": {"message": "Ding'an"}, "city__dingxi": {"message": "Dingxi"}, "city__diqing": {"message": "Diqing"}, "city__dongfang": {"message": "<PERSON><PERSON>g"}, "city__dongguan": {"message": "Dongguan"}, "city__dongying": {"message": "<PERSON><PERSON>"}, "city__enshi": {"message": "<PERSON><PERSON>"}, "city__ezhou": {"message": "Ezhou"}, "city__fangchenggang": {"message": "Fangchenggang"}, "city__foshan": {"message": "<PERSON><PERSON><PERSON>"}, "city__fushun": {"message": "<PERSON><PERSON><PERSON>"}, "city__fuxin": {"message": "Fuxin"}, "city__fuyang": {"message": "Fuyang"}, "city__fuzhou": {"message": "Fuzhou"}, "city__gannan": {"message": "<PERSON><PERSON><PERSON>"}, "city__ganzhou": {"message": "Ganzhou"}, "city__ganzi": {"message": "Ganzi"}, "city__guangan": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__guangyuan": {"message": "Guangyuan"}, "city__guangzhou": {"message": "Guangzhou"}, "city__guigang": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__guilin": {"message": "<PERSON><PERSON><PERSON>"}, "city__guiyang": {"message": "Guiyang"}, "city__guolok": {"message": "Guolok"}, "city__guyuan": {"message": "<PERSON><PERSON>"}, "city__haibei": {"message": "Haibei"}, "city__haidong": {"message": "Haidong"}, "city__haikou": {"message": "Hai<PERSON><PERSON>"}, "city__hainan": {"message": "Hainan"}, "city__haixi": {"message": "Haixi"}, "city__hami": {"message": "<PERSON><PERSON>"}, "city__handan": {"message": "<PERSON><PERSON>"}, "city__hangzhou": {"message": "Hangzhou"}, "city__hanzhong": {"message": "Hanzhong"}, "city__harbin": {"message": "<PERSON><PERSON><PERSON>"}, "city__hebi": {"message": "<PERSON><PERSON>"}, "city__hechi": {"message": "<PERSON><PERSON>"}, "city__hefei": {"message": "<PERSON><PERSON><PERSON>"}, "city__hegang": {"message": "<PERSON><PERSON><PERSON>"}, "city__heihe": {"message": "<PERSON><PERSON><PERSON>"}, "city__hengshui": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__hengyang": {"message": "Hengyang"}, "city__heyuan": {"message": "<PERSON><PERSON>"}, "city__heze": {"message": "<PERSON><PERSON>"}, "city__hezhou": {"message": "Hezhou"}, "city__hohhot": {"message": "Hohhot"}, "city__honghe": {"message": "<PERSON><PERSON>"}, "city__hongkongisland": {"message": "Hong Kong Island"}, "city__hotan": {"message": "<PERSON><PERSON>"}, "city__hsinchu": {"message": "<PERSON><PERSON><PERSON>"}, "city__huaian": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__huaibei": {"message": "<PERSON><PERSON><PERSON>"}, "city__huaihua": {"message": "Huaihua"}, "city__huaisouth": {"message": "Huai South"}, "city__hualien": {"message": "<PERSON><PERSON><PERSON>"}, "city__huanggang": {"message": "<PERSON><PERSON><PERSON>"}, "city__huangnan": {"message": "Huangnan"}, "city__huangshan": {"message": "<PERSON><PERSON>"}, "city__huangshi": {"message": "<PERSON><PERSON>"}, "city__huizhou": {"message": "Huizhou"}, "city__huludao": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__hulunbuir": {"message": "Hulunbuir"}, "city__huzhou": {"message": "Huzhou"}, "city__jiamusi": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__jian": {"message": "<PERSON><PERSON><PERSON>"}, "city__jiangmen": {"message": "Jiangmen"}, "city__jiaozuo": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__jiaxing": {"message": "Jiaxing"}, "city__jiayuguan": {"message": "Jiayuguan"}, "city__jieyang": {"message": "<PERSON><PERSON><PERSON>"}, "city__jilin": {"message": "<PERSON><PERSON>"}, "city__jinan": {"message": "<PERSON><PERSON>"}, "city__jinchang": {"message": "Jinchang"}, "city__jincheng": {"message": "Jincheng"}, "city__jingdezhen": {"message": "Jingdez<PERSON>"}, "city__jingmen": {"message": "Jingmen"}, "city__jingzhou": {"message": "Jingzhou"}, "city__jinhua": {"message": "Jinhua"}, "city__jining": {"message": "Jining"}, "city__jinzhong": {"message": "Jinzhong"}, "city__jinzhou": {"message": "Jinzhou"}, "city__jiujiang": {"message": "Jiujiang"}, "city__jiuquan": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__jixi": {"message": "Ji<PERSON>"}, "city__kaifeng": {"message": "Kaifeng"}, "city__kaohsiung": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__karamay": {"message": "<PERSON><PERSON><PERSON>"}, "city__kashgar": {"message": "Ka<PERSON><PERSON>"}, "city__keelung": {"message": "Keelung"}, "city__kinmen": {"message": "Kinmen"}, "city__kizilsu": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__kowloon": {"message": "Kowloon"}, "city__kunming": {"message": "Ku<PERSON><PERSON>"}, "city__laibin": {"message": "<PERSON><PERSON>"}, "city__laiwu": {"message": "<PERSON><PERSON>"}, "city__langfang": {"message": "<PERSON><PERSON><PERSON>"}, "city__lanzhou": {"message": "Lanzhou"}, "city__ledong": {"message": "<PERSON><PERSON>"}, "city__leshan": {"message": "<PERSON><PERSON>"}, "city__lhasa": {"message": "Lhasa"}, "city__liangshan": {"message": "Liangshan"}, "city__lianyungang": {"message": "Lianyungang"}, "city__liaocheng": {"message": "<PERSON><PERSON><PERSON>"}, "city__liaoyang": {"message": "<PERSON><PERSON><PERSON>"}, "city__liaoyuan": {"message": "<PERSON><PERSON><PERSON>"}, "city__lienchiang": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__lijiang": {"message": "Lijiang"}, "city__lincang": {"message": "Lincang"}, "city__linfen": {"message": "Linfen"}, "city__lingao": {"message": "Lingao"}, "city__lingshui": {"message": "<PERSON><PERSON><PERSON>"}, "city__linxia": {"message": "Lin<PERSON>"}, "city__linyi": {"message": "<PERSON><PERSON>"}, "city__lishui": {"message": "Li<PERSON><PERSON>"}, "city__liupanshui": {"message": "Liupanshu<PERSON>"}, "city__liuzhou": {"message": "Liuzhou"}, "city__longnan": {"message": "<PERSON><PERSON>"}, "city__longyan": {"message": "<PERSON><PERSON>"}, "city__loudi": {"message": "<PERSON><PERSON>"}, "city__luan": {"message": "<PERSON><PERSON><PERSON>"}, "city__luohe": {"message": "<PERSON><PERSON><PERSON>"}, "city__luoyang": {"message": "<PERSON><PERSON><PERSON>"}, "city__luzhou": {"message": "Luzhou"}, "city__lüliang": {"message": "<PERSON>眉liang"}, "city__maanshan": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__macauoutlyingislands": {"message": "Macau Outlying Islands"}, "city__macaupeninsula": {"message": "Macau Peninsula"}, "city__maoming": {"message": "<PERSON><PERSON>"}, "city__meishan": {"message": "<PERSON><PERSON>"}, "city__meizhou": {"message": "Meizhou"}, "city__mianyang": {"message": "Mianyang"}, "city__miaoli": {"message": "<PERSON><PERSON>"}, "city__mudanjiang": {"message": "Mudanjiang"}, "city__nagqu": {"message": "<PERSON>g<PERSON>u"}, "city__nanchang": {"message": "Nanchang"}, "city__nanchong": {"message": "<PERSON><PERSON><PERSON>"}, "city__nanjing": {"message": "Nanjing"}, "city__nanning": {"message": "Nanning"}, "city__nanping": {"message": "<PERSON><PERSON>"}, "city__nantong": {"message": "Nantong"}, "city__nantou": {"message": "<PERSON><PERSON><PERSON>"}, "city__nanyang": {"message": "Nanyang"}, "city__neijiang": {"message": "Neijiang"}, "city__newterritories": {"message": "New Territories"}, "city__ngari": {"message": "<PERSON><PERSON>"}, "city__ningbo": {"message": "Ningbo"}, "city__ningde": {"message": "<PERSON><PERSON><PERSON>"}, "city__nujiang": {"message": "Nujiang"}, "city__nyingchi": {"message": "Nyingchi"}, "city__ordos": {"message": "Ordos"}, "city__panjin": {"message": "Panjin"}, "city__panzhihua": {"message": "Pan Zhihua"}, "city__penghu": {"message": "<PERSON><PERSON><PERSON>"}, "city__pingdingshan": {"message": "Pingdingshan"}, "city__pingliang": {"message": "<PERSON><PERSON><PERSON>"}, "city__pingtung": {"message": "<PERSON><PERSON><PERSON>"}, "city__pingxiang": {"message": "<PERSON><PERSON><PERSON>"}, "city__puer": {"message": "<PERSON>u'er"}, "city__putian": {"message": "<PERSON><PERSON>"}, "city__puyang": {"message": "P<PERSON><PERSON>"}, "city__qiandongnan": {"message": "Qiandongnan"}, "city__qianjiang": {"message": "Qianjiang"}, "city__qiannan": {"message": "<PERSON><PERSON><PERSON>"}, "city__qianxinan": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__qingdao": {"message": "Qingdao"}, "city__qingyang": {"message": "Qingyang"}, "city__qingyuan": {"message": "Qingyuan"}, "city__qinhuangdao": {"message": "Qinhuangdao"}, "city__qinzhou": {"message": "Qinzhou"}, "city__qionghai": {"message": "Qionghai"}, "city__qiongzhong": {"message": "Qiongzhong"}, "city__qiqihar": {"message": "Qiqihar"}, "city__qitaihe": {"message": "<PERSON><PERSON><PERSON>"}, "city__quanzhou": {"message": "Quanzhou"}, "city__qujing": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__quzhou": {"message": "Quzhou"}, "city__rizhao": {"message": "<PERSON><PERSON><PERSON>"}, "city__sanmenxia": {"message": "Sanmenxia"}, "city__sanming": {"message": "Sanming"}, "city__sanya": {"message": "Sanya"}, "city__shangluo": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__shangqiu": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__shangrao": {"message": "<PERSON><PERSON><PERSON>"}, "city__shannan": {"message": "Shannan"}, "city__shantou": {"message": "<PERSON><PERSON><PERSON>"}, "city__shanwei": {"message": "Shanwei"}, "city__shaoguan": {"message": "Shaoguan"}, "city__shaoxing": {"message": "Shaoxing"}, "city__shaoyang": {"message": "Shaoyang"}, "city__shennongjiaforestarea": {"message": "Shennongjia Forest Area"}, "city__shenyang": {"message": "Shenyang"}, "city__shenzhen": {"message": "Shenzhen"}, "city__shigatse": {"message": "Shigat<PERSON>"}, "city__shihezi": {"message": "<PERSON><PERSON><PERSON>"}, "city__shijiazhuang": {"message": "Shijiazhuang"}, "city__shiyan": {"message": "<PERSON><PERSON>"}, "city__shizuishan": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__shuangyashan": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__shuozhou": {"message": "Shuozhou"}, "city__siping": {"message": "<PERSON><PERSON>"}, "city__songyuan": {"message": "Songyuan"}, "city__suihua": {"message": "Su<PERSON>ua"}, "city__suining": {"message": "Suining"}, "city__suizhou": {"message": "<PERSON><PERSON><PERSON>"}, "city__suqian": {"message": "<PERSON><PERSON><PERSON>"}, "city__suzhou": {"message": "Suzhou"}, "city__tacheng": {"message": "Tacheng"}, "city__taian": {"message": "Tai'an"}, "city__taichung": {"message": "Taichung"}, "city__tainan": {"message": "Tainan"}, "city__taipei": {"message": "Taipei"}, "city__taitung": {"message": "<PERSON><PERSON><PERSON>"}, "city__taiyuan": {"message": "Taiyuan"}, "city__taizhou": {"message": "Taizhou"}, "city__tangshan": {"message": "Tangshan"}, "city__taoyuan": {"message": "Taoyuan"}, "city__tianmen": {"message": "Tianmen"}, "city__tianshui": {"message": "<PERSON><PERSON><PERSON>"}, "city__tieling": {"message": "Tieling"}, "city__tongchuan": {"message": "<PERSON><PERSON><PERSON>"}, "city__tonghua": {"message": "Tonghua"}, "city__tongliao": {"message": "<PERSON><PERSON><PERSON>"}, "city__tongling": {"message": "Tongling"}, "city__tongren": {"message": "<PERSON><PERSON>"}, "city__tumushuke": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__tunchang": {"message": "Tunchang"}, "city__turpan": {"message": "<PERSON><PERSON><PERSON>"}, "city__ulanqab": {"message": "Ulanqab"}, "city__urumqi": {"message": "Urumqi"}, "city__wanning": {"message": "Wanning"}, "city__weifang": {"message": "<PERSON><PERSON><PERSON>"}, "city__weihai": {"message": "Weihai"}, "city__weinan": {"message": "Weinan"}, "city__wenchang": {"message": "Wenchang"}, "city__wenshan": {"message": "<PERSON><PERSON>"}, "city__wenzhou": {"message": "Wenzhou"}, "city__wuhai": {"message": "Wu<PERSON>"}, "city__wuhan": {"message": "<PERSON><PERSON>"}, "city__wuhu": {"message": "<PERSON><PERSON>"}, "city__wujiaqu": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__wuwei": {"message": "<PERSON><PERSON>"}, "city__wuxi": {"message": "Wuxi"}, "city__wuzhishan": {"message": "Wuzhishan"}, "city__wuzhong": {"message": "Wuzhong"}, "city__wuzhou": {"message": "Wuzhou"}, "city__xiamen": {"message": "Xiamen"}, "city__xian": {"message": "Xi'<PERSON>"}, "city__xiangfan": {"message": "<PERSON><PERSON><PERSON>"}, "city__xiangtan": {"message": "Xiangtan"}, "city__xiangxi": {"message": "Xiangxi"}, "city__xianning": {"message": "Xianning"}, "city__xiantao": {"message": "Xiantao"}, "city__xianyang": {"message": "<PERSON><PERSON><PERSON>"}, "city__xiaogan": {"message": "<PERSON><PERSON>"}, "city__xilingol": {"message": "Xilingol"}, "city__xinganleague": {"message": "Xing'an League"}, "city__xingtai": {"message": "Xingtai"}, "city__xining": {"message": "Xining"}, "city__xinxiang": {"message": "Xinxiang"}, "city__xinyang": {"message": "Xinyang"}, "city__xinyu": {"message": "Xinyu"}, "city__xinzhou": {"message": "Xinzhou"}, "city__xishuangbanna": {"message": "Xishuangbanna"}, "city__xuancheng": {"message": "Xuancheng"}, "city__xuchang": {"message": "Xuchang"}, "city__xuzhou": {"message": "Xuzhou"}, "city__yaan": {"message": "Ya'an"}, "city__yanan": {"message": "Yan'<PERSON>"}, "city__yanbian": {"message": "Yanbian"}, "city__yancheng": {"message": "Yancheng"}, "city__yangjiang": {"message": "Yangjiang"}, "city__yangquan": {"message": "<PERSON><PERSON><PERSON>"}, "city__yangzhou": {"message": "Yangzhou"}, "city__yantai": {"message": "Yantai"}, "city__yibin": {"message": "<PERSON><PERSON>"}, "city__yichang": {"message": "Yichang"}, "city__yichun": {"message": "<PERSON><PERSON><PERSON>"}, "city__yilan": {"message": "Yilan"}, "city__yili": {"message": "<PERSON><PERSON>"}, "city__yinchuan": {"message": "<PERSON><PERSON><PERSON>"}, "city__yingkou": {"message": "<PERSON><PERSON><PERSON>"}, "city__yingtan": {"message": "Yingtan"}, "city__yiwu": {"message": "<PERSON><PERSON>"}, "city__yiyang": {"message": "Yiyang"}, "city__yongzhou": {"message": "Yongzhou"}, "city__yueyang": {"message": "<PERSON><PERSON><PERSON>"}, "city__yulin": {"message": "Yulin"}, "city__yuncheng": {"message": "Yuncheng"}, "city__yunfu": {"message": "<PERSON><PERSON>"}, "city__yunlin": {"message": "<PERSON><PERSON>"}, "city__yushu": {"message": "Yushu"}, "city__yuxi": {"message": "Yuxi"}, "city__zaozhuang": {"message": "Zaozhuang"}, "city__zhangjiajie": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__zhangjiakou": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__zhangye": {"message": "<PERSON><PERSON>"}, "city__zhangzhou": {"message": "Zhangzhou"}, "city__zhanjiang": {"message": "Zhanjiang"}, "city__zhaoqing": {"message": "Zhaoqing"}, "city__zhaotong": {"message": "Zhaotong"}, "city__zhengzhou": {"message": "Zhengzhou"}, "city__zhenjiang": {"message": "Zhenjiang"}, "city__zhongshan": {"message": "Zhongshan"}, "city__zhongwei": {"message": "Zhongwei"}, "city__zhoukou": {"message": "<PERSON><PERSON><PERSON>"}, "city__zhoushan": {"message": "Zhoushan"}, "city__zhuhai": {"message": "Zhu<PERSON>"}, "city__zhumadian": {"message": "Zhumadian"}, "city__zhuzhou": {"message": "Zhuzhou"}, "city__zibo": {"message": "Zibo"}, "city__zigong": {"message": "Zigong"}, "city__ziyang": {"message": "<PERSON><PERSON><PERSON>"}, "city__zunyi": {"message": "<PERSON><PERSON><PERSON>"}, "click_copy_product_info": {"message": "Kliknij Kopiuj informacje o produkcie"}, "commmon_txt_expired": {"message": "Przedawniony"}, "common__date_range_12m": {"message": "1 rok"}, "common__date_range_1m": {"message": "1 miesiąc"}, "common__date_range_1w": {"message": "1 tydzień"}, "common__date_range_2w": {"message": "2 tygodnie"}, "common__date_range_3m": {"message": "3 miesiące"}, "common__date_range_3w": {"message": "3 tygodnie"}, "common__date_range_6m": {"message": "6 miesięcy"}, "common_btn_cancel": {"message": "<PERSON><PERSON><PERSON>"}, "common_btn_close": {"message": "Zamknij"}, "common_btn_save": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "common_btn_setting": {"message": "Konfiguracja"}, "common_email": {"message": "E-mail"}, "common_error_msg_no_data": {"message": "<PERSON><PERSON> da<PERSON>"}, "common_error_msg_no_result": {"message": "<PERSON><PERSON><PERSON>, nie znaleziono wynik."}, "common_favorites": {"message": "Ulubione"}, "common_feedback": {"message": "Informacje zwrotne"}, "common_help": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "common_loading": {"message": "Ład<PERSON><PERSON><PERSON>"}, "common_login": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "common_logout": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "common_no": {"message": "<PERSON><PERSON>"}, "common_powered_by_aliprice": {"message": "Obsługiwane przez AliPrice.com"}, "common_setting": {"message": "Oprawa"}, "common_sign_up": {"message": "Zarejestruj się"}, "common_system_upgrading_title": {"message": "Aktualizacja systemu"}, "common_system_upgrading_txt": {"message": "Spróbuj później"}, "common_txt__currency": {"message": "<PERSON><PERSON><PERSON>"}, "common_txt__video_tutorial": {"message": "Film instruktażowy"}, "common_txt_ago_time": {"message": "$time$ dni temu", "placeholders": {"time": {"content": "$1"}}}, "common_txt_all_product": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "common_txt_analysis": {"message": "<PERSON><PERSON><PERSON>"}, "common_txt_basically_used": {"message": "<PERSON><PERSON><PERSON> nigdy <PERSON>"}, "common_txt_biaoti_link": {"message": "Tytuł+Link"}, "common_txt_biaoti_link_dian_pu": {"message": "Tytuł+Link+Nazwa sklepu"}, "common_txt_blacklist": {"message": "<PERSON>a zablokowanych"}, "common_txt_cancel": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "common_txt_category": {"message": "Kategoria"}, "common_txt_chakan": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "common_txt_colors": {"message": "zabarwienie"}, "common_txt_confirm": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "common_txt_copied": {"message": "Skopiowano"}, "common_txt_copy": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "common_txt_copy_link": {"message": "Skopiuj link"}, "common_txt_copy_title": {"message": "<PERSON><PERSON><PERSON><PERSON> t<PERSON>"}, "common_txt_copy_title__link": {"message": "Skopiuj tytuł i link"}, "common_txt_day": {"message": "niebo"}, "common_txt_day__short": {"message": "D"}, "common_txt_delete": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "common_txt_dian_pu_link": {"message": "Skopiuj nazwę sklepu + link"}, "common_txt_download": {"message": "Pobieranie"}, "common_txt_downloaded": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "common_txt_export_as_csv": {"message": "Eksportuj Excel"}, "common_txt_export_as_txt": {"message": "Eksportuj txt"}, "common_txt_fail": {"message": "Ponieść porażkę"}, "common_txt_format": {"message": "Format"}, "common_txt_get": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "common_txt_incert_selection": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> w<PERSON>"}, "common_txt_install": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "common_txt_load_failed": {"message": "<PERSON><PERSON> udało się załadowa<PERSON>"}, "common_txt_month": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "common_txt_more": {"message": "<PERSON><PERSON><PERSON><PERSON>j"}, "common_txt_new_unused": {"message": "Zupełnie nowy, nieużywany"}, "common_txt_next": {"message": "Kolejny"}, "common_txt_no_limit": {"message": "Nieograniczony"}, "common_txt_no_noticeable": {"message": "Brak widocznych zarysowań i zabrudzeń"}, "common_txt_on_sale": {"message": "Dostępny"}, "common_txt_opt_in_out": {"message": "Wł./wył"}, "common_txt_order": {"message": "Zamówienie"}, "common_txt_others": {"message": "<PERSON><PERSON>"}, "common_txt_overall_poor_condition": {"message": "Ogólny stan zły"}, "common_txt_patterns": {"message": "Wzory"}, "common_txt_platform": {"message": "Platformy"}, "common_txt_please_select": {"message": "<PERSON><PERSON><PERSON> w<PERSON>"}, "common_txt_prev": {"message": "<PERSON>rz"}, "common_txt_price": {"message": "Cena £"}, "common_txt_privacy_policy": {"message": "Polityka p<PERSON>watności"}, "common_txt_product_condition": {"message": "Status produktu"}, "common_txt_rating": {"message": "Ocena"}, "common_txt_ratings": {"message": "<PERSON><PERSON><PERSON>"}, "common_txt_reload": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "common_txt_reset": {"message": "Resetowanie"}, "common_txt_review": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "common_txt_sale": {"message": "Dostępny"}, "common_txt_same": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "common_txt_scratches_and_dirt": {"message": "Z zadrapaniami i brudem"}, "common_txt_search_title": {"message": "<PERSON>ys<PERSON><PERSON> tytuł"}, "common_txt_select_all": {"message": "Zaznacz wszystko"}, "common_txt_selected": {"message": "wybrany"}, "common_txt_share": {"message": "Udostępnij to"}, "common_txt_sold": {"message": "sprzedany"}, "common_txt_sold_out": {"message": "Wyprzedane"}, "common_txt_some_scratches": {"message": "Kilka rys i brudu"}, "common_txt_sort_by": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "common_txt_state": {"message": "Państwowy"}, "common_txt_success": {"message": "Powodzenie"}, "common_txt_sys_err": {"message": "błąd systemu"}, "common_txt_today": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "common_txt_total": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "common_txt_unselect_all": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> w<PERSON>"}, "common_txt_upload_image": {"message": "Załaduj obrazek"}, "common_txt_visit": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "common_txt_whitelist": {"message": "Biała lista"}, "common_txt_wildberries": {"message": "Wildberries"}, "common_txt_year": {"message": "Rok"}, "common_yes": {"message": "tak"}, "compare_tool_btn_clear_all": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> wszystko"}, "compare_tool_btn_compare": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "compare_tool_btn_contact": {"message": "Kontakt"}, "compare_tool_tips_max_compared": {"message": "Adicione até $maxComparedCount$", "placeholders": {"maxComparedCount": {"content": "$1"}}}, "configure_notifiactions": {"message": "Skonfiguruj powiadomienia"}, "contact_us": {"message": "Skontaktuj się z nami"}, "context_menu_screenshot_search": {"message": "Wyszukiwanie zrzutów ekranu dla tego samego stylu"}, "context_menus_aliprice_search_by_image": {"message": "<PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON>"}, "context_menus_goote_trans": {"message": "Przetłumacz stronę/<PERSON>każ orygin<PERSON>ł"}, "context_menus_search_by_image": {"message": "Szukaj obrazem na $storeName$", "placeholders": {"storeName": {"content": "$1"}}}, "context_menus_search_by_image_capture": {"message": "Przechwyć do $storeName$", "placeholders": {"storeName": {"content": "$1"}}}, "context_menus_translator": {"message": "Przechwyć do przetłumaczenia"}, "converter_modal_amount_placeholder": {"message": "Wpisz kwotę tutaj"}, "converter_modal_btn_convert": {"message": "konwertować"}, "converter_modal_exchange_rate_source": {"message": "Dane pochodzą z kursu walut $boc$ Czas aktualizacji: $updatedAt$", "placeholders": {"boc": {"content": "$1"}, "updatedAt": {"content": "$2"}}}, "converter_modal_name": {"message": "wymiana walut"}, "converter_modal_search_placeholder": {"message": "s<PERSON>j waluty"}, "copy_all_contact_us_notice": {"message": "<PERSON> strona nie jest obecnie obsługiwana, prosimy o kontakt"}, "copy_product_info": {"message": "Skopiuj informacje o produkcie"}, "copy_suggest_search_kw": {"message": "<PERSON><PERSON><PERSON><PERSON> <PERSON>y rozwijan<PERSON>"}, "country__han_gou": {"message": "Korea Południowa"}, "country__ri_ben": {"message": "Japonia"}, "country__yue_nan": {"message": "Wietnam"}, "currency_convert__custom": {"message": "Niestandardowy kurs wymiany"}, "currency_convert__sync_server": {"message": "Synchroni<PERSON><PERSON> serwer"}, "dang_ri_fa_huo": {"message": "Wysyłka tego samego dnia"}, "dao_chu_quan_dian_shang_pin": {"message": "Eksportuj wszystkie produkty ze sklepu"}, "dao_chu_wei_CSV": {"message": "Eksport"}, "dao_chu_zi_duan": {"message": "Eksportuj pola"}, "delivery_address": {"message": "<PERSON><PERSON> w<PERSON>"}, "delivery_company": {"message": "<PERSON><PERSON>"}, "di_zhi": {"message": "adres"}, "dian_ji_cha_xun": {"message": "<PERSON><PERSON><PERSON><PERSON>, aby wysłać zapytanie"}, "dian_pu_ID": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> sklepu"}, "dian_pu_di_zhi": {"message": "<PERSON><PERSON> skle<PERSON>"}, "dian_pu_lian_jie": {"message": "Link do sklepu"}, "dian_pu_ming": {"message": "Nazwa sklepu"}, "dian_pu_ming_cheng": {"message": "Nazwa sklepu"}, "dian_pu_shang_pin_zong_hsu": {"message": "Całkowita liczba produktów w sklepie: $num$", "placeholders": {"num": {"content": "$1"}}}, "dian_pu_xin_xi": {"message": "Przechowaj informację"}, "ding_zai_zuo_ce": {"message": "przybity do lewej"}, "download_image__SKU_variant_images": {"message": "Warianty obrazów SKU"}, "download_image__assume": {"message": "Na przykład mamy 2 obrazy, product1.jpg i product2.gif.\nimg_{$no$} zostanie przemianowane na img_01.jpg, img_02.gif;\n{$group$}_{$no$} zostanie przemianowane na main_image_01.jpg, main_image_02.gif;", "placeholders": {"no": {"content": "$3"}, "group": {"content": "$2"}}}, "download_image__batch_download": {"message": "<PERSON>bie<PERSON><PERSON> wsadowe"}, "download_image__combined_image": {"message": "Połączony obraz ze szczegółami produktu"}, "download_image__continue_downloading": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "download_image__description_images": {"message": "<PERSON><PERSON><PERSON> opisowe"}, "download_image__download_combined_image": {"message": "Pobierz połączony obraz ze szczegółami produktu"}, "download_image__download_zip": {"message": "Pobierz plik zip"}, "download_image__enlarge_check": {"message": "Obsługuje tylko obrazy JPEG, JPG, GIF i PNG, maksymalny rozmiar pojedynczego obrazu: 1600 * 1600"}, "download_image__enlarge_image": {"message": "Powiększ obraz"}, "download_image__export": {"message": "Eksportuj linki"}, "download_image__height": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "download_image__ignore_videos": {"message": "Film został <PERSON>, ponieważ nie można go wyeksportować"}, "download_image__img_translate": {"message": "Przetłumacz obraz"}, "download_image__main_image": {"message": "główny obraz"}, "download_image__multi_folder": {"message": "Wiele folderów"}, "download_image__name": {"message": "pob<PERSON>z obraz"}, "download_image__notice_content": {"message": "Proszę nie zaznaczać opcji \"Pytaj gdzie zapisać każdy plik przed pobraniem\" w ustawieniach pobierania swojej przeglądarki!!! W przeciwnym razie pojawi się wiele okien dialogowych."}, "download_image__notice_ignore": {"message": "Nie pytaj ponownie o tę wiadomość"}, "download_image__order_number": {"message": "{$no$} numer seryjny; {$group$} nazwa grupy; {$date$} znacznik czasu", "placeholders": {"no": {"content": "$1"}, "group": {"content": "$2"}, "date": {"content": "$3"}}}, "download_image__overview": {"message": "Przegląd"}, "download_image__prompt_download_zip": {"message": "Jest za dużo obrazów, le<PERSON>j pobrać je jako folder zip."}, "download_image__rename": {"message": "Zmień nazwę"}, "download_image__rule": {"message": "<PERSON><PERSON><PERSON> nazewnict<PERSON>"}, "download_image__single_folder": {"message": "Pojedynczy folder"}, "download_image__sku_image": {"message": "Obrazy SKU"}, "download_image__video": {"message": "wideo"}, "download_image__width": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "download_reviews__download_images": {"message": "Pobierz obraz recenzji"}, "download_reviews__dropdown_title": {"message": "Pobierz obraz recenzji"}, "download_reviews__export_csv": {"message": "wyeksportuj plik CSV"}, "download_reviews__no_images": {"message": "0 zdjęć dostępnych do pobrania"}, "download_reviews__no_reviews": {"message": "Brak recenzji do pobrania!"}, "download_reviews__notice": {"message": "Wskazówka:"}, "download_reviews__notice__chrome_settings": {"message": "Ustaw przeglądarkę Chrome tak, aby pytała, g<PERSON><PERSON> zapisa<PERSON> każdy plik przed pobraniem, ustaw na „Wyłączone”"}, "download_reviews__notice__wait": {"message": "W zależności od ilości recenzji czas oczekiwania może się wydłużyć"}, "download_reviews__pages_list__all": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "download_reviews__pages_list__page": {"message": "Poprzednie strony $page$", "placeholders": {"page": {"content": "$1"}}}, "download_reviews__pages_list_title": {"message": "Zakres wyboru"}, "export_shopping_cart__csv_filed__details_url": {"message": "Link do produktu"}, "export_shopping_cart__csv_filed__details_url_with_sku": {"message": "Link do jednostki SKU echa"}, "export_shopping_cart__csv_filed__images": {"message": "Link do obrazu"}, "export_shopping_cart__csv_filed__quantity": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "export_shopping_cart__csv_filed__sale_price": {"message": "<PERSON><PERSON>"}, "export_shopping_cart__csv_filed__specs": {"message": "Specyfikacje"}, "export_shopping_cart__csv_filed__store_name": {"message": "Nazwa sklepu"}, "export_shopping_cart__csv_filed__store_url": {"message": "Link do sklepu"}, "export_shopping_cart__csv_filed__title": {"message": "Nazwa produktu"}, "export_shopping_cart__export_btn": {"message": "Eksport"}, "export_shopping_cart__export_empty": {"message": "Wybierz produkt!"}, "fa_huo_shi_jian": {"message": "Wysyłka"}, "favorite_add_email": {"message": "Dodaj e-mail"}, "favorite_add_favorites": {"message": "Dodaj do ulubionych"}, "favorite_added": {"message": "Dodano"}, "favorite_btn_add": {"message": "Alarm Spadku Ceny."}, "favorite_btn_notify": {"message": "<PERSON><PERSON><PERSON>"}, "favorite_cate_name_all": {"message": "Wszystkie produkty"}, "favorite_current_price": {"message": "Aktualna cena"}, "favorite_due_date": {"message": "Data pła<PERSON>ności"}, "favorite_enable_notification": {"message": "Włącz powiadomienia e-mail"}, "favorite_expired": {"message": "<PERSON><PERSON><PERSON>ł<PERSON>"}, "favorite_go_to_enable": {"message": "Przejdź do włączenia"}, "favorite_msg_add_success": {"message": "Dodaj do ulubionych"}, "favorite_msg_del_success": {"message": "Usuń z ulubionych"}, "favorite_msg_failure": {"message": "Zawieść! Odśwież stronę i spróbuj ponownie."}, "favorite_please_add_email": {"message": "Dodaj e-mail"}, "favorite_price_drop": {"message": "<PERSON> dół"}, "favorite_price_rise": {"message": "W górę"}, "favorite_price_untracked": {"message": "<PERSON><PERSON>"}, "favorite_saved_price": {"message": "Zapisana cena"}, "favorite_stop_tracking": {"message": "Zatrzymaj <PERSON>"}, "favorite_sub_email_address": {"message": "Adres e-mail subskrypcji"}, "favorite_tracking_period": {"message": "Okres śledzenia"}, "favorite_tracking_prices": {"message": "Śledzenie cen"}, "favorite_verify_email": {"message": "Zweryfikuj adres e-mail"}, "favorites_list_remove_prompt_msg": {"message": "<PERSON>zy na pewno chcesz to usunąć?"}, "favorites_update_button": {"message": "Zaktualizuj ceny teraz"}, "fen_lei": {"message": "Kategoria"}, "fen_xia_yan_xuan": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON> dystry<PERSON>"}, "find_similar": {"message": "Znajdź podobne"}, "first_ali_price_date": {"message": "Data pierwszego przechwycenia przez robota AliPrice"}, "fooview_coupons_modal_no_data": {"message": "Brak kuponów"}, "fooview_coupons_modal_title": {"message": "<PERSON><PERSON><PERSON>"}, "fooview_favorites__product_sub__tooltip_l1": {"message": "Cena < $lowerPrice$", "placeholders": {"lowerPrice": {"content": "$1"}}}, "fooview_favorites__product_sub__tooltip_l2": {"message": "lub cena > $higherPrice$", "placeholders": {"higherPrice": {"content": "$1"}}}, "fooview_favorites__product_sub__tooltip_l3": {"message": "Ostateczny termin"}, "fooview_favorites_error_msg_no_favorites": {"message": "Dodaj ulubione produkty tutaj, aby <PERSON><PERSON><PERSON><PERSON> alert o spadku cen."}, "fooview_favorites_filter_latest": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "fooview_favorites_filter_price_drop": {"message": "W GÓRĘ"}, "fooview_favorites_filter_price_up": {"message": "NA DÓŁ"}, "fooview_favorites_modal_title": {"message": "<PERSON><PERSON>"}, "fooview_favorites_modal_title_title": {"message": "Przejdź do Ulubionego AliPrice"}, "fooview_favorites_track_price": {"message": "<PERSON><PERSON> cenę"}, "fooview_price_history_app_price": {"message": "Cena w aplikacji :"}, "fooview_price_history_title": {"message": "Historia cen"}, "fooview_product_list_feedback": {"message": "Ocena"}, "fooview_product_list_orders": {"message": "Zamówienie"}, "fooview_product_list_price": {"message": "<PERSON><PERSON>"}, "fooview_reviews_error_msg_no_review": {"message": "Nie znaleźliśmy żadnych recenzji o tym produkcie."}, "fooview_reviews_filter_buyer_reviews": {"message": "Zdjęcia kupujących"}, "fooview_reviews_modal_title": {"message": "Opinie"}, "fooview_same_product_choose_category": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "fooview_same_product_filter_feedback": {"message": "Ocena"}, "fooview_same_product_filter_orders": {"message": "Zamówienie"}, "fooview_same_product_filter_price": {"message": "<PERSON><PERSON>"}, "fooview_same_product_filter_rating": {"message": "Ocena"}, "fooview_same_product_modal_title": {"message": "Znaleźć ten sam produkt"}, "fooview_same_product_search_by_image": {"message": "<PERSON><PERSON><PERSON> według obrazu"}, "fooview_seller_analysis_modal_title": {"message": "Sprzedawca Analiza"}, "for_12_months": {"message": "Na 1 rok"}, "for_12_months_list_pro": {"message": "12 miesięcy"}, "for_12_months_nei": {"message": "W ciągu 12 miesięcy"}, "for_1_months": {"message": "1 miesiąc"}, "for_1_months_nei": {"message": "W ciągu 1 miesiąca"}, "for_3_months": {"message": "Za 3 miesięcy"}, "for_3_months_nei": {"message": "W ciągu 3 miesięcy"}, "for_6_months": {"message": "Za 6 miesięcy"}, "for_6_months_nei": {"message": "W ciągu 6 miesięcy"}, "for_9_months": {"message": "9 miesięcy"}, "for_9_months_nei": {"message": "W ciągu 9 miesięcy"}, "fu_gou_lv": {"message": "<PERSON><PERSON>"}, "gao_liang_bu_tong_dian": {"message": "zaznacz różnice"}, "gao_liang_guang_gao_chan_pin": {"message": "Wyróżnij produkty reklamowe"}, "geng_duo_xin_xi": {"message": "Więcej informacji"}, "geng_xin_shi_jian": {"message": "Czas aktualizacji"}, "get_store_products_fail_tip": {"message": "Kliknij OK, aby przejść do weryfikacji w celu zapewnienia normalnego dostępu"}, "gong_x_kuan_shang_pin": {"message": "Łącznie $amount$ produktów", "placeholders": {"amount": {"content": "$1"}}}, "gong_ying_shang": {"message": "Dostawca"}, "gong_ying_shang_ID": {"message": "identy<PERSON><PERSON><PERSON> dostawcy"}, "gong_ying_shang_deng_ji": {"message": "<PERSON><PERSON><PERSON> dos<PERSON>"}, "gong_ying_shang_nian_zhan": {"message": "Dostawca jest starszy"}, "gong_ying_shang_xin_xi": {"message": "informacje o dostawcy"}, "gong_ying_shang_zhu_ye_lian_jie": {"message": "Link do strony głównej dostawcy"}, "green_rocket": {"message": "로켓프레시"}, "grey_rocket": {"message": "로켓설치"}, "gu_ji_shou_jia": {"message": "Szacowana cena s<PERSON>rz<PERSON>y"}, "guan_jian_zi": {"message": "Słowo kluczowe"}, "guang_gao_chan_pin": {"message": "Produkty reklamowe"}, "guang_gao_zhan_bi": {"message": "Współczynnik reklam"}, "guo_ji_wu_liu_yun_fei": {"message": "Opłata za wysyłkę międzynarodową"}, "guo_lv_tiao_jian": {"message": "Filtry"}, "hao_ping_lv": {"message": "Pozytywna ocena"}, "highest_price": {"message": "<PERSON><PERSON><PERSON>"}, "historical_trend": {"message": "Trend historyczny"}, "how_to_screenshot": {"message": "Przytrzymaj lewy przycisk myszy, aby wy<PERSON><PERSON> obszar, naciśnij prawy przycisk myszy lub klaw<PERSON>z Esc, aby wyj<PERSON><PERSON> z ekranu"}, "howt_it_works": {"message": "Jak to działa"}, "hui_fu_lv": {"message": "Odsetek odpowiedzi"}, "hui_tou_lv": {"message": "stopa zwrotu"}, "inquire_freightFee": {"message": "Zapytanie o fracht"}, "inquire_freightFee_Yuan": {"message": "Fracht/Yuan"}, "inquire_freightFee_province": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "inquire_freightFee_the": {"message": "Fracht to $num$, co oznacza, że ​​region oferuje bezpłatną wysyłkę.", "placeholders": {"num": {"content": "$1"}}}, "is_ad": {"message": "Ogłoszenie."}, "jia_ge": {"message": "<PERSON><PERSON>"}, "jia_ge_dan_wei": {"message": "Jednostka"}, "jia_ge_qu_shi": {"message": "Tenden<PERSON><PERSON>"}, "jia_zai_n_ge_shang_pin": {"message": "Załaduj $num$ produktów", "placeholders": {"num": {"content": "$1"}}}, "jin_30_tian_xiao_liang_zhan_bi": {"message": "Procent wolumenu sprzedaży w ciągu ostatnich 30 dni"}, "jin_30_tian_xiao_shou_e_zhan_bi": {"message": "Procent przychodu w ciągu ostatnich 30 dni"}, "jin_30d_xiao_liang": {"message": "<PERSON><PERSON><PERSON>"}, "jin_30d_xiao_liang__desc": {"message": "Całkowita sprzedaż w ciągu ostatnich 30 dni"}, "jin_30d_xiao_shou_e": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "jin_30d_xiao_shou_e__desc": {"message": "Całkowity obrót w ciągu ostatnich 30 dni"}, "jin_90_tian_mai_jia_shu": {"message": "Kupujący w ciągu ostatnich 90 dni"}, "jin_90_tian_xiao_shou_liang": {"message": "Sprzedaż w ciągu ostatnich 90 dni"}, "jing_xuan_huo_yuan": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "jing_ying_mo_shi": {"message": "Model biznesowy"}, "jing_ying_mo_shi__gong_chang": {"message": "Producent"}, "jiu_fen_jie_jue": {"message": "Rozwiązywanie sporów"}, "jiu_fen_jie_jue__desc": {"message": "Rozliczanie sporów dotyczących praw sklepowych sprzedawców"}, "jiu_fen_lv": {"message": "Wskaźnik sporu"}, "jiu_fen_lv__desc": {"message": "Część zamówień z reklamacjami zrealizowanymi w ciągu ostatnich 30 dni, za które odpowiedzialność ponosi sprzedawca lub obie strony"}, "kai_dian_ri_qi": {"message": "Data otwarcia"}, "keywords": {"message": "Słowa kluczowe"}, "kua_jin_Select_pan_huo": {"message": "Wybór transgraniczny"}, "last15_days": {"message": "Ostatnie 15 dni"}, "last180_days": {"message": "Ostatnie 180 dni"}, "last30_days": {"message": "W ciągu ostatnich 30 dni"}, "last360_days": {"message": "Ostatnie 360 ​​dni"}, "last45_days": {"message": "Ostatnie 45 dni"}, "last60_days": {"message": "Ostatnie 60 dni"}, "last7_days": {"message": "Ostatnie 7 dni"}, "last90_days": {"message": "Ostatnie 90 dni"}, "last_30d_sales": {"message": "Sprzedaż z ostatnich 30 dni"}, "lei_ji": {"message": "Łączny"}, "lei_ji_xiao_liang": {"message": "Całkowity"}, "lei_ji_xiao_liang__desc": {"message": "Cała sprzedaż po produkcie na półce"}, "lei_ji_xiao_liang_pai_xu__desc": {"message": "Skumulowany wolumen sprzedaży w ciągu ostatnich 30 dni, posortowany od najwyższej do najniższej"}, "lian_xi_fang_shi": {"message": "Informacje kontaktowe"}, "list_time": {"message": "Data przydatności do spożycia"}, "load_more": {"message": "Załaduj więcej"}, "login_to_aliprice": {"message": "Zaloguj się do AliPrice"}, "long_link": {"message": "Długi link"}, "lowest_price": {"message": "<PERSON><PERSON>"}, "mai_jia_shu": {"message": "Sprzedawcy"}, "mao_li_lv": {"message": "Marża"}, "mobile_view__dkxbqy": {"message": "Otwórz nową kartę"}, "mobile_view__sjdxq": {"message": "Szczegóły w aplikacji"}, "mobile_view__sjdxqy": {"message": "Strona szczegółów w aplikacji"}, "mobile_view__smck": {"message": "<PERSON><PERSON><PERSON><PERSON>, aby w<PERSON>"}, "mobile_view__smckms": {"message": "Użyj aparatu lub aplikacji, aby zeskanować i wyświetlić"}, "modified_failed": {"message": "Modyfikacja nie powiodła się"}, "modified_successfully": {"message": "Zmodyfikowan<PERSON>"}, "nav_btn_favorites": {"message": "<PERSON><PERSON>"}, "nav_btn_package": {"message": "pakiet"}, "nav_btn_product_info": {"message": "O produkcie"}, "nav_btn_viewed": {"message": "Oglądane"}, "no_suggest_search_kw": {"message": "Loading..."}, "none": {"message": "Brak"}, "normal_link": {"message": "Normalny link"}, "notice": {"message": "Wskazówka"}, "number_reviews": {"message": "Opinie"}, "only_show_num": {"message": "Łączna liczba produktów: $allnum$, Ukryte: $hidenum2$", "placeholders": {"allnum": {"content": "$1"}, "hidenum2": {"content": "$2"}}}, "only_show_selected": {"message": "Usuń niezaznaczone"}, "open": {"message": "Otwórz"}, "open_links": {"message": "Otwórz linki"}, "options_page_tab_check_links": {"message": "Sprawdź linki"}, "options_page_tab_gernal": {"message": "Gene<PERSON>ł"}, "options_page_tab_notifications": {"message": "Powiadomienia"}, "options_page_tab_others": {"message": "<PERSON><PERSON>"}, "options_page_tab_sbi": {"message": "<PERSON><PERSON><PERSON> według obrazu"}, "options_page_tab_shortcuts": {"message": "Skróty"}, "options_page_tab_shortcuts_title": {"message": "Rozmiar czcionki dla skrótów"}, "options_page_tab_similar_products": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "orange_rocket": {"message": "판매자로켓"}, "order_list_open_links_tip": {"message": "Wiele linków do produktów zostanie otwartych"}, "order_list_sku_show_title": {"message": "Pokaż wybrane warianty w udostępnionych linkach"}, "orders_last30_days": {"message": "Liczba zamówień w ciągu ostatnich 30 dni"}, "pTutorial_favorites_block1_desc1": {"message": "Produkty, które śledziłeś, są tutaj wymienione"}, "pTutorial_favorites_block1_title": {"message": "Ulubione"}, "pTutorial_popup_block1_desc1": {"message": "Zielona etykieta oznacza, że istnieją produkty o obniżonej cenie"}, "pTutorial_popup_block1_title": {"message": "Skróty i ulubione"}, "pTutorial_price_history_block1_desc1": {"message": "Kliknij „Śledź cenę”, dodaj produkty do ulubionych. Gdy ich ceny spadną, b<PERSON><PERSON><PERSON><PERSON> otrzymywać powiadomienia"}, "pTutorial_price_history_block1_title": {"message": "Śledź cenę"}, "pTutorial_reviews_block1_desc1": {"message": "Opinie kupujących z Itao i prawdziwe zdjęcia z opinii AliExpress"}, "pTutorial_reviews_block1_title": {"message": "Recenzje"}, "pTutorial_reviews_block2_desc1": {"message": "<PERSON><PERSON><PERSON> pomocne jest sprawdzanie opinii innych kupujących"}, "pTutorial_same_products_block1_desc1": {"message": "<PERSON><PERSON><PERSON><PERSON> je p<PERSON>, aby do<PERSON><PERSON>ć najlepszego wyboru"}, "pTutorial_same_products_block1_desc2": {"message": "<PERSON><PERSON><PERSON><PERSON> „<PERSON><PERSON><PERSON><PERSON><PERSON>”, aby „Wyszukaj według obrazu”"}, "pTutorial_same_products_block1_title": {"message": "Te same produkty"}, "pTutorial_same_products_by_image_search_block1_desc1": {"message": "Upuść tam obraz produktu i wybierz kategorię"}, "pTutorial_same_products_by_image_search_block1_title": {"message": "<PERSON><PERSON><PERSON> według obrazu"}, "pTutorial_seller_analysis_block1_desc1": {"message": "Pozytywny wskaźnik opinii sprzedającego, oceny opinii oraz czas, przez jaki sprzedawca jest na rynku"}, "pTutorial_seller_analysis_block1_title": {"message": "<PERSON><PERSON><PERSON> sprzedaw<PERSON>"}, "pTutorial_seller_analysis_block2_desc2": {"message": "Ocena sprzedawcy oparta jest na 3 indeksach: pozycja zgodna z opisem, komunikacja Szybkość wysyłki"}, "pTutorial_seller_analysis_block3_desc3": {"message": "Używamy 3 kolorów i ikon, aby wskazać poziom zaufania sprzedawców"}, "page_count": {"message": "Numer stron"}, "pai_chu": {"message": "Wyłączony"}, "pai_chu_HK_bu_yi_xia_xiaoshou": {"message": "Wyklucz Hongkong — ograniczenia"}, "pai_chu_JP_bu_yi_xia_xiaoshou": {"message": "Wyklucz Japonia — ograniczenia"}, "pai_chu_KR_bu_yi_xia_xiaoshou": {"message": "Wyklucz Korea — ograniczenia"}, "pai_chu_KZ_bu_yi_xia_xiaoshou": {"message": "Wyklucz Kazachstan — ograniczenia"}, "pai_chu_MO_bu_yi_xia_xiaoshou": {"message": "Wyklucz Makau — ograniczenia"}, "pai_chu_RU_bu_yi_xia_xiaoshou": {"message": "Wyklucz Europa Wschodnia — ograniczenia"}, "pai_chu_SA_bu_yi_xia_xiaoshou": {"message": "Wyklucz Arabia Saudyjska — ograniczenia"}, "pai_chu_TW_bu_yi_xia_xiaoshou": {"message": "Wyklucz Tajwan — ograniczenia"}, "pai_chu_USA_bu_yi_xia_xiaoshou": {"message": "Wyklucz USA — ograniczenia"}, "pai_chu_VN_bu_yi_xia_xiaoshou": {"message": "Wyklucz Wietnam — ograniczenia"}, "pai_chu_bu_yi_xia_xiaoshou": {"message": "Wyklucz produkty objęte ograniczeniami"}, "payable_price_formula": {"message": "Cena + Wysyłka + Rabat"}, "pdd_check_retail_btn_txt": {"message": "Sprawdź detaliczny"}, "pdd_pifa_to_retail_btn_txt": {"message": "Kup w sprzedaży detalicznej"}, "pdp_copy_fail": {"message": "Kopiowanie nie powiodło się!"}, "pdp_copy_success": {"message": "Kopiowanie powiodło się!"}, "pdp_share_modal_subtitle": {"message": "Udostępnij zrzut ekranu, on/ona zobaczy Twój wybór."}, "pdp_share_modal_title": {"message": "Podziel się swoim wyborem"}, "pdp_share_screenshot": {"message": "Udostępnij zrzut ekranu"}, "pei_song": {"message": "Wysyłka"}, "pin_lei": {"message": "Kategoria"}, "pin_zhi_ti_yan": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON> produktu"}, "pin_zhi_ti_yan__desc": {"message": "Stawka zwrotu jakości w sklepie sprzedawcy"}, "pin_zhi_tui_kuan_lv": {"message": "Stopa zwrotu"}, "pin_zhi_tui_kuan_lv__desc": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON> zamówień, w przypadku których zwrócono środki i zwrócono je w ciągu ostatnich 30 dni"}, "ping_fen": {"message": "Ocena"}, "ping_jun_fa_huo_su_du": {"message": "Średnia prędkość wysyłki"}, "pkgInfo_hide": {"message": "Informacje logistyczne: wł./wył"}, "pkgInfo_no_trace": {"message": "Brak informacji logistycznych"}, "platform_name__1688": {"message": "1688"}, "platform_name__17zwd": {"message": "17zwd.com"}, "platform_name__51taoyang": {"message": "51taoyang.com"}, "platform_name__5ts": {"message": "5ts.com"}, "platform_name__91jf": {"message": "91jf.com"}, "platform_name__alibaba": {"message": "Alibaba.com"}, "platform_name__aliexpress": {"message": "AliExpress"}, "platform_name__amazon": {"message": "Amazon"}, "platform_name__bao66": {"message": "bao66.cn"}, "platform_name__chinagoods": {"message": "chinagoods.com"}, "platform_name__dhgate": {"message": "DHgate"}, "platform_name__e3hui": {"message": "e3hui.com"}, "platform_name__ebay": {"message": "Ebay"}, "platform_name__hznzcn": {"message": "hznzcn.com"}, "platform_name__jd": {"message": "JD"}, "platform_name__juyi5": {"message": "juyi5.cn"}, "platform_name__naver_shopping": {"message": "Naver Shopping"}, "platform_name__pinduoduo": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "platform_name__pinduoduo_pifa": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON> hurtowo"}, "platform_name__shopee": {"message": "<PERSON>ee"}, "platform_name__sjxzw": {"message": "571xz.com"}, "platform_name__sooxie": {"message": "sooxie.com"}, "platform_name__taobao": {"message": "Taobao"}, "platform_name__taobao_lite": {"message": "Taobao Lite"}, "platform_name__vvic": {"message": "vvic.com"}, "platform_name__walmart": {"message": "Walmart"}, "platform_name__wsy": {"message": "wsy.com"}, "platform_name__xingfujie": {"message": "xingfujie.cn"}, "platform_name__yiwugo": {"message": "Yiwugo.com"}, "platform_name__yunchepin": {"message": "yunchepin.cn"}, "platform_name__zhaojiafang": {"message": "zhaojiafang.com"}, "popup_go_to_home": {"message": "strona główna"}, "popup_go_to_platform": {"message": "Idź do $name$", "placeholders": {"name": {"content": "$1"}}}, "popup_search_placeholder": {"message": "<PERSON><PERSON><PERSON><PERSON> dla..."}, "popup_track_package_btn_track": {"message": "Tor"}, "popup_track_package_desc": {"message": "Wszystko-w-Jednym Śledzenie przesyłek"}, "popup_track_package_search_placeholder": {"message": "Numer przesyłki"}, "popup_translate_search_placeholder": {"message": "Tłumacz i szukaj w $searchOn$", "placeholders": {"searchOn": {"content": "$1"}}}, "price_history": {"message": "Historia cen"}, "price_history_chart_tip_ae": {"message": "Wskazówka: liczba zamówień to skumulowana liczba zamówień od momentu uruchomienia"}, "price_history_chart_tip_coupang": {"message": "Wskazówka: Coupang usunie liczbę zamówień oszukańczych"}, "price_history_inm_1688_l1": {"message": "<PERSON><PERSON><PERSON>"}, "price_history_inm_1688_l2": {"message": "Asystent zakupów AliPrice dla 1688"}, "price_history_panel_lowest_price": {"message": "Najniższa cena: "}, "price_history_panel_tab_price_tracking": {"message": "Historia cen"}, "price_history_panel_tab_seller_analysis": {"message": "Sprzedawca Analiza"}, "price_history_pro_modal_title": {"message": "Historia cen i historia zamówień"}, "privacy_consent__btn_agree": {"message": "Powtórz zgodę na zbieranie danych"}, "privacy_consent__btn_disable_all": {"message": "<PERSON><PERSON>"}, "privacy_consent__btn_enable_all": {"message": "Włącz wszystkie"}, "privacy_consent__btn_uninstall": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "privacy_consent__desc_privacy": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>, że bez danych lub plików cookie niektóre funkcje będą wyłączone, poniew<PERSON>ż te funkcje wymagają wyjaśnienia danych lub plików cookie, ale nadal możesz korzystać z innych funkcji."}, "privacy_consent__desc_privacy_L1": {"message": "Niestety bez danych lub plików cookie nie b<PERSON><PERSON><PERSON>, ponieważ potrzebujemy wyjaśnienia danych lub plików cookie."}, "privacy_consent__desc_privacy_L2": {"message": "<PERSON><PERSON><PERSON> nie pozwalasz nam zbierać tych informacji, usuń je."}, "privacy_consent__item_cookies_desc": {"message": "<PERSON><PERSON>, dane <PERSON><PERSON> waluty otrzymujemy tylko w plikach cookie podczas zakupów online, aby wyświ<PERSON>lić historię cen."}, "privacy_consent__item_cookies_title": {"message": "Wymagane pliki cookie"}, "privacy_consent__item_functional_desc_L1": {"message": "1. <PERSON><PERSON>j pliki cookie do przeglądarki, aby anonimowo identyfikować Twój komputer lub urządzenie."}, "privacy_consent__item_functional_desc_L2": {"message": "2. <PERSON><PERSON><PERSON> dane funk<PERSON>jonal<PERSON> w dodatku do pracy z funkcją."}, "privacy_consent__item_functional_title": {"message": "Funkcjonalne i analityczne pliki cookie"}, "privacy_consent__more_desc": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>, że nie udostępnia<PERSON>ich danych osobowych innym firmom i żadne firmy reklamowe nie gromadzą danych za pośrednictwem naszego serwisu."}, "privacy_consent__options__btn__desc": {"message": "<PERSON><PERSON> k<PERSON> ze wszystkich funkcji, musisz go włączyć."}, "privacy_consent__options__btn__label": {"message": "Włącz to"}, "privacy_consent__options__desc_L1": {"message": "Będziemy gromadzić na<PERSON> dane, które osobiście Cię identyfikują:"}, "privacy_consent__options__desc_L2": {"message": "- pliki cookie, dane <PERSON>j wa<PERSON>y są zapisywane w plikach cookie tylko podczas zakupów online w celu wyświetlenia historii cen."}, "privacy_consent__options__desc_L3": {"message": "- i dodawać pliki cookie w przeglądarce, aby anonimowo identyfikować komputer lub urządzenie."}, "privacy_consent__options__desc_L4": {"message": "- inne anonimowe dane spra<PERSON>, że to rozszerzenie jest wygodniejsze."}, "privacy_consent__options__desc_L5": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>, że nie udostępniamy twoich danych osobowych innym firmom i żadne firmy reklamowe nie zbierają danych za pośrednictwem naszego serwisu."}, "privacy_consent__privacy_preferences": {"message": "Preferencje dotyczące prywatności"}, "privacy_consent__read_more": {"message": "<PERSON><PERSON><PERSON><PERSON> więcej >>"}, "privacy_consent__title_privacy": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "product_info": {"message": "Informacje o produkcie"}, "product_recommend__name": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "product_research": {"message": "Badania produktów"}, "product_sub__email_desc": {"message": "E-mail z powiadomieniem o cenie"}, "product_sub__email_edit": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "product_sub__email_not_verified": {"message": "Zweryfikuj e-mail"}, "product_sub__email_required": {"message": "<PERSON><PERSON><PERSON> podać e-mail"}, "product_sub__form_countdown": {"message": "Automatyczne zamknięcie po $seconds$ sekundach", "placeholders": {"seconds": {"content": "$1"}}}, "product_sub__form_fail": {"message": "Dodaj przypomnienie nie powiodło się!"}, "product_sub__form_input_price": {"message": "<PERSON><PERSON>"}, "product_sub__form_item_country": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "product_sub__form_item_current_price": {"message": "Aktualna cena"}, "product_sub__form_item_duration": {"message": "Śledź dla"}, "product_sub__form_item_higher_price": {"message": "lub cena >"}, "product_sub__form_item_invalid_higher_price": {"message": "Cena musi być wyższa niż $price$", "placeholders": {"price": {"content": "$1"}}}, "product_sub__form_item_invalid_lower_price": {"message": "Cena musi być niższa niż $price$", "placeholders": {"price": {"content": "$1"}}}, "product_sub__form_item_lower_price": {"message": "<PERSON><PERSON><PERSON> cena <"}, "product_sub__form_submit": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "product_sub__form_success": {"message": "Dodaj sukces przypomnienia!"}, "product_sub__high_price_notify": {"message": "Powiadamiaj mnie o podwyżkach cen"}, "product_sub__low_price_notify": {"message": "Powiadamiaj mnie o obniżkach cen"}, "product_sub__modal_title": {"message": "Przypomnienie o zmianie ceny subskrypcji"}, "province__an_hui": {"message": "<PERSON><PERSON>"}, "province__ao_men": {"message": "Macau"}, "province__bei_jing": {"message": "Beijing"}, "province__chong_qing": {"message": "Chongqing"}, "province__fu_jian": {"message": "Fujian"}, "province__gan_su": {"message": "Gansu"}, "province__guang_dong": {"message": "Guangdong"}, "province__guang_xi": {"message": "Guangxi"}, "province__gui_zhou": {"message": "Guizhou"}, "province__hai_nan": {"message": "Hainan"}, "province__he_bei": {"message": "Hebei"}, "province__he_nan": {"message": "<PERSON><PERSON>"}, "province__hei_long_jiang": {"message": "Heilongjiang"}, "province__hu_bei": {"message": "Hubei"}, "province__hu_nan": {"message": "Hunan"}, "province__ji_lin": {"message": "<PERSON><PERSON>"}, "province__jiang_su": {"message": "Jiangsu"}, "province__jiang_xi": {"message": "Jiangxi"}, "province__liao_ning": {"message": "Liaoning"}, "province__nei_meng_gu": {"message": "Inner Mongolia"}, "province__ning_xia": {"message": "Ningxia"}, "province__qing_hai": {"message": "Qinghai"}, "province__shan_dong": {"message": "Shandong"}, "province__shan_xi": {"message": "Shaanxi"}, "province__shang_hai": {"message": "Shanghai"}, "province__si_chuan": {"message": "Sichuan"}, "province__tai_wan": {"message": "Taiwan"}, "province__tian_jin": {"message": "Tianjin"}, "province__xi_zhang": {"message": "Tibet"}, "province__xiang_gang": {"message": "Hong Kong"}, "province__xin_jiang": {"message": "Xinjiang"}, "province__yun_nan": {"message": "Yunnan"}, "province__zhe_jiang": {"message": "Zhejiang"}, "purple_rocket": {"message": "로켓직구"}, "qi_ding_liang": {"message": "MOQ"}, "qi_ding_liang_qi_ding_jia": {"message": "MOQ i MOP"}, "qi_ye_mian_ji": {"message": "Strefa przedsiębiorstwa"}, "qing_zhi_shao_xuan_ze_yi_ge_chan_pin": {"message": "Proszę wybierz przynajmniej jeden produkt"}, "qing_zhi_shao_xuan_ze_yi_ge_zi_duan": {"message": "<PERSON><PERSON><PERSON> wy<PERSON>ć przynajmniej jedno pole"}, "qu_deng_lu": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "quan_guo_yan_xuan": {"message": "Globalny wybór"}, "recommendation_popup_banner_btn_install": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON> to"}, "recommendation_popup_banner_desc": {"message": "Wyświetl historię cen w ciągu 3/6 miesięcy i powiadomienie o spadku ceny"}, "region__all": {"message": "Wszystkie regiony"}, "region__hai_wai": {"message": "Overseas"}, "region__hua_bei_qu": {"message": "North China"}, "region__hua_dong_qu": {"message": "East China"}, "region__hua_nan_qu": {"message": "South China"}, "region__hua_zhong_qu": {"message": "Central China"}, "region__jiang_zhe_lu": {"message": "Jiangsu, Zhejiang and Shanghai"}, "remove_web_limits": {"message": "Włącz prawy przycisk myszy"}, "ren_zheng_gong_chang": {"message": "Certyfikowana fabryka"}, "ren_zheng_gong_ying_shang_nian_zhan": {"message": "Lata jako c<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "required_to_aliprice_login": {"message": "Musisz się zalogować do AliPrice"}, "revenue_last30_days": {"message": "Kwota sprzedaży w ciągu ostatnich 30 dni"}, "review_counts": {"message": "Liczba kolektorów"}, "rocket": {"message": "로켓"}, "ru_zhu_nian_xian": {"message": "<PERSON><PERSON>"}, "sales_amount_last30_days": {"message": "Całkowita sprzedaż w ciągu ostatnich 30 dni"}, "sales_last30_days": {"message": "Sprzedaż w ciągu ostatnich 30 dni"}, "sbi_alibaba_cate__accessories": {"message": "Akcesoria"}, "sbi_alibaba_cate__aqfk": {"message": "Bezpieczeństwo"}, "sbi_alibaba_cate__bags_cases": {"message": "Torby i walizki"}, "sbi_alibaba_cate__beauty": {"message": "Piękno"}, "sbi_alibaba_cate__beverage": {"message": "Napój"}, "sbi_alibaba_cate__bgwh": {"message": "<PERSON><PERSON><PERSON>"}, "sbi_alibaba_cate__bz": {"message": "<PERSON><PERSON>"}, "sbi_alibaba_cate__ccyj": {"message": "Sp<PERSON><PERSON>t kuchenny"}, "sbi_alibaba_cate__clothes": {"message": "s<PERSON><PERSON><PERSON>"}, "sbi_alibaba_cate__cmgd": {"message": "Nadawanie mediów"}, "sbi_alibaba_cate__coat_jacket": {"message": "Płaszcz kurtka"}, "sbi_alibaba_cate__consumer_electronics": {"message": "Elektroniki użytkowej"}, "sbi_alibaba_cate__cryp": {"message": "Produkty dla dorosłych"}, "sbi_alibaba_cate__csyp": {"message": "Narzuty na łóżko"}, "sbi_alibaba_cate__cwyy": {"message": "Ogrodnictwo dla zwierząt"}, "sbi_alibaba_cate__cysx": {"message": "<PERSON><PERSON>"}, "sbi_alibaba_cate__dgdq": {"message": "Elektryk"}, "sbi_alibaba_cate__dl": {"message": "Gra aktorska"}, "sbi_alibaba_cate__dress_suits": {"message": "Sukienka i garnitury"}, "sbi_alibaba_cate__dszm": {"message": "Oświetlenie"}, "sbi_alibaba_cate__dzqj": {"message": "Urządzenie elektroniczne"}, "sbi_alibaba_cate__essb": {"message": "Używany sprzęt"}, "sbi_alibaba_cate__food": {"message": "j<PERSON><PERSON><PERSON>"}, "sbi_alibaba_cate__fspj": {"message": "Ubrania i akcesoria"}, "sbi_alibaba_cate__furniture": {"message": "Meble"}, "sbi_alibaba_cate__fzpg": {"message": "Skóra tekstylna"}, "sbi_alibaba_cate__ghjq": {"message": "Higieny osobistej"}, "sbi_alibaba_cate__gt": {"message": "Stal"}, "sbi_alibaba_cate__gyp": {"message": "Rzemieślnictwo"}, "sbi_alibaba_cate__hb": {"message": "Przyjazny środowisku"}, "sbi_alibaba_cate__hfcz": {"message": "Makijaż do pielęgnacji skóry"}, "sbi_alibaba_cate__hg": {"message": "Prz<PERSON>ysł chemiczny"}, "sbi_alibaba_cate__jg": {"message": "Przetwarzanie"}, "sbi_alibaba_cate__jianccai": {"message": "<PERSON><PERSON><PERSON><PERSON> budowlane"}, "sbi_alibaba_cate__jichuang": {"message": "Narzędzie <PERSON>ne"}, "sbi_alibaba_cate__jjry": {"message": "Codzienne użytkowanie w gospodarstwie domowym"}, "sbi_alibaba_cate__jtys": {"message": "Transport"}, "sbi_alibaba_cate__jxsb": {"message": "Ekwipunek"}, "sbi_alibaba_cate__jxwj": {"message": "Sprzęt mechaniczny"}, "sbi_alibaba_cate__jydq": {"message": "Sprzęt AGD"}, "sbi_alibaba_cate__jzjc": {"message": "Materiały budowlane do majsterkowania"}, "sbi_alibaba_cate__jzjf": {"message": "Tekstylia domowe"}, "sbi_alibaba_cate__mj": {"message": "Ręcznik"}, "sbi_alibaba_cate__myyp": {"message": "Produkty dla d<PERSON>ci"}, "sbi_alibaba_cate__nanz": {"message": "męskie"}, "sbi_alibaba_cate__nvz": {"message": "Ubrania Damskie"}, "sbi_alibaba_cate__ny": {"message": "Energia"}, "sbi_alibaba_cate__others": {"message": "<PERSON><PERSON>"}, "sbi_alibaba_cate__qcyp": {"message": "Akcesoria sa<PERSON>chodowe"}, "sbi_alibaba_cate__qmpj": {"message": "Części samochodowe"}, "sbi_alibaba_cate__shoes": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "sbi_alibaba_cate__smdn": {"message": "Komputer cyfrowy"}, "sbi_alibaba_cate__snqj": {"message": "Przechowywanie i czyszczenie"}, "sbi_alibaba_cate__spjs": {"message": "<PERSON><PERSON><PERSON> picie"}, "sbi_alibaba_cate__swfw": {"message": "Usługi biznesowe"}, "sbi_alibaba_cate__toys_hobbies": {"message": "Zabawka"}, "sbi_alibaba_cate__trousers_skirt": {"message": "Spodnie i spódnica"}, "sbi_alibaba_cate__txcp": {"message": "Produkty komunikacyjne"}, "sbi_alibaba_cate__tz": {"message": "Ubrania dziecięce"}, "sbi_alibaba_cate__underwear": {"message": "Bielizna"}, "sbi_alibaba_cate__wjgj": {"message": "Narzędzia sprzętowe"}, "sbi_alibaba_cate__xgpi": {"message": "Skórzane to<PERSON>"}, "sbi_alibaba_cate__xmhz": {"message": "współpraca projektowa"}, "sbi_alibaba_cate__xs": {"message": "<PERSON><PERSON>"}, "sbi_alibaba_cate__ydfs": {"message": "Odzież sportowa"}, "sbi_alibaba_cate__ydhw": {"message": "Sport na świeżym powietrzu"}, "sbi_alibaba_cate__yjkc": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "sbi_alibaba_cate__yqyb": {"message": "Oprzyrządowanie"}, "sbi_alibaba_cate__ys": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "sbi_alibaba_cate__yyby": {"message": "Opieka medyczna"}, "sbi_alibaba_cn_kj_90mjs": {"message": "Liczba kupujących w ciągu ostatnich 90 dni"}, "sbi_alibaba_cn_kj_90xsl": {"message": "Wielkość sprzedaży w ciągu ostatnich 90 dni"}, "sbi_alibaba_cn_kj_gjsj": {"message": "Szacunkowa cena"}, "sbi_alibaba_cn_kj_gjwlyf": {"message": "Międzynarodowa opłata za wysyłkę"}, "sbi_alibaba_cn_kj_gjyf": {"message": "Opłata przewozowa"}, "sbi_alibaba_cn_kj_gssj": {"message": "Szacunkowa cena"}, "sbi_alibaba_cn_kj_lr": {"message": "Zysk"}, "sbi_alibaba_cn_kj_lrgs": {"message": "Zysk = szacunkowa cena x marża zysku"}, "sbi_alibaba_cn_kj_pjfhsd": {"message": "Średnia pręd<PERSON><PERSON><PERSON>"}, "sbi_alibaba_cn_kj_qtfy": {"message": "inna opłata"}, "sbi_alibaba_cn_kj_qtfygs": {"message": "Inny koszt = szacunkowa cena x inny stosunek kosztów"}, "sbi_alibaba_cn_kj_spjg": {"message": "Cena £"}, "sbi_alibaba_cn_kj_spzl": {"message": "W<PERSON>"}, "sbi_alibaba_cn_kj_szd": {"message": "Lokalizacja"}, "sbi_alibaba_cn_kj_unit_ge": {"message": "Kawałki"}, "sbi_alibaba_cn_kj_unit_jian": {"message": "Kawałki"}, "sbi_alibaba_cn_kj_unit_ke": {"message": "Gram"}, "sbi_alibaba_cn_kj_unit_ren": {"message": "<PERSON><PERSON><PERSON>ą<PERSON>"}, "sbi_alibaba_cn_kj_unit_tai": {"message": "Kawałki"}, "sbi_alibaba_cn_kj_unit_tao": {"message": "Zestawy"}, "sbi_alibaba_cn_kj_unit_tian": {"message": "Dni"}, "sbi_alibaba_cn_kj_zwbj": {"message": "<PERSON>z ceny"}, "sbi_aliprice_alibaba_cn__jiage": {"message": "Cena £"}, "sbi_aliprice_alibaba_cn__keshou": {"message": "Dostępny na sprzedaż"}, "sbi_aliprice_alibaba_cn__moren": {"message": "domyślny"}, "sbi_aliprice_alibaba_cn__queding": {"message": "Pewnie"}, "sbi_aliprice_alibaba_cn__xiaoliang": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "sbi_aliprice_alibaba_cn_cate__jiaju": {"message": "meble"}, "sbi_aliprice_alibaba_cn_cate__linghsi": {"message": "przekąska"}, "sbi_aliprice_alibaba_cn_cate__meizhuang": {"message": "makijaże"}, "sbi_aliprice_alibaba_cn_cate__neiyi": {"message": "Bielizna"}, "sbi_aliprice_alibaba_cn_cate__peishi": {"message": "Akcesoria"}, "sbi_aliprice_alibaba_cn_cate__pinchui": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "sbi_aliprice_alibaba_cn_cate__qita": {"message": "<PERSON><PERSON>"}, "sbi_aliprice_alibaba_cn_cate__qunzhuang": {"message": "Spódnica"}, "sbi_aliprice_alibaba_cn_cate__shangyi": {"message": "<PERSON><PERSON>"}, "sbi_aliprice_alibaba_cn_cate__shuma": {"message": "Elektronika"}, "sbi_aliprice_alibaba_cn_cate__wanju": {"message": "Zabawka"}, "sbi_aliprice_alibaba_cn_cate__xiangbao": {"message": "Bagaż"}, "sbi_aliprice_alibaba_cn_cate__xiazhuang": {"message": "Statki"}, "sbi_aliprice_alibaba_cn_cate__xiezi": {"message": "but"}, "sbi_aliprice_cate__apparel": {"message": "Strój"}, "sbi_aliprice_cate__automobiles_motorcycles": {"message": "Samochody i Motocykle"}, "sbi_aliprice_cate__beauty_health": {"message": "Uroda i zdrowie"}, "sbi_aliprice_cate__cellphones_telecommunications": {"message": "Telefony komórkowe i telekomunikacja"}, "sbi_aliprice_cate__computer_office": {"message": "Komputer i biuro"}, "sbi_aliprice_cate__consumer_electronics": {"message": "Elektroniki użytkowej"}, "sbi_aliprice_cate__education_office_supplies": {"message": "Artykuły edukacyjne i biurowe"}, "sbi_aliprice_cate__electronic_components_supplies": {"message": "Komponenty elektroniczne i materiały eksploatacyjne"}, "sbi_aliprice_cate__furniture": {"message": "Meble"}, "sbi_aliprice_cate__hair_extensions_wigs": {"message": "Przedłużanie włosów i peruki"}, "sbi_aliprice_cate__home_garden": {"message": "<PERSON> i ogród"}, "sbi_aliprice_cate__home_improvement": {"message": "Majsterkowanie w domu"}, "sbi_aliprice_cate__jewelry_accessories": {"message": "Biżuteria i akcesoria"}, "sbi_aliprice_cate__luggage_bags": {"message": "Bagaże i torby"}, "sbi_aliprice_cate__mother_kids": {"message": "Matka i d<PERSON>ci"}, "sbi_aliprice_cate__novelty_special_use": {"message": "Nowość i specjalne zastosowanie"}, "sbi_aliprice_cate__security_protection": {"message": "Bezpieczeństwo i ochrona"}, "sbi_aliprice_cate__shoes": {"message": "<PERSON>y"}, "sbi_aliprice_cate__sports_entertainment": {"message": "Sport i rozrywka"}, "sbi_aliprice_cate__toys_hobbies": {"message": "Zabawki i hobby"}, "sbi_aliprice_cate__watches": {"message": "Zegarki"}, "sbi_aliprice_cate__weddings_events": {"message": "<PERSON><PERSON> i imprezy"}, "sbi_btn_capture_txt": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "sbi_btn_source_now_txt": {"message": "Źródło teraz"}, "sbi_button__chat_with_me": {"message": "Czatuj ze mną"}, "sbi_button__contact_supplier": {"message": "Kontakt"}, "sbi_button__hide_on_this_site": {"message": "<PERSON><PERSON> pokazuj na tej stronie"}, "sbi_button__open_settings": {"message": "Skonfiguruj wyszukiwanie według obrazu"}, "sbi_capture_shortcut_tip": {"message": "lub na<PERSON><PERSON><PERSON><PERSON> klawisz „Enter” na klawiaturze"}, "sbi_capturing_tip": {"message": "Przechwytywanie"}, "sbi_composed_rating_45": {"message": "4,5 - 5,0 g<PERSON><PERSON><PERSON>"}, "sbi_crop_and_search": {"message": "Szukaj"}, "sbi_crop_start": {"message": "Użyj zrzutu ekranu"}, "sbi_err_captcha_action": {"message": "Zwer<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "sbi_err_captcha_for_alibaba_cn": {"message": "Potrzebujesz weryfikacji. Prześlij zdjęcie w celu weryfikacji. (Wyświetl $video_tutorial$ lub spróbuj wyczyścić pliki cookie)", "placeholders": {"feedback": {"content": "$1"}, "video_tutorial": {"content": "$1"}}}, "sbi_err_captcha_for_aliprice": {"message": "Nietypowy ruch, zweryfikuj"}, "sbi_err_captcha_for_taobao": {"message": "Taobao prosi o weryfikację, ręcznie prześlij zdjęcie i wyszukaj je, aby je zweryfikować. Ten błąd jest spowodowany nową polityką weryfikacji „TaoBao search by image”. Sugerujemy zbyt częste sprawdzanie reklamacji na Taobao $feedback$.", "placeholders": {"feedback": {"content": "$1"}}}, "sbi_err_captcha_for_taobao_feedback": {"message": "sprzężenie zwrotne"}, "sbi_err_captcha_msg": {"message": "$platform$ wymaga przesłania obrazu do wyszukiwania lub przeprowadzenia weryfikacji zabezpieczeń w celu usunięcia ograniczeń wyszukiwania", "placeholders": {"platform": {"content": "$1"}}}, "sbi_err_checking_if_low_version": {"message": "Sprawdź, czy to najnowsza wersja"}, "sbi_err_cookie_btn_clear": {"message": "<PERSON><PERSON>ń pliki cookie"}, "sbi_err_cookie_for_alibaba_cn": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> wyczyścić pliki cookie 1688? (<PERSON><PERSON><PERSON><PERSON> się zalogować ponownie)"}, "sbi_err_desperate_feature_pdd": {"message": "Funkcja wyszukiwania obrazów została przeniesiona do Pinduoduo Search według rozszerzenia obrazu."}, "sbi_err_hidden_skill_of_improve_search_rate": {"message": "<PERSON><PERSON> wskaźnik sukcesu wyszukiwania grafiki?"}, "sbi_err_img_undersize": {"message": "Obraz > $imgMinimumSize$", "placeholders": {"imgMinimumSize": {"content": "$1"}}}, "sbi_err_login_required": {"message": "Zaloguj się $loginSite$", "placeholders": {"loginSite": {"content": "$1"}}}, "sbi_err_login_required_action": {"message": "<PERSON><PERSON><PERSON><PERSON> sie"}, "sbi_err_low_version": {"message": "Zainstaluj najnowszą wersję ($latestVersion$)", "placeholders": {"latestVersion": {"content": "$1"}}}, "sbi_err_low_version_action": {"message": "Ściągnij"}, "sbi_err_need_help": {"message": "Potrzebujesz pomocy"}, "sbi_err_network": {"message": "Błąd sieci. Upewnij się, że możesz odwiedzić wit<PERSON>"}, "sbi_err_not_low_version": {"message": "Zainstalowano najnowszą wersję ($latestVersion$)", "placeholders": {"latestVersion": {"content": "$1"}}}, "sbi_err_try_again": {"message": "Spróbuj ponownie"}, "sbi_err_try_again_action": {"message": "Spróbuj ponownie"}, "sbi_err_visit_and_try": {"message": "Spróbuj ponownie lub odwiedź witrynę $website$ i spróbuj jeszcze raz", "placeholders": {"website": {"content": "$1"}}}, "sbi_err_visit_site": {"message": "Odwiedź stronę główną $siteName$", "placeholders": {"siteName": {"content": "$1"}}}, "sbi_fail_tip": {"message": "Ładowanie nie powiodło się, od<PERSON><PERSON><PERSON><PERSON> stronę i spróbuj ponownie."}, "sbi_kuajing_filter_area": {"message": "Powierzchnia"}, "sbi_kuajing_filter_au": {"message": "Australia"}, "sbi_kuajing_filter_btn_confirm": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "sbi_kuajing_filter_de": {"message": "<PERSON><PERSON><PERSON>"}, "sbi_kuajing_filter_destination_country": {"message": "<PERSON><PERSON>"}, "sbi_kuajing_filter_es": {"message": "Hiszpania"}, "sbi_kuajing_filter_estimate": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "sbi_kuajing_filter_estimate_price": {"message": "Szacunkowa cena"}, "sbi_kuajing_filter_estimate_price_formula": {"message": "Formuła szacowanej ceny = (cena towaru + międzynarodowy transport logistyczny)/(1 - marża zysku - inny stosunek kosztów)"}, "sbi_kuajing_filter_fr": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "sbi_kuajing_filter_kw_placeholder": {"message": "Wpisz słowa kluczowe pasujące do tytułu"}, "sbi_kuajing_filter_logistics": {"message": "Szablon logistyczny"}, "sbi_kuajing_filter_logistics_china_post": {"message": "Chińska Poczta Lotnicza"}, "sbi_kuajing_filter_logistics_discount": {"message": "<PERSON>bat log<PERSON>"}, "sbi_kuajing_filter_logistics_epacket": {"message": "e-pakiet"}, "sbi_kuajing_filter_logistics_price_formula": {"message": "Transport międzynarodowy logistyczny = (waga x cena wysyłki + opłata rejestracyjna) x (1 - rabat)"}, "sbi_kuajing_filter_others_fee": {"message": "Inna opła<PERSON>"}, "sbi_kuajing_filter_profit_percent": {"message": "Marża zysku"}, "sbi_kuajing_filter_prop": {"message": "Atrybuty"}, "sbi_kuajing_filter_ru": {"message": "<PERSON><PERSON><PERSON>"}, "sbi_kuajing_filter_total": {"message": "Dopasuj $count$ podobne przedmioty", "placeholders": {"count": {"content": "$1"}}}, "sbi_kuajing_filter_uk": {"message": "Wielka Brytania"}, "sbi_kuajing_filter_usa": {"message": "Ameryka"}, "sbi_login_punish_title__pdd_pifa": {"message": "Hurtownia Pinduoduo"}, "sbi_msg_no_result": {"message": "Nie znaleziono wyników,zaloguj się do $loginSite$ lub wypróbuj inne zdjęcie", "placeholders": {"loginSite": {"content": "$1"}}}, "sbi_msg_no_result_check_support_page_l1": {"message": "Tymczasowo niedostępne dla Safari, użyj $supportPage$.", "placeholders": {"supportPage": {"content": "$1"}}}, "sbi_msg_no_result_check_support_page_l2": {"message": "Przeglądarka Chrome i jej rozszerzenia"}, "sbi_msg_no_result_reinstall_l1": {"message": "Brak wyników, zaloguj się na $loginSite$, wypróbuj inne zdjęcie lub zainstaluj ponownie $latestExtUrl$", "placeholders": {"loginSite": {"content": "$1"}, "latestExtUrl": {"content": "$2"}}}, "sbi_msg_no_result_reinstall_l2": {"message": "Ostatnia wersja", "placeholders": {}}, "sbi_search_by_selected_area": {"message": "Wybrany obszar"}, "sbi_shipping_": {"message": "Wysyłka tego samego dnia"}, "sbi_specify_category": {"message": "Określ kategorię:"}, "sbi_start_crop": {"message": "<PERSON><PERSON><PERSON><PERSON> obszar"}, "sbi_store_name_alibabaCNKuaJing": {"message": "1688 za granicą"}, "sbi_tutorial_btn_more": {"message": "Zastosowanie 2"}, "sbi_tutorial_find_coupons_on_taobao": {"message": "Znajdź kupony Taobao"}, "sbi_txt__empty_retry": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, nie znaleziono wyników, spróbuj ponownie."}, "sbi_txt__min_order": {"message": "Min. <PERSON>"}, "sbi_visiting": {"message": "Przeglądanie"}, "sbi_yiwugo__jiagexiangtan": {"message": "Skontaktuj się ze sprzedawcą w sprawie ceny"}, "sbi_yiwugo__qigou": {"message": "$num$ Sztuk (MOQ)", "placeholders": {"num": {"content": "$1"}}}, "sbi_yiwugo__xing": {"message": "Gwiazdy"}, "searchByImage_screenshot": {"message": "Zrzut ekranu jednym kliknięciem"}, "searchByImage_search": {"message": "Wyszukiwanie tych samych elementów jednym kliknięciem"}, "searchByImage_size_type": {"message": "Rozmiar pliku nie może być większy niż $num$ MB, tylko $type$", "placeholders": {"num": {"content": "$1"}, "type": {"content": "$2"}}}, "search_by_image_progress_analysing": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "search_by_image_progress_searching": {"message": "Wyszukaj produkty"}, "search_by_image_progress_sending": {"message": "Wysyłanie obrazu"}, "search_by_image_response_rate": {"message": "Wskaźnik odpowiedzi: $responseRate$ kupuj<PERSON><PERSON>ch, którzy skontaktowali się z tym dostawcą, otrzymali odpowiedź w ciągu $responseInHour$ godzin.", "placeholders": {"responseRate": {"content": "$1"}, "responseInHour": {"content": "$2"}}}, "search_by_keyword": {"message": "<PERSON><PERSON>j według słowa kluczowego:"}, "select_country_language_modal_title_country": {"message": "<PERSON><PERSON>"}, "select_country_language_modal_title_language": {"message": "Język"}, "select_country_region_modal_title": {"message": "Wybierz kraj/region"}, "select_language_modal_title": {"message": "Wybierz język:"}, "select_shop": {"message": "<PERSON><PERSON><PERSON><PERSON> sklep"}, "sellers_count": {"message": "Liczba sprzedawców na bieżącej stronie"}, "sellers_count_per_page": {"message": "Liczba sprzedawców na bieżącej stronie"}, "service_score": {"message": "Kompleksowa ocena usług"}, "set_shortcut_keys": {"message": "Ustaw klawisze skrótu"}, "setting_logo_title": {"message": "Asystent zakupów"}, "setting_modal_options_position_title": {"message": "Lokalizacja"}, "setting_modal_options_position_value_left": {"message": "<PERSON> lewe<PERSON>"}, "setting_modal_options_position_value_right": {"message": "Po prawej"}, "setting_modal_options_theme_title": {"message": "<PERSON><PERSON> m<PERSON>"}, "setting_modal_options_theme_value_dark": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "setting_modal_options_theme_value_light": {"message": "Światło"}, "setting_modal_title": {"message": "Ustawienia"}, "setting_options_country_title": {"message": "Kraj/Region"}, "setting_options_hover_zoom_desc": {"message": "<PERSON>rz<PERSON><PERSON><PERSON> kursor, aby p<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "setting_options_hover_zoom_title": {"message": "Hover Zoom"}, "setting_options_jd_coupon_desc": {"message": "Znaleziono kupon na JD.com"}, "setting_options_jd_coupon_title": {"message": "Kupon JD.com"}, "setting_options_language_title": {"message": "Język"}, "setting_options_price_drop_alert_desc": {"message": "Gdy tylko spadnie cena towarów z\" Moje ulubione \", otrzymasz powiadomienie."}, "setting_options_price_drop_alert_title": {"message": "Śledź cenę"}, "setting_options_price_history_on_list_page_desc": {"message": "Wyświetl historię cen na stronie wyszukiwania produktów"}, "setting_options_price_history_on_list_page_title": {"message": "Historia cen (strona z listą)"}, "setting_options_price_history_on_produt_page_desc": {"message": "Wyświetl historię produktu na stronie szczegółów produktu"}, "setting_options_price_history_on_produt_page_title": {"message": "Historia cen (strona szczegółów)"}, "setting_options_sales_analysis_desc": {"message": "Wspieraj statystyki cen, wielkości sprzedaży, liczby sprzedawców i wskaźnika sprzedaży w sklepie na stronie z listą produktów $platforms$", "placeholders": {"platforms": {"content": "$1"}}}, "setting_options_sales_analysis_title": {"message": "<PERSON><PERSON><PERSON>rz<PERSON>"}, "setting_options_save_success_msg": {"message": "Powodzenie"}, "setting_options_tacking_price_title": {"message": "Powiadomienie o zmianie ceny"}, "setting_options_value_off": {"message": "Off"}, "setting_options_value_on": {"message": "On"}, "setting_pkg_quick_view_desc": {"message": "Wsparcie: 1688 i Taobao"}, "setting_saved_message": {"message": "Zmiany zostały zapisane"}, "setting_section_enable_platform_title": {"message": "On-off"}, "setting_section_setting_title": {"message": "Ustawienia"}, "setting_section_shortcuts_title": {"message": "Skrót :"}, "settings_aliprice_agent__desc": {"message": "Wyświetlane na stronie szczegółów produktu $platforms$", "placeholders": {"platforms": {"content": "$1"}}}, "settings_aliprice_agent__title": {"message": "<PERSON><PERSON> dla mnie"}, "settings_copy_link__desc": {"message": "Wyświetl na stronie szczegółów produktu"}, "settings_copy_link__title": {"message": "Przycisk Kopiuj i Wyszukaj tytuł"}, "settings_currency_desc__for_detail": {"message": "Obsługa strony szczegółów produktu 1688"}, "settings_currency_desc__for_list": {"message": "<PERSON><PERSON><PERSON> <PERSON><PERSON> obra<PERSON> (w tym 1688/1688 za granicą/Taobao)"}, "settings_currency_desc__for_sbi": {"message": "<PERSON><PERSON><PERSON><PERSON> cenę"}, "settings_currency_desc_display_for_list": {"message": "Wyświetlane w wyszukiwaniu obrazów (w tym 1688/1688 za granicą/Taobao)"}, "settings_currency_rate_desc": {"message": "Aktualizacja kursu wymiany z \"$currencyRateFrom$\"", "placeholders": {"currencyRateFrom": {"content": "$1"}}}, "settings_currency_rate_from_boc": {"message": "bank Chin"}, "settings_download_images__desc": {"message": "Wsparcie dla pobierania obrazów z $platforms$", "placeholders": {"platforms": {"content": "$1"}}}, "settings_download_images__title": {"message": "przycisk pobierania obrazu"}, "settings_download_reviews__desc": {"message": "Wyświetlane na stronie szczegółów produktu $platforms$", "placeholders": {"platforms": {"content": "$1"}}}, "settings_download_reviews__title": {"message": "Pobierz obrazy recenzji"}, "settings_google_translate_desc": {"message": "Kliknij prawym przyciskiem myszy, aby u<PERSON><PERSON><PERSON> pasek tłumacza google"}, "settings_google_translate_title": {"message": "tłumaczenie stron internetowych"}, "settings_historical_trend_desc": {"message": "Wyświetl w prawym dolnym rogu obrazu na stronie listy produktów"}, "settings_modal_btn_more": {"message": "Wię<PERSON>j ustawień"}, "settings_productInfo_desc": {"message": "Wyświetl bardziej szczegółowe informacje o produkcie na stronie listy produktów. Włączenie tej opcji może zwiększyć obciążenie komputera i spowodować opóźnienie strony. <PERSON><PERSON><PERSON> ma to wpływ na wydajność, zaleca się jej wyłączenie."}, "settings_product_recommend__desc": {"message": "Wyświetlane pod głównym obrazem na stronie szczegółów produktu $platforms$", "placeholders": {"platforms": {"content": "$1"}}}, "settings_product_recommend__name": {"message": "Produkty zalecane"}, "settings_research_desc": {"message": "Zapytaj o bardziej szczegółowe informacje na stronie listy produktów"}, "settings_sbi_add_to_list": {"message": "Dodaj do $listType$", "placeholders": {"listType": {"content": "$1"}}}, "settings_sbi_detail_page_icon_title_desc": {"message": "Miniatura wyników wyszukiwania grafiki"}, "settings_sbi_remove_from_list": {"message": "Usuń z $listType$", "placeholders": {"listType": {"content": "$1"}}}, "settings_search_by_image_add_to_blacklist": {"message": "Dodać do czarnej listy"}, "settings_search_by_image_adjust_entrance_entrance": {"message": "Dostosuj pozycję wejściową"}, "settings_search_by_image_blacklist_desc": {"message": "Nie pokazuj ikony na stronach internetowych na czarnej liście."}, "settings_search_by_image_blacklist_title": {"message": "Czarna lista"}, "settings_search_by_image_bottom_left": {"message": "Lewy dolny róg"}, "settings_search_by_image_bottom_right": {"message": "Prawy dolny róg"}, "settings_search_by_image_clear_blacklist": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> czarną listę"}, "settings_search_by_image_detail_page_icon_title": {"message": "Miniaturka"}, "settings_search_by_image_detail_page_icon_val_large": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "settings_search_by_image_detail_page_icon_val_small": {"message": "Mniejszy"}, "settings_search_by_image_display_button_desc": {"message": "Jedno kliknięcie na ikonę, aby wyszukać obraz"}, "settings_search_by_image_display_button_title": {"message": "Ikona na obrazach"}, "settings_search_by_image_sourece_websites_desc": {"message": "Znajdź produkt źródłowy na tych stronach"}, "settings_search_by_image_sourece_websites_title": {"message": "S<PERSON>j według wyniku obrazu"}, "settings_search_by_image_top_left": {"message": "Lewy górny róg"}, "settings_search_by_image_top_right": {"message": "Prawy górny róg"}, "settings_search_keyword_on_x__desc": {"message": "Szukaj słów na $platform$", "placeholders": {"platform": {"content": "$1"}}}, "settings_search_keyword_on_x__title": {"message": "Pokaż ikonę $platform$ po zaznaczeniu słów", "placeholders": {"platform": {"content": "$1"}}}, "settings_similar_products_desc": {"message": "Spróbuj znaleźć ten sam produkt w tych witrynach (maksymalnie 5)"}, "settings_similar_products_title": {"message": "Znajdź ten sam produkt"}, "settings_toolbar_expand_title": {"message": "Minimalizuj wtycz<PERSON>ę"}, "settings_top_toolbar_desc": {"message": "Pasek wyszukiwania na górze strony"}, "settings_top_toolbar_title": {"message": "Pasek wyszukiwania"}, "settings_translate_search_desc": {"message": "Przetłumacz na chiński i wyszukaj"}, "settings_translate_search_title": {"message": "Wyszukiwanie wielojęzyczne"}, "settings_translator_contextmenu_title": {"message": "Przechwyć do przetłumaczenia"}, "settings_translator_title": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "shai_xuan_dao_chu": {"message": "<PERSON><PERSON><PERSON><PERSON>, aby wyeksportować"}, "shai_xuan_zi_duan": {"message": "Filtruj pola"}, "shang_jia_shi_jian": {"message": "Na półce"}, "shang_pin_biao_ti": {"message": "tytuł produktu"}, "shang_pin_dui_bi": {"message": "porównanie produktów"}, "shang_pin_lian_jie": {"message": "link do produktu"}, "shang_pin_xin_xi": {"message": "Informacje o produkcie"}, "share_modal__content": {"message": "Podziel się z przyjaciółmi"}, "share_modal__disable_for_while": {"message": "Nie chcę się niczym <PERSON>"}, "share_modal__title": {"message": "Lubisz $extensionName$?", "placeholders": {"extensionName": {"content": "$1"}}}, "sheng_yu_ku_cun": {"message": "Pozostało"}, "shi_fou_ke_ding_zhi": {"message": "<PERSON><PERSON> można go dos<PERSON><PERSON>?"}, "shi_fou_ren_zheng_gong_ying_sha": {"message": "Certyfikowany dostawca"}, "shi_fou_ren_zheng_gong_ying_shang_zong_shu": {"message": "<PERSON>rty<PERSON><PERSON><PERSON><PERSON> dosta<PERSON>"}, "shi_fou_you_mao_yi_dan_bao": {"message": "Ubezpieczenie handlowe"}, "shi_fou_you_mao_yi_dan_bao_zong_shu": {"message": "<PERSON><PERSON><PERSON><PERSON> handlowe"}, "shipping_fee": {"message": "Opłata przewozowa"}, "shop_followers": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> sklep"}, "shou_qi": {"message": "Mniej"}, "similar_products_warn_max_platforms": {"message": "Maksymalnie 5"}, "sku_calc_price": {"message": "O<PERSON>liczona cena"}, "sku_calc_price_settings": {"message": "Ustawienia obliczonej ceny"}, "sku_formula": {"message": "Formuła"}, "sku_formula_desc": {"message": "<PERSON>is formuły"}, "sku_formula_desc_text": {"message": "Obsługuje złożone formuły matematyczne, przy czym pierwotna cena jest reprezentowana przez A, a fracht przez B\n\n<br/>\n\nObsługuje nawiasy (), plus +, minus -, mnożenie * i dzielenie /\n\n<br/>\n\nPrzykład:\n\n<br/>\n\n1. <PERSON><PERSON> uzy<PERSON>ć 1,2-<PERSON><PERSON><PERSON><PERSON><PERSON> pierwotnej ceny, a następnie dodać fracht, formuła jest następująca: A*1,2+B\n\n<br/>\n\n2. <PERSON>by u<PERSON><PERSON><PERSON> pierwotną cenę plus 1 juan, a następnie pomnożyć przez 1,2 razy, formuła jest następująca: (A+1)*1,2\n\n<br/>\n\n3. <PERSON><PERSON> u<PERSON><PERSON>ć pierwotną cenę plus 10 juanów, a następnie pomnożyć przez 1,2 razy, a następnie odjąć 3 juany, formuła jest następująca: (A+10)*1,2-3"}, "sku_in_stock": {"message": "W magazynie"}, "sku_invalid_formula_format": {"message": "Nieprawidłowy format formuły"}, "sku_inventory": {"message": "Zapasy"}, "sku_link_copy_fail": {"message": "Skopiowano pomyślnie, specyfikacje i atrybuty SKU nie zostały wybrane"}, "sku_link_copy_success": {"message": "Skopiowano <PERSON>, wybrano specyfikacje i atrybuty SKU"}, "sku_list": {"message": "Lista SKU"}, "sku_min_qrder_qty": {"message": "Minimalna ilość zamówienia"}, "sku_name": {"message": "Nazwa SKU"}, "sku_no": {"message": "Nr"}, "sku_original_price": {"message": "<PERSON><PERSON>"}, "sku_price": {"message": "Cena SKU"}, "stop_track_time_label": {"message": "<PERSON><PERSON><PERSON>:"}, "suo_zai_di_qu": {"message": "Lokalizacja"}, "tab_pkg_quick_view": {"message": "Monitor logistyki"}, "tab_product_details_price_history": {"message": "Historia"}, "tab_product_details_reviews": {"message": "Opinie"}, "tab_product_details_seller_analysis": {"message": "<PERSON><PERSON><PERSON>"}, "tab_product_details_similar_products": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "total_days_listed_per_product": {"message": "Suma dni przechowywania ÷ Liczba produktów"}, "total_items": {"message": "Całkowita liczba produktów"}, "total_price_per_product": {"message": "Suma cen ÷ Liczba produktów"}, "total_rating_per_product": {"message": "Suma ocen ÷ Liczba produktów"}, "total_revenue": {"message": "Całkowity dochód"}, "total_revenue40_items": {"message": "Całkowity przychód z $amount$ produktów na bieżącej stronie", "placeholders": {"amount": {"content": "$1"}}}, "total_sales": {"message": "Całkowita sprzedaż"}, "total_sales40_items": {"message": "Łączna sprzedaż $amount$ produktów na bieżącej stronie", "placeholders": {"amount": {"content": "$1"}}}, "track_for_12_months": {"message": "Śledź przez: 1 rok"}, "track_for_3_months": {"message": "Śledź przez: 3 miesiące"}, "track_for_6_months": {"message": "Śledź przez: 6 miesięcy"}, "tracking_price_email_add_btn": {"message": "Dodaj e-mail"}, "tracking_price_email_edit_btn": {"message": "Edytuj e-mail"}, "tracking_price_email_intro": {"message": "Powiadomimy Cię e-mailem."}, "tracking_price_email_invalid": {"message": "Proszę podać poprawny adres e-mail"}, "tracking_price_email_verified_desc": {"message": "Możesz teraz otrzymać nasz alert o spadku cen."}, "tracking_price_email_verified_title": {"message": "Pomyślnie zweryfikowano"}, "tracking_price_email_verify_desc_line1": {"message": "Wysłaliśmy link weryfikacyjny na Twój adres e-mail,"}, "tracking_price_email_verify_desc_line2": {"message": "sprawdź swoją skrzynkę odbiorczą."}, "tracking_price_email_verify_title": {"message": "Zweryfikuj email"}, "tracking_price_web_push_notification_intro": {"message": "Na komputerze: Ali<PERSON><PERSON> m<PERSON> dla Ciebie dowolny produkt i wysyłać Ci powiadomienie push w sieci po zmianie ceny."}, "tracking_price_web_push_notification_title": {"message": "Powiadomienia push w sieci Web"}, "translate_im__login_required": {"message": "Przetłumaczone przez AliPrice, zaloguj się na $loginUrl$", "placeholders": {"loginUrl": {"content": "$1"}}}, "translate_im__paste_fail": {"message": "Przetłumaczone i skopiowane do schowka, ale ze względu na ograniczenia Aliwangwanga musisz wkleić je ręcznie!"}, "translate_im__send": {"message": "Przetłumacz i wyślij"}, "translate_search": {"message": "Przetłumacz i wyszukaj"}, "translation_originals_translated": {"message": "Oryginalne i chińskie"}, "translation_translated": {"message": "<PERSON><PERSON><PERSON>"}, "translator_btn_capture_txt": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "translator_language_auto_detect": {"message": "Automatyczne wykrywanie"}, "translator_language_detected": {"message": "Wykryto"}, "translator_language_search_placeholder": {"message": "Wyszukaj język"}, "try_again": {"message": "Spróbuj ponownie"}, "tu_pian_chi_cun": {"message": "<PERSON><PERSON><PERSON><PERSON> o<PERSON>zu:"}, "tu_pian_lian_jie": {"message": "Link do obrazu"}, "tui_huan_ti_yan": {"message": "Zwróć <PERSON>wiadczeni<PERSON>"}, "tui_huan_ti_yan__desc": {"message": "Oceń wskaźniki posprzedażowe sprzedawców"}, "tutorial__show_all": {"message": "Wszystkie funkcje"}, "tutorial_ae_popup_title": {"message": "Przypnij rozszerzenie, otwórz Aliexpress"}, "tutorial_aliexpress_reviews_analysis": {"message": "Analiza recenzji AliExpress"}, "tutorial_aliprice_agent_with1688_desc_l1": {"message": "Wsparcie USD"}, "tutorial_aliprice_agent_with1688_desc_l2": {"message": "Wysyłka do Korei/Japonii/Chin k<PERSON>"}, "tutorial_aliprice_agent_with1688_title": {"message": "1688 obsługuje zakupy za granicą"}, "tutorial_auto_apply_coupon_title": {"message": "Automatycznie zastosuj kupon"}, "tutorial_btn_end": {"message": "Koniec"}, "tutorial_btn_example": {"message": "Przykład"}, "tutorial_btn_have_a_try": {"message": "OK, spróbuj"}, "tutorial_btn_next": {"message": "Kolejny"}, "tutorial_btn_see_more": {"message": "<PERSON><PERSON><PERSON><PERSON>j"}, "tutorial_compare_products": {"message": "Porównaj z tym samym stylem"}, "tutorial_currency_convert_title": {"message": "przeliczanie kursów walut"}, "tutorial_export_shopping_cart": {"message": "Eksportuj CSV, wspieraj Taobao i 1688"}, "tutorial_export_shopping_cart_title": {"message": "koszyk eksportowy"}, "tutorial_price_history_pro": {"message": "Wyświetlane na stronie szczegółów produktu.\nWspieraj Shopee, Lazadę, Amazon i Ebay"}, "tutorial_price_history_pro_title": {"message": "Pełna roczna historia cen i historia zamówień"}, "tutorial_sbi_context_menu_screenshot_title": {"message": "Wyszukiwanie zrzutów ekranu dla tego samego stylu"}, "tutorial_translate_search": {"message": "Przetłumacz na wyszukiwarkę"}, "tutorial_translate_search_and_package_tracking": {"message": "Wyszukiwanie tłumaczeń i śledzenie przesyłek"}, "unit_bao": {"message": "szt"}, "unit_ben": {"message": "szt"}, "unit_bi": {"message": "Zamówienia"}, "unit_chuang": {"message": "szt"}, "unit_dai": {"message": "szt"}, "unit_dui": {"message": "para"}, "unit_fen": {"message": "szt"}, "unit_ge": {"message": "szt"}, "unit_he": {"message": "szt"}, "unit_jian": {"message": "szt"}, "unit_li_fang_mi": {"message": "m³"}, "unit_ping": {"message": "szt"}, "unit_ping_fang_mi": {"message": "㎡"}, "unit_shuang": {"message": "para"}, "unit_tai": {"message": "szt"}, "unit_ti": {"message": "szt"}, "unit_tiao": {"message": "szt"}, "unit_xiang": {"message": "szt"}, "unit_zhang": {"message": "szt"}, "unit_zhi": {"message": "szt"}, "verify_contact_support": {"message": "Skontaktuj się z pomocą techniczną"}, "verify_human_verification": {"message": "Weryfikacja przez człowieka"}, "verify_unusual_access": {"message": "Wykryto nietypowy dostęp"}, "view_history_clean_all": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> wszystko"}, "view_history_clean_all_warring": {"message": "<PERSON><PERSON><PERSON><PERSON>ć wszystkie przeglądane rekordy?"}, "view_history_clean_all_warring_title": {"message": "Ostrzeżenie"}, "view_history_viewd": {"message": "Oglądane"}, "website": {"message": "strona internetowa"}, "weight": {"message": "W<PERSON>"}, "wu_fa_huo_qu_dao_gai_shu_ju": {"message": "Nie można uzyskać danych"}, "wu_liu_shi_xiao": {"message": "Wysyłka na czas"}, "wu_liu_shi_xiao__desc": {"message": "48-godzinny wskaźnik odbioru i wskaźnik realizacji sklepu sprzedawcy"}, "xia_dan_jia": {"message": "<PERSON>na <PERSON>"}, "xian_xuan_ze_product_attributes": {"message": "Wybierz atrybuty produktu"}, "xiao_liang": {"message": "Wielkość sprzedaży"}, "xiao_liang_zhan_bi": {"message": "<PERSON><PERSON> w<PERSON> s<PERSON>rz<PERSON>"}, "xiao_shi": {"message": "$num$ godzin", "placeholders": {"num": {"content": "$1"}}}, "xiao_shou_e": {"message": "Prz<PERSON><PERSON><PERSON>"}, "xiao_shou_e_zhan_bi": {"message": "<PERSON><PERSON>"}, "xuan_zhong_x_tiao_ji_lu": {"message": "Wybierz $amount$ rekordy", "placeholders": {"amoun": {"content": "$1"}, "amount": {"content": "$1"}}}, "yan_xuan": {"message": "Wybór"}, "yi_ding_zai_zuo_ce": {"message": "Przypię<PERSON>"}, "yi_jia_zai_wan_suo_you_shang_pin": {"message": "Wszystkie produkty załadowane"}, "yi_nian_xiao_liang": {"message": "Roczna sprzedaż"}, "yi_nian_xiao_liang_zhan_bi": {"message": "Roczny udział w sprzedaży"}, "yi_nian_xiao_shou_e": {"message": "Roczny obrót"}, "yi_nian_xiao_shou_e_zhan_bi": {"message": "Roczny udział w obrotach"}, "yi_shua_xin": {"message": "Odświeżony"}, "yin_cang_xiang_tong_dian": {"message": "<PERSON><PERSON>ć podobieństwa"}, "you_xiao_liang": {"message": "Z wielkością sprzedaży"}, "yu_ji_dao_da_shi_jian": {"message": "Szacowany czas przyjazdu"}, "yuan_gong_ren_shu": {"message": "Liczba pracowników"}, "yue_cheng_jiao": {"message": "Miesięczny wolumen"}, "yue_dai_xiao": {"message": "Dropshipping"}, "yue_dai_xiao__desc": {"message": "Sprzedaż dropshippingowa w ciągu ostatnich 30 dni"}, "yue_dai_xiao_pai_xu__desc": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON> metodą dropshipping w ciągu ostatnich 30 dni, posortowana od najwyższej do najniższej"}, "yue_xiao_liang__desc": {"message": "Wielkość sprzedaży w ciągu ostatnich 30 dni"}, "zhan_kai": {"message": "<PERSON><PERSON><PERSON><PERSON>j"}, "zhe_kou": {"message": "Rabat"}, "zhi_chi_yi_jian_dai_fa": {"message": "Dropshipping"}, "zhi_chi_yi_jian_dai_fa_bao_you": {"message": "Dar<PERSON><PERSON>"}, "zhi_fu_ding_dan_shu": {"message": "Płatne zamówienia"}, "zhi_fu_ding_dan_shu__desc": {"message": "Liczba zamówień na ten produkt (30 dni)"}, "zhu_ce_xing_zhi": {"message": "<PERSON><PERSON><PERSON>"}, "zi_ding_yi_tiao_jian": {"message": "Warunki niestandardowe"}, "zi_duan": {"message": "Pola"}, "zi_ti_xiao_liang": {"message": "Wariant sprzedany"}, "zong_he_fu_wu_fen": {"message": "Ogólna ocena"}, "zong_he_fu_wu_fen__desc": {"message": "Ogólna ocena obsługi sprzedawcy"}, "zong_he_fu_wu_fen__short": {"message": "Ocena"}, "zong_he_ti_yan_fen": {"message": "Ocena"}, "zong_he_ti_yan_fen_3": {"message": "Poniżej 4 gwiazdki"}, "zong_he_ti_yan_fen_4": {"message": "4 - 4,5 gwiazdki"}, "zong_he_ti_yan_fen_4_5": {"message": "4,5 - 5,0 g<PERSON><PERSON><PERSON>"}, "zong_he_ti_yan_fen_5": {"message": "5 gwiazdek"}, "zong_ku_cun": {"message": "Całkowity inwentarz"}, "zong_xiao_liang": {"message": "Całkowita sprzedaż"}, "zui_jin_30D_3Min_xiang_ying_lv": {"message": "Współczynnik odpowiedzi w ciągu 3 minut w ciągu ostatnich 30 dni"}, "zui_jin_30D_48H_lan_shou_lv": {"message": "48-<PERSON><PERSON>ny wskaźnik powrotu do zdrowia w ciągu ostatnich 30 dni"}, "zui_jin_30D_48H_lv_yue_lv": {"message": "Wskaźnik wydajności 48 godzin w ciągu ostatnich 30 dni"}, "zui_jin_30D_jiao_yi_ji_lu": {"message": "<PERSON><PERSON><PERSON> handlowy (30 dni)"}, "zui_jin_30D_jiao_yi_ji_lu__desc": {"message": "<PERSON><PERSON><PERSON> handlowy (30 dni)"}, "zui_jin_30D_jiu_fen_lv": {"message": "Wskaźnik sporów w ciągu ostatnich 30 dni"}, "zui_jin_30D_pin_zhi_tui_kuan_lv": {"message": "Stawka zwrotu kosztów jakości w ciągu ostatnich 30 dni"}, "zui_jin_30D_zhi_fu_ding_dan_shu": {"message": "Liczba zleceń płatniczych w ciągu ostatnich 30 dni"}}