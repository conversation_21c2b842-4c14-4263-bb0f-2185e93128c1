{"EXTENSION_NAME": {"message": "TODO: EXTENSION_NAME"}, "EXTENSION_DESCRIPTION": {"message": "TODO: EXTENSION_DESCRIPTION"}, "1688_cross_border_hot_selling": {"message": "1688 Cross-border Hot Selling"}, "1688_shi_li_ren_zheng": {"message": "1688 Strength Certification"}, "1_jian_qi_pi": {"message": "MOQ: 1"}, "1year_yi_shang": {"message": "More than 1 year"}, "24H": {"message": "24H"}, "24H_fa_huo": {"message": "Delivery within 24 hours"}, "24H_lan_shou_lv": {"message": "24-hour Packaging rate"}, "30D_shang_xin": {"message": "Monthly New Arrivals"}, "30d_sales": {"message": "$amount$ sold in 30 days", "placeholders": {"amount": {"content": "$1"}}}, "3Min_xiang_ying_lv": {"message": "Response within 3 min."}, "3Min_xiang_ying_lv__desc": {"message": "The proportion of Wangwang’s effective responses to buyer inquiry messages within 3 minutes in the past 30 days"}, "48H": {"message": "48H"}, "48H_fa_huo": {"message": "Delivery within 48 hours"}, "48H_lan_shou_lv": {"message": "48-hour Packaging rate"}, "48H_lan_shou_lv__desc": {"message": "<PERSON><PERSON> of the picked up order number within 48 hours to the total number of orders"}, "48H_lv_yue_lv": {"message": "48-hour performance rate"}, "48H_lv_yue_lv__desc": {"message": "Ratio of the picked up or delivered order number within 48 hours to the total number of orders"}, "72H": {"message": "72H"}, "7D_shang_xin": {"message": "Weekly New Arrivals"}, "7D_wu_li_you": {"message": "7 days care free"}, "ABS_title_text": {"message": "This listing includes a brand story"}, "AC_title_text": {"message": "This listing has the Amazon's Choice badge"}, "A_title_text": {"message": "This listing has an A+ content page"}, "BS_title_text": {"message": "This listing is ranked as the $num$ Best Seller in the $type$ category", "placeholders": {"type": {"content": "$1"}, "num": {"content": "$2"}}}, "LTD_title_text": {"message": "LTD (Limited Time Deal) means that this listing is part of a \"7-day promotion\" event"}, "NR_title_text": {"message": "This listing is ranked as the $num$ New Release in the $type$ category", "placeholders": {"type": {"content": "$1"}, "num": {"content": "$2"}}}, "SBV_title_text": {"message": "This listing has a video ad, a type of PPC ad that usually appears in the middle of search results"}, "SB_title_text": {"message": "This listing has a brand ad, a type of PPC ad that usually appears at the top or bottom of search results"}, "SP_title_text": {"message": "This listing has a Sponsored Product ad"}, "V_title_text": {"message": "This listing has a video introduction"}, "advanced_research": {"message": "Advanced Research"}, "agent_ds1688___my_order": {"message": "My Orders"}, "agent_ds1688__add_to_cart": {"message": "Overseas Purchase"}, "agent_ds1688__cart": {"message": "Shopping cart"}, "agent_ds1688__desc": {"message": "Provided by 1688. It supports direct purchase from overseas, payment in USD and delivery to your transit warehouse in China."}, "agent_ds1688__freight": {"message": "Shipping Cost Calculator"}, "agent_ds1688__help": {"message": "Help"}, "agent_ds1688__packages": {"message": "Waybill"}, "agent_ds1688__profile": {"message": "Personal Center"}, "agent_ds1688__warehouse": {"message": "My warehouse"}, "ai_comment_analysis_advantage": {"message": "Pros"}, "ai_comment_analysis_ai": {"message": "AI review analysis"}, "ai_comment_analysis_available": {"message": "Available"}, "ai_comment_analysis_balance": {"message": "Insufficient coins, please top up"}, "ai_comment_analysis_behavior": {"message": "Behaviour"}, "ai_comment_analysis_characteristic": {"message": "Crowd characteristics"}, "ai_comment_analysis_comment": {"message": "The product does not have enough reviews to draw accurate conclusions, please select a product with more reviews."}, "ai_comment_analysis_consume": {"message": "Estimated Consumption"}, "ai_comment_analysis_default": {"message": "Default reviews"}, "ai_comment_analysis_desire": {"message": "Customer expectations"}, "ai_comment_analysis_disadvantage": {"message": "Cons"}, "ai_comment_analysis_free": {"message": "Free attempts"}, "ai_comment_analysis_freeNum": {"message": "1 free credit will be used"}, "ai_comment_analysis_go_recharge": {"message": "Go to top up"}, "ai_comment_analysis_intelligence": {"message": "Intelligent review analysis"}, "ai_comment_analysis_location": {"message": "Location"}, "ai_comment_analysis_motive": {"message": "Purchase motivation"}, "ai_comment_analysis_network_error": {"message": "Network error, please try again"}, "ai_comment_analysis_normal": {"message": "Photo reviews"}, "ai_comment_analysis_number_reviews": {"message": "Number of Reviews: $num$, Estimated Consumption: $consume$", "placeholders": {"num": {"content": "$1"}, "consume": {"content": "$2"}}}, "ai_comment_analysis_ordinary": {"message": "General comments"}, "ai_comment_analysis_percentage": {"message": "Percentage"}, "ai_comment_analysis_problem": {"message": "Problems with payment"}, "ai_comment_analysis_reanalysis": {"message": "Re-analyse"}, "ai_comment_analysis_reason": {"message": "Reason"}, "ai_comment_analysis_recharge": {"message": "Top up"}, "ai_comment_analysis_recharged": {"message": "I have topped up"}, "ai_comment_analysis_retry": {"message": "Retry"}, "ai_comment_analysis_scene": {"message": "Usage scenario"}, "ai_comment_analysis_start": {"message": "Start analysing"}, "ai_comment_analysis_subject": {"message": "Topics"}, "ai_comment_analysis_time": {"message": "Time of use"}, "ai_comment_analysis_tool": {"message": "AI tool"}, "ai_comment_analysis_user_portrait": {"message": "User profile"}, "ai_comment_analysis_welcome": {"message": "Welcome to AI review analysis"}, "ai_comment_analysis_year": {"message": "Comments from the past year"}, "ai_listing_Exclude_keywords": {"message": "Exclude keywords"}, "ai_listing_Login_the_feature": {"message": "Login is required for the feature"}, "ai_listing_aI_generation": {"message": "AI generation"}, "ai_listing_add_automatic": {"message": "Automatic"}, "ai_listing_add_dictionary_new": {"message": "Create a new library"}, "ai_listing_add_enter_keywords": {"message": "Enter keywords"}, "ai_listing_add_inputkey_selling": {"message": "Enter a selling point and press $key$ to finish adding", "placeholders": {"key": {"content": "$1"}}}, "ai_listing_add_inputkey_warning": {"message": "Limit exceeded, up to $amount$ selling points", "placeholders": {"amount": {"content": "$1"}}}, "ai_listing_add_keywords": {"message": "Add keywords"}, "ai_listing_add_manually": {"message": "Manually add"}, "ai_listing_add_selling": {"message": "Add selling points"}, "ai_listing_added_keywords": {"message": "Added keywords"}, "ai_listing_added_successfully": {"message": "Added successfully"}, "ai_listing_addexcluded_keywords": {"message": "Enter the excluded keywords, press enter to finish adding."}, "ai_listing_adding_selling": {"message": "Added selling points"}, "ai_listing_addkeyword_enter": {"message": "Type in the key attribute words and press enter to finish adding"}, "ai_listing_ai_description": {"message": "AI description word library"}, "ai_listing_ai_dictionary": {"message": "AI title word library"}, "ai_listing_ai_title": {"message": "AI title"}, "ai_listing_aidescription_repeated": {"message": "AI description word library name cannot be repeated"}, "ai_listing_aititle_repeated": {"message": "AI title word library name cannot be repeated"}, "ai_listing_data_comes_from": {"message": "The data comes from:"}, "ai_listing_deleted_successfully": {"message": "Deleted successfully"}, "ai_listing_dictionary_name": {"message": "Library name"}, "ai_listing_edit_dictionary": {"message": "Modify library..."}, "ai_listing_edit_word_library": {"message": "Edit the word library"}, "ai_listing_enter_keywords": {"message": "Enter keywords and press $key$ to finish adding", "placeholders": {"key": {"content": "$1"}}}, "ai_listing_exceeded_maximum": {"message": "The limit has been exceeded, maximum $amount$ keywords", "placeholders": {"amount": {"content": "$1"}}}, "ai_listing_excluded_word_library": {"message": "Excluded word library"}, "ai_listing_generate_characters": {"message": "Generate characters"}, "ai_listing_generation_platform": {"message": "Generation platform"}, "ai_listing_help_optimize": {"message": "Help me optimize the product title, the original title is"}, "ai_listing_include_selling": {"message": "Other selling points included:"}, "ai_listing_included_keyword": {"message": "Included keywords"}, "ai_listing_included_keywords": {"message": "Included keywords"}, "ai_listing_input_selling": {"message": "Enter a selling point"}, "ai_listing_input_selling_fit": {"message": "Enter selling points to match the title"}, "ai_listing_input_selling_please": {"message": "Please enter selling points"}, "ai_listing_intelligently_title": {"message": "Enter the required content above to generate the title intelligently"}, "ai_listing_keyword_product_title": {"message": "Keyword product title"}, "ai_listing_keywords_repeated": {"message": "Keywords cannot be repeated"}, "ai_listing_listed_selling_points": {"message": "Included selling points"}, "ai_listing_long_title_1": {"message": "Contains basic information such as brand name, product type, product features, etc."}, "ai_listing_long_title_2": {"message": "On the basis of the standard product title, keywords conducive to SEO are added."}, "ai_listing_long_title_3": {"message": "In addition to containing brand name, product type, product features and keywords, long-tail keywords are also included to achieve higher rankings in specific, segmented search queries."}, "ai_listing_longtail_keyword_product_title": {"message": "Long-tail keyword product title"}, "ai_listing_manually_enter": {"message": "Manually enter ..."}, "ai_listing_network_not_working": {"message": "Internet is not available, VPN is required to access ChatGPT"}, "ai_listing_new_dictionary": {"message": "Create a new word library ..."}, "ai_listing_new_generate": {"message": "Generate"}, "ai_listing_optional_words": {"message": "Optional words"}, "ai_listing_original_title": {"message": "Original title"}, "ai_listing_other_keywords_included": {"message": "Other keywords included:"}, "ai_listing_please_again": {"message": "Please try again"}, "ai_listing_please_select": {"message": "The following titles have been generated for you, please select:"}, "ai_listing_product_category": {"message": "Product category"}, "ai_listing_product_category_is": {"message": "The product category is"}, "ai_listing_product_category_to": {"message": "What category does the product belong to?"}, "ai_listing_random_keywords": {"message": "Random $amount$ keywords", "placeholders": {"amount": {"content": "$1"}}}, "ai_listing_random_selling": {"message": "Random $amount$ selling points", "placeholders": {"amount": {"content": "$1"}}}, "ai_listing_randomize_keywords": {"message": "Randomize from the word library"}, "ai_listing_search_selling": {"message": "Search by selling point"}, "ai_listing_select_product_categories": {"message": "Automatically select product categories."}, "ai_listing_select_product_selling_points": {"message": "Automatically select product selling points"}, "ai_listing_select_word_library": {"message": "Select word library"}, "ai_listing_selling": {"message": "Selling points"}, "ai_listing_selling_ask": {"message": "What other selling point requirements are there for the title?"}, "ai_listing_selling_optional": {"message": "Optional selling points"}, "ai_listing_selling_repeat": {"message": "Points cannot be duplicated"}, "ai_listing_set_excluded": {"message": "Set as excluded word library"}, "ai_listing_set_include_selling_points": {"message": "Include selling points"}, "ai_listing_set_included": {"message": "Set as included word library"}, "ai_listing_set_selling_dictionary": {"message": "Set as selling point library"}, "ai_listing_standard_product_title": {"message": "Standard product title"}, "ai_listing_translated_title": {"message": "Translated Title"}, "ai_listing_visit_chatGPT": {"message": "Visit ChatGPT"}, "ai_listing_what_other_keywords": {"message": "What other keywords are required for the title?"}, "aliprice_coupons_apply_again": {"message": "Apply Again"}, "aliprice_coupons_apply_coupons": {"message": "Apply coupons"}, "aliprice_coupons_apply_success": {"message": "Found coupon: Save $amount$", "placeholders": {"amount": {"content": "$1"}}}, "aliprice_coupons_applying": {"message": "Testing codes for the best deals..."}, "aliprice_coupons_applying_desc": {"message": "Checking out: $code$", "placeholders": {"code": {"content": "$1"}}}, "aliprice_coupons_back_to_checkout": {"message": "Continue to Checkout"}, "aliprice_coupons_found_coupons": {"message": "We found $amount$ coupons", "placeholders": {"amount": {"content": "$1"}}}, "aliprice_coupons_found_coupons_desc": {"message": "Ready to checkout? Let’s make sure \nyou get the best price!"}, "aliprice_coupons_no_coupon_aviable": {"message": "Those codes didn’t work. \nNo biggie—you’re already \ngetting the best price."}, "aliprice_coupons_toolbar_btn": {"message": "Get Coupons"}, "aliww_translate": {"message": "Aliwangwang Chat Translator"}, "aliww_translate_supports": {"message": "Support: 1688 & Taobao"}, "amazon_extended_keywords_Keywords": {"message": "Keywords"}, "amazon_extended_keywords_copy_all": {"message": "Copy all"}, "amazon_extended_keywords_more": {"message": "More"}, "an_lei_ji_xiao_liang_pai_xu": {"message": "Sort by cumulative sales"}, "an_lei_xing_cha_kan": {"message": "Type"}, "an_yue_dai_xiao_pai_xu": {"message": "Ranking by dropshipping sales"}, "apra_btn__cat_name": {"message": "Reviews analysis"}, "apra_chart__name": {"message": "Percentage of product sales by country"}, "apra_chart__update_at": {"message": "Update time $time$", "placeholders": {"time": {"content": "$1"}}}, "apra_name": {"message": "Sales statistics of countries"}, "auto_opening": {"message": "Automatically opening in $num$ seconds", "placeholders": {"num": {"content": "$1"}}}, "auto_page_next_x_pages": {"message": "Next $autoPaging$ pages", "placeholders": {"autoPaging": {"content": "$1"}}}, "average_days_listed": {"message": "Average on shelf days"}, "average_hui_fu_lv": {"message": "Average reply rate"}, "average_ping_gong_ying_shang_deng_ji": {"message": "Average supplier level"}, "average_price": {"message": "Average price"}, "average_qi_ding_liang": {"message": "Average MOQ"}, "average_rating": {"message": "Average rating"}, "average_ren_zheng_gong_ying_nian_chang": {"message": "Average years of certification"}, "average_revenue": {"message": "Average revenue"}, "average_revenue_per_product": {"message": "Total revenue ÷ Number of products"}, "average_sales": {"message": "Average sales"}, "average_sales_per_product": {"message": "Total sales ÷ Number of products"}, "bao_han": {"message": "Contains"}, "bao_zheng_jin": {"message": "Security Deposit"}, "bian_ti_shu": {"message": "Variations"}, "biao_ti": {"message": "Title"}, "blacklist_add_blacklist": {"message": "Block this store"}, "blacklist_address_incorrect": {"message": "The address is incorrect. Please check it."}, "blacklist_blacked_out": {"message": "Store has been blocked"}, "blacklist_blacklist": {"message": "Blacklist"}, "blacklist_no_records_yet": {"message": "No record yet!"}, "blue_rocket": {"message": "로켓배송"}, "brand_name": {"message": "Brand"}, "btn_aliprice_agent__daigou": {"message": "Buy-for-me Agent"}, "btn_aliprice_agent__dropshipping": {"message": "Dropshipping"}, "btn_have_a_try": {"message": "Have a try"}, "btn_refresh": {"message": "Refresh"}, "btn_try_it_now": {"message": "Try it now"}, "btn_txt_view_on_aliprice": {"message": "View on AliPrice"}, "bu_bao_han": {"message": "Does Not Contain"}, "bulk_copy_links": {"message": "Bulk Copy Links"}, "bulk_copy_products": {"message": "Bulk Copy Products"}, "cai_gou_zi_xun": {"message": "Customer service"}, "cai_gou_zi_xun__desc": {"message": "<PERSON><PERSON>’s three-minute response rate"}, "can_ping_lei_xing": {"message": "Type"}, "cao_zuo": {"message": "Operation"}, "chan_pin_ID": {"message": "Product ID"}, "chan_pin_e_wai_xin_xi": {"message": "Product Extra Info"}, "chan_pin_lian_jie": {"message": "Product Link"}, "cheng_li_shi_jian": {"message": "Establishment time"}, "city__aba": {"message": "Aba"}, "city__aksu": {"message": "<PERSON><PERSON><PERSON>"}, "city__alaer": {"message": "<PERSON><PERSON><PERSON>"}, "city__altai": {"message": "Altai"}, "city__alxaleague": {"message": "Alxa League"}, "city__ankang": {"message": "Ankan<PERSON>"}, "city__anqing": {"message": "Anqing"}, "city__anshan": {"message": "<PERSON><PERSON>"}, "city__anshun": {"message": "<PERSON><PERSON><PERSON>"}, "city__anyang": {"message": "Anyang"}, "city__baicheng": {"message": "Baicheng"}, "city__baise": {"message": "Baise"}, "city__baisha": {"message": "Baisha"}, "city__baishan": {"message": "Baishan"}, "city__baiyin": {"message": "<PERSON><PERSON><PERSON>"}, "city__baoding": {"message": "<PERSON>od<PERSON>"}, "city__baoji": {"message": "<PERSON><PERSON><PERSON>"}, "city__baoshan": {"message": "<PERSON><PERSON><PERSON>"}, "city__baoting": {"message": "Baoting"}, "city__baotou": {"message": "<PERSON><PERSON><PERSON>"}, "city__bayannur": {"message": "Bayannur"}, "city__bayinguoleng": {"message": "Bayinguoleng"}, "city__bazhong": {"message": "Bazhong"}, "city__beihai": {"message": "Beihai"}, "city__bengbu": {"message": "<PERSON><PERSON><PERSON>"}, "city__benxi": {"message": "Benxi"}, "city__bijie": {"message": "Bijie"}, "city__binzhou": {"message": "Binzhou"}, "city__bortala": {"message": "<PERSON><PERSON><PERSON>"}, "city__bozhou": {"message": "Bozhou"}, "city__cangzhou": {"message": "Cangzhou"}, "city__chamdo": {"message": "<PERSON><PERSON><PERSON>"}, "city__changchun": {"message": "<PERSON><PERSON><PERSON>"}, "city__changde": {"message": "<PERSON><PERSON>"}, "city__changhua": {"message": "Changhua"}, "city__changji": {"message": "Changji"}, "city__changjiang": {"message": "Changjiang"}, "city__changsha": {"message": "Changsha"}, "city__changzhi": {"message": "Changzhi"}, "city__changzhou": {"message": "Changzhou"}, "city__chaohu": {"message": "<PERSON><PERSON>"}, "city__chaoyang": {"message": "Chaoyang"}, "city__chaozhou": {"message": "Chaozhou"}, "city__chengde": {"message": "Chengde"}, "city__chengdu": {"message": "Chengdu"}, "city__chengmai": {"message": "<PERSON><PERSON><PERSON>"}, "city__chenzhou": {"message": "Chenzhou"}, "city__chiayi": {"message": "<PERSON><PERSON><PERSON>"}, "city__chifeng": {"message": "<PERSON><PERSON>"}, "city__chizhou": {"message": "Chizhou"}, "city__chongzuo": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__chuxiong": {"message": "Chuxiong"}, "city__chuzhou": {"message": "Chuzhou"}, "city__dali": {"message": "<PERSON><PERSON>"}, "city__dalian": {"message": "<PERSON><PERSON>"}, "city__dandong": {"message": "Dandong"}, "city__danzhou": {"message": "Danzhou"}, "city__daqing": {"message": "Daqing"}, "city__datong": {"message": "<PERSON><PERSON><PERSON>"}, "city__daxinganling": {"message": "<PERSON><PERSON>'<PERSON>ling"}, "city__dazhou": {"message": "Dazhou"}, "city__dehong": {"message": "<PERSON><PERSON>"}, "city__deyang": {"message": "Deyang"}, "city__dezhou": {"message": "Dezhou"}, "city__dingan": {"message": "Ding'an"}, "city__dingxi": {"message": "Dingxi"}, "city__diqing": {"message": "Diqing"}, "city__dongfang": {"message": "<PERSON><PERSON>g"}, "city__dongguan": {"message": "Dongguan"}, "city__dongying": {"message": "<PERSON><PERSON>"}, "city__enshi": {"message": "<PERSON><PERSON>"}, "city__ezhou": {"message": "Ezhou"}, "city__fangchenggang": {"message": "Fangchenggang"}, "city__foshan": {"message": "<PERSON><PERSON><PERSON>"}, "city__fushun": {"message": "<PERSON><PERSON><PERSON>"}, "city__fuxin": {"message": "Fuxin"}, "city__fuyang": {"message": "Fuyang"}, "city__fuzhou": {"message": "Fuzhou"}, "city__gannan": {"message": "<PERSON><PERSON><PERSON>"}, "city__ganzhou": {"message": "Ganzhou"}, "city__ganzi": {"message": "Ganzi"}, "city__guangan": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__guangyuan": {"message": "Guangyuan"}, "city__guangzhou": {"message": "Guangzhou"}, "city__guigang": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__guilin": {"message": "<PERSON><PERSON><PERSON>"}, "city__guiyang": {"message": "Guiyang"}, "city__guolok": {"message": "Guolok"}, "city__guyuan": {"message": "<PERSON><PERSON>"}, "city__haibei": {"message": "Haibei"}, "city__haidong": {"message": "Haidong"}, "city__haikou": {"message": "Hai<PERSON><PERSON>"}, "city__hainan": {"message": "Hainan"}, "city__haixi": {"message": "Haixi"}, "city__hami": {"message": "<PERSON><PERSON>"}, "city__handan": {"message": "<PERSON><PERSON>"}, "city__hangzhou": {"message": "Hangzhou"}, "city__hanzhong": {"message": "Hanzhong"}, "city__harbin": {"message": "<PERSON><PERSON><PERSON>"}, "city__hebi": {"message": "<PERSON><PERSON>"}, "city__hechi": {"message": "<PERSON><PERSON>"}, "city__hefei": {"message": "<PERSON><PERSON><PERSON>"}, "city__hegang": {"message": "<PERSON><PERSON><PERSON>"}, "city__heihe": {"message": "<PERSON><PERSON><PERSON>"}, "city__hengshui": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__hengyang": {"message": "Hengyang"}, "city__heyuan": {"message": "<PERSON><PERSON>"}, "city__heze": {"message": "<PERSON><PERSON>"}, "city__hezhou": {"message": "Hezhou"}, "city__hohhot": {"message": "Hohhot"}, "city__honghe": {"message": "<PERSON><PERSON>"}, "city__hongkongisland": {"message": "Hong Kong Island"}, "city__hotan": {"message": "<PERSON><PERSON>"}, "city__hsinchu": {"message": "<PERSON><PERSON><PERSON>"}, "city__huaian": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__huaibei": {"message": "<PERSON><PERSON><PERSON>"}, "city__huaihua": {"message": "Huaihua"}, "city__huaisouth": {"message": "Huai South"}, "city__hualien": {"message": "<PERSON><PERSON><PERSON>"}, "city__huanggang": {"message": "<PERSON><PERSON><PERSON>"}, "city__huangnan": {"message": "Huangnan"}, "city__huangshan": {"message": "<PERSON><PERSON>"}, "city__huangshi": {"message": "<PERSON><PERSON>"}, "city__huizhou": {"message": "Huizhou"}, "city__huludao": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__hulunbuir": {"message": "Hulunbuir"}, "city__huzhou": {"message": "Huzhou"}, "city__jiamusi": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__jian": {"message": "<PERSON><PERSON><PERSON>"}, "city__jiangmen": {"message": "Jiangmen"}, "city__jiaozuo": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__jiaxing": {"message": "Jiaxing"}, "city__jiayuguan": {"message": "Jiayuguan"}, "city__jieyang": {"message": "<PERSON><PERSON><PERSON>"}, "city__jilin": {"message": "<PERSON><PERSON>"}, "city__jinan": {"message": "<PERSON><PERSON>"}, "city__jinchang": {"message": "Jinchang"}, "city__jincheng": {"message": "Jincheng"}, "city__jingdezhen": {"message": "Jingdez<PERSON>"}, "city__jingmen": {"message": "Jingmen"}, "city__jingzhou": {"message": "Jingzhou"}, "city__jinhua": {"message": "Jinhua"}, "city__jining": {"message": "Jining"}, "city__jinzhong": {"message": "Jinzhong"}, "city__jinzhou": {"message": "Jinzhou"}, "city__jiujiang": {"message": "Jiujiang"}, "city__jiuquan": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__jixi": {"message": "Ji<PERSON>"}, "city__kaifeng": {"message": "Kaifeng"}, "city__kaohsiung": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__karamay": {"message": "<PERSON><PERSON><PERSON>"}, "city__kashgar": {"message": "Ka<PERSON><PERSON>"}, "city__keelung": {"message": "Keelung"}, "city__kinmen": {"message": "Kinmen"}, "city__kizilsu": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__kowloon": {"message": "Kowloon"}, "city__kunming": {"message": "Ku<PERSON><PERSON>"}, "city__laibin": {"message": "<PERSON><PERSON>"}, "city__laiwu": {"message": "<PERSON><PERSON>"}, "city__langfang": {"message": "<PERSON><PERSON><PERSON>"}, "city__lanzhou": {"message": "Lanzhou"}, "city__ledong": {"message": "<PERSON><PERSON>"}, "city__leshan": {"message": "<PERSON><PERSON>"}, "city__lhasa": {"message": "Lhasa"}, "city__liangshan": {"message": "Liangshan"}, "city__lianyungang": {"message": "Lianyungang"}, "city__liaocheng": {"message": "<PERSON><PERSON><PERSON>"}, "city__liaoyang": {"message": "<PERSON><PERSON><PERSON>"}, "city__liaoyuan": {"message": "<PERSON><PERSON><PERSON>"}, "city__lienchiang": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__lijiang": {"message": "Lijiang"}, "city__lincang": {"message": "Lincang"}, "city__linfen": {"message": "Linfen"}, "city__lingao": {"message": "Lingao"}, "city__lingshui": {"message": "<PERSON><PERSON><PERSON>"}, "city__linxia": {"message": "Lin<PERSON>"}, "city__linyi": {"message": "<PERSON><PERSON>"}, "city__lishui": {"message": "Li<PERSON><PERSON>"}, "city__liupanshui": {"message": "Liupanshu<PERSON>"}, "city__liuzhou": {"message": "Liuzhou"}, "city__longnan": {"message": "<PERSON><PERSON>"}, "city__longyan": {"message": "<PERSON><PERSON>"}, "city__loudi": {"message": "<PERSON><PERSON>"}, "city__luan": {"message": "<PERSON><PERSON><PERSON>"}, "city__luohe": {"message": "<PERSON><PERSON><PERSON>"}, "city__luoyang": {"message": "<PERSON><PERSON><PERSON>"}, "city__luzhou": {"message": "Luzhou"}, "city__lüliang": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__maanshan": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__macauoutlyingislands": {"message": "Macau Outlying Islands"}, "city__macaupeninsula": {"message": "Macau Peninsula"}, "city__maoming": {"message": "<PERSON><PERSON>"}, "city__meishan": {"message": "<PERSON><PERSON>"}, "city__meizhou": {"message": "Meizhou"}, "city__mianyang": {"message": "Mianyang"}, "city__miaoli": {"message": "<PERSON><PERSON>"}, "city__mudanjiang": {"message": "Mudanjiang"}, "city__nagqu": {"message": "<PERSON>g<PERSON>u"}, "city__nanchang": {"message": "Nanchang"}, "city__nanchong": {"message": "<PERSON><PERSON><PERSON>"}, "city__nanjing": {"message": "Nanjing"}, "city__nanning": {"message": "Nanning"}, "city__nanping": {"message": "<PERSON><PERSON>"}, "city__nantong": {"message": "Nantong"}, "city__nantou": {"message": "<PERSON><PERSON><PERSON>"}, "city__nanyang": {"message": "Nanyang"}, "city__neijiang": {"message": "Neijiang"}, "city__newterritories": {"message": "New Territories"}, "city__ngari": {"message": "<PERSON><PERSON>"}, "city__ningbo": {"message": "Ningbo"}, "city__ningde": {"message": "<PERSON><PERSON><PERSON>"}, "city__nujiang": {"message": "Nujiang"}, "city__nyingchi": {"message": "Nyingchi"}, "city__ordos": {"message": "Ordos"}, "city__panjin": {"message": "Panjin"}, "city__panzhihua": {"message": "Pan Zhihua"}, "city__penghu": {"message": "<PERSON><PERSON><PERSON>"}, "city__pingdingshan": {"message": "Pingdingshan"}, "city__pingliang": {"message": "<PERSON><PERSON><PERSON>"}, "city__pingtung": {"message": "<PERSON><PERSON><PERSON>"}, "city__pingxiang": {"message": "<PERSON><PERSON><PERSON>"}, "city__puer": {"message": "<PERSON>u'er"}, "city__putian": {"message": "<PERSON><PERSON>"}, "city__puyang": {"message": "P<PERSON><PERSON>"}, "city__qiandongnan": {"message": "Qiandongnan"}, "city__qianjiang": {"message": "Qianjiang"}, "city__qiannan": {"message": "<PERSON><PERSON><PERSON>"}, "city__qianxinan": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__qingdao": {"message": "Qingdao"}, "city__qingyang": {"message": "Qingyang"}, "city__qingyuan": {"message": "Qingyuan"}, "city__qinhuangdao": {"message": "Qinhuangdao"}, "city__qinzhou": {"message": "Qinzhou"}, "city__qionghai": {"message": "Qionghai"}, "city__qiongzhong": {"message": "Qiongzhong"}, "city__qiqihar": {"message": "Qiqihar"}, "city__qitaihe": {"message": "<PERSON><PERSON><PERSON>"}, "city__quanzhou": {"message": "Quanzhou"}, "city__qujing": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__quzhou": {"message": "Quzhou"}, "city__rizhao": {"message": "<PERSON><PERSON><PERSON>"}, "city__sanmenxia": {"message": "Sanmenxia"}, "city__sanming": {"message": "Sanming"}, "city__sanya": {"message": "Sanya"}, "city__shangluo": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__shangqiu": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__shangrao": {"message": "<PERSON><PERSON><PERSON>"}, "city__shannan": {"message": "Shannan"}, "city__shantou": {"message": "<PERSON><PERSON><PERSON>"}, "city__shanwei": {"message": "Shanwei"}, "city__shaoguan": {"message": "Shaoguan"}, "city__shaoxing": {"message": "Shaoxing"}, "city__shaoyang": {"message": "Shaoyang"}, "city__shennongjiaforestarea": {"message": "Shennongjia Forest Area"}, "city__shenyang": {"message": "Shenyang"}, "city__shenzhen": {"message": "Shenzhen"}, "city__shigatse": {"message": "Shigat<PERSON>"}, "city__shihezi": {"message": "<PERSON><PERSON><PERSON>"}, "city__shijiazhuang": {"message": "Shijiazhuang"}, "city__shiyan": {"message": "<PERSON><PERSON>"}, "city__shizuishan": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__shuangyashan": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__shuozhou": {"message": "Shuozhou"}, "city__siping": {"message": "<PERSON><PERSON>"}, "city__songyuan": {"message": "Songyuan"}, "city__suihua": {"message": "Su<PERSON>ua"}, "city__suining": {"message": "Suining"}, "city__suizhou": {"message": "<PERSON><PERSON><PERSON>"}, "city__suqian": {"message": "<PERSON><PERSON><PERSON>"}, "city__suzhou": {"message": "Suzhou"}, "city__tacheng": {"message": "Tacheng"}, "city__taian": {"message": "Tai'an"}, "city__taichung": {"message": "Taichung"}, "city__tainan": {"message": "Tainan"}, "city__taipei": {"message": "Taipei"}, "city__taitung": {"message": "<PERSON><PERSON><PERSON>"}, "city__taiyuan": {"message": "Taiyuan"}, "city__taizhou": {"message": "Taizhou"}, "city__tangshan": {"message": "Tangshan"}, "city__taoyuan": {"message": "Taoyuan"}, "city__tianmen": {"message": "Tianmen"}, "city__tianshui": {"message": "<PERSON><PERSON><PERSON>"}, "city__tieling": {"message": "Tieling"}, "city__tongchuan": {"message": "<PERSON><PERSON><PERSON>"}, "city__tonghua": {"message": "Tonghua"}, "city__tongliao": {"message": "<PERSON><PERSON><PERSON>"}, "city__tongling": {"message": "Tongling"}, "city__tongren": {"message": "<PERSON><PERSON>"}, "city__tumushuke": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__tunchang": {"message": "Tunchang"}, "city__turpan": {"message": "<PERSON><PERSON><PERSON>"}, "city__ulanqab": {"message": "Ulanqab"}, "city__urumqi": {"message": "Urumqi"}, "city__wanning": {"message": "Wanning"}, "city__weifang": {"message": "<PERSON><PERSON><PERSON>"}, "city__weihai": {"message": "Weihai"}, "city__weinan": {"message": "Weinan"}, "city__wenchang": {"message": "Wenchang"}, "city__wenshan": {"message": "<PERSON><PERSON>"}, "city__wenzhou": {"message": "Wenzhou"}, "city__wuhai": {"message": "Wu<PERSON>"}, "city__wuhan": {"message": "<PERSON><PERSON>"}, "city__wuhu": {"message": "<PERSON><PERSON>"}, "city__wujiaqu": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__wuwei": {"message": "<PERSON><PERSON>"}, "city__wuxi": {"message": "Wuxi"}, "city__wuzhishan": {"message": "Wuzhishan"}, "city__wuzhong": {"message": "Wuzhong"}, "city__wuzhou": {"message": "Wuzhou"}, "city__xiamen": {"message": "Xiamen"}, "city__xian": {"message": "Xi'<PERSON>"}, "city__xiangfan": {"message": "<PERSON><PERSON><PERSON>"}, "city__xiangtan": {"message": "Xiangtan"}, "city__xiangxi": {"message": "Xiangxi"}, "city__xianning": {"message": "Xianning"}, "city__xiantao": {"message": "Xiantao"}, "city__xianyang": {"message": "<PERSON><PERSON><PERSON>"}, "city__xiaogan": {"message": "<PERSON><PERSON>"}, "city__xilingol": {"message": "Xilingol"}, "city__xinganleague": {"message": "Xing'an League"}, "city__xingtai": {"message": "Xingtai"}, "city__xining": {"message": "Xining"}, "city__xinxiang": {"message": "Xinxiang"}, "city__xinyang": {"message": "Xinyang"}, "city__xinyu": {"message": "Xinyu"}, "city__xinzhou": {"message": "Xinzhou"}, "city__xishuangbanna": {"message": "Xishuangbanna"}, "city__xuancheng": {"message": "Xuancheng"}, "city__xuchang": {"message": "Xuchang"}, "city__xuzhou": {"message": "Xuzhou"}, "city__yaan": {"message": "Ya'an"}, "city__yanan": {"message": "Yan'<PERSON>"}, "city__yanbian": {"message": "Yanbian"}, "city__yancheng": {"message": "Yancheng"}, "city__yangjiang": {"message": "Yangjiang"}, "city__yangquan": {"message": "<PERSON><PERSON><PERSON>"}, "city__yangzhou": {"message": "Yangzhou"}, "city__yantai": {"message": "Yantai"}, "city__yibin": {"message": "<PERSON><PERSON>"}, "city__yichang": {"message": "Yichang"}, "city__yichun": {"message": "<PERSON><PERSON><PERSON>"}, "city__yilan": {"message": "Yilan"}, "city__yili": {"message": "<PERSON><PERSON>"}, "city__yinchuan": {"message": "<PERSON><PERSON><PERSON>"}, "city__yingkou": {"message": "<PERSON><PERSON><PERSON>"}, "city__yingtan": {"message": "Yingtan"}, "city__yiwu": {"message": "<PERSON><PERSON>"}, "city__yiyang": {"message": "Yiyang"}, "city__yongzhou": {"message": "Yongzhou"}, "city__yueyang": {"message": "<PERSON><PERSON><PERSON>"}, "city__yulin": {"message": "Yulin"}, "city__yuncheng": {"message": "Yuncheng"}, "city__yunfu": {"message": "<PERSON><PERSON>"}, "city__yunlin": {"message": "<PERSON><PERSON>"}, "city__yushu": {"message": "Yushu"}, "city__yuxi": {"message": "Yuxi"}, "city__zaozhuang": {"message": "Zaozhuang"}, "city__zhangjiajie": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__zhangjiakou": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__zhangye": {"message": "<PERSON><PERSON>"}, "city__zhangzhou": {"message": "Zhangzhou"}, "city__zhanjiang": {"message": "Zhanjiang"}, "city__zhaoqing": {"message": "Zhaoqing"}, "city__zhaotong": {"message": "Zhaotong"}, "city__zhengzhou": {"message": "Zhengzhou"}, "city__zhenjiang": {"message": "Zhenjiang"}, "city__zhongshan": {"message": "Zhongshan"}, "city__zhongwei": {"message": "Zhongwei"}, "city__zhoukou": {"message": "<PERSON><PERSON><PERSON>"}, "city__zhoushan": {"message": "Zhoushan"}, "city__zhuhai": {"message": "Zhu<PERSON>"}, "city__zhumadian": {"message": "Zhumadian"}, "city__zhuzhou": {"message": "Zhuzhou"}, "city__zibo": {"message": "Zibo"}, "city__zigong": {"message": "Zigong"}, "city__ziyang": {"message": "<PERSON><PERSON><PERSON>"}, "city__zunyi": {"message": "<PERSON><PERSON><PERSON>"}, "click_copy_product_info": {"message": "Click Copy product info"}, "commmon_txt_expired": {"message": "Expired"}, "common__date_range_12m": {"message": "1 year"}, "common__date_range_1m": {"message": "1 month"}, "common__date_range_1w": {"message": "1 week"}, "common__date_range_2w": {"message": "2 weeks"}, "common__date_range_3m": {"message": "3 months"}, "common__date_range_3w": {"message": "3 weeks"}, "common__date_range_6m": {"message": "6 months"}, "common_btn_cancel": {"message": "Cancel"}, "common_btn_close": {"message": "Close"}, "common_btn_save": {"message": "Save"}, "common_btn_setting": {"message": "Setup"}, "common_email": {"message": "Email"}, "common_error_msg_no_data": {"message": "No data"}, "common_error_msg_no_result": {"message": "Sorry, no result found."}, "common_favorites": {"message": "Favorites"}, "common_feedback": {"message": "<PERSON><PERSON><PERSON>"}, "common_help": {"message": "Help"}, "common_loading": {"message": "Loading"}, "common_login": {"message": "<PERSON><PERSON>"}, "common_logout": {"message": "Logout"}, "common_no": {"message": "No"}, "common_powered_by_aliprice": {"message": "Powered by AliPrice.com"}, "common_setting": {"message": "Setting"}, "common_sign_up": {"message": "Sign up"}, "common_system_upgrading_title": {"message": "System upgrading"}, "common_system_upgrading_txt": {"message": "Please try it later"}, "common_txt__currency": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "common_txt__video_tutorial": {"message": "Video tutorial"}, "common_txt_ago_time": {"message": "$time$ days ago", "placeholders": {"time": {"content": "$1"}}}, "common_txt_all_product": {"message": "All"}, "common_txt_analysis": {"message": "Analysis"}, "common_txt_basically_used": {"message": "Basically used"}, "common_txt_biaoti_link": {"message": "Title+Link"}, "common_txt_biaoti_link_dian_pu": {"message": "Title+Link+Store Name"}, "common_txt_blacklist": {"message": "Blocklist"}, "common_txt_cancel": {"message": "Cancel"}, "common_txt_category": {"message": "Category"}, "common_txt_chakan": {"message": "Check"}, "common_txt_colors": {"message": "colors"}, "common_txt_confirm": {"message": "Confirm"}, "common_txt_copied": {"message": "<PERSON>pied"}, "common_txt_copy": {"message": "Copy"}, "common_txt_copy_link": {"message": "Copy link"}, "common_txt_copy_title": {"message": "Copy title"}, "common_txt_copy_title__link": {"message": "Copy title & link"}, "common_txt_day": {"message": "sky"}, "common_txt_day__short": {"message": "D"}, "common_txt_delete": {"message": "Delete"}, "common_txt_dian_pu_link": {"message": "Copy store name + link"}, "common_txt_download": {"message": "Download"}, "common_txt_downloaded": {"message": "Download"}, "common_txt_export_as_csv": {"message": "Export Excel"}, "common_txt_export_as_txt": {"message": "Export Txt"}, "common_txt_fail": {"message": "Fail"}, "common_txt_format": {"message": "Format"}, "common_txt_get": {"message": "get"}, "common_txt_incert_selection": {"message": "Invert selection"}, "common_txt_install": {"message": "Install"}, "common_txt_load_failed": {"message": "Failed to load"}, "common_txt_month": {"message": "month"}, "common_txt_more": {"message": "More"}, "common_txt_new_unused": {"message": "New, unused"}, "common_txt_next": {"message": "Next"}, "common_txt_no_limit": {"message": "Unlimited"}, "common_txt_no_noticeable": {"message": "No noticeable scratches or dirt"}, "common_txt_on_sale": {"message": "On sale"}, "common_txt_opt_in_out": {"message": "On/Off"}, "common_txt_order": {"message": "Order"}, "common_txt_others": {"message": "Others"}, "common_txt_overall_poor_condition": {"message": "Overall poor condition"}, "common_txt_patterns": {"message": "patterns"}, "common_txt_platform": {"message": "Platforms"}, "common_txt_please_select": {"message": "Please select"}, "common_txt_prev": {"message": "Prev"}, "common_txt_price": {"message": "Price"}, "common_txt_privacy_policy": {"message": "Privacy Policy"}, "common_txt_product_condition": {"message": "Product condition"}, "common_txt_rating": {"message": "Rating"}, "common_txt_ratings": {"message": "Ratings"}, "common_txt_reload": {"message": "Reload"}, "common_txt_reset": {"message": "Reset"}, "common_txt_retail": {"message": "Retail"}, "common_txt_review": {"message": "Review"}, "common_txt_sale": {"message": "On sale"}, "common_txt_same": {"message": "Same"}, "common_txt_scratches_and_dirt": {"message": "Scratches and dirt"}, "common_txt_search_title": {"message": "Search title"}, "common_txt_select_all": {"message": "Select All"}, "common_txt_selected": {"message": "Selected"}, "common_txt_share": {"message": "Share"}, "common_txt_sold": {"message": "sold"}, "common_txt_sold_out": {"message": "Sold out"}, "common_txt_some_scratches": {"message": "Some scratches and dirt"}, "common_txt_sort_by": {"message": "Sort by"}, "common_txt_state": {"message": "Status"}, "common_txt_success": {"message": "Success"}, "common_txt_sys_err": {"message": "System error"}, "common_txt_today": {"message": "Today"}, "common_txt_total": {"message": "Total"}, "common_txt_unselect_all": {"message": "Invert Selection"}, "common_txt_upload_image": {"message": "Upload image"}, "common_txt_visit": {"message": "Visit"}, "common_txt_whitelist": {"message": "Whitelist"}, "common_txt_wholesale": {"message": "Wholesale"}, "common_txt_wildberries": {"message": "Wildberries"}, "common_txt_year": {"message": "Year"}, "common_yes": {"message": "Yes"}, "compare_tool_btn_clear_all": {"message": "Clear All"}, "compare_tool_btn_compare": {"message": "Compare"}, "compare_tool_btn_contact": {"message": "Contact"}, "compare_tool_tips_max_compared": {"message": "Add up to $maxComparedCount$", "placeholders": {"maxComparedCount": {"content": "$1"}}}, "configure_notifiactions": {"message": "Configure Notifications"}, "contact_us": {"message": "Contact us"}, "context_menu_screenshot_search": {"message": "Capture to search by image"}, "context_menus_aliprice_search_by_image": {"message": "Search Image on AliPrice"}, "context_menus_goote_trans": {"message": "Translate page/Display original"}, "context_menus_search_by_image": {"message": "Search by image on $storeName$", "placeholders": {"storeName": {"content": "$1"}}}, "context_menus_search_by_image_capture": {"message": "Capture to $storeName$", "placeholders": {"storeName": {"content": "$1"}}}, "context_menus_translator": {"message": "Capture to translate"}, "converter_modal_amount_placeholder": {"message": "Enter amount here"}, "converter_modal_btn_convert": {"message": "Convert"}, "converter_modal_exchange_rate_source": {"message": "The data comes from $boc$ foreign exchange rate. Update time: $updatedAt$", "placeholders": {"boc": {"content": "$1"}, "updatedAt": {"content": "$2"}}}, "converter_modal_name": {"message": "Currency Converter"}, "converter_modal_search_placeholder": {"message": "Search currency"}, "copy_all_contact_us_notice": {"message": "This site is not supported at this time, please contact us"}, "copy_product_info": {"message": "Copy product info"}, "copy_suggest_search_kw": {"message": "Copy Dropdown Lists"}, "country__han_gou": {"message": "Korea"}, "country__ri_ben": {"message": "Japan"}, "country__yue_nan": {"message": "Vietnam"}, "currency_convert__custom": {"message": "Custom exchange rate"}, "currency_convert__sync_server": {"message": "Synchronize server"}, "dang_ri_fa_huo": {"message": "Same day shipping"}, "dao_chu_quan_dian_shang_pin": {"message": "Export All Store Products"}, "dao_chu_wei_CSV": {"message": "Export"}, "dao_chu_zi_duan": {"message": "Export Fields"}, "delivery_address": {"message": "Delivery address"}, "delivery_company": {"message": "Delivery company"}, "di_zhi": {"message": "Address"}, "dian_ji_cha_xun": {"message": "Click to query"}, "dian_pu_ID": {"message": "Store ID"}, "dian_pu_di_zhi": {"message": "Store Address"}, "dian_pu_lian_jie": {"message": "Store Link"}, "dian_pu_ming": {"message": "Store Name"}, "dian_pu_ming_cheng": {"message": "Store Name"}, "dian_pu_shang_pin_zong_hsu": {"message": "Total Number of Products in the Store: $num$", "placeholders": {"num": {"content": "$1"}}}, "dian_pu_xin_xi": {"message": "Store information"}, "ding_zai_zuo_ce": {"message": "<PERSON>nne<PERSON> on the left"}, "disable_old_version_tips_disable_btn_title": {"message": "Disable old version"}, "download_image__SKU_variant_images": {"message": "SKU variant images"}, "download_image__assume": {"message": "For example, we have 2 images, product1.jpg and product2.gif.\n\nimg_{$no$} will be renamed to img_01.jpg, img_02.gif; \n\n{$group$}_{$no$} will be renamed to main_image_01.jpg, main_image_02.gif;", "placeholders": {"no": {"content": "$3"}, "group": {"content": "$2"}}}, "download_image__batch_download": {"message": "Batch Download"}, "download_image__combined_image": {"message": "Combined product detail image"}, "download_image__continue_downloading": {"message": "Continue downloading"}, "download_image__description_images": {"message": "Description images"}, "download_image__download_combined_image": {"message": "Download combined product detail image"}, "download_image__download_zip": {"message": "Download zip"}, "download_image__enlarge_check": {"message": "Only supports JPEG, JPG, GIF and PNG images, maximum size of a single image: 1600 * 1600"}, "download_image__enlarge_image": {"message": "Enlarge image"}, "download_image__export": {"message": "Export links"}, "download_image__height": {"message": "Height"}, "download_image__ignore_videos": {"message": "The video has been ignored, as it cannot be exported"}, "download_image__img_translate": {"message": "Image Translate"}, "download_image__main_image": {"message": "Main image"}, "download_image__multi_folder": {"message": "Multi-folder"}, "download_image__name": {"message": "Download image"}, "download_image__notice_content": {"message": "Please don't check \"Ask where to save each file before downloading\" in your browser's download settings!!! Otherwise there will be lots of dialog boxes."}, "download_image__notice_ignore": {"message": "Don't prompt for this message again"}, "download_image__order_number": {"message": "{$no$} serial number; {$group$} group name; {$date$} timestamp", "placeholders": {"no": {"content": "$1"}, "group": {"content": "$2"}, "date": {"content": "$3"}}}, "download_image__overview": {"message": "Overview"}, "download_image__prompt_download_zip": {"message": "There are too many images, you'd better download them as a zip folder."}, "download_image__rename": {"message": "<PERSON><PERSON>"}, "download_image__rule": {"message": "Naming rules"}, "download_image__single_folder": {"message": "Single folder"}, "download_image__sku_image": {"message": "SKU image"}, "download_image__video": {"message": "Video"}, "download_image__width": {"message": "<PERSON><PERSON><PERSON>"}, "download_reviews__download_images": {"message": "Download review image"}, "download_reviews__dropdown_title": {"message": "Download review image"}, "download_reviews__export_csv": {"message": "Export as CSV"}, "download_reviews__no_images": {"message": "Sample copy: 0 images available for download"}, "download_reviews__no_reviews": {"message": "No reviews to download!"}, "download_reviews__notice": {"message": "Tip:"}, "download_reviews__notice__chrome_settings": {"message": "Set Chrome browser to ask where to save each file before downloading, set to \"Off\""}, "download_reviews__notice__wait": {"message": "Depending on the number of reviews, the wait time may be longer"}, "download_reviews__pages_list__all": {"message": "All"}, "download_reviews__pages_list__page": {"message": "Previous $page$ pages", "placeholders": {"page": {"content": "$1"}}}, "download_reviews__pages_list_title": {"message": "Selection range"}, "export_shopping_cart__csv_filed__details_url": {"message": "Product link"}, "export_shopping_cart__csv_filed__details_url_with_sku": {"message": "Show selected variants in the shared links"}, "export_shopping_cart__csv_filed__images": {"message": "Image link"}, "export_shopping_cart__csv_filed__quantity": {"message": "Quantity"}, "export_shopping_cart__csv_filed__sale_price": {"message": "Price"}, "export_shopping_cart__csv_filed__specs": {"message": "Specifications"}, "export_shopping_cart__csv_filed__store_name": {"message": "Store name"}, "export_shopping_cart__csv_filed__store_url": {"message": "Store link"}, "export_shopping_cart__csv_filed__title": {"message": "Product name"}, "export_shopping_cart__export_btn": {"message": "Export"}, "export_shopping_cart__export_empty": {"message": "Please select a product!"}, "fa_huo_shi_jian": {"message": "Shipping"}, "favorite_add_email": {"message": "Add <PERSON>"}, "favorite_add_favorites": {"message": "Add to Favorites"}, "favorite_added": {"message": "Added"}, "favorite_btn_add": {"message": "Price Drop Alert."}, "favorite_btn_notify": {"message": "Track price"}, "favorite_cate_name_all": {"message": "All products"}, "favorite_current_price": {"message": "Current price"}, "favorite_due_date": {"message": "Due date"}, "favorite_enable_notification": {"message": "Please enable email notification"}, "favorite_expired": {"message": "Expired"}, "favorite_go_to_enable": {"message": "Go to enable"}, "favorite_msg_add_success": {"message": "Added to favorites"}, "favorite_msg_del_success": {"message": "Deleted from favorites"}, "favorite_msg_failure": {"message": "Fail! Refresh <PERSON> and try again."}, "favorite_please_add_email": {"message": "Please add Email"}, "favorite_price_drop": {"message": "Price drop"}, "favorite_price_rise": {"message": "Price rise"}, "favorite_price_untracked": {"message": "Price untracked"}, "favorite_saved_price": {"message": "Saved price"}, "favorite_stop_tracking": {"message": "Stop tracking"}, "favorite_sub_email_address": {"message": "Subscription email address"}, "favorite_tracking_period": {"message": "Tracking period"}, "favorite_tracking_prices": {"message": "Tracking prices"}, "favorite_verify_email": {"message": "Verify email address"}, "favorites_list_remove_prompt_msg": {"message": "Are you sure to delete it?"}, "favorites_update_button": {"message": "Update prices now"}, "fen_lei": {"message": "Category"}, "fen_xia_yan_xuan": {"message": "Distributor's Choice"}, "find_similar": {"message": "Find Similar"}, "first_ali_price_date": {"message": "The date when first captured by the AliPrice crawler"}, "fooview_coupons_modal_no_data": {"message": "No coupons"}, "fooview_coupons_modal_title": {"message": "Coupons"}, "fooview_favorites__product_sub__tooltip_l1": {"message": "Price < $lowerPrice$", "placeholders": {"lowerPrice": {"content": "$1"}}}, "fooview_favorites__product_sub__tooltip_l2": {"message": "or price > $higherPrice$", "placeholders": {"higherPrice": {"content": "$1"}}}, "fooview_favorites__product_sub__tooltip_l3": {"message": "Deadline"}, "fooview_favorites_error_msg_no_favorites": {"message": "Add favorite products here to receive price drop alert."}, "fooview_favorites_filter_latest": {"message": "Latest"}, "fooview_favorites_filter_price_drop": {"message": "Price Drop"}, "fooview_favorites_filter_price_up": {"message": "Price Rise"}, "fooview_favorites_modal_title": {"message": "My Favorites"}, "fooview_favorites_modal_title_title": {"message": "Go to <PERSON><PERSON><PERSON> Favorite"}, "fooview_favorites_track_price": {"message": "To track the price"}, "fooview_price_history_app_price": {"message": "APP Price :"}, "fooview_price_history_title": {"message": "Price History"}, "fooview_product_list_feedback": {"message": "<PERSON><PERSON><PERSON>"}, "fooview_product_list_orders": {"message": "Orders"}, "fooview_product_list_price": {"message": "Price"}, "fooview_reviews_error_msg_no_review": {"message": "We didn't find any reviews for this product."}, "fooview_reviews_filter_buyer_reviews": {"message": "Buyers' photos"}, "fooview_reviews_modal_title": {"message": "Reviews"}, "fooview_same_product_choose_category": {"message": "Choose category"}, "fooview_same_product_filter_feedback": {"message": "<PERSON><PERSON><PERSON>"}, "fooview_same_product_filter_orders": {"message": "Orders"}, "fooview_same_product_filter_price": {"message": "Price"}, "fooview_same_product_filter_rating": {"message": "Rating"}, "fooview_same_product_modal_title": {"message": "Find the same product"}, "fooview_same_product_search_by_image": {"message": "Search by image"}, "fooview_seller_analysis_modal_title": {"message": "<PERSON><PERSON> Analysis"}, "for_12_months": {"message": "For 1 year"}, "for_12_months_list_pro": {"message": "12 months"}, "for_12_months_nei": {"message": "Within 12 months"}, "for_1_months": {"message": "1 month"}, "for_1_months_nei": {"message": "Within 1 month"}, "for_3_months": {"message": "For 3 months"}, "for_3_months_nei": {"message": "Within 3 months"}, "for_6_months": {"message": "For 6 months"}, "for_6_months_nei": {"message": "Within 6 months"}, "for_9_months": {"message": "9 months"}, "for_9_months_nei": {"message": "Within 9 months"}, "fu_gou_lv": {"message": "Repurchase rate"}, "gao_liang_bu_tong_dian": {"message": "Highlight the differences"}, "gao_liang_guang_gao_chan_pin": {"message": "Highlight Ad Products"}, "geng_duo_xin_xi": {"message": "More info"}, "geng_xin_shi_jian": {"message": "Updated"}, "get_store_products_fail_tip": {"message": "Click OK to go to verification to ensure normal access"}, "gong_x_kuan_shang_pin": {"message": "Total $amount$ items", "placeholders": {"amount": {"content": "$1"}}}, "gong_ying_shang": {"message": "Supplier"}, "gong_ying_shang_ID": {"message": "Supplier ID"}, "gong_ying_shang_deng_ji": {"message": "Supplier Rating"}, "gong_ying_shang_nian_zhan": {"message": "Supplier is older"}, "gong_ying_shang_xin_xi": {"message": "Supplier Information"}, "gong_ying_shang_zhu_ye_lian_jie": {"message": "Supplier Homepage Link"}, "green_rocket": {"message": "로켓프레시"}, "grey_rocket": {"message": "로켓설치"}, "gu_ji_shou_jia": {"message": "Estimated Selling Price"}, "guan_jian_zi": {"message": "Keyword"}, "guang_gao_chan_pin": {"message": "Ad. Products"}, "guang_gao_zhan_bi": {"message": "<PERSON><PERSON>"}, "guo_ji_wu_liu_yun_fei": {"message": "International Shipping Fee"}, "guo_lv_tiao_jian": {"message": "Filters"}, "hao_ping_lv": {"message": "Positive rating"}, "highest_price": {"message": "High"}, "historical_trend": {"message": "Historical trend"}, "how_to_screenshot": {"message": "Hold down the left mouse button to select the area, tap the right mouse button or Esc key to exit the screenshot"}, "howt_it_works": {"message": "How it works"}, "hui_fu_lv": {"message": "Response Rate"}, "hui_tou_lv": {"message": "Repurchase Rate"}, "inquire_freightFee": {"message": "Freight Inquiry"}, "inquire_freightFee_Yuan": {"message": "Freight/Yuan"}, "inquire_freightFee_province": {"message": "Province"}, "inquire_freightFee_the": {"message": "The freight is $num$ , which means that the region has free shipping.", "placeholders": {"num": {"content": "$1"}}}, "is_ad": {"message": "Ad."}, "jia_ge": {"message": "Price"}, "jia_ge_dan_wei": {"message": "Unit"}, "jia_ge_qu_shi": {"message": "Trends"}, "jia_zai_n_ge_shang_pin": {"message": "Load $num$ Products", "placeholders": {"num": {"content": "$1"}}}, "jin_30_tian_xiao_liang_zhan_bi": {"message": "Percentage of sales volume in the last 30 days"}, "jin_30_tian_xiao_shou_e_zhan_bi": {"message": "Percentage of revenue in the last 30 days"}, "jin_30d_xiao_liang": {"message": "Sales"}, "jin_30d_xiao_liang__desc": {"message": "Total sales in the last 30 days"}, "jin_30d_xiao_shou_e": {"message": "Turnover"}, "jin_30d_xiao_shou_e__desc": {"message": "Total turnover in the last 30 days"}, "jin_90_tian_mai_jia_shu": {"message": "Buyers in Last 90 Days"}, "jin_90_tian_xiao_shou_liang": {"message": "Sales in Last 90 Days"}, "jing_xuan_huo_yuan": {"message": "Selected sources"}, "jing_ying_mo_shi": {"message": "Business model"}, "jing_ying_mo_shi__gong_chang": {"message": "Manufacturer"}, "jiu_fen_jie_jue": {"message": "Dispute resolution"}, "jiu_fen_jie_jue__desc": {"message": "Accounting of sellers’ store rights disputes"}, "jiu_fen_lv": {"message": "Dispute rate"}, "jiu_fen_lv__desc": {"message": "Proportion of orders with complaints completed in the past 30 days and judged to be the responsibility of the seller or both parties"}, "kai_dian_ri_qi": {"message": "Open date"}, "keywords": {"message": "Keywords"}, "kua_jin_Select_pan_huo": {"message": "Cross-border Select"}, "last15_days": {"message": "Last 15 days"}, "last180_days": {"message": "Last 180 days"}, "last30_days": {"message": "In the last 30 days"}, "last360_days": {"message": "Last 360 days"}, "last45_days": {"message": "Last 45 days"}, "last60_days": {"message": "Last 60 days"}, "last7_days": {"message": "Last 7 days"}, "last90_days": {"message": "Last 90 days"}, "last_30d_sales": {"message": "Last 30 days sales"}, "lei_ji": {"message": "Cumulative"}, "lei_ji_xiao_liang": {"message": "Total"}, "lei_ji_xiao_liang__desc": {"message": "All sales after product on shelf"}, "lei_ji_xiao_liang_pai_xu__desc": {"message": "Cumulative sales volume in the last 30 days, sorted from high to low"}, "lian_xi_fang_shi": {"message": "Contact Information"}, "list_time": {"message": "On shelf date"}, "load_more": {"message": "Load More"}, "login_to_aliprice": {"message": "Log in to AliPrice"}, "long_link": {"message": "Long Link"}, "lowest_price": {"message": "Low"}, "mai_jia_shu": {"message": "Sellers\n"}, "mao_li_lv": {"message": "Gross margin"}, "mobile_view__dkxbqy": {"message": "Open a New Tab"}, "mobile_view__sjdxq": {"message": "Details in App"}, "mobile_view__sjdxqy": {"message": "Detail Page in App"}, "mobile_view__smck": {"message": "<PERSON>an to <PERSON>"}, "mobile_view__smckms": {"message": "Please use the camera or app to scan and view"}, "modified_failed": {"message": "Modification failed"}, "modified_successfully": {"message": "Modified successfully"}, "nav_btn_favorites": {"message": "My Collections"}, "nav_btn_package": {"message": "Package"}, "nav_btn_product_info": {"message": "About the product"}, "nav_btn_viewed": {"message": "Viewed"}, "no_suggest_search_kw": {"message": "Loading..."}, "none": {"message": "None"}, "normal_link": {"message": "Normal Link"}, "notice": {"message": "Note"}, "number_reviews": {"message": "Reviews"}, "only_show_num": {"message": "Total products: $allnum$, Hidden: $hidenum2$", "placeholders": {"allnum": {"content": "$1"}, "hidenum2": {"content": "$2"}}}, "only_show_selected": {"message": "Remove Unchecked"}, "open": {"message": "Open"}, "open_links": {"message": "Open links"}, "options_page_tab_check_links": {"message": "Check links"}, "options_page_tab_gernal": {"message": "General"}, "options_page_tab_notifications": {"message": "Notifications"}, "options_page_tab_others": {"message": "Others"}, "options_page_tab_sbi": {"message": "Search by image"}, "options_page_tab_shortcuts": {"message": "Shortcuts"}, "options_page_tab_shortcuts_title": {"message": "Font size for shortcuts"}, "options_page_tab_similar_products": {"message": "Same products"}, "orange_rocket": {"message": "판매자로켓"}, "order_list_open_links_tip": {"message": "Multiple product links are about to open"}, "order_list_sku_show_title": {"message": "Show selected variants in the shared links"}, "orders_last30_days": {"message": "Number of orders in the last 30 days"}, "pTutorial_favorites_block1_desc1": {"message": "The products that you tracked are listed here"}, "pTutorial_favorites_block1_title": {"message": "Favorites "}, "pTutorial_popup_block1_desc1": {"message": "A green label means there are price dropped products"}, "pTutorial_popup_block1_title": {"message": "Shortcuts and Favorites"}, "pTutorial_price_history_block1_desc1": {"message": "Click \"Track Price\",  add products to Favorites. Once their prices drop, you will receive notifications\n"}, "pTutorial_price_history_block1_title": {"message": "Track price"}, "pTutorial_reviews_block1_desc1": {"message": "Buyers' reviews from Itao and real photos from the AliExpress feedback"}, "pTutorial_reviews_block1_title": {"message": "Reviews"}, "pTutorial_reviews_block2_desc1": {"message": "It's always helpful to check reviews from other buyers"}, "pTutorial_same_products_block1_desc1": {"message": "You can compare them to make the best choice"}, "pTutorial_same_products_block1_desc2": {"message": "Click 'More' to \"Search by image\""}, "pTutorial_same_products_block1_title": {"message": "Same products"}, "pTutorial_same_products_by_image_search_block1_desc1": {"message": "Drop product image there and choose a category"}, "pTutorial_same_products_by_image_search_block1_title": {"message": "Search by image"}, "pTutorial_seller_analysis_block1_desc1": {"message": "<PERSON><PERSON>'s positive feedback rate, feedback scores and how long the seller has been on the market"}, "pTutorial_seller_analysis_block1_title": {"message": "Seller rating"}, "pTutorial_seller_analysis_block2_desc2": {"message": "Seller rating is based on 3 indexes: item as Described, Communication Shipping Speed"}, "pTutorial_seller_analysis_block3_desc3": {"message": "We use 3 colors and icons to indicate sellers' trust levels"}, "page_count": {"message": "Number of pages"}, "pai_chu": {"message": "Excluded"}, "pai_chu_HK_bu_yi_xia_xiaoshou": {"message": "Exclude Hong Kong-Restricted"}, "pai_chu_JP_bu_yi_xia_xiaoshou": {"message": "Exclude Japan-Restricted"}, "pai_chu_KR_bu_yi_xia_xiaoshou": {"message": "Exclude Korea-Restricted"}, "pai_chu_KZ_bu_yi_xia_xiaoshou": {"message": "Exclude Kazakhstan-Restricted"}, "pai_chu_MO_bu_yi_xia_xiaoshou": {"message": "Exclude Macau-Restricted"}, "pai_chu_RU_bu_yi_xia_xiaoshou": {"message": "Exclude Eastern Europe-Restricted"}, "pai_chu_SA_bu_yi_xia_xiaoshou": {"message": "Exclude Saudi Arabia-Restricted"}, "pai_chu_TW_bu_yi_xia_xiaoshou": {"message": "Exclude Taiwan-Restricted"}, "pai_chu_USA_bu_yi_xia_xiaoshou": {"message": "Exclude U.S.-Restricted"}, "pai_chu_VN_bu_yi_xia_xiaoshou": {"message": "Exclude Vietnam-Restricted"}, "pai_chu_bu_yi_xia_xiaoshou": {"message": "Exclude Restricted Items"}, "payable_price_formula": {"message": "Price + Shipping + Discount"}, "pdd_check_retail_btn_txt": {"message": "Check retail"}, "pdd_pifa_to_retail_btn_txt": {"message": "Buy at retail"}, "pdp_copy_fail": {"message": "Copy failed!"}, "pdp_copy_success": {"message": "Copy successed!"}, "pdp_share_modal_subtitle": {"message": "Share screenshot, he/she will see your chioce."}, "pdp_share_modal_title": {"message": "Share your choice"}, "pdp_share_screenshot": {"message": "Share screenshot"}, "pei_song": {"message": "Fulfil.\n"}, "pin_lei": {"message": "Category"}, "pin_zhi_ti_yan": {"message": "Product quality"}, "pin_zhi_ti_yan__desc": {"message": "Quality refund rate of seller’s store"}, "pin_zhi_tui_kuan_lv": {"message": "Refund rate"}, "pin_zhi_tui_kuan_lv__desc": {"message": "Proportion of orders that have only been refunded and returned in the past 30 days"}, "ping_fen": {"message": "Rating"}, "ping_jun_fa_huo_su_du": {"message": "Average Shipping Speed"}, "pkgInfo_hide": {"message": "Logistics info: on/off"}, "pkgInfo_no_trace": {"message": "No logistics info"}, "platform_name__1688": {"message": "1688"}, "platform_name__17zwd": {"message": "17zwd.com"}, "platform_name__51taoyang": {"message": "51taoyang.com"}, "platform_name__5ts": {"message": "5ts.com"}, "platform_name__91jf": {"message": "91jf.com"}, "platform_name__alibaba": {"message": "Alibaba.com"}, "platform_name__aliexpress": {"message": "AliExpress"}, "platform_name__amazon": {"message": "Amazon"}, "platform_name__bao66": {"message": "bao66.cn"}, "platform_name__chinagoods": {"message": "chinagoods.com"}, "platform_name__dhgate": {"message": "DHgate"}, "platform_name__e3hui": {"message": "e3hui.com"}, "platform_name__ebay": {"message": "Ebay"}, "platform_name__hznzcn": {"message": "hznzcn.com"}, "platform_name__jd": {"message": "JD"}, "platform_name__juyi5": {"message": "juyi5.cn"}, "platform_name__naver_shopping": {"message": "Naver Shopping"}, "platform_name__pinduoduo": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "platform_name__pinduoduo_pifa": {"message": "Pinduoduo wholesale"}, "platform_name__shopee": {"message": "<PERSON>ee"}, "platform_name__sjxzw": {"message": "571xz.com"}, "platform_name__sooxie": {"message": "sooxie.com"}, "platform_name__taobao": {"message": "Taobao"}, "platform_name__taobao_lite": {"message": "Taobao Lite"}, "platform_name__vvic": {"message": "vvic.com"}, "platform_name__walmart": {"message": "Walmart"}, "platform_name__wsy": {"message": "wsy.com"}, "platform_name__xingfujie": {"message": "xingfujie.cn"}, "platform_name__yiwugo": {"message": "Yiwugo.com"}, "platform_name__yunchepin": {"message": "yunchepin.cn"}, "platform_name__zhaojiafang": {"message": "zhaojiafang.com"}, "popup_go_to_home": {"message": "Home"}, "popup_go_to_platform": {"message": "Go to $name$", "placeholders": {"name": {"content": "$1"}}}, "popup_search_placeholder": {"message": "I'm shopping for..."}, "popup_track_package_btn_track": {"message": "TRACK"}, "popup_track_package_desc": {"message": "ALL-IN-ONE PACKAGE TRACKING"}, "popup_track_package_search_placeholder": {"message": "Tracking number"}, "popup_translate_search_placeholder": {"message": "Translate and Search on $searchOn$", "placeholders": {"searchOn": {"content": "$1"}}}, "price_history": {"message": "Price History"}, "price_history_chart_tip_ae": {"message": "Tip: The number of orders is the cumulative number of orders since launch"}, "price_history_chart_tip_coupang": {"message": "Tip: <PERSON><PERSON><PERSON> will delete the order count of fraudulent orders"}, "price_history_inm_1688_l1": {"message": "Please install"}, "price_history_inm_1688_l2": {"message": "AliPrice Shopping Assistant for 1688"}, "price_history_panel_lowest_price": {"message": "Lowest Price: "}, "price_history_panel_tab_price_tracking": {"message": "Price History"}, "price_history_panel_tab_seller_analysis": {"message": "<PERSON><PERSON> Analysis"}, "price_history_pro_modal_title": {"message": "Price history & Order history"}, "privacy_consent__btn_agree": {"message": "Revisit data collection consent"}, "privacy_consent__btn_disable_all": {"message": "Not accept"}, "privacy_consent__btn_enable_all": {"message": "Enable All"}, "privacy_consent__btn_uninstall": {"message": "Remove"}, "privacy_consent__desc_privacy": {"message": "Note that, without data or cookies some functions will be off because those functions need the explanation of data or cookies, but you can still use the other functions."}, "privacy_consent__desc_privacy_L1": {"message": "Unfortunately, without data or cookies it won't work because we need the explanation of data or cookies."}, "privacy_consent__desc_privacy_L2": {"message": "If you don't allow us to collect these informations ,please remove it."}, "privacy_consent__item_cookies_desc": {"message": "<PERSON><PERSON>, we only get your currency data in cookies when shopping online to show the price history."}, "privacy_consent__item_cookies_title": {"message": "Required <PERSON><PERSON>"}, "privacy_consent__item_functional_desc_L1": {"message": "1. Add cookies in browser to anonymously identify your computer or device."}, "privacy_consent__item_functional_desc_L2": {"message": "2. Add functional data in add-on to work with function."}, "privacy_consent__item_functional_title": {"message": "Functional and Analytics Cookies"}, "privacy_consent__more_desc": {"message": "Please know that we don't share your personal data with other companies and no ad companies collect data through our service."}, "privacy_consent__options__btn__desc": {"message": "To use all features, You need to turn it on."}, "privacy_consent__options__btn__label": {"message": "Turn it on"}, "privacy_consent__options__desc_L1": {"message": "We will collect the following data that personally identifies you:"}, "privacy_consent__options__desc_L2": {"message": "- cookies, we only get your currency data in cookies when you're shopping online to show the price history."}, "privacy_consent__options__desc_L3": {"message": "- and add cookies in browser to anonymously identify your computer or device."}, "privacy_consent__options__desc_L4": {"message": "- other anonymous data makes this extension more convenient."}, "privacy_consent__options__desc_L5": {"message": "Please note that we don't share your personal data with other companies and no ad companies collect data through our service."}, "privacy_consent__privacy_preferences": {"message": "Privacy preferences"}, "privacy_consent__read_more": {"message": "Read more>>"}, "privacy_consent__title_privacy": {"message": "Privacy"}, "product_info": {"message": "Product Info"}, "product_recommend__name": {"message": "Similar products"}, "product_research": {"message": "Product Research"}, "product_sub__email_desc": {"message": "Email for Price Alert"}, "product_sub__email_edit": {"message": "Edit"}, "product_sub__email_not_verified": {"message": "Please verify the email"}, "product_sub__email_required": {"message": "Please provide an email"}, "product_sub__form_countdown": {"message": "Automatic close after $seconds$ seconds", "placeholders": {"seconds": {"content": "$1"}}}, "product_sub__form_fail": {"message": "Add reminder fail!"}, "product_sub__form_input_price": {"message": "input price"}, "product_sub__form_item_country": {"message": "Country"}, "product_sub__form_item_current_price": {"message": "Current price"}, "product_sub__form_item_duration": {"message": "Track for"}, "product_sub__form_item_higher_price": {"message": "or price >"}, "product_sub__form_item_invalid_higher_price": {"message": "Price must be greater than $price$", "placeholders": {"price": {"content": "$1"}}}, "product_sub__form_item_invalid_lower_price": {"message": "Price must be lower than $price$", "placeholders": {"price": {"content": "$1"}}}, "product_sub__form_item_lower_price": {"message": "When the price <"}, "product_sub__form_submit": {"message": "Submit"}, "product_sub__form_success": {"message": "Add reminder success!"}, "product_sub__high_price_notify": {"message": "Notify me of price increases"}, "product_sub__low_price_notify": {"message": "Notify me of price reductions"}, "product_sub__modal_title": {"message": "Subscribe price change alert"}, "province__an_hui": {"message": "<PERSON><PERSON>"}, "province__ao_men": {"message": "Macau"}, "province__bei_jing": {"message": "Beijing"}, "province__chong_qing": {"message": "Chongqing"}, "province__fu_jian": {"message": "Fujian"}, "province__gan_su": {"message": "Gansu"}, "province__guang_dong": {"message": "Guangdong"}, "province__guang_xi": {"message": "Guangxi"}, "province__gui_zhou": {"message": "Guizhou"}, "province__hai_nan": {"message": "Hainan"}, "province__he_bei": {"message": "Hebei"}, "province__he_nan": {"message": "<PERSON><PERSON>"}, "province__hei_long_jiang": {"message": "Heilongjiang"}, "province__hu_bei": {"message": "Hubei"}, "province__hu_nan": {"message": "Hunan"}, "province__ji_lin": {"message": "<PERSON><PERSON>"}, "province__jiang_su": {"message": "Jiangsu"}, "province__jiang_xi": {"message": "Jiangxi"}, "province__liao_ning": {"message": "Liaoning"}, "province__nei_meng_gu": {"message": "Inner Mongolia"}, "province__ning_xia": {"message": "Ningxia"}, "province__qing_hai": {"message": "Qinghai"}, "province__shan_dong": {"message": "Shandong"}, "province__shan_xi": {"message": "Shaanxi"}, "province__shang_hai": {"message": "Shanghai"}, "province__si_chuan": {"message": "Sichuan"}, "province__tai_wan": {"message": "Taiwan"}, "province__tian_jin": {"message": "Tianjin"}, "province__xi_zhang": {"message": "Tibet"}, "province__xiang_gang": {"message": "Hong Kong"}, "province__xin_jiang": {"message": "Xinjiang"}, "province__yun_nan": {"message": "Yunnan"}, "province__zhe_jiang": {"message": "Zhejiang"}, "purple_rocket": {"message": "로켓직구"}, "qi_ding_liang": {"message": "MOQ"}, "qi_ding_liang_qi_ding_jia": {"message": "MOQ & MOP"}, "qi_ye_mian_ji": {"message": "Enterprise Area"}, "qing_zhi_shao_xuan_ze_yi_ge_chan_pin": {"message": "Please select at least one product"}, "qing_zhi_shao_xuan_ze_yi_ge_zi_duan": {"message": "Please select at least one field"}, "qu_deng_lu": {"message": "Log in"}, "quan_guo_yan_xuan": {"message": "Global Choice"}, "recommendation_popup_banner_btn_install": {"message": "Install it"}, "recommendation_popup_banner_desc": {"message": "Display price history within 3/6 months, and Price drop notification"}, "region__all": {"message": "All regions"}, "region__hai_wai": {"message": "Overseas"}, "region__hua_bei_qu": {"message": "North China"}, "region__hua_dong_qu": {"message": "East China"}, "region__hua_nan_qu": {"message": "South China"}, "region__hua_zhong_qu": {"message": "Central China"}, "region__jiang_zhe_lu": {"message": "Jiangsu, Zhejiang and Shanghai"}, "remove_web_limits": {"message": "Enable right click"}, "ren_zheng_gong_chang": {"message": "Certified Factory"}, "ren_zheng_gong_ying_shang_nian_zhan": {"message": "Years as Certified Supplier"}, "required_to_aliprice_login": {"message": "Need to log in to AliPrice"}, "revenue_last30_days": {"message": "Sales amount in the last 30 days"}, "review_counts": {"message": "Number of collectors"}, "rocket": {"message": "로켓"}, "ru_zhu_nian_xian": {"message": "Entry Period"}, "sales_amount_last30_days": {"message": "Total sales in the last 30 days"}, "sales_last30_days": {"message": "Sales in the last 30 days"}, "sbi_alibaba_cate__accessories": {"message": "Accessories"}, "sbi_alibaba_cate__aqfk": {"message": "Security"}, "sbi_alibaba_cate__bags_cases": {"message": "Bags & Cases"}, "sbi_alibaba_cate__beauty": {"message": "Beauty"}, "sbi_alibaba_cate__beverage": {"message": "Beverage"}, "sbi_alibaba_cate__bgwh": {"message": "Office Culture"}, "sbi_alibaba_cate__bz": {"message": "Package"}, "sbi_alibaba_cate__ccyj": {"message": "Kitchenware"}, "sbi_alibaba_cate__clothes": {"message": "<PERSON><PERSON><PERSON>"}, "sbi_alibaba_cate__cmgd": {"message": "Media Broadcasting"}, "sbi_alibaba_cate__coat_jacket": {"message": "Coat & Jacket"}, "sbi_alibaba_cate__consumer_electronics": {"message": "Consumer Electronics"}, "sbi_alibaba_cate__cryp": {"message": "Adult Products"}, "sbi_alibaba_cate__csyp": {"message": "Bedding"}, "sbi_alibaba_cate__cwyy": {"message": "Pet & Garden"}, "sbi_alibaba_cate__cysx": {"message": "Catering Fresh"}, "sbi_alibaba_cate__dgdq": {"message": "Electrician"}, "sbi_alibaba_cate__dl": {"message": "Agent"}, "sbi_alibaba_cate__dress_suits": {"message": "Dress & Suits"}, "sbi_alibaba_cate__dszm": {"message": "Lighting"}, "sbi_alibaba_cate__dzqj": {"message": "Electronic Device"}, "sbi_alibaba_cate__essb": {"message": "Second-hand Equipment"}, "sbi_alibaba_cate__food": {"message": "Food"}, "sbi_alibaba_cate__fspj": {"message": "Clothing & Accessories"}, "sbi_alibaba_cate__furniture": {"message": "Furniture"}, "sbi_alibaba_cate__fzpg": {"message": "Textile Leather"}, "sbi_alibaba_cate__ghjq": {"message": "Personal Care & Household Cleaning"}, "sbi_alibaba_cate__gt": {"message": "Steel"}, "sbi_alibaba_cate__gyp": {"message": "Crafts"}, "sbi_alibaba_cate__hb": {"message": "Environmental Protection"}, "sbi_alibaba_cate__hfcz": {"message": "Skin Care & Makeup"}, "sbi_alibaba_cate__hg": {"message": "Chemical Industry"}, "sbi_alibaba_cate__jg": {"message": "Processing"}, "sbi_alibaba_cate__jianccai": {"message": "Building Materials"}, "sbi_alibaba_cate__jichuang": {"message": "Machine Tool"}, "sbi_alibaba_cate__jjry": {"message": "Household Daily Use"}, "sbi_alibaba_cate__jtys": {"message": "Transportation"}, "sbi_alibaba_cate__jxsb": {"message": "Equipment"}, "sbi_alibaba_cate__jxwj": {"message": "Mechanical hardware"}, "sbi_alibaba_cate__jydq": {"message": "Household Appliances"}, "sbi_alibaba_cate__jzjc": {"message": "Home Improvement Materials"}, "sbi_alibaba_cate__jzjf": {"message": "Home Textiles"}, "sbi_alibaba_cate__mj": {"message": "<PERSON><PERSON><PERSON>"}, "sbi_alibaba_cate__myyp": {"message": "Mom & Baby"}, "sbi_alibaba_cate__nanz": {"message": "Men's clothing"}, "sbi_alibaba_cate__nvz": {"message": "Women's clothing"}, "sbi_alibaba_cate__ny": {"message": "Energy"}, "sbi_alibaba_cate__others": {"message": "Others"}, "sbi_alibaba_cate__qcyp": {"message": "Auto Accessories"}, "sbi_alibaba_cate__qmpj": {"message": "Auto Parts"}, "sbi_alibaba_cate__shoes": {"message": "Shoes"}, "sbi_alibaba_cate__smdn": {"message": "Digital Computer"}, "sbi_alibaba_cate__snqj": {"message": "Storage & Cleaning"}, "sbi_alibaba_cate__spjs": {"message": "Food & Drink"}, "sbi_alibaba_cate__swfw": {"message": "Business Services"}, "sbi_alibaba_cate__toys_hobbies": {"message": "Toy"}, "sbi_alibaba_cate__trousers_skirt": {"message": "Trousers & Skirt"}, "sbi_alibaba_cate__txcp": {"message": "Communication Products"}, "sbi_alibaba_cate__tz": {"message": "Children's clothing"}, "sbi_alibaba_cate__underwear": {"message": "Underwear"}, "sbi_alibaba_cate__wjgj": {"message": "Hardware Tools"}, "sbi_alibaba_cate__xgpi": {"message": "<PERSON><PERSON>gs"}, "sbi_alibaba_cate__xmhz": {"message": "Project Cooperation"}, "sbi_alibaba_cate__xs": {"message": "<PERSON><PERSON><PERSON>"}, "sbi_alibaba_cate__ydfs": {"message": "Sportswear"}, "sbi_alibaba_cate__ydhw": {"message": "Outdoor Sport"}, "sbi_alibaba_cate__yjkc": {"message": "Metallurgical Minerals"}, "sbi_alibaba_cate__yqyb": {"message": "Instrumentation"}, "sbi_alibaba_cate__ys": {"message": "Print"}, "sbi_alibaba_cate__yyby": {"message": "Medical Care"}, "sbi_alibaba_cn_kj_90mjs": {"message": "Number of buyers in the past 90 days"}, "sbi_alibaba_cn_kj_90xsl": {"message": "Sales volume in the past 90 days"}, "sbi_alibaba_cn_kj_gjsj": {"message": "Estimated price"}, "sbi_alibaba_cn_kj_gjwlyf": {"message": "International shipping fee"}, "sbi_alibaba_cn_kj_gjyf": {"message": "Shipping fee"}, "sbi_alibaba_cn_kj_gssj": {"message": "Estimated price"}, "sbi_alibaba_cn_kj_lr": {"message": "Profit"}, "sbi_alibaba_cn_kj_lrgs": {"message": "Profit = estimated price x profit margin"}, "sbi_alibaba_cn_kj_pjfhsd": {"message": "Average delivery speed"}, "sbi_alibaba_cn_kj_qtfy": {"message": "other fee"}, "sbi_alibaba_cn_kj_qtfygs": {"message": "Other cost = estimated price x other cost ratio"}, "sbi_alibaba_cn_kj_spjg": {"message": "Price"}, "sbi_alibaba_cn_kj_spzl": {"message": "Weight"}, "sbi_alibaba_cn_kj_szd": {"message": "Location"}, "sbi_alibaba_cn_kj_unit_ge": {"message": "Pieces"}, "sbi_alibaba_cn_kj_unit_jian": {"message": "Pieces"}, "sbi_alibaba_cn_kj_unit_ke": {"message": "Gram"}, "sbi_alibaba_cn_kj_unit_ren": {"message": "Buyers"}, "sbi_alibaba_cn_kj_unit_tai": {"message": "Pieces"}, "sbi_alibaba_cn_kj_unit_tao": {"message": "Sets"}, "sbi_alibaba_cn_kj_unit_tian": {"message": "Days"}, "sbi_alibaba_cn_kj_zwbj": {"message": "No price"}, "sbi_aliprice_alibaba_cn__jiage": {"message": "Price"}, "sbi_aliprice_alibaba_cn__keshou": {"message": "For sale"}, "sbi_aliprice_alibaba_cn__moren": {"message": "<PERSON><PERSON><PERSON>"}, "sbi_aliprice_alibaba_cn__queding": {"message": "Sure"}, "sbi_aliprice_alibaba_cn__xiaoliang": {"message": "Sales"}, "sbi_aliprice_alibaba_cn_cate__jiaju": {"message": "Furniture"}, "sbi_aliprice_alibaba_cn_cate__linghsi": {"message": "Snacks"}, "sbi_aliprice_alibaba_cn_cate__meizhuang": {"message": "Makeups"}, "sbi_aliprice_alibaba_cn_cate__neiyi": {"message": "Underwear"}, "sbi_aliprice_alibaba_cn_cate__peishi": {"message": "Accessories"}, "sbi_aliprice_alibaba_cn_cate__pinchui": {"message": "Drinks"}, "sbi_aliprice_alibaba_cn_cate__qita": {"message": "Others"}, "sbi_aliprice_alibaba_cn_cate__qunzhuang": {"message": "Dress"}, "sbi_aliprice_alibaba_cn_cate__shangyi": {"message": "Tops"}, "sbi_aliprice_alibaba_cn_cate__shuma": {"message": "Electronics"}, "sbi_aliprice_alibaba_cn_cate__wanju": {"message": "Toys"}, "sbi_aliprice_alibaba_cn_cate__xiangbao": {"message": "Bag&Luggage"}, "sbi_aliprice_alibaba_cn_cate__xiazhuang": {"message": "Bottoms"}, "sbi_aliprice_alibaba_cn_cate__xiezi": {"message": "Shoes"}, "sbi_aliprice_cate__apparel": {"message": "<PERSON><PERSON><PERSON>"}, "sbi_aliprice_cate__automobiles_motorcycles": {"message": "Automobiles & Motorcycles"}, "sbi_aliprice_cate__beauty_health": {"message": "Beauty & Health"}, "sbi_aliprice_cate__cellphones_telecommunications": {"message": "Cellphones & Telecommunications"}, "sbi_aliprice_cate__computer_office": {"message": "Computer & Office"}, "sbi_aliprice_cate__consumer_electronics": {"message": "Consumer Electronics"}, "sbi_aliprice_cate__education_office_supplies": {"message": "Education & Office Supplies"}, "sbi_aliprice_cate__electronic_components_supplies": {"message": "Electronic Components & Supplies"}, "sbi_aliprice_cate__furniture": {"message": "Furniture"}, "sbi_aliprice_cate__hair_extensions_wigs": {"message": "Hair Extensions & Wigs"}, "sbi_aliprice_cate__home_garden": {"message": "Home & Garden"}, "sbi_aliprice_cate__home_improvement": {"message": "Home Improvement"}, "sbi_aliprice_cate__jewelry_accessories": {"message": "Jewelry & Accessories"}, "sbi_aliprice_cate__luggage_bags": {"message": "Luggage & Bags"}, "sbi_aliprice_cate__mother_kids": {"message": "Mother & Kids"}, "sbi_aliprice_cate__novelty_special_use": {"message": "Novelty & Special Use"}, "sbi_aliprice_cate__security_protection": {"message": "Security & Protection"}, "sbi_aliprice_cate__shoes": {"message": "Shoes"}, "sbi_aliprice_cate__sports_entertainment": {"message": "Sports & Entertainment"}, "sbi_aliprice_cate__toys_hobbies": {"message": "Toys & Hobbies"}, "sbi_aliprice_cate__watches": {"message": "Watches"}, "sbi_aliprice_cate__weddings_events": {"message": "Weddings & Events"}, "sbi_btn_capture_txt": {"message": "Capture"}, "sbi_btn_source_now_txt": {"message": "Source now"}, "sbi_button__chat_with_me": {"message": "Chat with me"}, "sbi_button__contact_supplier": {"message": "Contact"}, "sbi_button__hide_on_this_site": {"message": "Don't show on this site"}, "sbi_button__open_settings": {"message": "Configure search by image"}, "sbi_capture_shortcut_tip": {"message": "or press the \"Enter\" key on the keyboard"}, "sbi_capturing_tip": {"message": "Capturing"}, "sbi_composed_rating_45": {"message": "4.5 - 5.0 Stars"}, "sbi_crop_and_search": {"message": "Search"}, "sbi_crop_start": {"message": "Use Screenshot "}, "sbi_err_captcha_action": {"message": "Verify"}, "sbi_err_captcha_for_alibaba_cn": {"message": "Need verification, please upload a picture to verify. (View $video_tutorial$ or try clearing cookies)", "placeholders": {"feedback": {"content": "$1"}, "video_tutorial": {"content": "$1"}}}, "sbi_err_captcha_for_aliprice": {"message": "Unusual traffic, please verify"}, "sbi_err_captcha_for_taobao": {"message": "Taobao requests you to verify, please manually upload a picture and search to verify it. This error is due to \"TaoBao search by image\" new verify policy, we suggest you that complaint verify too frequent on Taobao $feedback$.", "placeholders": {"feedback": {"content": "$1"}}}, "sbi_err_captcha_for_taobao_feedback": {"message": "feedback"}, "sbi_err_captcha_msg": {"message": "$platform$ requires you to upload an image to search or complete security verification to remove search restrictions", "placeholders": {"platform": {"content": "$1"}}}, "sbi_err_checking_if_low_version": {"message": "Check if it's the latest version"}, "sbi_err_cookie_btn_clear": {"message": "Clear Cookies"}, "sbi_err_cookie_for_alibaba_cn": {"message": "Try to clear 1688 cookies?(Need to login again)"}, "sbi_err_desperate_feature_pdd": {"message": "The image search function has been moved to Pinduoduo Search by Image extension."}, "sbi_err_hidden_skill_of_improve_search_rate": {"message": "How to improve the success rate of image search?"}, "sbi_err_img_undersize": {"message": "Image > $imgMinimumSize$", "placeholders": {"imgMinimumSize": {"content": "$1"}}}, "sbi_err_login_required": {"message": "Log in $loginSite$", "placeholders": {"loginSite": {"content": "$1"}}}, "sbi_err_login_required_action": {"message": "Log in"}, "sbi_err_low_version": {"message": "Install the latest version ($latestVersion$)", "placeholders": {"latestVersion": {"content": "$1"}}}, "sbi_err_low_version_action": {"message": "Download"}, "sbi_err_need_help": {"message": "Need Help"}, "sbi_err_network": {"message": "Network error, make sure you can visit the website"}, "sbi_err_not_low_version": {"message": "The latest version has been installed ($latestVersion$)", "placeholders": {"latestVersion": {"content": "$1"}}}, "sbi_err_try_again": {"message": "Try again"}, "sbi_err_try_again_action": {"message": "Try again"}, "sbi_err_visit_and_try": {"message": "Try again, or visit $website$ to retry", "placeholders": {"website": {"content": "$1"}}}, "sbi_err_visit_site": {"message": "Visit $siteName$ home page", "placeholders": {"siteName": {"content": "$1"}}}, "sbi_fail_tip": {"message": "Loading failed, please refresh the page and try again."}, "sbi_kuajing_filter_area": {"message": "Area"}, "sbi_kuajing_filter_au": {"message": "Australia"}, "sbi_kuajing_filter_btn_confirm": {"message": "Confirm"}, "sbi_kuajing_filter_de": {"message": "Germany"}, "sbi_kuajing_filter_destination_country": {"message": "Destination country"}, "sbi_kuajing_filter_es": {"message": "Spain"}, "sbi_kuajing_filter_estimate": {"message": "Estimate"}, "sbi_kuajing_filter_estimate_price": {"message": "Estimated price"}, "sbi_kuajing_filter_estimate_price_formula": {"message": "Estimated price formula = (commodity price + international logistics freight)/(1 - profit margin - other cost ratio)"}, "sbi_kuajing_filter_fr": {"message": "France"}, "sbi_kuajing_filter_kw_placeholder": {"message": "Enter keywords to match the title"}, "sbi_kuajing_filter_logistics": {"message": "Logistics template"}, "sbi_kuajing_filter_logistics_china_post": {"message": "China Post Air Mail"}, "sbi_kuajing_filter_logistics_discount": {"message": "Logistics discount"}, "sbi_kuajing_filter_logistics_epacket": {"message": "epacket"}, "sbi_kuajing_filter_logistics_price_formula": {"message": "International logistics freight = (weight x shipping price + registration fee) x (1 - discount)"}, "sbi_kuajing_filter_others_fee": {"message": "Other fee"}, "sbi_kuajing_filter_profit_percent": {"message": "Profit margin"}, "sbi_kuajing_filter_prop": {"message": "Attributes"}, "sbi_kuajing_filter_ru": {"message": "Russia"}, "sbi_kuajing_filter_total": {"message": "Match$count$ similar items", "placeholders": {"count": {"content": "$1"}}}, "sbi_kuajing_filter_uk": {"message": "U.K"}, "sbi_kuajing_filter_usa": {"message": "America"}, "sbi_login_punish_title__pdd_pifa": {"message": "Pinduoduo wholesale"}, "sbi_msg_no_result": {"message": "No result found,please login to $loginSite$ or try another image", "placeholders": {"loginSite": {"content": "$1"}}}, "sbi_msg_no_result_check_support_page_l1": {"message": "Temporarily not available for Safari, please use $supportPage$.", "placeholders": {"supportPage": {"content": "$1"}}}, "sbi_msg_no_result_check_support_page_l2": {"message": "Chrome browser and its extensions"}, "sbi_msg_no_result_reinstall_l1": {"message": "No results found, please log in to $loginSite$ or try another picture, or reinstall $latestExtUrl$ ", "placeholders": {"loginSite": {"content": "$1"}, "latestExtUrl": {"content": "$2"}}}, "sbi_msg_no_result_reinstall_l2": {"message": "Latest version", "placeholders": {}}, "sbi_search_by_selected_area": {"message": "Selected area"}, "sbi_shipping_": {"message": "Same day shipping"}, "sbi_specify_category": {"message": "Specify category:"}, "sbi_start_crop": {"message": "Select area"}, "sbi_store_name_alibabaCNKuaJing": {"message": "1688 oversea"}, "sbi_tutorial_btn_more": {"message": "More ways"}, "sbi_tutorial_find_coupons_on_taobao": {"message": "Find coupons on Taobao"}, "sbi_txt__empty_retry": {"message": "Sorry, no results found, please try again."}, "sbi_txt__min_order": {"message": "Min. order"}, "sbi_visiting": {"message": "Browsing"}, "sbi_yiwugo__jiagexiangtan": {"message": "Contact seller"}, "sbi_yiwugo__qigou": {"message": "$num$ Pieces (MOQ)", "placeholders": {"num": {"content": "$1"}}}, "sbi_yiwugo__xing": {"message": "Stars"}, "searchByImage_screenshot": {"message": "One-click screenshot"}, "searchByImage_search": {"message": "One-click search for same items"}, "searchByImage_size_type": {"message": "File size cannot be larger than $num$ MB, $type$ only", "placeholders": {"num": {"content": "$1"}, "type": {"content": "$2"}}}, "search_by_image_progress_analysing": {"message": "Analysing image"}, "search_by_image_progress_searching": {"message": "Search for products"}, "search_by_image_progress_sending": {"message": "Sending image"}, "search_by_image_response_rate": {"message": "Response Rate: $responseRate$ of buyers who contacted this supplier received a response within $responseInHour$ hours.", "placeholders": {"responseRate": {"content": "$1"}, "responseInHour": {"content": "$2"}}}, "search_by_keyword": {"message": "Search by keyword:"}, "select_country_language_modal_title_country": {"message": "Country"}, "select_country_language_modal_title_language": {"message": "Language"}, "select_country_region_modal_title": {"message": "Select a country/region"}, "select_language_modal_title": {"message": "Select a language:"}, "select_shop": {"message": "Select store"}, "sellers_count": {"message": "Number of sellers on the current page"}, "sellers_count_per_page": {"message": "Number of sellers on the current page"}, "service_score": {"message": "Comprehensive service rating"}, "set_shortcut_keys": {"message": "Set shortcut keys"}, "setting_logo_title": {"message": "Shopping assistant"}, "setting_modal_options_position_title": {"message": "Plug-in position"}, "setting_modal_options_position_value_left": {"message": "Left corner"}, "setting_modal_options_position_value_right": {"message": "Right corner"}, "setting_modal_options_theme_title": {"message": "Theme color"}, "setting_modal_options_theme_value_dark": {"message": "Dark"}, "setting_modal_options_theme_value_light": {"message": "Light"}, "setting_modal_title": {"message": "Settings"}, "setting_options_country_title": {"message": "Country/Region"}, "setting_options_hover_zoom_desc": {"message": "Mouse over to zoom in"}, "setting_options_hover_zoom_title": {"message": "Hover Zoom"}, "setting_options_jd_coupon_desc": {"message": "Found coupon on JD.com"}, "setting_options_jd_coupon_title": {"message": "JD.com coupon"}, "setting_options_language_title": {"message": "Language"}, "setting_options_price_drop_alert_desc": {"message": "When the price of products in My Favorite drops, you will receive push notification."}, "setting_options_price_drop_alert_title": {"message": "Price drop alert"}, "setting_options_price_history_on_list_page_desc": {"message": "Display price history on product search page"}, "setting_options_price_history_on_list_page_title": {"message": "Price History (List Page)"}, "setting_options_price_history_on_produt_page_desc": {"message": "Display product history on product detail page"}, "setting_options_price_history_on_produt_page_title": {"message": "Price history (details page)"}, "setting_options_sales_analysis_desc": {"message": "Support statistics of price, sales volume, number of sellers and store sales ratio on the $platforms$ product list page", "placeholders": {"platforms": {"content": "$1"}}}, "setting_options_sales_analysis_title": {"message": "Sales analysis"}, "setting_options_save_success_msg": {"message": "Success"}, "setting_options_tacking_price_title": {"message": "Price change alert"}, "setting_options_value_off": {"message": "Off"}, "setting_options_value_on": {"message": "On"}, "setting_pkg_quick_view_desc": {"message": "Support: 1688 & Taobao"}, "setting_saved_message": {"message": "Changes saved successfully"}, "setting_section_enable_platform_title": {"message": "On-off"}, "setting_section_setting_title": {"message": "Settings"}, "setting_section_shortcuts_title": {"message": "Shortcuts"}, "settings_aliprice_agent__desc": {"message": "Displayed on $platforms$ product detail page", "placeholders": {"platforms": {"content": "$1"}}}, "settings_aliprice_agent__title": {"message": "Buy-for-me"}, "settings_copy_link__desc": {"message": "Displayed on product details page"}, "settings_copy_link__title": {"message": "Copy button & Search title"}, "settings_currency_desc__for_detail": {"message": "Support 1688 product detail page"}, "settings_currency_desc__for_list": {"message": "Search by image(include 1688/1688 overseas/Taobao)"}, "settings_currency_desc__for_sbi": {"message": "Select the price"}, "settings_currency_desc_display_for_list": {"message": "Shown in image search (including 1688/1688 overseas/Taobao)"}, "settings_currency_rate_desc": {"message": "Exchange rate updating from \"$currencyRateFrom$\"", "placeholders": {"currencyRateFrom": {"content": "$1"}}}, "settings_currency_rate_from_boc": {"message": "Bank of China"}, "settings_download_images__desc": {"message": "Support for downloading images from $platforms$", "placeholders": {"platforms": {"content": "$1"}}}, "settings_download_images__title": {"message": "Download images button"}, "settings_download_reviews__desc": {"message": "Displayed on $platforms$ product detail page", "placeholders": {"platforms": {"content": "$1"}}}, "settings_download_reviews__title": {"message": "Download review images"}, "settings_google_translate_desc": {"message": "Right click to get google translate bar"}, "settings_google_translate_title": {"message": "web page translation"}, "settings_historical_trend_desc": {"message": "Display in the bottom right corner of the image on the product list page"}, "settings_modal_btn_more": {"message": "More settings"}, "settings_productInfo_desc": {"message": "Display more detailed product information on the product list page. Enabling this may increase the computer's load and cause page lag. If it affects performance, it is recommended to disable it."}, "settings_product_recommend__desc": {"message": "Displayed below the main image on the $platforms$ product details page", "placeholders": {"platforms": {"content": "$1"}}}, "settings_product_recommend__name": {"message": "Products Recommended"}, "settings_research_desc": {"message": "Query more detailed information on the product list page"}, "settings_sbi_add_to_list": {"message": "Add into the $listType$", "placeholders": {"listType": {"content": "$1"}}}, "settings_sbi_detail_page_icon_title_desc": {"message": "Image search result thumbnail"}, "settings_sbi_remove_from_list": {"message": "Remove from the $listType$", "placeholders": {"listType": {"content": "$1"}}}, "settings_search_by_image_add_to_blacklist": {"message": "Add to blocklist"}, "settings_search_by_image_adjust_entrance_entrance": {"message": "Adjust entrance position"}, "settings_search_by_image_blacklist_desc": {"message": "Do not show icon on websites in the blocklist."}, "settings_search_by_image_blacklist_title": {"message": "Blocklist"}, "settings_search_by_image_bottom_left": {"message": "Bottom Left"}, "settings_search_by_image_bottom_right": {"message": "Bottom Right"}, "settings_search_by_image_clear_blacklist": {"message": "Clear blocklist"}, "settings_search_by_image_detail_page_icon_title": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "settings_search_by_image_detail_page_icon_val_large": {"message": "Larger"}, "settings_search_by_image_detail_page_icon_val_small": {"message": "Smaller"}, "settings_search_by_image_display_button_desc": {"message": "One click on the icon to search by image"}, "settings_search_by_image_display_button_title": {"message": "Icon on images"}, "settings_search_by_image_sourece_websites_desc": {"message": "Find the source product on these websites"}, "settings_search_by_image_sourece_websites_title": {"message": "Search by image result"}, "settings_search_by_image_top_left": {"message": "Top Left"}, "settings_search_by_image_top_right": {"message": "Top Right"}, "settings_search_keyword_on_x__desc": {"message": "Search words on $platform$", "placeholders": {"platform": {"content": "$1"}}}, "settings_search_keyword_on_x__title": {"message": "Show $platform$ icon when selected words", "placeholders": {"platform": {"content": "$1"}}}, "settings_similar_products_desc": {"message": "Try to find the same product in those websites(max to 5)"}, "settings_similar_products_title": {"message": "Find the same product"}, "settings_toolbar_expand_title": {"message": "Plug-in minimize"}, "settings_top_toolbar_desc": {"message": "Search Bar on the top of page "}, "settings_top_toolbar_title": {"message": "Search Bar"}, "settings_translate_search_desc": {"message": "Translate to Chinese and Search."}, "settings_translate_search_title": {"message": "Multi-lang Search"}, "settings_translator_contextmenu_title": {"message": "Capture to translate"}, "settings_translator_title": {"message": "Translate"}, "shai_xuan_dao_chu": {"message": "Filter to Export"}, "shai_xuan_zi_duan": {"message": "Filter fields"}, "shang_jia_shi_jian": {"message": "On shelf time"}, "shang_pin_biao_ti": {"message": "Product Title"}, "shang_pin_dui_bi": {"message": "Product Comparison"}, "shang_pin_lian_jie": {"message": "Product Link"}, "shang_pin_xin_xi": {"message": "Product info"}, "share_modal__content": {"message": "Share with your friends"}, "share_modal__disable_for_while": {"message": "I don't want to share anything"}, "share_modal__title": {"message": "Do you like $extensionName$?", "placeholders": {"extensionName": {"content": "$1"}}}, "sheng_yu_ku_cun": {"message": "Remaining"}, "shi_fou_ke_ding_zhi": {"message": "Is it customizable?"}, "shi_fou_ren_zheng_gong_ying_sha": {"message": "Certified Supplier"}, "shi_fou_ren_zheng_gong_ying_shang_zong_shu": {"message": "Certified suppliers"}, "shi_fou_you_mao_yi_dan_bao": {"message": "Trade Assurance"}, "shi_fou_you_mao_yi_dan_bao_zong_shu": {"message": "Trade guarantees"}, "shipping_fee": {"message": "Shipping fee"}, "shop_followers": {"message": "Shop followers"}, "shou_qi": {"message": "Less"}, "similar_products_warn_max_platforms": {"message": "Max to 5"}, "sku_calc_price": {"message": "Calculated Price"}, "sku_calc_price_settings": {"message": "Calculated Price Settings"}, "sku_formula": {"message": "Formula"}, "sku_formula_desc": {"message": "Formula Description"}, "sku_formula_desc_text": {"message": "Supports complex mathematical formulas, with the original price represented by A and the freight represented by B\n<br/>\nSupports brackets (), plus +, minus -, multiplication *, and division /\n<br/>\nExample:\n<br/>\n1. To achieve 1.2 times the original price and then add the freight, the formula is: A*1.2+B\n<br/>\n2. To achieve the original price plus 1 yuan, then multiply by 1.2 times, the formula is: (A+1)*1.2\n<br/>\n3. To achieve the original price plus 10 yuan, then multiply by 1.2 times, and then subtract 3 yuan, the formula is: (A+10)*1.2-3"}, "sku_in_stock": {"message": "In Stock"}, "sku_invalid_formula_format": {"message": "Invalid formula format"}, "sku_inventory": {"message": "Inventory"}, "sku_link_copy_fail": {"message": "Copied successfully, sku specifications and attributes are not selected"}, "sku_link_copy_success": {"message": "Copied successfully, sku specifications and attributes selected"}, "sku_list": {"message": "SKU List"}, "sku_min_qrder_qty": {"message": "Minimum Order Quantity"}, "sku_name": {"message": "SKU Name"}, "sku_no": {"message": "No."}, "sku_original_price": {"message": "Original Price"}, "sku_price": {"message": "SKU Price"}, "stop_track_time_label": {"message": "Tracking deadline:"}, "suo_zai_di_qu": {"message": "Location"}, "tab_pkg_quick_view": {"message": "Logistics Monitor"}, "tab_product_details_price_history": {"message": "Price history"}, "tab_product_details_reviews": {"message": "Photo reviews"}, "tab_product_details_seller_analysis": {"message": "<PERSON><PERSON> Analysis"}, "tab_product_details_similar_products": {"message": "Same products"}, "total_days_listed_per_product": {"message": "Sum of on shelf days ÷ Number of products"}, "total_items": {"message": "Total number of products"}, "total_price_per_product": {"message": "Sum of prices ÷ Number of products"}, "total_rating_per_product": {"message": "Sum of ratings ÷ Number of products"}, "total_revenue": {"message": "Total revenue"}, "total_revenue40_items": {"message": "Total revenue of the $amount$ products on the current page", "placeholders": {"amount": {"content": "$1"}}}, "total_sales": {"message": "Total sales"}, "total_sales40_items": {"message": "Total sales of the $amount$ products on the current page", "placeholders": {"amount": {"content": "$1"}}}, "track_for_12_months": {"message": "Track for: 1 year"}, "track_for_3_months": {"message": "Track for: 3 months"}, "track_for_6_months": {"message": "Track for: 6 months"}, "tracking_price_email_add_btn": {"message": "Add email"}, "tracking_price_email_edit_btn": {"message": "Edit email"}, "tracking_price_email_intro": {"message": "We will notify you via email."}, "tracking_price_email_invalid": {"message": "Please provide a valid email"}, "tracking_price_email_verified_desc": {"message": "You can now receive our price drop alert."}, "tracking_price_email_verified_title": {"message": "Successfully verified"}, "tracking_price_email_verify_desc_line1": {"message": "We have sent a verification link to your email address,"}, "tracking_price_email_verify_desc_line2": {"message": "please check your email inbox."}, "tracking_price_email_verify_title": {"message": "Verify email"}, "tracking_price_web_push_notification_intro": {"message": "On Desktop: AliPrice can monitor any product for you and send you a Web Push Notification once the price changes."}, "tracking_price_web_push_notification_title": {"message": "Web Push Notifications"}, "translate_im__login_required": {"message": "Translated by AliPrice, please log in to $loginUrl$", "placeholders": {"loginUrl": {"content": "$1"}}}, "translate_im__paste_fail": {"message": "Translated and copied to the clipboard, but due to the limitation of Aliwangwang, you need to paste it manually!"}, "translate_im__send": {"message": "Translate & Send"}, "translate_search": {"message": "Translate and Search"}, "translation_originals_translated": {"message": "Original and Chinese"}, "translation_translated": {"message": "Chinese"}, "translator_btn_capture_txt": {"message": "Translate"}, "translator_language_auto_detect": {"message": "Automatic detection"}, "translator_language_detected": {"message": "Detected"}, "translator_language_search_placeholder": {"message": "Search language"}, "try_again": {"message": "Try again"}, "tu_pian_chi_cun": {"message": "Image size:"}, "tu_pian_lian_jie": {"message": "Image Link"}, "tui_huan_ti_yan": {"message": "Return experience"}, "tui_huan_ti_yan__desc": {"message": "Assess sellers’ after-sales indicators"}, "tutorial__show_all": {"message": "All features"}, "tutorial_ae_popup_title": {"message": "Pin the extension, open Aliexpress"}, "tutorial_aliexpress_reviews_analysis": {"message": "AliExpress reviews analysis"}, "tutorial_aliprice_agent_with1688_desc_l1": {"message": "Support USD KRW JPY HKD TWD"}, "tutorial_aliprice_agent_with1688_desc_l2": {"message": "Shipping to Korea/Japan/Mainland China"}, "tutorial_aliprice_agent_with1688_title": {"message": "1688 Supports Overseas Purchase"}, "tutorial_auto_apply_coupon_title": {"message": "Auto apply coupon"}, "tutorial_btn_end": {"message": "End"}, "tutorial_btn_example": {"message": "Example"}, "tutorial_btn_have_a_try": {"message": "Ok, have a try"}, "tutorial_btn_next": {"message": "Next"}, "tutorial_btn_see_more": {"message": "More"}, "tutorial_compare_products": {"message": "Compare Products"}, "tutorial_currency_convert_title": {"message": "Currency Conversion"}, "tutorial_export_shopping_cart": {"message": "Export as CSV, Support Taobao and 1688"}, "tutorial_export_shopping_cart_title": {"message": "Export cart"}, "tutorial_price_history_pro": {"message": "Displayed on product detail page.\nSupport Shopee, Lazada, Amazon, Ebay"}, "tutorial_price_history_pro_title": {"message": "Whole year Price history & Order history"}, "tutorial_sbi_context_menu_screenshot_title": {"message": "Capture to search by image"}, "tutorial_translate_search": {"message": "Translate to search"}, "tutorial_translate_search_and_package_tracking": {"message": "Translate search & Package tracking"}, "unit_bao": {"message": "pcs"}, "unit_ben": {"message": "pcs"}, "unit_bi": {"message": "orders"}, "unit_chuang": {"message": "pcs"}, "unit_dai": {"message": "pcs"}, "unit_dui": {"message": "prs"}, "unit_fen": {"message": "pcs"}, "unit_ge": {"message": "pcs"}, "unit_he": {"message": "pcs"}, "unit_jian": {"message": "pcs"}, "unit_li_fang_mi": {"message": "m³"}, "unit_ping": {"message": "pcs"}, "unit_ping_fang_mi": {"message": "㎡"}, "unit_shuang": {"message": "prs"}, "unit_tai": {"message": "pcs"}, "unit_ti": {"message": "pcs"}, "unit_tiao": {"message": "pcs"}, "unit_xiang": {"message": "pcs"}, "unit_zhang": {"message": "pcs"}, "unit_zhi": {"message": "pcs"}, "verify_contact_support": {"message": "Contact Support"}, "verify_human_verification": {"message": "Human Verification"}, "verify_unusual_access": {"message": "Unusual access detected"}, "view_history_clean_all": {"message": "Clean All"}, "view_history_clean_all_warring": {"message": "Clean all viewed records?"}, "view_history_clean_all_warring_title": {"message": "Warning"}, "view_history_viewd": {"message": "Viewed"}, "website": {"message": "website"}, "weight": {"message": "Weight"}, "wu_fa_huo_qu_dao_gai_shu_ju": {"message": "Unable to get the data"}, "wu_liu_shi_xiao": {"message": "On-time shipment"}, "wu_liu_shi_xiao__desc": {"message": "The 48-hour collection rate and fulfillment rate of the seller’s store"}, "xia_dan_jia": {"message": "Final price"}, "xian_xuan_ze_product_attributes": {"message": "Select the product attributes"}, "xiao_liang": {"message": "Sales Volume"}, "xiao_liang_zhan_bi": {"message": "Percentage of sales volume"}, "xiao_shi": {"message": "$num$ Hours", "placeholders": {"num": {"content": "$1"}}}, "xiao_shou_e": {"message": "Revenue"}, "xiao_shou_e_zhan_bi": {"message": "Percentage of revenue"}, "xuan_zhong_x_tiao_ji_lu": {"message": "Select $amount$ records", "placeholders": {"amoun": {"content": "$1"}, "amount": {"content": "$1"}}}, "yan_xuan": {"message": "Choice"}, "yi_ding_zai_zuo_ce": {"message": "Pinned"}, "yi_jia_zai_wan_suo_you_shang_pin": {"message": "All Products Loaded"}, "yi_nian_xiao_liang": {"message": "Annual Sales"}, "yi_nian_xiao_liang_zhan_bi": {"message": "Annual Sales Share"}, "yi_nian_xiao_shou_e": {"message": "Annual Turnover"}, "yi_nian_xiao_shou_e_zhan_bi": {"message": "Annual Turnover Share"}, "yi_shua_xin": {"message": "Refreshed"}, "yin_cang_xiang_tong_dian": {"message": "Hide Similarities"}, "you_xiao_liang": {"message": "With Sales Volume"}, "yu_ji_dao_da_shi_jian": {"message": "Estimated arrival time"}, "yuan_gong_ren_shu": {"message": "Number of employees"}, "yue_cheng_jiao": {"message": "Monthly volume"}, "yue_dai_xiao": {"message": "Dropshipping"}, "yue_dai_xiao__desc": {"message": "Dropshipping sales in the last 30 days"}, "yue_dai_xiao_pai_xu__desc": {"message": "Dropshipping sales in the last 30 days, sorted from high to low"}, "yue_xiao_liang__desc": {"message": "Sales volume in the last 30 days"}, "zhan_kai": {"message": "More"}, "zhe_kou": {"message": "Discount"}, "zhi_chi_yi_jian_dai_fa": {"message": "Dropshipping"}, "zhi_chi_yi_jian_dai_fa_bao_you": {"message": "Free shipping"}, "zhi_fu_ding_dan_shu": {"message": "Paid orders"}, "zhi_fu_ding_dan_shu__desc": {"message": "Number of orders for this product (30 days)"}, "zhu_ce_xing_zhi": {"message": "Nature of registration"}, "zi_ding_yi_tiao_jian": {"message": "Custom Conditions"}, "zi_duan": {"message": "Fields"}, "zi_ti_xiao_liang": {"message": "Variation Sold"}, "zong_he_fu_wu_fen": {"message": "Overall rating"}, "zong_he_fu_wu_fen__desc": {"message": "Overall rating of seller service"}, "zong_he_fu_wu_fen__short": {"message": "Rating"}, "zong_he_ti_yan_fen": {"message": "Rating"}, "zong_he_ti_yan_fen_3": {"message": "Below 4 Stars"}, "zong_he_ti_yan_fen_4": {"message": "4 - 4.5 Stars"}, "zong_he_ti_yan_fen_4_5": {"message": "4.5 - 5.0 Stars"}, "zong_he_ti_yan_fen_5": {"message": "5 Stars"}, "zong_ku_cun": {"message": "Total inventory"}, "zong_xiao_liang": {"message": "Total sales"}, "zui_jin_30D_3Min_xiang_ying_lv": {"message": "3-minute Response rate in the last 30 days"}, "zui_jin_30D_48H_lan_shou_lv": {"message": "48H Taking express rate in the last 30 days"}, "zui_jin_30D_48H_lv_yue_lv": {"message": "48H Fulfillment rate in last 30 days"}, "zui_jin_30D_jiao_yi_ji_lu": {"message": "Trading record(30 days)"}, "zui_jin_30D_jiao_yi_ji_lu__desc": {"message": "Trading record(30 days)"}, "zui_jin_30D_jiu_fen_lv": {"message": "Dispute rate in the last 30 days"}, "zui_jin_30D_pin_zhi_tui_kuan_lv": {"message": "Quality refund rate in the last 30 days"}, "zui_jin_30D_zhi_fu_ding_dan_shu": {"message": "The number of payment orders in the last 30 days"}}