#!/usr/bin/env node

/**
 * 测试提交设置脚本
 *
 * 该脚本用于验证 commitlint、husky 和 commitizen 的配置是否正确：
 * - 检查配置文件是否存在
 * - 验证 commitlint 规则是否正常工作
 * - 测试自定义验证规则
 * - 检查 husky 钩子是否正确设置
 */

import fs from 'fs';
import path from 'path';
import { execSync } from 'child_process';

console.log('🔍 检查提交设置配置...\n');

// 检查配置文件
const configFiles = [
  'commitlint.config.cjs',
  '.czrc',
  'scripts/commitizen-adapter.cjs',
  '.husky/commit-msg',
  '.husky/pre-commit',
];

console.log('📁 检查配置文件:');
for (const file of configFiles) {
  const exists = fs.existsSync(file);
  console.log(`  ${exists ? '✅' : '❌'} ${file}`);
  if (!exists) {
    console.log(`     文件不存在: ${file}`);
  }
}

// 检查 package.json 中的脚本
console.log('\n📦 检查 package.json 脚本:');
try {
  const pkg = JSON.parse(fs.readFileSync('package.json', 'utf8'));
  const requiredScripts = ['commit', 'release'];

  for (const script of requiredScripts) {
    const exists = pkg.scripts && pkg.scripts[script];
    console.log(`  ${exists ? '✅' : '❌'} ${script}: ${exists || '未定义'}`);
  }
} catch (error) {
  console.log('  ❌ 无法读取 package.json');
}

// 检查依赖包
console.log('\n📚 检查必需的依赖包:');
try {
  const pkg = JSON.parse(fs.readFileSync('package.json', 'utf8'));
  const requiredDeps = [
    '@commitlint/cli',
    '@commitlint/config-conventional',
    'commitizen',
    'husky',
  ];

  for (const dep of requiredDeps) {
    const exists =
      (pkg.devDependencies && pkg.devDependencies[dep]) ||
      (pkg.dependencies && pkg.dependencies[dep]);
    console.log(`  ${exists ? '✅' : '❌'} ${dep}: ${exists || '未安装'}`);
  }
} catch (error) {
  console.log('  ❌ 无法检查依赖包');
}

// 测试 commitlint 配置
console.log('\n🧪 测试 commitlint 配置:');
try {
  // 测试有效的提交信息
  const validCommit = `feat: 添加新功能

Issue-ID: 1#20250101-01
Applies-To: test_plugin`;

  fs.writeFileSync('.test-commit-msg', validCommit);

  try {
    execSync('pnpm commitlint --edit .test-commit-msg', { stdio: 'pipe' });
    console.log('  ✅ 有效提交信息通过验证');
  } catch (error) {
    console.log('  ❌ 有效提交信息验证失败');
    console.log('     错误:', error.message);
  }

  // 测试无效的提交信息
  const invalidCommit = `feat: 添加新功能

没有必需的字段`;

  fs.writeFileSync('.test-commit-msg', invalidCommit);

  try {
    execSync('pnpm commitlint --edit .test-commit-msg', { stdio: 'pipe' });
    console.log('  ❌ 无效提交信息应该被拒绝但通过了验证');
  } catch (error) {
    console.log('  ✅ 无效提交信息正确被拒绝');
  }

  // 清理测试文件
  fs.unlinkSync('.test-commit-msg');
} catch (error) {
  console.log('  ❌ commitlint 测试失败:', error.message);
}

// 检查插件目录
console.log('\n📂 检查插件目录:');
const extensionsPath = 'packages/extensions';
if (fs.existsSync(extensionsPath)) {
  try {
    const plugins = fs
      .readdirSync(extensionsPath, { withFileTypes: true })
      .filter((dirent) => dirent.isDirectory())
      .map((dirent) => dirent.name);

    console.log(`  ✅ 找到 ${plugins.length} 个插件:`);
    plugins.forEach((plugin) => console.log(`     - ${plugin}`));
  } catch (error) {
    console.log('  ❌ 无法读取插件目录');
  }
} else {
  console.log('  ❌ 插件目录不存在');
}

console.log('\n🎉 配置检查完成！');
console.log('\n💡 使用说明:');
console.log('  - 交互式提交: pnpm commit');
console.log('  - 预览发布: pnpm release preview <plugin-name>');
console.log('  - 执行发布: pnpm release run');
console.log('  - 预演发布: pnpm release run --dry-run');
