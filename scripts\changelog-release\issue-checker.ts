import fs from 'fs-extra';
import { createLogger } from '../helpers/logger.js';
import type { ParsedCommit } from './types.js';

const logger = createLogger('Issue检查');

interface Issue {
  id: string;
  status: string;
}

/**
 * 从 JSON 文件加载 issue 状态
 *
 * 该函数从指定的 JSON 文件中读取 issue 状态信息：
 * - 检查文件是否存在，如果不存在则记录警告并返回空数组
 * - 使用 fs-extra 的 readJson 方法解析 JSON 文件
 * - 处理文件读取或 JSON 解析错误，记录错误信息并返回空数组
 * - 返回的数组用于后续的 issue 状态检查
 *
 * @param {string} filePath - issue 状态文件的绝对路径或相对路径
 * @returns {Promise<Issue[]>} issue 对象数组，每个对象包含 id 和 status 字段
 *   - id: issue 的唯一标识符
 *   - status: issue 的当前状态（如 "Done", "In Progress" 等）
 *
 * @example
 * // 加载项目根目录的 issue 状态文件
 * const issues = await loadIssueStatuses('./issue_status.json');
 * // 返回: [
 * //   { id: '1#20250101-01', status: 'Done' },
 * //   { id: '2#20250101-02', status: 'In Progress' }
 * // ]
 *
 * @example
 * // 文件不存在的情况
 * const issues = await loadIssueStatuses('./nonexistent.json');
 * // 返回: [] （空数组）
 */
export async function loadIssueStatuses(filePath: string): Promise<Issue[]> {
  try {
    if (await fs.pathExists(filePath)) {
      return await fs.readJson(filePath);
    }
    logger.warn(`Issue 状态文件未找到: ${filePath}。将跳过状态检查。`);
    return [];
  } catch (error) {
    logger.error(`读取或解析 issue 状态文件失败: ${filePath}`, error);
    return [];
  }
}

/**
 * 根据 issue 状态检查提交列表
 *
 * 该函数检查提交中引用的 issue 是否已完成：
 * - 将 issue 状态数组转换为 Map 以提高查找效率
 * - 遍历所有提交，检查每个提交的 issueId 字段
 * - 对于有 issueId 的提交，查找对应的状态信息
 * - 如果 issue 状态不是 "Done"（不区分大小写），生成警告信息
 * - 返回所有未完成 issue 的警告信息数组
 *
 * @param {ParsedCommit[]} commits - 解析后的提交对象数组，包含 issueId 和 subject 字段
 * @param {Issue[]} statuses - issue 状态对象数组，包含 id 和 status 字段
 * @returns {string[]} 包含未完成 issue 警告信息的字符串数组
 *   每个警告包含 issue ID、当前状态和相关提交信息
 *
 * @example
 * // 检查包含未完成 issue 的提交
 * const warnings = checkIssues(
 *   [{ issueId: '1#20250101-01', subject: '添加功能' }],
 *   [{ id: '1#20250101-01', status: 'In Progress' }]
 * );
 * // 返回: ["Issue '1#20250101-01' 的状态是 'In Progress' (来自提交: 添加功能)"]
 *
 * @example
 * // 检查已完成的 issue
 * const warnings = checkIssues(
 *   [{ issueId: '2#20250101-02', subject: '修复bug' }],
 *   [{ id: '2#20250101-02', status: 'Done' }]
 * );
 * // 返回: [] （无警告）
 */
export function checkIssues(commits: ParsedCommit[], statuses: Issue[]): string[] {
  const warnings: string[] = [];
  const statusMap = new Map(statuses.map((s) => [s.id, s.status]));

  for (const commit of commits) {
    if (commit.issueId) {
      const status = statusMap.get(commit.issueId);
      // 假设 "Done" 是完成状态，不区分大小写
      if (status && status.toLowerCase() !== 'done') {
        const warning = `Issue '${commit.issueId}' 的状态是 '${status}' (来自提交: ${commit.subject})`;
        warnings.push(warning);
      }
    }
  }

  return warnings;
}
