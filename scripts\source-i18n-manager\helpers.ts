import fs from 'fs-extra';
import path from 'path';
import { FlatMessageEntry, NestedObject } from './types.js';

/**
 * 从文本中提取 $xxx$ 格式的占位符
 * @param text - 要解析的文本
 * @returns 占位符名称数组（不包含 $ 符号）
 */
export function extractPlaceholders(text: string): string[] {
  if (typeof text !== 'string') return [];
  const matches = text.match(/\$([a-zA-Z_][a-zA-Z0-9_]*)\$/g);
  return matches ? matches.map((m) => m.slice(1, -1)) : [];
}

/**
 * 将 Excel 格式的占位符转换为 JSON 格式
 * 将 $PLACEHOLDER_xxx$ 转换为 $xxx$
 * @param text - 包含 Excel 格式占位符的文本
 * @returns 转换后的文本
 */
export function convertExcelToJsonPlaceholders(text: string): string {
  if (typeof text !== 'string') return '';
  return text.replace(/\$PLACEHOLDER_([a-zA-Z_][a-zA-Z0-9_]*)\$/g, '$$$1$$');
}

/**
 * 将 JSON 格式的占位符转换为 Excel 格式
 * 将 $xxx$ 转换为 $PLACEHOLDER_xxx$
 * @param text - 包含 JSON 格式占位符的文本
 * @returns 转换后的文本
 */
export function convertJsonToExcelPlaceholders(text: string): string {
  if (typeof text !== 'string') return '';
  return text.replace(/\$([a-zA-Z_][a-zA-Z0-9_]*)\$/g, `$PLACEHOLDER_$1$`);
}

/**
 * 检查键名是否匹配指定的模式列表
 * @param key - 要检查的键名
 * @param patterns - 模式列表，支持正则表达式字符串或普通字符串
 * @returns 是否匹配任一模式
 */
export function matchesPatterns(key: string, patterns: string[]): boolean {
  if (patterns.length === 0) return false;
  return patterns.some((pattern) => {
    try {
      const regex = new RegExp(pattern);
      return regex.test(key);
    } catch {
      return key === pattern;
    }
  });
}

/**
 * 根据包含和排除规则过滤文案
 * @param baseKeys - 基础键名列表
 * @param includeKeys - 包含规则列表，为空表示包含所有
 * @param excludeKeys - 排除规则列表
 * @returns 过滤后的键名列表
 */
export function filterMessageKeys(
  baseKeys: string[],
  includeKeys: string[] = [],
  excludeKeys: string[] = [],
): string[] {
  let filteredKeys = baseKeys;
  if (includeKeys.length > 0) {
    filteredKeys = filteredKeys.filter((key) => matchesPatterns(key, includeKeys));
  }
  if (excludeKeys.length > 0) {
    filteredKeys = filteredKeys.filter((key) => !matchesPatterns(key, excludeKeys));
  }
  return filteredKeys;
}

/**
 * 校验单个翻译条目的基本有效性
 * @param entry - 翻译条目对象
 * @param entry.messageKey - 文案名
 * @param entry.translation - 翻译文本
 * @param entry.locale - 语言代码
 * @param entry.rowNumber - Excel 行号
 * @returns 校验结果对象
 */
export function validateTranslationEntry(entry: {
  messageKey: string;
  translation: string;
  locale: string;
  rowNumber: number;
}): { valid: boolean; error?: string; warning?: string } {
  if (!entry.messageKey.trim()) {
    return { valid: false, error: `第 ${entry.rowNumber} 行：message key 不能为空` };
  }
  if (!entry.translation.trim()) {
    return { valid: false, warning: `第 ${entry.rowNumber} 行：${entry.locale} 语言的翻译为空` };
  }

  // 特定字段的长度校验
  if (entry.messageKey === 'EXTENSION_NAME' && entry.translation.length > 75) {
    return {
      valid: false,
      error: `第 ${entry.rowNumber} 行：EXTENSION_NAME 长度超限: ${entry.translation.length}/75 字符 - "${entry.translation}"`,
    };
  }
  if (entry.messageKey === 'EXTENSION_DESCRIPTION' && entry.translation.length > 132) {
    return {
      valid: false,
      error: `第 ${entry.rowNumber} 行：EXTENSION_DESCRIPTION 长度超限: ${entry.translation.length}/132 字符 - "${entry.translation}"`,
    };
  }

  return { valid: true };
}

/**
 * 校验翻译文本与源文本的占位符一致性
 * @param translation - 翻译文本
 * @param source - 源文本（通常是英文或需求文本）
 * @param locale - 语言代码
 * @param messageKey - 文案名
 * @returns 校验结果对象
 */
export function validatePlaceholders(
  translation: string,
  source: string,
  locale: string,
  messageKey: string,
): { valid: boolean; error?: string; warning?: string } {
  const srcPlaceholders = extractPlaceholders(convertExcelToJsonPlaceholders(source));
  const tgtPlaceholders = extractPlaceholders(convertExcelToJsonPlaceholders(translation));
  const srcSet = new Set(srcPlaceholders);
  const tgtSet = new Set(tgtPlaceholders);
  if (
    srcSet.size !== tgtSet.size ||
    srcPlaceholders.sort().join(',') !== tgtPlaceholders.sort().join(',')
  ) {
    return {
      valid: false,
      error: `占位符不一致: key=${messageKey}, ${source}=${JSON.stringify(srcPlaceholders)}, ${locale}=${JSON.stringify(tgtPlaceholders)}`,
    };
  }
  return { valid: true };
}

/**
 * 批量校验翻译条目列表
 * @param entries - 翻译条目数组
 * @param allRows - 所有行数据，用于占位符一致性校验
 * @returns 校验结果汇总
 */
export function validateTranslationEntries(
  entries: Array<{
    messageKey: string;
    requirement: string;
    translation: string;
    locale: string;
    rowNumber: number;
  }>,
  allRows?: Array<{
    messageKey: string;
    requirement: string;
    translations: Record<string, string>;
  }>,
) {
  const validEntries: typeof entries = [];
  const invalidEntries: Array<
    { validationResult: ReturnType<typeof validateTranslationEntry> } & (typeof entries)[0]
  > = [];
  let errors = 0;
  let warnings = 0;
  for (const entry of entries) {
    let validationResult = { valid: true, error: '', warning: '' } as ReturnType<
      typeof validateTranslationEntry
    >;

    // 译文非空校验
    validationResult = validateTranslationEntry(entry);

    // 占位符一致性校验
    if (validationResult.valid && allRows) {
      const source = allRows.find((r) => r.messageKey === entry.messageKey)?.requirement || '';
      const placeholderResult = validatePlaceholders(
        entry.translation,
        source,
        entry.locale,
        entry.messageKey,
      );
      if (!placeholderResult.valid) {
        validationResult = placeholderResult;
      }
    }

    if (validationResult.valid) {
      validEntries.push(entry);
    } else {
      invalidEntries.push({ ...entry, validationResult });
      if (validationResult.error) errors++;
      if (validationResult.warning) warnings++;
    }
  }
  return {
    validEntries,
    invalidEntries,
    summary: {
      valid: validEntries.length,
      invalid: invalidEntries.length,
      errors,
      warnings,
    },
  };
}

/**
 * 递归收集嵌套对象中的所有字符串类型的键路径
 * @param obj - 要遍历的对象
 * @param prefix - 当前路径前缀
 * @param keys - 用于收集键路径的 Set 对象
 */
export function collectKeys(obj: Record<string, unknown>, prefix: string, keys: Set<string>): void {
  for (const [key, value] of Object.entries(obj)) {
    const fullKey = prefix ? `${prefix}.${key}` : key;
    if (typeof value === 'object' && value !== null && !Array.isArray(value)) {
      collectKeys(value as Record<string, unknown>, fullKey, keys);
    } else if (typeof value === 'string') {
      keys.add(fullKey);
    }
  }
}

/**
 * 根据点分隔的路径获取嵌套对象中的值
 * @param obj - 要查询的对象
 * @param path - 点分隔的路径字符串，如 'a.b.c'
 * @returns 找到的值，如果路径不存在则返回 undefined
 */
export function getNestedValue(obj: Record<string, unknown>, path: string): unknown {
  const keys = path.split('.');
  let current: unknown = obj;
  for (const key of keys) {
    if (typeof current === 'object' && current !== null && key in current) {
      current = (current as Record<string, unknown>)[key];
    } else {
      return undefined;
    }
  }
  return current;
}

/**
 * 从扁平化消息条目中提取所有基础键名
 * @param flatEntries - 扁平化消息条目数组
 * @returns 去重并排序的基础键名数组
 */
export function getBaseKeys(flatEntries: FlatMessageEntry[]): string[] {
  const baseKeys = new Set<string>();
  for (const entry of flatEntries) {
    baseKeys.add(entry.baseKey);
  }
  return Array.from(baseKeys).sort();
}

/**
 * 获取指定目录下所有语言包文件的语言代码列表
 * @param localesDir - 语言包目录路径
 * @returns 语言代码数组，按字母顺序排序
 */
export async function getExistingLocales(localesDir: string): Promise<string[]> {
  try {
    if (!(await fs.pathExists(localesDir))) {
      return [];
    }
    const files = await fs.readdir(localesDir);
    return files
      .filter((file) => file.endsWith('.json'))
      .map((file) => path.basename(file, '.json'))
      .sort();
  } catch (error) {
    // 这里不直接用 logger，避免循环依赖，主流程文件负责日志
    return [];
  }
}

/**
 * 读取 JSON 文件内容
 * @param filePath - JSON 文件路径
 * @returns JSON 对象或 null（如果读取失败）
 */
export async function readJson(filePath: string): Promise<NestedObject | null> {
  try {
    if (!(await fs.pathExists(filePath))) {
      return null;
    }
    return await fs.readJson(filePath);
  } catch (error) {
    return null;
  }
}

/**
 * 写入 JSON 文件
 * @param filePath - JSON 文件路径
 * @param data - 要写入的数据
 * @returns 是否写入成功
 */
export async function writeJson(filePath: string, data: NestedObject): Promise<boolean> {
  try {
    await fs.ensureDir(path.dirname(filePath));
    await fs.writeJson(filePath, data, { spaces: 2 });
    return true;
  } catch (error) {
    return false;
  }
}

/**
 * 备份文件
 * @param filePath - 原文件路径
 * @param suffix - 备份文件后缀，默认为 '.bak'
 * @returns 是否备份成功
 */
export async function backupFile(filePath: string, suffix = '.bak'): Promise<boolean> {
  try {
    if (!(await fs.pathExists(filePath))) {
      return false;
    }
    const backupPath = `${filePath}${suffix}`;
    await fs.copy(filePath, backupPath);
    return true;
  } catch (error) {
    return false;
  }
}

/**
 * 检查 JSON 文件是否存在
 * @param localesDir - 语言包目录
 * @param locale - 语言代码
 * @returns 文件是否存在
 */
export function jsonFileExists(localesDir: string, locale: string): boolean {
  const filePath = path.join(localesDir, `${locale}.json`);
  return fs.pathExistsSync(filePath);
}
