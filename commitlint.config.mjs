/**
 * Commitlint 配置文件
 *
 * 该配置扩展了标准的 Conventional Commits 规范
 * 暂时使用基本配置，后续可添加自定义验证规则
 */

export default {
  // 扩展标准的 Conventional Commits 配置
  extends: ['@commitlint/config-conventional'],

  // 自定义规则
  rules: {
    // 标准规则配置
    'type-enum': [
      2,
      'always',
      [
        'feat', // 新功能
        'fix', // 修复
        'docs', // 文档
        'style', // 格式（不影响代码运行的变动）
        'refactor', // 重构（即不是新增功能，也不是修改bug的代码变动）
        'perf', // 性能优化
        'test', // 增加测试
        'chore', // 构建过程或辅助工具的变动
        'ci', // CI配置文件和脚本的变动
        'build', // 影响构建系统或外部依赖的变动
        'revert', // 回滚
      ],
    ],

    // 主题行长度限制
    'subject-max-length': [2, 'always', 100],
    'subject-min-length': [2, 'always', 5],

    // 正文长度限制
    'body-max-line-length': [2, 'always', 200],
  },
};
