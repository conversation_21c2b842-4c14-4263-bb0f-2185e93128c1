// https://developer.mozilla.org/en-US/docs/Mozilla/Add-ons/WebExtensions/manifest.json/permissions#api_permissions
// https://developer.chrome.com/docs/extensions/reference/permissions-list

// permissions
/**
 * 允许扩展程序修改辅助功能状态。
 * @warning 安装时会显示警告，显示的警告：更改无障碍设置。
 */
export const accessibilityFeaturesModify = 'accessibilityFeatures.modify';
/**
 * 允许扩展程序读取辅助功能状态。
 * @warning 安装时会显示警告，显示的警告：查看您的无障碍设置。
 */
export const accessibilityFeaturesRead = 'accessibilityFeatures.read';
/**
 * 授予扩展程序对当前活动标签页的临时访问权限，当用户调用该扩展程序时（例如点击其浏览器操作按钮）。
 */
export const activeTab = 'activeTab';
/**
 * 允许扩展程序使用 chrome.alarms API 来安排代码定期运行或在将来的特定时间运行。
 */
export const alarms = 'alarms';
/**
 * 允许扩展程序使用 chrome.audio API 获取和控制连接到系统的音频设备。
 */
export const audio = 'audio';
/**
 * 使 Chrome 提前启动（用户登录计算机后，在用户启动 Chrome 之前）并稍后关闭（即使其最后一个窗口已关闭，直到用户明确退出 Chrome）。通常用于具有后台脚本的扩展。
 */
export const background = 'background';
/**
 * 允许扩展程序使用 chrome.bookmarks API 与浏览器书签系统进行交互。
 * @warning 安装时会显示警告，显示的警告：读取和更改您的书签。
 */
export const bookmarks = 'bookmarks';
/**
 * 允许扩展程序使用 chrome.browsingData API 清除用户的浏览数据。
 */
export const browsingData = 'browsingData';
/**
 * 允许扩展程序使用 chrome.certificateProvider API 向平台公开证书，这些证书可用于 TLS 身份验证。
 */
export const certificateProvider = 'certificateProvider';
/**
 * 允许扩展程序从剪贴板读取数据。
 * @warning 安装时会显示警告，显示的警告：读取您复制和粘贴的数据。
 */
export const clipboardRead = 'clipboardRead';
/**
 * 允许扩展程序向剪贴板写入数据。
 * @warning 安装时会显示警告，显示的警告：修改您复制和粘贴的数据。
 */
export const clipboardWrite = 'clipboardWrite';
/**
 * 允许扩展程序使用 chrome.contentSettings API 控制网站是否可以使用 Cookie、JavaScript 和插件等功能。
 * @warning 安装时会显示警告，显示的警告：更改您用于控制以下权限的设置：网站对 Cookie、JavaScript、插件、地理位置信息、麦克风、摄像头等功能的使用权限。
 */
export const contentSettings = 'contentSettings';
/**
 * 允许扩展程序使用 chrome.contextMenus API 向 Google Chrome 浏览器上下文菜单添加项目。
 */
export const contextMenus = 'contextMenus';
/**
 * 允许扩展程序使用 chrome.cookies API 查询和修改 Cookie，并在更改时收到通知。
 */
export const cookies = 'cookies';
/**
 * 允许扩展程序使用 chrome.debugger API 与 Chrome 的调试协议进行交互。
 * @warning 不能设为可选权限
 * @warning 安装时会显示警告：
 *  - 访问页面调试程序后端。
 *  - 读取和更改您在所有网站上的所有数据。
 */
export const permissionDebugger = 'debugger';
/**
 * 允许扩展程序使用 chrome.declarativeContent API 根据页面内容显示其页面操作，而无需读取页面内容。
 */
export const declarativeContent = 'declarativeContent';
/**
 * 允许扩展程序使用 chrome.declarativeNetRequest API 在不拦截网络请求和查看其内容的情况下，阻止或修改网络请求。
 * @warning 安装时会显示警告，显示的警告：屏蔽所有网页上的内容。
 * @warning 不能设为可选权限
 */
export const declarativeNetRequest = 'declarativeNetRequest';
/**
 * 允许扩展程序使用 `chrome.declarativeNetRequest` API 修改网络请求。
 * 与基础的 `declarativeNetRequest` 权限相比，此权限允许执行需要相应主机权限的操作（例如修改请求头或响应头）。
 * 基础权限主要用于阻止请求或执行有限的重定向/头部修改，通常无需额外的主机权限。
 */
export const declarativeNetRequestWithHostAccess = 'declarativeNetRequestWithHostAccess';
/**
 * 允许扩展程序向开发者工具写入关于其声明性网络请求规则的信息。
 * @warning 安装时会显示警告，显示的警告：读取您的浏览记录。
 * @warning 主要用于调试，不应在生产环境滥用
 */
export const declarativeNetRequestFeedback = 'declarativeNetRequestFeedback';
/**
 * 允许扩展程序使用 chrome.dns API 进行 DNS 解析。
 */
export const dns = 'dns';
/**
 * 允许扩展程序使用 chrome.desktopCapture API 捕获屏幕、单个窗口或单个选项卡的内容。
 * @warning 安装时会显示警告，显示的警告：截取屏幕内容。
 */
export const desktopCapture = 'desktopCapture';
/**
 * 允许扩展程序使用 chrome.documentScan API 发现和检索来自附加文档扫描仪的图像。
 */
export const documentScan = 'documentScan';
/**
 * 允许扩展程序使用 chrome.downloads API 以编程方式启动、监视、操作和搜索下载。
 * @warning 安装时会显示警告，显示的警告：管理您的下载内容。
 */
export const downloads = 'downloads';
/**
 * 允许扩展程序使用 chrome.downloads.open() 方法打开已下载文件所在的文件夹。
 * @warning 安装时会显示警告，显示的警告：管理您的下载内容。
 */
export const downloadsOpen = 'downloads.open';
/**
 * 允许扩展程序使用 chrome.downloads.ui API 在浏览器下载管理器界面中显示自定义界面。
 * @warning 安装时会显示警告，显示的警告：管理您的下载内容。
 */
export const downloadsUi = 'downloads.ui';
/**
 * 允许扩展程序使用 chrome.enterprise.deviceAttributes API 读取设备属性。
 */
export const enterpriseDeviceAttributes = 'enterprise.deviceAttributes';
/**
 * 允许扩展程序使用 chrome.enterprise.hardwarePlatform API 获取运行 Chrome 的硬件平台的制造商和型号。
 */
export const enterpriseHardwarePlatform = 'enterprise.hardwarePlatform';
/**
 * 允许扩展程序使用 chrome.enterprise.networkingAttributes API 读取网络属性。
 */
export const enterpriseNetworkingAttributes = 'enterprise.networkingAttributes';
/**
 * 允许扩展程序使用 chrome.enterprise.platformKeys API 生成密钥并为这些密钥安装证书。
 */
export const enterprisePlatformKeys = 'enterprise.platformKeys';
/**
 * 允许扩展程序访问 favicon API 以获取网站图标。
 * @warning 安装时会显示警告，显示的警告：查看您访问的网站的图标。
 */
export const favicon = 'favicon';
/**
 * 允许扩展程序使用 chrome.fileBrowserHandler API 扩展 Chrome OS 文件浏览器。
 */
export const fileBrowserHandler = 'fileBrowserHandler';
/**
 * 允许扩展程序使用 chrome.fileSystemProvider API 创建可从 Chrome OS 文件管理器访问的文件系统。
 */
export const fileSystemProvider = 'fileSystemProvider';
/**
 * 允许扩展程序使用 chrome.fontSettings API 管理 Chrome 的字体设置。
 */
export const fontSettings = 'fontSettings';
/**
 * 允许扩展程序使用 chrome.gcm API 通过 Firebase 云消息传递 (FCM) 发送和接收消息。
 */
export const gcm = 'gcm';
/**
 * 允许扩展程序在未经用户许可的情况下获取用户的地理位置。
 * @warning 安装时会显示警告，显示的警告：检测您所在的地理位置。
 * @warning 不能设为可选权限
 */
export const geolocation = 'geolocation';
/**
 * 允许扩展程序使用 chrome.history API 与浏览器的已访问页面记录进行交互。
 * @warning 安装时会显示警告，显示的警告：读取和更改您在所有已登录的设备上的浏览记录。
 */
export const history = 'history';
/**
 * 允许扩展程序使用 chrome.identity API 获取 OAuth2 访问令牌。
 * @warning 安装时会显示警告
 */
export const identity = 'identity';
/**
 * 允许扩展程序使用 chrome.identity API 获取用户的电子邮件地址。
 * @warning 安装时会显示警告，显示的警告：知道您的电子邮件地址。
 */
export const identityEmail = 'identity.email';
/**
 * 允许扩展程序使用 chrome.idle API 检测机器的空闲状态何时发生变化。
 */
export const idle = 'idle';
/**
 * 允许扩展程序使用 chrome.loginState API 读取和监视登录状态。
 */
export const loginState = 'loginState';
/**
 * 允许扩展程序使用 chrome.management API 管理已安装和正在运行的扩展程序列表。
 * @warning 安装时会显示警告，显示的警告：管理您的应用、扩展程序和主题背景。
 */
export const management = 'management';
/**
 * 允许扩展程序与本机应用程序交换消息。
 * @warning 安装时会显示警告，显示的警告：与协作的原生应用通信。
 */
export const nativeMessaging = 'nativeMessaging';
/**
 * 允许扩展程序使用 chrome.notifications API 创建丰富的通知，并在系统托盘中显示这些通知。
 * @warning 安装时会显示警告，显示的警告：显示通知
 */
export const notifications = 'notifications';
/**
 * 允许扩展程序使用 chrome.offscreen API 创建和管理离屏文档。
 */
export const offscreen = 'offscreen';
/**
 * 允许扩展程序使用 chrome.pageCapture API 将选项卡另存为 MHTML。
 * @warning 安装时会显示警告，显示的警告：读取和更改您在所有网站上的所有数据。
 */
export const pageCapture = 'pageCapture';
/**
 * 允许扩展程序使用 chrome.platformKeys API 访问由平台管理的客户端证书。
 */
export const platformKeys = 'platformKeys';
/**
 * 允许扩展程序使用 chrome.power API 覆盖系统的电源管理功能。
 */
export const power = 'power';
/**
 * 允许扩展程序使用 chrome.printerProvider API 查询打印机、查询其功能以及提交打印作业到这些打印机。
 */
export const printerProvider = 'printerProvider';
/**
 * 允许扩展程序使用 chrome.printing API 向连接到 ChromeOS 的打印机发送打印作业。
 */
export const printing = 'printing';
/**
 * 允许扩展程序使用 chrome.printingMetrics API 获取有关打印使用情况的数据。
 */
export const printingMetrics = 'printingMetrics';
/**
 * 允许扩展程序使用 chrome.privacy API 控制浏览器中可能影响用户隐私的功能的使用。
 * @warning 安装时会显示警告，显示的警告：更改与隐私权相关的设置。
 */
export const privacy = 'privacy';
/**
 * 允许扩展程序使用 chrome.processes API 与浏览器的进程进行交互。
 */
export const processes = 'processes';
/**
 * 允许扩展程序使用 chrome.proxy API 管理 Chrome 的代理设置。
 * @warning 安装时会显示警告，显示的警告：读取和更改您在所有网站上的所有数据。
 * @warning 不能设为可选权限
 */
export const proxy = 'proxy';
/**
 * 允许扩展程序使用 chrome.readingList API 在阅读列表中添加和删除项目。
 * @warning 安装时会显示警告，显示的警告：读取和更改阅读清单中的条目。
 */
export const readingList = 'readingList';
/**
 * 允许扩展程序访问 chrome.runtime API。除了 runtime.getManifest 外，所有扩展程序都可以访问此 API。
 */
export const runtime = 'runtime';
/**
 * 允许扩展程序使用 chrome.scripting API 在不同上下文中执行脚本。
 */
export const scripting = 'scripting';
/**
 * 允许扩展程序使用 chrome.search API 通过默认提供程序进行搜索。
 */
export const search = 'search';
/**
 * 允许扩展程序使用 chrome.sessions API 查询和恢复浏览器会话中的选项卡和窗口。
 * @warning 安装时会显示警告，显示的警告：
    - 与 "history" 权限搭配使用时：读取和更改您在所有已登录的设备上的浏览记录。
    - 与 "tabs" 权限搭配使用时：读取您在所有已登录的设备上的浏览记录。
 */
export const sessions = 'sessions';
/**
 * 允许扩展程序使用 chrome.sidePanel API 在浏览器侧边栏中托管内容。
 */
export const sidePanel = 'sidePanel';
/**
 * 允许扩展程序使用 chrome.storage API 存储、检索和跟踪用户数据的更改。
 */
export const storage = 'storage';
/**
 * 允许扩展程序使用 chrome.system.cpu API 查询 CPU 元数据。
 */
export const systemCpu = 'system.cpu';
/**
 * 允许扩展程序使用 chrome.system.display API 查询显示元数据。
 */
export const systemDisplay = 'system.display';
/**
 * 允许扩展程序使用 chrome.system.memory API 获取有关系统内存的信息。
 */
export const systemMemory = 'system.memory';
/**
 * 允许扩展程序使用 chrome.system.storage API 查询存储设备信息并在可移动存储设备连接和分离时收到通知。
 * @warning 安装时会显示警告
 */
export const systemStorage = 'system.storage';
/**
 * 允许扩展程序使用 chrome.tabCapture API 与选项卡媒体流进行交互。
 * @warning 安装时会显示警告，显示的警告：读取和更改您在所有网站上的所有数据。
 */
export const tabCapture = 'tabCapture';
/**
 * 允许扩展程序使用 chrome.tabGroups API 与浏览器选项卡分组系统进行交互。
 * @warning 安装时会显示警告，显示的警告：查看和管理您的标签页组。
 */
export const tabGroups = 'tabGroups';
/**
 * 用于授予对多个 API（包括 chrome.tabs 和 chrome.windows）使用的标签页对象的特许字段的访问权限。您通常不需要声明此权限即可使用这些 API。
 * @warning 安装时会显示警告，显示的警告：读取您的浏览记录。
 */
export const tabs = 'tabs';
/**
 * 允许扩展程序使用 chrome.topSites API 访问用户最常访问的网站列表。
 * @warning 安装时会显示警告，显示的警告：查看您最常访问的网站列表。
 */
export const topSites = 'topSites';
/**
 * 允许扩展程序使用 chrome.tts API 播放合成的文本转语音 (TTS)。
 * @warning 不能设为可选权限
 */
export const tts = 'tts';
/**
 * 允许扩展程序使用 chrome.ttsEngine API 实现文本转语音引擎。
 * @warning 安装时会显示警告，显示的警告：使用合成语音朗读所说出的所有文字。
 * @warning 不能设为可选权限
 */
export const ttsEngine = 'ttsEngine';
/**
 * 为扩展程序提供无限的 quota，用于存储 HTML5 文件系统、IndexedDB、本地存储和 WebSQL 数据库。
 * @refer: https://developer.chrome.com/docs/extensions/develop/concepts/storage-and-cookies?hl=zh-cn
 */
export const unlimitedStorage = 'unlimitedStorage';
/**
 * 允许扩展程序使用 chrome.vpnProvider API 实现 VPN 客户端。
 */
export const vpnProvider = 'vpnProvider';
/**
 * 允许扩展程序使用 chrome.wallpaper API 更改 ChromeOS 壁纸。
 * @warning 不能设为可选权限
 */
export const wallpaper = 'wallpaper';
/**
 * 允许扩展程序使用 chrome.webAuthenticationProxy API 与远程桌面环境中的 Web Authentication 请求进行交互。
 * @warning 安装时会显示警告，显示的警告：读取和更改您在所有网站上的所有数据。
 */
export const webAuthenticationProxy = 'webAuthenticationProxy';
/**
 * 允许扩展程序使用 chrome.webNavigation API 在传输过程中接收有关导航请求状态的通知。
 * @warning 安装时会显示警告，显示的警告：读取您的浏览记录。
 */
export const webNavigation = 'webNavigation';
/**
 * 允许扩展程序使用 chrome.webRequest API 观察和分析流量，并拦截、阻止或修改传输中的请求。
 */
export const webRequest = 'webRequest';
/**
 * 在使用 chrome.webRequest API 时需要此权限来阻止请求。
 */
export const webRequestBlocking = 'webRequestBlocking';
/**
 * 允许扩展程序使用 chrome.devtools API。
 * @warning 不能设为可选权限
 */
export const devtools = 'devtools';
/**
 * 允许扩展程序使用 chrome.mdns API 发现网络上的服务。
 * @warning 不能设为可选权限
 */
export const mdns = 'mdns';
/**
 * 允许扩展程序使用 chrome.commands API 添加键盘快捷键，以触发扩展程序中的操作。
 */
export const commands = 'commands';
/**
 * 安装插件时会显示警告的权限
 * @doc https://developer.chrome.com/docs/extensions/mv3/permission_warnings/
 */
export const PERMISSIONS_WILL_CAUSE_WARNING_DISPLAYED = [
  accessibilityFeaturesModify,
  accessibilityFeaturesRead,
  bookmarks,
  clipboardRead,
  clipboardWrite,
  contentSettings,
  declarativeNetRequest,
  declarativeNetRequestFeedback,
  desktopCapture,
  downloads,
  downloadsOpen,
  downloadsUi,
  favicon,
  geolocation,
  history,
  identity,
  identityEmail,
  management,
  nativeMessaging,
  notifications,
  pageCapture,
  privacy,
  proxy,
  readingList,
  systemStorage,
  tabCapture,
  tabGroups,
  tabs,
  topSites,
  ttsEngine,
  webAuthenticationProxy,
  webNavigation,
];
/**
 * 不能设置为可选权限的权限
 * @doc https://developer.chrome.com/docs/extensions/reference/permissions/#implementing-optional-permissions
 */
export const PERMISSIONS_CAN_NOT_BE_OPTIONAL = [
  permissionDebugger,
  declarativeNetRequest,
  devtools,
  geolocation,
  mdns,
  proxy,
  tts,
  ttsEngine,
  wallpaper,
];

// hosts permissions

/**
 * 特殊的权限字符串 `<all_urls>`，允许访问所有 URL 方案（包括 http, https, file, ftp, chrome-extension 等）。
 * @warning 安装时会显示警告：允许扩展读取和更改您在所有网站上的数据。
 */
export const allUrls = '<all_urls>';
/**
 * 允许访问所有支持的协议（http, https, ws, wss, ftp, 有时包括 file）下的所有主机的所有路径。
 * @warning 安装时会显示警告：允许扩展读取和更改您在所有网站上的数据。
 */
export const allProtocolUrls = '*://*/*';
/**
 * 允许访问所有 HTTP 和 HTTPS 协议下的所有主机的所有路径。
 * @warning 安装时会显示警告：允许扩展读取和更改您在所有网站上的数据。
 */
export const allHttp = ['http://*/*', 'https://*/*'];

export const alibabaCNHostPermissions = ['*://*.1688.com/*'];
export const alipriceHostPermissions = ['*://*.aliprice.com/*'];
export const alipriceAgentHostPermissions = ['*://agent.aliprice.com/*'];
export const googleAnalyticsSSLHostPermissions = ['*://ssl.google-analytics.com/*'];
export const googleAnalyticsHostPermissions = [...googleAnalyticsSSLHostPermissions];
export const aliexpressHostPermissions = [
  '*://*.aliexpress.br/*',
  '*://*.aliexpress.by/*',
  '*://*.aliexpress.com/*',
  '*://*.aliexpress.de/*',
  '*://*.aliexpress.es/*',
  '*://*.aliexpress.fr/*',
  '*://*.aliexpress.hk/*',
  '*://*.aliexpress.id/*',
  '*://*.aliexpress.il/*',
  '*://*.aliexpress.it/*',
  '*://*.aliexpress.jp/*',
  '*://*.aliexpress.kr/*',
  '*://*.aliexpress.lv/*',
  '*://*.aliexpress.nl/*',
  '*://*.aliexpress.pl/*',
  '*://*.aliexpress.pt/*',
  '*://*.aliexpress.ru/*',
  '*://*.aliexpress.tr/*',
  '*://*.aliexpress.us/*',
  '*://*.aliexpress.vn/*',
];
export const tmallCOMHostPermissions = ['*://*.tmall.com/*'];
export const tmallRUHostPermissions = ['*://*.tmall.ru/*'];
export const tmallHKHostPermissions = ['*://*.tmall.hk/*'];
export const tmallTSHostPermissions = ['*://*.ts.tmall.com/*'];
export const tmallHostPermissions = [
  ...tmallCOMHostPermissions,
  ...tmallRUHostPermissions,
  ...tmallHKHostPermissions,
  ...tmallTSHostPermissions,
];
export const alibabaHostPermissions = ['*://*.alibaba.com/*'];
export const alipayHostPermissions = ['*://*.alipay.com/*'];
export const amazonHostPermissions = [
  '*://*.amazon.eg/*',
  '*://*.amazon.sa/*',
  '*://*.amazon.ae/*',
  '*://*.amazon.ca/*',
  '*://*.amazon.co.jp/*',
  '*://*.amazon.co.uk/*',
  '*://*.amazon.com.au/*',
  '*://*.amazon.com.br/*',
  '*://*.amazon.com.mx/*',
  '*://*.amazon.com.tr/*',
  '*://*.amazon.com/*',
  '*://*.amazon.de/*',
  '*://*.amazon.es/*',
  '*://*.amazon.fr/*',
  '*://*.amazon.in/*',
  '*://*.amazon.it/*',
  '*://*.amazon.nl/*',
  '*://*.amazon.au/*',
  '*://*.amazon.br/*',
  '*://*.amazon.cn/*',
];
export const banggoodHostPermissions = ['*://*.banggood.com/*', '*://*.banggood.in/*'];
export const bookingHostPermissions = ['*://*.booking.com/*'];
export const gotogateHostPermissions = ['*://*.gotogate.com/*'];
export const bukalapakHostPermissions = ['*://*.bukalapak.com/*'];
export const darazHostPermissions = [
  '*://*.daraz.com.bd/*',
  '*://*.daraz.com.np/*',
  '*://*.daraz.lk/*',
  '*://*.daraz.pk/*',
];
export const dhgateHostPermissions = ['*://*.dhgate.com/*'];
export const ebayHostPermissions = ['*://*.ebay.com/*'];
export const etsyHostPermissions = ['*://*.etsy.com/*'];
export const flipkartHostPermissions = ['*://*.flipkart.com/*'];
export const gearbestHostPermissions = ['*://*.gearbest.com/*'];
export const itaoHostPermissions = ['*://*.itao.com/*'];
export const jdHostPermissions = [
  '*://*.jd.com/*',
  '*://*.jd.es/*',
  '*://*.jd.id/*',
  '*://*.jd.ru/*',
];
export const joomHostPermissions = ['*://*.joom.com/*'];
export const joybuyHostPermissions = ['*://*.joybuy.com/*', '*://*.joybuy.es/*'];
export const lazadaHostPermissions = [
  '*://*.lazada.co.id/*',
  '*://*.lazada.co.th/*',
  '*://*.lazada.com.my/*',
  '*://*.lazada.com.ph/*',
  '*://*.lazada.sg/*',
  '*://*.lazada.vn/*',
];
export const ozonHostPermissions = ['*://*.ozon.ru/*'];
export const shopMMHostPermissions = ['*://*.shop.com.mm/*'];
export const xiapibuyHostPermissions = [
  '*://sg.xiapibuy.com/*',
  '*://id.xiapibuy.com/*',
  '*://my.xiapibuy.com/*',
  '*://ph.xiapibuy.com/*',
  '*://th.xiapibuy.com/*',
  '*://vn.xiapibuy.com/*',
  '*://xiapi.xiapibuy.com/*',
  '*://mx.xiapibuy.com/*',
  '*://co.xiapibuy.com/*',
  '*://cl.xiapibuy.com/*',
  '*://br.xiapibuy.com/*',
];
export const shopeeHostPermissions = [
  '*://*.shopee.co.id/*',
  '*://*.shopee.co.th/*',
  '*://*.shopee.com.my/*',
  '*://*.shopee.ph/*',
  '*://*.shopee.sg/*',
  '*://*.shopee.tw/*',
  '*://*.shopee.vn/*',
];
export const snapdealHostPermissions = ['*://*.snapdeal.com/*'];
export const souqHostPermissions = ['*://*.souq.com/*'];
export const taobaoHostPermissions = ['*://*.taobao.com/*'];
export const tokopediaHostPermissions = ['*://*.tokopedia.com/*'];
export const walmartHostPermissions = ['*://*.walmart.com/*'];
export const taobaoWorldHostPermissions = ['*://*.world.taobao.com/*'];
export const eleven11stHostPermissions = ['*://*.11st.co.kr/*'];
export const auctionHostPermissions = ['*://*.auction.co.kr/*'];
export const kurlyHostPermissions = ['*://*.kurly.com/*'];
export const stylenandaHostPermissions = ['*://*.stylenanda.com/*'];
export const wishHostPermissions = ['*://*.wish.com/*'];
export const wowmaHostPermissions = ['*://*.wowma.jp/*'];
export const bellemaisonHostPermissions = ['*://*.bellemaison.jp/*'];
export const biccameraHostPermissions = ['*://*.biccamera.com/*'];
export const coupangHostPermissions = ['*://*.coupang.com/*'];
export const dinosHostPermissions = ['*://*.dinos.co.jp/*'];
export const gmarketHostPermissions = ['*://*.gmarket.co.kr/*'];
export const interparkHostPermissions = ['*://*.interpark.com/*'];
export const kakakuHostPermissions = ['*://*.kakaku.com/*'];
export const lotteonHostPermissions = ['*://*.lotteon.com/*'];
export const mercariHostPermissions = ['*://*.jp.mercari.com/*'];
export const naverHostPermissions = ['*://*.naver.com/*'];
export const tmonHostPermissions = ['*://*.tmon.co.kr/*'];
export const qoo10HostPermissions = ['*://*.qoo10.jp/*', '*://*.qoo10.com/*'];
export const rakumaHostPermissions = ['*://*.fril.jp/*'];
export const rakutenHostPermissions = ['*://*.rakuten.co.jp/*'];
export const shopListHostPermissions = ['*://*.shop-list.com/*'];
export const ssgHostPermissions = ['*://*.ssg.com/*'];
export const yahooShoppingJPHostPermissions = ['*://*.shopping.yahoo.co.jp/*'];
export const yodobashiHostPermissions = ['*://*.yodobashi.com/*'];
export const zozoTownHostPermissions = ['*://*.zozo.jp/*'];
export const madeInChinaHostPermissions = ['*://*.made-in-china.com/*'];
export const sheinHostPermissions = ['*://*.shein.com/*'];
export const ehimartHostPermissions = ['*://*.e-himart.co.kr/*'];
export const domeggookHostPermissions = ['*://*.domeggook.com/*'];
export const lotteimallHostPermissions = ['*://*.lotteimall.com/*'];
export const ohouHostPermissions = ['*://*.ohou.se/*'];
export const yandexMarketHostPermissions = ['*://*.market.yandex.ru/*'];
export const ownerclanHostPermissions = ['*://*.ownerclan.com/*'];

// exclude host permissions
export const aliexpressExcludeMatches = [
  '*://console.aliexpress.com/*',
  '*://console.aliexpress.ru/*',
  '*://gcx.aliexpress.com/*',
  '*://gcx.aliexpress.ru/*',
  '*://msg.aliexpress.com/*',
  '*://msg.aliexpress.ru/*',
  '*://message.aliexpress.com/*',
  '*://message.aliexpress.ru/*',
  '*://helppage.aliexpress.com/*',
  '*://helppage.aliexpress.ru/*',
];
export const alibabaCNExcludeMatches = ['*://work.1688.com/*'];
export const amazonExcludeMatches = [
  '*://www.amazon.com/gp/help/chat/popup.html*',
  '*://www.amazon.com/gp/help/*',
  '*://*.aws.amazon.com/*',
];
export const shopeeExcludeMatches = [
  '*://seller.shopee.sg/*',
  '*://seller.shopee.tw/*',
  '*://seller.shopee.com.my/*',
  '*://seller.shopee.ph/*',
  '*://banhang.shopee.vn/*',
  '*://seller.shopee.co.id/*',
  '*://seller.shopee.co.th/*',
];
export const naverShoppingExcludeMatches = ['*://sell.smartstore.naver.com/*'];

// combo permissions
export const comboAlipriceTestHosts = [
  '*://*.chnprice.net/*',
  '*://*.aliprice.com.cn/*',
  ...alipriceHostPermissions,
];
export const basicDefaultPermissions = [
  // tabs,
  contextMenus,
  cookies,
  storage,
  notifications,
  alarms,
  ...alipriceHostPermissions,
];
