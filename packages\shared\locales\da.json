{"EXTENSION_NAME": {"message": "TODO: EXTENSION_NAME"}, "EXTENSION_DESCRIPTION": {"message": "TODO: EXTENSION_DESCRIPTION"}, "1688_cross_border_hot_selling": {"message": "1688 Grænseoverskridende Hot Selling Spot"}, "1688_shi_li_ren_zheng": {"message": "1688 styrke certificering"}, "1_jian_qi_pi": {"message": "MOQ: 1"}, "1year_yi_shang": {"message": "Mere end 1 år"}, "24H": {"message": "24H"}, "24H_fa_huo": {"message": "Levering indenfor 24 timer"}, "24H_lan_shou_lv": {"message": "24-timers p<PERSON><PERSON><PERSON><PERSON>"}, "30D_shang_xin": {"message": "Månedlige nye ankomster"}, "30d_sales": {"message": "$amount$ solgt på 30 dage", "placeholders": {"amount": {"content": "$1"}}}, "3Min_xiang_ying_lv": {"message": "<PERSON><PERSON> indenfor 3 min."}, "3Min_xiang_ying_lv__desc": {"message": "Andelen af Wangwangs effektive svar på meddelelser om køberforespørgsler inden for 3 minutter inden for de seneste 30 dage"}, "48H": {"message": "48H"}, "48H_fa_huo": {"message": "<PERSON><PERSON> indenfor 48 timer"}, "48H_lan_shou_lv": {"message": "48-timers p<PERSON><PERSON><PERSON><PERSON>"}, "48H_lan_shou_lv__desc": {"message": "Forholdet mellem det afhentede ordrenummer inden for 48 timer og det samlede antal ordrer"}, "48H_lv_yue_lv": {"message": "48-timers <PERSON><PERSON><PERSON><PERSON>"}, "48H_lv_yue_lv__desc": {"message": "Forholdet mellem det afhentede eller leverede ordrenummer inden for 48 timer og det samlede antal ordrer"}, "72H": {"message": "72H"}, "7D_shang_xin": {"message": "Ugentlige nye ankomster"}, "7D_wu_li_you": {"message": "7 dage plejefri"}, "ABS_title_text": {"message": "<PERSON><PERSON> liste inkluderer en brandhistorie"}, "AC_title_text": {"message": "<PERSON><PERSON> fortegnelse har Amazon's Choice-mærket"}, "A_title_text": {"message": "Denne fortegnelse har en A+ indholdsside"}, "BS_title_text": {"message": "Denne fortegnelse er rangeret som $num$ bedst sælgende i kategorien $type$", "placeholders": {"type": {"content": "$1"}, "num": {"content": "$2"}}}, "LTD_title_text": {"message": "LTD (Limited Time Deal) betyder, at denne fortegnelse er en del af en \"7-dages kampagne\"-beg<PERSON><PERSON>ed"}, "NR_title_text": {"message": "Denne fortegnelse er rangeret som $num$ New Release i kategorien $type$", "placeholders": {"type": {"content": "$1"}, "num": {"content": "$2"}}}, "SBV_title_text": {"message": "Denne fortegnelse har en videoannonce, en type PPC-annonce, der normalt vises midt i søgeresultaterne"}, "SB_title_text": {"message": "Denne fortegnelse har en varemærkeannonce, en type PPC-annonce, der normalt vises øverst eller nederst i søgeresultaterne"}, "SP_title_text": {"message": "Denne fortegnelse har en sponsoreret produktannonce"}, "V_title_text": {"message": "<PERSON><PERSON> liste har en videointroduktion"}, "advanced_research": {"message": "<PERSON><PERSON><PERSON> forskning"}, "agent_ds1688___my_order": {"message": "Mine Ordrer"}, "agent_ds1688__add_to_cart": {"message": "køb i udlandet"}, "agent_ds1688__cart": {"message": "Indkøbskurv"}, "agent_ds1688__desc": {"message": "Leveret af 1688. Det understøtter direkte køb fra udlandet, betaling i USD og levering til dit transitlager i Kina."}, "agent_ds1688__freight": {"message": "Forsendelsesomkostningsberegner"}, "agent_ds1688__help": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "agent_ds1688__packages": {"message": "<PERSON>ag<PERSON><PERSON><PERSON>"}, "agent_ds1688__profile": {"message": "Personligt center"}, "agent_ds1688__warehouse": {"message": "Mit lager"}, "ai_comment_analysis_advantage": {"message": "Fordele"}, "ai_comment_analysis_ai": {"message": "AI review analyse"}, "ai_comment_analysis_available": {"message": "<PERSON><PERSON>"}, "ai_comment_analysis_balance": {"message": "<PERSON><PERSON><PERSON> nok mønter, fyld venligst op"}, "ai_comment_analysis_behavior": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "ai_comment_analysis_characteristic": {"message": "Crowd karakteristika"}, "ai_comment_analysis_comment": {"message": "Produktet har ikke nok anmeldelser til at drage nøjagtige konklusioner, vælg venligst et produkt med flere anmeldelser."}, "ai_comment_analysis_consume": {"message": "Anslået forbrug"}, "ai_comment_analysis_default": {"message": "Standard anmeldelser"}, "ai_comment_analysis_desire": {"message": "<PERSON><PERSON><PERSON>"}, "ai_comment_analysis_disadvantage": {"message": "<PERSON><PERSON><PERSON>"}, "ai_comment_analysis_free": {"message": "<PERSON><PERSON><PERSON>"}, "ai_comment_analysis_freeNum": {"message": "1 gratis kredit vil blive brugt"}, "ai_comment_analysis_go_recharge": {"message": "Gå til top op"}, "ai_comment_analysis_intelligence": {"message": "Intelligent gennemgangsanalyse"}, "ai_comment_analysis_location": {"message": "Beliggenhed"}, "ai_comment_analysis_motive": {"message": "Købsmotivation"}, "ai_comment_analysis_network_error": {"message": "Netvæ<PERSON><PERSON><PERSON><PERSON>, prøv venligst igen"}, "ai_comment_analysis_normal": {"message": "Foto anmeldelser"}, "ai_comment_analysis_number_reviews": {"message": "Antal anmeldelser: $num$, estimeret forbrug: $consume$", "placeholders": {"num": {"content": "$1"}, "consume": {"content": "$2"}}}, "ai_comment_analysis_ordinary": {"message": "<PERSON><PERSON><PERSON>"}, "ai_comment_analysis_percentage": {"message": "Procent"}, "ai_comment_analysis_problem": {"message": "Problemer med betaling"}, "ai_comment_analysis_reanalysis": {"message": "Genanalysere"}, "ai_comment_analysis_reason": {"message": "<PERSON><PERSON><PERSON>"}, "ai_comment_analysis_recharge": {"message": "Fyld op"}, "ai_comment_analysis_recharged": {"message": "Jeg har fyldt op"}, "ai_comment_analysis_retry": {"message": "Prø<PERSON> igen"}, "ai_comment_analysis_scene": {"message": "Brugsscenarie"}, "ai_comment_analysis_start": {"message": "Begynd at analysere"}, "ai_comment_analysis_subject": {"message": "<PERSON><PERSON>"}, "ai_comment_analysis_time": {"message": "Brugstid"}, "ai_comment_analysis_tool": {"message": "AI værktøj"}, "ai_comment_analysis_user_portrait": {"message": "Brugerprofil"}, "ai_comment_analysis_welcome": {"message": "Velkommen til AI-gennemgangsanalyse"}, "ai_comment_analysis_year": {"message": "Kommentarer fra det seneste år"}, "ai_listing_Exclude_keywords": {"message": "<PERSON>ks<PERSON><PERSON><PERSON> sø<PERSON>"}, "ai_listing_Login_the_feature": {"message": "Login er påkrævet for funktionen"}, "ai_listing_aI_generation": {"message": "AI generation"}, "ai_listing_add_automatic": {"message": "Automatisk"}, "ai_listing_add_dictionary_new": {"message": "Opret et nyt bibliotek"}, "ai_listing_add_enter_keywords": {"message": "Indtast nøgleord"}, "ai_listing_add_inputkey_selling": {"message": "Indtast et salgsargument, og tryk på $key$ for at afslutte tilføjelsen", "placeholders": {"key": {"content": "$1"}}}, "ai_listing_add_inputkey_warning": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON> er overskredet, op til $amount$ salgspoint", "placeholders": {"amount": {"content": "$1"}}}, "ai_listing_add_keywords": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>ø<PERSON>"}, "ai_listing_add_manually": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "ai_listing_add_selling": {"message": "Tilføj salgsargumenter"}, "ai_listing_added_keywords": {"message": "Tilføjede søgeord"}, "ai_listing_added_successfully": {"message": "Tilføjet med succes"}, "ai_listing_addexcluded_keywords": {"message": "Indtast de ekskluderede søgeord, try<PERSON> på enter for at afslutte tilføjelsen."}, "ai_listing_adding_selling": {"message": "Tilføjede salgsargumenter"}, "ai_listing_addkeyword_enter": {"message": "Indtast nøgleattributordene, og tryk på Enter for at afslutte tilføjelsen"}, "ai_listing_ai_description": {"message": "AI-beskrivelsesordbibliotek"}, "ai_listing_ai_dictionary": {"message": "AI titel ordbibliotek"}, "ai_listing_ai_title": {"message": "AI titel"}, "ai_listing_aidescription_repeated": {"message": "AI-beskrivelsesordbibliotekets navn kan ikke gentages"}, "ai_listing_aititle_repeated": {"message": "AI-titelordbibliotekets navn kan ikke gentages"}, "ai_listing_data_comes_from": {"message": "Disse data kommer fra:"}, "ai_listing_deleted_successfully": {"message": "Slettet med succes"}, "ai_listing_dictionary_name": {"message": "Bibliotekets navn"}, "ai_listing_edit_dictionary": {"message": "Rediger bibliotek..."}, "ai_listing_edit_word_library": {"message": "Rediger ordbiblioteket"}, "ai_listing_enter_keywords": {"message": "Indtast nøgleord, og tryk på $key$ for at afslutte tilføjelsen", "placeholders": {"key": {"content": "$1"}}}, "ai_listing_exceeded_maximum": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON> er overskredet, maksimum $amount$ søgeord", "placeholders": {"amount": {"content": "$1"}}}, "ai_listing_excluded_word_library": {"message": "Udelukket ordbibliotek"}, "ai_listing_generate_characters": {"message": "Generer tegn"}, "ai_listing_generation_platform": {"message": "Generationsplatform"}, "ai_listing_help_optimize": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON> mig med at optimere produkttitlen, den originale titel er"}, "ai_listing_include_selling": {"message": "Andre salgsargumenter inkluderede:"}, "ai_listing_included_keyword": {"message": "Inkluderede sø<PERSON>ord"}, "ai_listing_included_keywords": {"message": "Inkluderede sø<PERSON>ord"}, "ai_listing_input_selling": {"message": "Indtast et salgsargument"}, "ai_listing_input_selling_fit": {"message": "Indtast salgsargumenter for at matche titlen"}, "ai_listing_input_selling_please": {"message": "Indtast venligst salgsargumenter"}, "ai_listing_intelligently_title": {"message": "Indtast det nødvendige indhold ovenfor for at generere titlen intelligent"}, "ai_listing_keyword_product_title": {"message": "Søgeords produkttitel"}, "ai_listing_keywords_repeated": {"message": "Nøgleord kan ikke gentages"}, "ai_listing_listed_selling_points": {"message": "<PERSON><PERSON><PERSON><PERSON> sal<PERSON>"}, "ai_listing_long_title_1": {"message": "Indeholder grundlæggende oplysninger såsom mærkenavn, produkttype, produktfunktioner osv."}, "ai_listing_long_title_2": {"message": "På basis af standardproduktets titel tilføjes søgeord, der fremmer SEO."}, "ai_listing_long_title_3": {"message": "Ud over at indeholde varemærkenavn, produkttype, produktfunktioner og søgeord, er long-tail søgeord også inkluderet for at opnå højere placeringer i specifikke, segmenterede søgeforespørgsler."}, "ai_listing_longtail_keyword_product_title": {"message": "Langhale søgeords produkttitel"}, "ai_listing_manually_enter": {"message": "Indtast manuelt..."}, "ai_listing_network_not_working": {"message": "Internet er ikke til<PERSON>, VPN er påkrævet for at få adgang til ChatGPT"}, "ai_listing_new_dictionary": {"message": "Opret et nyt ordbibliotek ..."}, "ai_listing_new_generate": {"message": "Frembringe"}, "ai_listing_optional_words": {"message": "Valgfrie ord"}, "ai_listing_original_title": {"message": "Original titel"}, "ai_listing_other_keywords_included": {"message": "<PERSON> inkluderede:"}, "ai_listing_please_again": {"message": "<PERSON><PERSON><PERSON><PERSON> igen"}, "ai_listing_please_select": {"message": "Følgende titler er blevet genereret til dig, vælg venligst:"}, "ai_listing_product_category": {"message": "Produktkategori"}, "ai_listing_product_category_is": {"message": "Produktkategorien er"}, "ai_listing_product_category_to": {"message": "Hvilken kategori tilhører produktet?"}, "ai_listing_random_keywords": {"message": "Tilfældige $amount$ søgeord", "placeholders": {"amount": {"content": "$1"}}}, "ai_listing_random_selling": {"message": "Tilfældige salgspoint på $amount$", "placeholders": {"amount": {"content": "$1"}}}, "ai_listing_randomize_keywords": {"message": "Randomiser fra ordbiblioteket"}, "ai_listing_search_selling": {"message": "<PERSON><PERSON>g efter salgsargument"}, "ai_listing_select_product_categories": {"message": "Vælg automatisk produktkategorier."}, "ai_listing_select_product_selling_points": {"message": "Vælg automatisk produktsalgspunkter"}, "ai_listing_select_word_library": {"message": "Vælg ordbibliotek"}, "ai_listing_selling": {"message": "Salgspunkter"}, "ai_listing_selling_ask": {"message": "<PERSON>vil<PERSON> andre salgsargumentkrav er der til titlen?"}, "ai_listing_selling_optional": {"message": "Valgf<PERSON> sal<PERSON>"}, "ai_listing_selling_repeat": {"message": "Point kan ikke duplikeres"}, "ai_listing_set_excluded": {"message": "Indstil som udelukket ordbibliotek"}, "ai_listing_set_include_selling_points": {"message": "Inkluder salgsargumenter"}, "ai_listing_set_included": {"message": "Indstil som inkluderet ordbibliotek"}, "ai_listing_set_selling_dictionary": {"message": "Sæt som salgsargumentbibliotek"}, "ai_listing_standard_product_title": {"message": "Standard produkttitel"}, "ai_listing_translated_title": {"message": "Oversat titel"}, "ai_listing_visit_chatGPT": {"message": "Navštivte ChatGPT"}, "ai_listing_what_other_keywords": {"message": "Hvilke andre søgeord kræves til titlen?"}, "aliprice_coupons_apply_again": {"message": "Ansøg igen"}, "aliprice_coupons_apply_coupons": {"message": "<PERSON><PERSON><PERSON>"}, "aliprice_coupons_apply_success": {"message": "Fundet kupon: Spar $amount$", "placeholders": {"amount": {"content": "$1"}}}, "aliprice_coupons_applying": {"message": "Tester koder for de bedste tilbud..."}, "aliprice_coupons_applying_desc": {"message": "Tjek ud: $code$", "placeholders": {"code": {"content": "$1"}}}, "aliprice_coupons_back_to_checkout": {"message": "Fortsæt til Checkout"}, "aliprice_coupons_found_coupons": {"message": "Vi fandt $amount$ kuponer", "placeholders": {"amount": {"content": "$1"}}}, "aliprice_coupons_found_coupons_desc": {"message": "Klar til at betale? Lad os sikre os\ndu får den bedste pris!"}, "aliprice_coupons_no_coupon_aviable": {"message": "De koder virkede ikke.\nNo biggie – det er du allerede\nfå den bedste pris."}, "aliprice_coupons_toolbar_btn": {"message": "<PERSON><PERSON> k<PERSON>er"}, "aliww_translate": {"message": "Aliwangwang Chat <PERSON>"}, "aliww_translate_supports": {"message": "Support: 1688 & Taobao"}, "amazon_extended_keywords_Keywords": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "amazon_extended_keywords_copy_all": {"message": "<PERSON><PERSON><PERSON> alle"}, "amazon_extended_keywords_more": {"message": "<PERSON><PERSON>"}, "an_lei_ji_xiao_liang_pai_xu": {"message": "Sorter efter kumulativt salg"}, "an_lei_xing_cha_kan": {"message": "Type"}, "an_yue_dai_xiao_pai_xu": {"message": "Rangering efter dropshipping-salg"}, "apra_btn__cat_name": {"message": "Anmeldelser analyse"}, "apra_chart__name": {"message": "Procentdel af produktsalg efter land"}, "apra_chart__update_at": {"message": "Opdateringstid $time$", "placeholders": {"time": {"content": "$1"}}}, "apra_name": {"message": "Salgsstatistik for lande"}, "auto_opening": {"message": "Åbner automatisk om $num$ sekunder", "placeholders": {"num": {"content": "$1"}}}, "auto_page_next_x_pages": {}, "average_days_listed": {"message": "Gennemsnit på lagerdage"}, "average_hui_fu_lv": {"message": "Gennemsnitlig svarprocent"}, "average_ping_gong_ying_shang_deng_ji": {"message": "Gennemsnitligt leverandørniveau"}, "average_price": {"message": "Gennemsnitspris"}, "average_qi_ding_liang": {"message": "Gennemsnitlig MOQ"}, "average_rating": {"message": "Gennemsnitlig vurdering"}, "average_ren_zheng_gong_ying_nian_chang": {"message": "Gennemsnitlige år med certificering"}, "average_revenue": {"message": "Gennemsnitlig omsætning"}, "average_revenue_per_product": {"message": "Samlet omsætning ÷ Antal produkter"}, "average_sales": {"message": "Gennemsnitligt salg"}, "average_sales_per_product": {"message": "Samlet salg ÷ Antal produkter"}, "bao_han": {"message": "Indeholder"}, "bao_zheng_jin": {"message": "<PERSON><PERSON>"}, "bian_ti_shu": {"message": "Variationer"}, "biao_ti": {"message": "Titel"}, "blacklist_add_blacklist": {"message": "Bloker denne butik"}, "blacklist_address_incorrect": {"message": "<PERSON><PERSON><PERSON> er forkert. <PERSON><PERSON><PERSON> sød at tjekke det."}, "blacklist_blacked_out": {"message": "Butikken er blevet blokeret"}, "blacklist_blacklist": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "blacklist_no_records_yet": {"message": "Ingen rekord endnu!"}, "blue_rocket": {"message": "로켓배송"}, "brand_name": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "btn_aliprice_agent__daigou": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "btn_aliprice_agent__dropshipping": {"message": "Dropshipping"}, "btn_have_a_try": {"message": "<PERSON><PERSON><PERSON><PERSON> det"}, "btn_refresh": {"message": "Opdater"}, "btn_try_it_now": {"message": "<PERSON>røv det nu"}, "btn_txt_view_on_aliprice": {"message": "Visning på AliPrice"}, "bu_bao_han": {"message": "Indeholder ikke"}, "bulk_copy_links": {"message": "Massekopiering af links"}, "bulk_copy_products": {"message": "Massekopieringsprodukter"}, "cai_gou_zi_xun": {"message": "Kunde service"}, "cai_gou_zi_xun__desc": {"message": "<PERSON><PERSON><PERSON><PERSON> svarrate på tre minutter"}, "can_ping_lei_xing": {"message": "Type"}, "cao_zuo": {"message": "Operation"}, "chan_pin_ID": {"message": "Produkt ID"}, "chan_pin_e_wai_xin_xi": {"message": "Produkt ekstra info"}, "chan_pin_lian_jie": {"message": "Produktlink"}, "cheng_li_shi_jian": {"message": "Etableringstidspunkt"}, "city__aba": {"message": "Aba"}, "city__aksu": {"message": "<PERSON><PERSON><PERSON>"}, "city__alaer": {"message": "<PERSON><PERSON><PERSON>"}, "city__altai": {"message": "Altai"}, "city__alxaleague": {"message": "Alxa League"}, "city__ankang": {"message": "Ankan<PERSON>"}, "city__anqing": {"message": "Anqing"}, "city__anshan": {"message": "<PERSON><PERSON>"}, "city__anshun": {"message": "<PERSON><PERSON><PERSON>"}, "city__anyang": {"message": "Anyang"}, "city__baicheng": {"message": "Baicheng"}, "city__baise": {"message": "Baise"}, "city__baisha": {"message": "Baisha"}, "city__baishan": {"message": "Baishan"}, "city__baiyin": {"message": "<PERSON><PERSON><PERSON>"}, "city__baoding": {"message": "<PERSON>od<PERSON>"}, "city__baoji": {"message": "<PERSON><PERSON><PERSON>"}, "city__baoshan": {"message": "<PERSON><PERSON><PERSON>"}, "city__baoting": {"message": "Baoting"}, "city__baotou": {"message": "<PERSON><PERSON><PERSON>"}, "city__bayannur": {"message": "Bayannur"}, "city__bayinguoleng": {"message": "Bayinguoleng"}, "city__bazhong": {"message": "Bazhong"}, "city__beihai": {"message": "Beihai"}, "city__bengbu": {"message": "<PERSON><PERSON><PERSON>"}, "city__benxi": {"message": "Benxi"}, "city__bijie": {"message": "Bijie"}, "city__binzhou": {"message": "Binzhou"}, "city__bortala": {"message": "<PERSON><PERSON><PERSON>"}, "city__bozhou": {"message": "Bozhou"}, "city__cangzhou": {"message": "Cangzhou"}, "city__chamdo": {"message": "<PERSON><PERSON><PERSON>"}, "city__changchun": {"message": "<PERSON><PERSON><PERSON>"}, "city__changde": {"message": "<PERSON><PERSON>"}, "city__changhua": {"message": "Changhua"}, "city__changji": {"message": "Changji"}, "city__changjiang": {"message": "Changjiang"}, "city__changsha": {"message": "Changsha"}, "city__changzhi": {"message": "Changzhi"}, "city__changzhou": {"message": "Changzhou"}, "city__chaohu": {"message": "<PERSON><PERSON>"}, "city__chaoyang": {"message": "Chaoyang"}, "city__chaozhou": {"message": "Chaozhou"}, "city__chengde": {"message": "Chengde"}, "city__chengdu": {"message": "Chengdu"}, "city__chengmai": {"message": "<PERSON><PERSON><PERSON>"}, "city__chenzhou": {"message": "Chenzhou"}, "city__chiayi": {"message": "<PERSON><PERSON><PERSON>"}, "city__chifeng": {"message": "<PERSON><PERSON>"}, "city__chizhou": {"message": "Chizhou"}, "city__chongzuo": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__chuxiong": {"message": "Chuxiong"}, "city__chuzhou": {"message": "Chuzhou"}, "city__dali": {"message": "<PERSON><PERSON>"}, "city__dalian": {"message": "<PERSON><PERSON>"}, "city__dandong": {"message": "Dandong"}, "city__danzhou": {"message": "Danzhou"}, "city__daqing": {"message": "Daqing"}, "city__datong": {"message": "<PERSON><PERSON><PERSON>"}, "city__daxinganling": {"message": "<PERSON><PERSON>'<PERSON>ling"}, "city__dazhou": {"message": "Dazhou"}, "city__dehong": {"message": "<PERSON><PERSON>"}, "city__deyang": {"message": "Deyang"}, "city__dezhou": {"message": "Dezhou"}, "city__dingan": {"message": "Ding'an"}, "city__dingxi": {"message": "Dingxi"}, "city__diqing": {"message": "Diqing"}, "city__dongfang": {"message": "<PERSON><PERSON>g"}, "city__dongguan": {"message": "Dongguan"}, "city__dongying": {"message": "<PERSON><PERSON>"}, "city__enshi": {"message": "<PERSON><PERSON>"}, "city__ezhou": {"message": "Ezhou"}, "city__fangchenggang": {"message": "Fangchenggang"}, "city__foshan": {"message": "<PERSON><PERSON><PERSON>"}, "city__fushun": {"message": "<PERSON><PERSON><PERSON>"}, "city__fuxin": {"message": "Fuxin"}, "city__fuyang": {"message": "Fuyang"}, "city__fuzhou": {"message": "Fuzhou"}, "city__gannan": {"message": "<PERSON><PERSON><PERSON>"}, "city__ganzhou": {"message": "Ganzhou"}, "city__ganzi": {"message": "Ganzi"}, "city__guangan": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__guangyuan": {"message": "Guangyuan"}, "city__guangzhou": {"message": "Guangzhou"}, "city__guigang": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__guilin": {"message": "<PERSON><PERSON><PERSON>"}, "city__guiyang": {"message": "Guiyang"}, "city__guolok": {"message": "Guolok"}, "city__guyuan": {"message": "<PERSON><PERSON>"}, "city__haibei": {"message": "Haibei"}, "city__haidong": {"message": "Haidong"}, "city__haikou": {"message": "Hai<PERSON><PERSON>"}, "city__hainan": {"message": "Hainan"}, "city__haixi": {"message": "Haixi"}, "city__hami": {"message": "<PERSON><PERSON>"}, "city__handan": {"message": "<PERSON><PERSON>"}, "city__hangzhou": {"message": "Hangzhou"}, "city__hanzhong": {"message": "Hanzhong"}, "city__harbin": {"message": "<PERSON><PERSON><PERSON>"}, "city__hebi": {"message": "<PERSON><PERSON>"}, "city__hechi": {"message": "<PERSON><PERSON>"}, "city__hefei": {"message": "<PERSON><PERSON><PERSON>"}, "city__hegang": {"message": "<PERSON><PERSON><PERSON>"}, "city__heihe": {"message": "<PERSON><PERSON><PERSON>"}, "city__hengshui": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__hengyang": {"message": "Hengyang"}, "city__heyuan": {"message": "<PERSON><PERSON>"}, "city__heze": {"message": "<PERSON><PERSON>"}, "city__hezhou": {"message": "Hezhou"}, "city__hohhot": {"message": "Hohhot"}, "city__honghe": {"message": "<PERSON><PERSON>"}, "city__hongkongisland": {"message": "Hong Kong Island"}, "city__hotan": {"message": "<PERSON><PERSON>"}, "city__hsinchu": {"message": "<PERSON><PERSON><PERSON>"}, "city__huaian": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__huaibei": {"message": "<PERSON><PERSON><PERSON>"}, "city__huaihua": {"message": "Huaihua"}, "city__huaisouth": {"message": "Huai South"}, "city__hualien": {"message": "<PERSON><PERSON><PERSON>"}, "city__huanggang": {"message": "<PERSON><PERSON><PERSON>"}, "city__huangnan": {"message": "Huangnan"}, "city__huangshan": {"message": "<PERSON><PERSON>"}, "city__huangshi": {"message": "<PERSON><PERSON>"}, "city__huizhou": {"message": "Huizhou"}, "city__huludao": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__hulunbuir": {"message": "Hulunbuir"}, "city__huzhou": {"message": "Huzhou"}, "city__jiamusi": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__jian": {"message": "<PERSON><PERSON><PERSON>"}, "city__jiangmen": {"message": "Jiangmen"}, "city__jiaozuo": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__jiaxing": {"message": "Jiaxing"}, "city__jiayuguan": {"message": "Jiayuguan"}, "city__jieyang": {"message": "<PERSON><PERSON><PERSON>"}, "city__jilin": {"message": "<PERSON><PERSON>"}, "city__jinan": {"message": "<PERSON><PERSON>"}, "city__jinchang": {"message": "Jinchang"}, "city__jincheng": {"message": "Jincheng"}, "city__jingdezhen": {"message": "Jingdez<PERSON>"}, "city__jingmen": {"message": "Jingmen"}, "city__jingzhou": {"message": "Jingzhou"}, "city__jinhua": {"message": "Jinhua"}, "city__jining": {"message": "Jining"}, "city__jinzhong": {"message": "Jinzhong"}, "city__jinzhou": {"message": "Jinzhou"}, "city__jiujiang": {"message": "Jiujiang"}, "city__jiuquan": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__jixi": {"message": "Ji<PERSON>"}, "city__kaifeng": {"message": "Kaifeng"}, "city__kaohsiung": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__karamay": {"message": "<PERSON><PERSON><PERSON>"}, "city__kashgar": {"message": "Ka<PERSON><PERSON>"}, "city__keelung": {"message": "Keelung"}, "city__kinmen": {"message": "Kinmen"}, "city__kizilsu": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__kowloon": {"message": "Kowloon"}, "city__kunming": {"message": "Ku<PERSON><PERSON>"}, "city__laibin": {"message": "<PERSON><PERSON>"}, "city__laiwu": {"message": "<PERSON><PERSON>"}, "city__langfang": {"message": "<PERSON><PERSON><PERSON>"}, "city__lanzhou": {"message": "Lanzhou"}, "city__ledong": {"message": "<PERSON><PERSON>"}, "city__leshan": {"message": "<PERSON><PERSON>"}, "city__lhasa": {"message": "Lhasa"}, "city__liangshan": {"message": "Liangshan"}, "city__lianyungang": {"message": "Lianyungang"}, "city__liaocheng": {"message": "<PERSON><PERSON><PERSON>"}, "city__liaoyang": {"message": "<PERSON><PERSON><PERSON>"}, "city__liaoyuan": {"message": "<PERSON><PERSON><PERSON>"}, "city__lienchiang": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__lijiang": {"message": "Lijiang"}, "city__lincang": {"message": "Lincang"}, "city__linfen": {"message": "Linfen"}, "city__lingao": {"message": "Lingao"}, "city__lingshui": {"message": "<PERSON><PERSON><PERSON>"}, "city__linxia": {"message": "Lin<PERSON>"}, "city__linyi": {"message": "<PERSON><PERSON>"}, "city__lishui": {"message": "Li<PERSON><PERSON>"}, "city__liupanshui": {"message": "Liupanshu<PERSON>"}, "city__liuzhou": {"message": "Liuzhou"}, "city__longnan": {"message": "<PERSON><PERSON>"}, "city__longyan": {"message": "<PERSON><PERSON>"}, "city__loudi": {"message": "<PERSON><PERSON>"}, "city__luan": {"message": "<PERSON><PERSON><PERSON>"}, "city__luohe": {"message": "<PERSON><PERSON><PERSON>"}, "city__luoyang": {"message": "<PERSON><PERSON><PERSON>"}, "city__luzhou": {"message": "Luzhou"}, "city__lüliang": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__maanshan": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__macauoutlyingislands": {"message": "Macau Outlying Islands"}, "city__macaupeninsula": {"message": "Macau Peninsula"}, "city__maoming": {"message": "<PERSON><PERSON>"}, "city__meishan": {"message": "<PERSON><PERSON>"}, "city__meizhou": {"message": "Meizhou"}, "city__mianyang": {"message": "Mianyang"}, "city__miaoli": {"message": "<PERSON><PERSON>"}, "city__mudanjiang": {"message": "Mudanjiang"}, "city__nagqu": {"message": "<PERSON>g<PERSON>u"}, "city__nanchang": {"message": "Nanchang"}, "city__nanchong": {"message": "<PERSON><PERSON><PERSON>"}, "city__nanjing": {"message": "Nanjing"}, "city__nanning": {"message": "Nanning"}, "city__nanping": {"message": "<PERSON><PERSON>"}, "city__nantong": {"message": "Nantong"}, "city__nantou": {"message": "<PERSON><PERSON><PERSON>"}, "city__nanyang": {"message": "Nanyang"}, "city__neijiang": {"message": "Neijiang"}, "city__newterritories": {"message": "New Territories"}, "city__ngari": {"message": "<PERSON><PERSON>"}, "city__ningbo": {"message": "Ningbo"}, "city__ningde": {"message": "<PERSON><PERSON><PERSON>"}, "city__nujiang": {"message": "Nujiang"}, "city__nyingchi": {"message": "Nyingchi"}, "city__ordos": {"message": "Ordos"}, "city__panjin": {"message": "Panjin"}, "city__panzhihua": {"message": "Pan Zhihua"}, "city__penghu": {"message": "<PERSON><PERSON><PERSON>"}, "city__pingdingshan": {"message": "Pingdingshan"}, "city__pingliang": {"message": "<PERSON><PERSON><PERSON>"}, "city__pingtung": {"message": "<PERSON><PERSON><PERSON>"}, "city__pingxiang": {"message": "<PERSON><PERSON><PERSON>"}, "city__puer": {"message": "<PERSON>u'er"}, "city__putian": {"message": "<PERSON><PERSON>"}, "city__puyang": {"message": "P<PERSON><PERSON>"}, "city__qiandongnan": {"message": "Qiandongnan"}, "city__qianjiang": {"message": "Qianjiang"}, "city__qiannan": {"message": "<PERSON><PERSON><PERSON>"}, "city__qianxinan": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__qingdao": {"message": "Qingdao"}, "city__qingyang": {"message": "Qingyang"}, "city__qingyuan": {"message": "Qingyuan"}, "city__qinhuangdao": {"message": "Qinhuangdao"}, "city__qinzhou": {"message": "Qinzhou"}, "city__qionghai": {"message": "Qionghai"}, "city__qiongzhong": {"message": "Qiongzhong"}, "city__qiqihar": {"message": "Qiqihar"}, "city__qitaihe": {"message": "<PERSON><PERSON><PERSON>"}, "city__quanzhou": {"message": "Quanzhou"}, "city__qujing": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__quzhou": {"message": "Quzhou"}, "city__rizhao": {"message": "<PERSON><PERSON><PERSON>"}, "city__sanmenxia": {"message": "Sanmenxia"}, "city__sanming": {"message": "Sanming"}, "city__sanya": {"message": "Sanya"}, "city__shangluo": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__shangqiu": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__shangrao": {"message": "<PERSON><PERSON><PERSON>"}, "city__shannan": {"message": "Shannan"}, "city__shantou": {"message": "<PERSON><PERSON><PERSON>"}, "city__shanwei": {"message": "Shanwei"}, "city__shaoguan": {"message": "Shaoguan"}, "city__shaoxing": {"message": "Shaoxing"}, "city__shaoyang": {"message": "Shaoyang"}, "city__shennongjiaforestarea": {"message": "Shennongjia Forest Area"}, "city__shenyang": {"message": "Shenyang"}, "city__shenzhen": {"message": "Shenzhen"}, "city__shigatse": {"message": "Shigat<PERSON>"}, "city__shihezi": {"message": "<PERSON><PERSON><PERSON>"}, "city__shijiazhuang": {"message": "Shijiazhuang"}, "city__shiyan": {"message": "<PERSON><PERSON>"}, "city__shizuishan": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__shuangyashan": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__shuozhou": {"message": "Shuozhou"}, "city__siping": {"message": "<PERSON><PERSON>"}, "city__songyuan": {"message": "Songyuan"}, "city__suihua": {"message": "Su<PERSON>ua"}, "city__suining": {"message": "Suining"}, "city__suizhou": {"message": "<PERSON><PERSON><PERSON>"}, "city__suqian": {"message": "<PERSON><PERSON><PERSON>"}, "city__suzhou": {"message": "Suzhou"}, "city__tacheng": {"message": "Tacheng"}, "city__taian": {"message": "Tai'an"}, "city__taichung": {"message": "Taichung"}, "city__tainan": {"message": "Tainan"}, "city__taipei": {"message": "Taipei"}, "city__taitung": {"message": "<PERSON><PERSON><PERSON>"}, "city__taiyuan": {"message": "Taiyuan"}, "city__taizhou": {"message": "Taizhou"}, "city__tangshan": {"message": "Tangshan"}, "city__taoyuan": {"message": "Taoyuan"}, "city__tianmen": {"message": "Tianmen"}, "city__tianshui": {"message": "<PERSON><PERSON><PERSON>"}, "city__tieling": {"message": "Tieling"}, "city__tongchuan": {"message": "<PERSON><PERSON><PERSON>"}, "city__tonghua": {"message": "Tonghua"}, "city__tongliao": {"message": "<PERSON><PERSON><PERSON>"}, "city__tongling": {"message": "Tongling"}, "city__tongren": {"message": "<PERSON><PERSON>"}, "city__tumushuke": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__tunchang": {"message": "Tunchang"}, "city__turpan": {"message": "<PERSON><PERSON><PERSON>"}, "city__ulanqab": {"message": "Ulanqab"}, "city__urumqi": {"message": "Urumqi"}, "city__wanning": {"message": "Wanning"}, "city__weifang": {"message": "<PERSON><PERSON><PERSON>"}, "city__weihai": {"message": "Weihai"}, "city__weinan": {"message": "Weinan"}, "city__wenchang": {"message": "Wenchang"}, "city__wenshan": {"message": "<PERSON><PERSON>"}, "city__wenzhou": {"message": "Wenzhou"}, "city__wuhai": {"message": "Wu<PERSON>"}, "city__wuhan": {"message": "<PERSON><PERSON>"}, "city__wuhu": {"message": "<PERSON><PERSON>"}, "city__wujiaqu": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__wuwei": {"message": "<PERSON><PERSON>"}, "city__wuxi": {"message": "Wuxi"}, "city__wuzhishan": {"message": "Wuzhishan"}, "city__wuzhong": {"message": "Wuzhong"}, "city__wuzhou": {"message": "Wuzhou"}, "city__xiamen": {"message": "Xiamen"}, "city__xian": {"message": "Xi'<PERSON>"}, "city__xiangfan": {"message": "<PERSON><PERSON><PERSON>"}, "city__xiangtan": {"message": "Xiangtan"}, "city__xiangxi": {"message": "Xiangxi"}, "city__xianning": {"message": "Xianning"}, "city__xiantao": {"message": "Xiantao"}, "city__xianyang": {"message": "<PERSON><PERSON><PERSON>"}, "city__xiaogan": {"message": "<PERSON><PERSON>"}, "city__xilingol": {"message": "Xilingol"}, "city__xinganleague": {"message": "Xing'an League"}, "city__xingtai": {"message": "Xingtai"}, "city__xining": {"message": "Xining"}, "city__xinxiang": {"message": "Xinxiang"}, "city__xinyang": {"message": "Xinyang"}, "city__xinyu": {"message": "Xinyu"}, "city__xinzhou": {"message": "Xinzhou"}, "city__xishuangbanna": {"message": "Xishuangbanna"}, "city__xuancheng": {"message": "Xuancheng"}, "city__xuchang": {"message": "Xuchang"}, "city__xuzhou": {"message": "Xuzhou"}, "city__yaan": {"message": "Ya'an"}, "city__yanan": {"message": "Yan'<PERSON>"}, "city__yanbian": {"message": "Yanbian"}, "city__yancheng": {"message": "Yancheng"}, "city__yangjiang": {"message": "Yangjiang"}, "city__yangquan": {"message": "<PERSON><PERSON><PERSON>"}, "city__yangzhou": {"message": "Yangzhou"}, "city__yantai": {"message": "Yantai"}, "city__yibin": {"message": "<PERSON><PERSON>"}, "city__yichang": {"message": "Yichang"}, "city__yichun": {"message": "<PERSON><PERSON><PERSON>"}, "city__yilan": {"message": "Yilan"}, "city__yili": {"message": "<PERSON><PERSON>"}, "city__yinchuan": {"message": "<PERSON><PERSON><PERSON>"}, "city__yingkou": {"message": "<PERSON><PERSON><PERSON>"}, "city__yingtan": {"message": "Yingtan"}, "city__yiwu": {"message": "<PERSON><PERSON>"}, "city__yiyang": {"message": "Yiyang"}, "city__yongzhou": {"message": "Yongzhou"}, "city__yueyang": {"message": "<PERSON><PERSON><PERSON>"}, "city__yulin": {"message": "Yulin"}, "city__yuncheng": {"message": "Yuncheng"}, "city__yunfu": {"message": "<PERSON><PERSON>"}, "city__yunlin": {"message": "<PERSON><PERSON>"}, "city__yushu": {"message": "Yushu"}, "city__yuxi": {"message": "Yuxi"}, "city__zaozhuang": {"message": "Zaozhuang"}, "city__zhangjiajie": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__zhangjiakou": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__zhangye": {"message": "<PERSON><PERSON>"}, "city__zhangzhou": {"message": "Zhangzhou"}, "city__zhanjiang": {"message": "Zhanjiang"}, "city__zhaoqing": {"message": "Zhaoqing"}, "city__zhaotong": {"message": "Zhaotong"}, "city__zhengzhou": {"message": "Zhengzhou"}, "city__zhenjiang": {"message": "Zhenjiang"}, "city__zhongshan": {"message": "Zhongshan"}, "city__zhongwei": {"message": "Zhongwei"}, "city__zhoukou": {"message": "<PERSON><PERSON><PERSON>"}, "city__zhoushan": {"message": "Zhoushan"}, "city__zhuhai": {"message": "Zhu<PERSON>"}, "city__zhumadian": {"message": "Zhumadian"}, "city__zhuzhou": {"message": "Zhuzhou"}, "city__zibo": {"message": "Zibo"}, "city__zigong": {"message": "Zigong"}, "city__ziyang": {"message": "<PERSON><PERSON><PERSON>"}, "city__zunyi": {"message": "<PERSON><PERSON><PERSON>"}, "click_copy_product_info": {"message": "Klik på Kopier produktoplysninger"}, "commmon_txt_expired": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "common__date_range_12m": {"message": "1 år"}, "common__date_range_1m": {"message": "1 måned"}, "common__date_range_1w": {"message": "En uge"}, "common__date_range_2w": {"message": "2 uger"}, "common__date_range_3m": {"message": "3 måneder"}, "common__date_range_3w": {"message": "3 uger"}, "common__date_range_6m": {"message": "6 måneder"}, "common_btn_cancel": {"message": "Afbestille"}, "common_btn_close": {"message": "<PERSON><PERSON><PERSON>"}, "common_btn_save": {"message": "<PERSON><PERSON><PERSON>"}, "common_btn_setting": {"message": "Opsætning"}, "common_email": {"message": "E-mail"}, "common_error_msg_no_data": {"message": "Ingen data"}, "common_error_msg_no_result": {"message": "Beklager, intet resultat fundet."}, "common_favorites": {"message": "Foretrukne"}, "common_feedback": {"message": "<PERSON><PERSON><PERSON>"}, "common_help": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "common_loading": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "common_login": {"message": "<PERSON>g på"}, "common_logout": {"message": "Log ud"}, "common_no": {"message": "Ingen"}, "common_powered_by_aliprice": {"message": "Drevet af AliPrice.com"}, "common_setting": {"message": "Indstilling"}, "common_sign_up": {"message": "Tilmelde"}, "common_system_upgrading_title": {"message": "Systemopgradering"}, "common_system_upgrading_txt": {"message": "<PERSON><PERSON><PERSON><PERSON> det senere"}, "common_txt__currency": {"message": "betalings<PERSON><PERSON><PERSON>"}, "common_txt__video_tutorial": {"message": "Video tutorial"}, "common_txt_ago_time": {"message": "$time$ dage siden", "placeholders": {"time": {"content": "$1"}}}, "common_txt_all_product": {"message": "alle"}, "common_txt_analysis": {"message": "Analyse"}, "common_txt_basically_used": {"message": "<PERSON><PERSON><PERSON> aldrig brugt"}, "common_txt_biaoti_link": {"message": "Titel + link"}, "common_txt_biaoti_link_dian_pu": {"message": "Titel + link + butiksnavn"}, "common_txt_blacklist": {"message": "Blokliste"}, "common_txt_cancel": {"message": "Ophæve"}, "common_txt_category": {"message": "<PERSON><PERSON><PERSON>"}, "common_txt_chakan": {"message": "Kontrollere"}, "common_txt_colors": {"message": "farver"}, "common_txt_confirm": {"message": "Bekræfte"}, "common_txt_copied": {"message": "<PERSON><PERSON><PERSON>"}, "common_txt_copy": {"message": "<PERSON><PERSON>"}, "common_txt_copy_link": {"message": "Kopier link"}, "common_txt_copy_title": {"message": "<PERSON><PERSON><PERSON><PERSON> titel"}, "common_txt_copy_title__link": {"message": "<PERSON><PERSON>r titel og link"}, "common_txt_day": {"message": "himmel"}, "common_txt_delete": {"message": "Slet"}, "common_txt_dian_pu_link": {"message": "Ko<PERSON>ér butiksnavn + link"}, "common_txt_download": {"message": "He<PERSON>"}, "common_txt_downloaded": {"message": "Download"}, "common_txt_export_as_csv": {"message": "Eksporter Excel"}, "common_txt_export_as_txt": {"message": "Eksporter tekst"}, "common_txt_fail": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "common_txt_format": {"message": "Format"}, "common_txt_get": {"message": "få"}, "common_txt_incert_selection": {"message": "Invertere valg"}, "common_txt_install": {"message": "Installere"}, "common_txt_load_failed": {"message": "Kunne ikke indlæses"}, "common_txt_month": {"message": "må<PERSON>"}, "common_txt_more": {"message": "<PERSON><PERSON>"}, "common_txt_new_unused": {"message": "Helt ny, ubrugt"}, "common_txt_next": {"message": "<PERSON><PERSON><PERSON>"}, "common_txt_no_limit": {"message": "Ubegrænset"}, "common_txt_no_noticeable": {"message": "Ingen synlige ridser eller snavs"}, "common_txt_on_sale": {"message": "Tilgængelig"}, "common_txt_opt_in_out": {"message": "Tænd sluk"}, "common_txt_order": {"message": "Bestille"}, "common_txt_others": {"message": "<PERSON>"}, "common_txt_overall_poor_condition": {"message": "Generelt dårlig stand"}, "common_txt_patterns": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "common_txt_platform": {"message": "Platforme"}, "common_txt_please_select": {"message": "<PERSON><PERSON><PERSON><PERSON> ven<PERSON>t"}, "common_txt_prev": {"message": "<PERSON><PERSON><PERSON>"}, "common_txt_price": {"message": "<PERSON><PERSON>"}, "common_txt_privacy_policy": {"message": "Fortrolighedspolitik"}, "common_txt_product_condition": {"message": "Produktstatus"}, "common_txt_rating": {"message": "Bed<PERSON><PERSON>lse"}, "common_txt_ratings": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "common_txt_reload": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "common_txt_reset": {"message": "Nulstil"}, "common_txt_review": {"message": "Anmeldelse"}, "common_txt_sale": {"message": "Tilgængelig"}, "common_txt_same": {"message": "<PERSON><PERSON>"}, "common_txt_scratches_and_dirt": {"message": "Med ridser og snavs"}, "common_txt_search_title": {"message": "Søgningstitel"}, "common_txt_select_all": {"message": "<PERSON><PERSON><PERSON><PERSON> alle"}, "common_txt_selected": {"message": "valgte"}, "common_txt_share": {"message": "Del"}, "common_txt_sold": {"message": "solgt"}, "common_txt_sold_out": {"message": "udsolgt"}, "common_txt_some_scratches": {"message": "Enkelte ridser og snavs"}, "common_txt_sort_by": {"message": "Sorter efter"}, "common_txt_state": {"message": "Status"}, "common_txt_success": {"message": "Succes"}, "common_txt_sys_err": {"message": "systemfejl"}, "common_txt_today": {"message": "I dag"}, "common_txt_total": {"message": "alle"}, "common_txt_unselect_all": {"message": "Invertere valg"}, "common_txt_upload_image": {"message": "Upload billede"}, "common_txt_visit": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "common_txt_whitelist": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "common_txt_wildberries": {"message": "Wildberries"}, "common_txt_year": {"message": "<PERSON><PERSON>"}, "common_yes": {"message": "<PERSON>a"}, "compare_tool_btn_clear_all": {"message": "Slet alt"}, "compare_tool_btn_compare": {"message": "Sammenligne"}, "compare_tool_btn_contact": {"message": "Kontakt"}, "compare_tool_tips_max_compared": {"message": "Tilføj op til $maxComparedCount$", "placeholders": {"maxComparedCount": {"content": "$1"}}}, "configure_notifiactions": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON> medd<PERSON><PERSON>"}, "contact_us": {"message": "Kontakt os"}, "context_menu_screenshot_search": {"message": "Optag for at søge efter billede"}, "context_menus_aliprice_search_by_image": {"message": "<PERSON><PERSON><PERSON> efter billede på AliPrice"}, "context_menus_goote_trans": {"message": "Oversæt side/Vis original"}, "context_menus_search_by_image": {"message": "<PERSON>øg efter billede på $storeName$", "placeholders": {"storeName": {"content": "$1"}}}, "context_menus_search_by_image_capture": {"message": "Optag til $storeName$", "placeholders": {"storeName": {"content": "$1"}}}, "context_menus_translator": {"message": "<PERSON><PERSON> for at oversætte"}, "converter_modal_amount_placeholder": {"message": "In<PERSON><PERSON> beløb her"}, "converter_modal_btn_convert": {"message": "konvertere"}, "converter_modal_exchange_rate_source": {"message": "Dataene kommer fra $boc$ udenlandsk valutakurs Opdateringstid: $updatedAt$", "placeholders": {"boc": {"content": "$1"}, "updatedAt": {"content": "$2"}}}, "converter_modal_name": {"message": "Valuta <PERSON>gni<PERSON>"}, "converter_modal_search_placeholder": {"message": "søgevaluta"}, "copy_all_contact_us_notice": {"message": "Denne side er ikke understøttet på nuværende tidspunkt, kontakt os venligst"}, "copy_product_info": {"message": "<PERSON><PERSON><PERSON><PERSON> produkt<PERSON>"}, "copy_suggest_search_kw": {"message": "<PERSON><PERSON><PERSON> dropdown lister"}, "country__han_gou": {"message": "Sydkorea"}, "country__ri_ben": {"message": "Japan"}, "country__yue_nan": {"message": "Vietnam"}, "currency_convert__custom": {"message": "Brugerdefineret valutakurs"}, "currency_convert__sync_server": {"message": "Synkroniser server"}, "dang_ri_fa_huo": {"message": "Forsendelse samme dag"}, "dao_chu_quan_dian_shang_pin": {"message": "Eksporter alle butiksprodukter"}, "dao_chu_wei_CSV": {"message": "Eksport"}, "dao_chu_zi_duan": {"message": "Eksporter felter"}, "delivery_address": {"message": "<PERSON>ering<PERSON><PERSON><PERSON>"}, "delivery_company": {"message": "Leveringsfirma"}, "di_zhi": {"message": "adresse"}, "dian_ji_cha_xun": {"message": "<PERSON><PERSON> for at forespørge"}, "dian_pu_ID": {"message": "Butiks-id"}, "dian_pu_di_zhi": {"message": "<PERSON><PERSON><PERSON> adresse"}, "dian_pu_lian_jie": {"message": "Butikslink"}, "dian_pu_ming": {"message": "Butiksnavn"}, "dian_pu_ming_cheng": {"message": "Butiksnavn"}, "dian_pu_shang_pin_zong_hsu": {"message": "Samlet antal produkter i butikken: $num$", "placeholders": {"num": {"content": "$1"}}}, "dian_pu_xin_xi": {"message": "<PERSON><PERSON>"}, "ding_zai_zuo_ce": {"message": "naglet til venstre"}, "disable_old_version_tips_disable_btn_title": {"message": "Deak<PERSON><PERSON> den gamle version"}, "download_image__SKU_variant_images": {"message": "SKU variant billeder"}, "download_image__assume": {"message": "For eksempel har vi 2 billeder, produkt1.jpg og produkt2.gif.\nimg_{$no$} vil blive omdøbt til img_01.jpg, img_02.gif;\n{$group$}_{$no$} vil blive omdøbt til main_image_01.jpg, main_image_02.gif;", "placeholders": {"no": {"content": "$3"}, "group": {"content": "$2"}}}, "download_image__batch_download": {"message": "Batch download"}, "download_image__combined_image": {"message": "Kombineret produktdetaljebillede"}, "download_image__continue_downloading": {"message": "Fortsæt med at downloade"}, "download_image__description_images": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON> billeder"}, "download_image__download_combined_image": {"message": "Download kombineret produktdetaljebillede"}, "download_image__download_zip": {"message": "Download zip"}, "download_image__enlarge_check": {"message": "<PERSON><PERSON><PERSON><PERSON> kun JPEG, JPG, GIF og PNG-billeder, maks<PERSON><PERSON> størrelse af et enkelt billede: 1600 * 1600"}, "download_image__enlarge_image": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "download_image__export": {"message": "Eksport"}, "download_image__height": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "download_image__ignore_videos": {"message": "<PERSON>en er blevet ignoreret, da den ikke kan eksporteres"}, "download_image__img_translate": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "download_image__main_image": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "download_image__multi_folder": {"message": "Multi-mappe"}, "download_image__name": {"message": "download billede"}, "download_image__notice_content": {"message": "Lad være med at markere \"<PERSON><PERSON><PERSON><PERSON> hvor hver fil skal gemmes før download\" i din browsers downloadindstillinger!!! Ellers vil der være masser af dialogbokse."}, "download_image__notice_ignore": {"message": "<PERSON><PERSON><PERSON><PERSON> ikke om denne besked igen"}, "download_image__order_number": {"message": "{$no$} serienummer; {$group$} gruppenavn; Tidsstempel for {$date$}", "placeholders": {"no": {"content": "$1"}, "group": {"content": "$2"}, "date": {"content": "$3"}}}, "download_image__overview": {"message": "Oversigt"}, "download_image__prompt_download_zip": {"message": "Der er for mange billeder, du må hellere downloade dem som en zip-mappe."}, "download_image__rename": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "download_image__rule": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "download_image__single_folder": {"message": "Enkelt mappe"}, "download_image__sku_image": {"message": "SKU billeder"}, "download_image__video": {"message": "video"}, "download_image__width": {"message": "Bredde"}, "download_reviews__download_images": {"message": "<PERSON><PERSON>"}, "download_reviews__dropdown_title": {"message": "<PERSON><PERSON>"}, "download_reviews__export_csv": {"message": "eksportere CSV"}, "download_reviews__no_images": {"message": "Prøvekopi: 0 billeder til download"}, "download_reviews__no_reviews": {"message": "Ingen kommentarer er tilgængelige for download!"}, "download_reviews__notice": {"message": "Tip:"}, "download_reviews__notice__chrome_settings": {"message": "Indstil Chrome-browseren til at spørge, hvor hver fil skal gemmes før download, indstil til \"Fra\""}, "download_reviews__notice__wait": {"message": "A<PERSON>hængigt af antallet af anmeldelser kan ventetiden være længere"}, "download_reviews__pages_list__all": {"message": "Alle"}, "download_reviews__pages_list__page": {"message": "Tidligere $page$ sider", "placeholders": {"page": {"content": "$1"}}}, "download_reviews__pages_list_title": {"message": "<PERSON><PERSON><PERSON>gsområ<PERSON>"}, "export_shopping_cart__csv_filed__details_url": {"message": "Produkt link"}, "export_shopping_cart__csv_filed__details_url_with_sku": {"message": "Echo SKU link"}, "export_shopping_cart__csv_filed__images": {"message": "Billed link"}, "export_shopping_cart__csv_filed__quantity": {"message": "<PERSON><PERSON>"}, "export_shopping_cart__csv_filed__sale_price": {"message": "<PERSON><PERSON>"}, "export_shopping_cart__csv_filed__specs": {"message": "specifikationer"}, "export_shopping_cart__csv_filed__store_name": {"message": "Butiksnavn"}, "export_shopping_cart__csv_filed__store_url": {"message": "Butikslink"}, "export_shopping_cart__csv_filed__title": {"message": "Produktnavn"}, "export_shopping_cart__export_btn": {"message": "Eksport"}, "export_shopping_cart__export_empty": {"message": "Vælg venligst et produkt!"}, "fa_huo_shi_jian": {"message": "Forsendelse"}, "favorite_add_email": {"message": "Tilføj e-mail"}, "favorite_add_favorites": {"message": "<PERSON><PERSON><PERSON> til favoritter"}, "favorite_added": {"message": "Tilføjet"}, "favorite_btn_add": {"message": "<PERSON><PERSON><PERSON><PERSON>."}, "favorite_btn_notify": {"message": "Spor pris"}, "favorite_cate_name_all": {"message": "Alle produkter"}, "favorite_current_price": {"message": "Nuvæ<PERSON><PERSON> pris"}, "favorite_due_date": {"message": "Forfaldsdato"}, "favorite_enable_notification": {"message": "Akt<PERSON><PERSON><PERSON> venligst e-mail-notifikationer"}, "favorite_expired": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "favorite_go_to_enable": {"message": "<PERSON><PERSON> til å<PERSON>ning"}, "favorite_msg_add_success": {"message": "Tilføjet til favoritter"}, "favorite_msg_del_success": {"message": "<PERSON><PERSON><PERSON> fra favoritter"}, "favorite_msg_failure": {"message": "Svigte! Opdater siden, og prøv igen."}, "favorite_please_add_email": {"message": "Tilføj venligst e-mail"}, "favorite_price_drop": {"message": "<PERSON>"}, "favorite_price_rise": {"message": "Op"}, "favorite_price_untracked": {"message": "<PERSON><PERSON> ikke sporet"}, "favorite_saved_price": {"message": "Gemt pris"}, "favorite_stop_tracking": {"message": "Stop med at spore"}, "favorite_sub_email_address": {"message": "E-mail-adresse for abonnement"}, "favorite_tracking_period": {"message": "Sporingsperiode"}, "favorite_tracking_prices": {"message": "Sporing af priser"}, "favorite_verify_email": {"message": "Bekræft e-mail"}, "favorites_list_remove_prompt_msg": {"message": "<PERSON>r du sikker på at slette den?"}, "favorites_update_button": {"message": "<PERSON><PERSON><PERSON> priser nu"}, "fen_lei": {"message": "<PERSON><PERSON><PERSON>"}, "fen_xia_yan_xuan": {"message": "Distributørens valg"}, "find_similar": {"message": "Find lignende"}, "first_ali_price_date": {"message": "<PERSON><PERSON><PERSON>, hvor den første gang blev fanget af AliPrice-crawleren"}, "fooview_coupons_modal_no_data": {"message": "<PERSON><PERSON> kuponer"}, "fooview_coupons_modal_title": {"message": "<PERSON><PERSON><PERSON>"}, "fooview_favorites__product_sub__tooltip_l1": {"message": "Pris < $lowerPrice$", "placeholders": {"lowerPrice": {"content": "$1"}}}, "fooview_favorites__product_sub__tooltip_l2": {"message": "eller pris > $higherPrice$", "placeholders": {"higherPrice": {"content": "$1"}}}, "fooview_favorites__product_sub__tooltip_l3": {"message": "Deadline"}, "fooview_favorites_error_msg_no_favorites": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON> favoritprodukter her for at modtage prisfaldalarm."}, "fooview_favorites_filter_latest": {"message": "Seneste"}, "fooview_favorites_filter_price_drop": {"message": "p<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "fooview_favorites_filter_price_up": {"message": "prisstigning"}, "fooview_favorites_modal_title": {"message": "Mine favoritter"}, "fooview_favorites_modal_title_title": {"message": "<PERSON><PERSON> <PERSON> AliPrice <PERSON>"}, "fooview_favorites_track_price": {"message": "At spore prisen"}, "fooview_price_history_app_price": {"message": "APP-pris:"}, "fooview_price_history_title": {"message": "<PERSON>ris historie"}, "fooview_product_list_feedback": {"message": "<PERSON><PERSON><PERSON>"}, "fooview_product_list_orders": {"message": "Ordre:% s"}, "fooview_product_list_price": {"message": "<PERSON><PERSON>"}, "fooview_reviews_error_msg_no_review": {"message": "<PERSON>i fandt ingen anmel<PERSON>ser for dette produkt."}, "fooview_reviews_filter_buyer_reviews": {"message": "<PERSON><PERSON><PERSON> fotos"}, "fooview_reviews_modal_title": {"message": "Anmeldelser"}, "fooview_same_product_choose_category": {"message": "<PERSON><PERSON><PERSON><PERSON> kate<PERSON>i"}, "fooview_same_product_filter_feedback": {"message": "<PERSON><PERSON><PERSON>"}, "fooview_same_product_filter_orders": {"message": "Ordre:% s"}, "fooview_same_product_filter_price": {"message": "<PERSON><PERSON>"}, "fooview_same_product_filter_rating": {"message": "Bed<PERSON><PERSON>lse"}, "fooview_same_product_modal_title": {"message": "Find det samme produkt"}, "fooview_same_product_search_by_image": {"message": "<PERSON><PERSON><PERSON> e<PERSON>"}, "fooview_seller_analysis_modal_title": {"message": "Sælgeranalyse"}, "for_12_months": {"message": "I 1 år"}, "for_12_months_list_pro": {"message": "12 måneder"}, "for_12_months_nei": {"message": "<PERSON><PERSON> for 12 m<PERSON><PERSON><PERSON>"}, "for_1_months": {"message": "1 måned"}, "for_1_months_nei": {"message": "<PERSON><PERSON> for 1 måned"}, "for_3_months": {"message": "I 3 måneder"}, "for_3_months_nei": {"message": "<PERSON><PERSON> for 3 måneder"}, "for_6_months": {"message": "I 6 måneder"}, "for_6_months_nei": {"message": "<PERSON><PERSON> for 6 må<PERSON><PERSON>"}, "for_9_months": {"message": "9 måneder"}, "for_9_months_nei": {"message": "<PERSON><PERSON> for 9 måneder"}, "fu_gou_lv": {"message": "Tilbagekøbskurs"}, "gao_liang_bu_tong_dian": {"message": "fremhæve forskelle"}, "gao_liang_guang_gao_chan_pin": {"message": "Fremhæv annonceprodukter"}, "geng_duo_xin_xi": {"message": "Mere info"}, "geng_xin_shi_jian": {"message": "<PERSON><PERSON><PERSON> tid"}, "get_store_products_fail_tip": {"message": "<PERSON><PERSON> på OK for at gå til verifikation for at sikre normal adgang"}, "gong_x_kuan_shang_pin": {"message": "I alt $amount$ produkter", "placeholders": {"amount": {"content": "$1"}}}, "gong_ying_shang": {"message": "Leverandør"}, "gong_ying_shang_ID": {"message": "Leverandør ID"}, "gong_ying_shang_deng_ji": {"message": "Leverandørvurdering"}, "gong_ying_shang_nian_zhan": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON> er ældre"}, "gong_ying_shang_xin_xi": {"message": "leverandøroplysninger"}, "gong_ying_shang_zhu_ye_lian_jie": {"message": "Link til leverandørens hjemmeside"}, "green_rocket": {"message": "로켓프레시"}, "grey_rocket": {"message": "로켓설치"}, "gu_ji_shou_jia": {"message": "Ansl<PERSON><PERSON> salgspris"}, "guan_jian_zi": {"message": "Søgeord"}, "guang_gao_chan_pin": {"message": "<PERSON><PERSON> produkter"}, "guang_gao_zhan_bi": {"message": "<PERSON><PERSON> forhold"}, "guo_ji_wu_liu_yun_fei": {"message": "International forsendelsesgebyr"}, "guo_lv_tiao_jian": {"message": "Filtre"}, "hao_ping_lv": {"message": "Positiv vurdering"}, "highest_price": {"message": "<PERSON><PERSON><PERSON>"}, "historical_trend": {"message": "Historisk tendens"}, "how_to_screenshot": {"message": "Hold venstre museknap nede for at vælge området, tryk på højre museknap eller Esc-tasten for at afslutte skærmbilledet"}, "howt_it_works": {"message": "<PERSON><PERSON><PERSON> det virker"}, "hui_fu_lv": {"message": "Svarp<PERSON><PERSON>"}, "hui_tou_lv": {"message": "returrate"}, "inquire_freightFee": {"message": "Fragtforespørgsel"}, "inquire_freightFee_Yuan": {"message": "Fragt/Yuan"}, "inquire_freightFee_province": {"message": "<PERSON><PERSON><PERSON>"}, "inquire_freightFee_the": {"message": "Fragten er $num$, hvilket betyder, at regionen har gratis fragt.", "placeholders": {"num": {"content": "$1"}}}, "is_ad": {"message": "Ad."}, "jia_ge": {"message": "<PERSON><PERSON>"}, "jia_ge_dan_wei": {"message": "Enhed"}, "jia_ge_qu_shi": {"message": "Trends"}, "jia_zai_n_ge_shang_pin": {"message": "Indlæs $num$-produkter", "placeholders": {"num": {"content": "$1"}}}, "jin_30_tian_xiao_liang_zhan_bi": {"message": "Procentdel af salgsvolumen inden for de seneste 30 dage"}, "jin_30_tian_xiao_shou_e_zhan_bi": {"message": "Procentdel af omsætning inden for de seneste 30 dage"}, "jin_90_tian_mai_jia_shu": {"message": "K<PERSON>bere inden for de sidste 90 dage"}, "jin_90_tian_xiao_shou_liang": {"message": "Salg i de sidste 90 dage"}, "jing_xuan_huo_yuan": {"message": "Udvalgte kilder"}, "jing_ying_mo_shi": {"message": "Forretningsmodel"}, "jiu_fen_jie_jue": {"message": "Konfliktløsning"}, "jiu_fen_jie_jue__desc": {"message": "Regnskab af sælgers butiksrettighedstvister"}, "jiu_fen_lv": {"message": "Tvistprocent"}, "jiu_fen_lv__desc": {"message": "<PERSON><PERSON> af ordrer med reklamationer gennemført inden for de seneste 30 dage og vurderet til at være sælgers eller begge parters ansvar"}, "kai_dian_ri_qi": {"message": "Åbningsdato"}, "keywords": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "kua_jin_Select_pan_huo": {"message": "Grænseoverskridende Vælg"}, "last15_days": {"message": "Sidste 15 dage"}, "last180_days": {"message": "<PERSON><PERSON> 180 dage"}, "last30_days": {"message": "I de sidste 30 dage"}, "last360_days": {"message": "Sidste 360 ​​dage"}, "last45_days": {"message": "Sidste 45 dage"}, "last60_days": {"message": "Sidste 60 dage"}, "last7_days": {"message": "Sidste 7 dage"}, "last90_days": {"message": "Sidste 90 dage"}, "last_30d_sales": {"message": "Sidste 30 dages salg"}, "lei_ji": {"message": "<PERSON>k<PERSON><PERSON><PERSON><PERSON>"}, "lei_ji_xiao_liang": {"message": "Total"}, "lei_ji_xiao_liang__desc": {"message": "Alt salg efter produkt på hylde"}, "lei_ji_xiao_liang_pai_xu__desc": {"message": "Akkumuleret salgsvolumen i de sidste 30 dage, sorteret fra høj til lav"}, "lian_xi_fang_shi": {"message": "Kontakt information"}, "list_time": {"message": "På hyldetid"}, "load_more": {"message": "Indlæs mere"}, "login_to_aliprice": {"message": "Log ind på AliPrice"}, "long_link": {"message": "Langt link"}, "lowest_price": {"message": "Lav"}, "mai_jia_shu": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "mao_li_lv": {"message": "Bru<PERSON><PERSON><PERSON>"}, "mobile_view__dkxbqy": {"message": "Åbn en ny fane"}, "mobile_view__sjdxq": {"message": "Detaljer i app"}, "mobile_view__sjdxqy": {"message": "Detaljeside i app"}, "mobile_view__smck": {"message": "<PERSON>an for at se"}, "mobile_view__smckms": {"message": "Brug kameraet eller appen til at scanne og se"}, "modified_failed": {"message": "<PERSON><PERSON><PERSON> mis<PERSON>es"}, "modified_successfully": {"message": "<PERSON><PERSON><PERSON> med succes"}, "nav_btn_favorites": {"message": "Mine samlinger"}, "nav_btn_package": {"message": "<PERSON><PERSON>"}, "nav_btn_product_info": {"message": "Om produktet"}, "nav_btn_viewed": {"message": "har set"}, "no_suggest_search_kw": {"message": "Loading..."}, "none": {"message": "Ingen"}, "normal_link": {"message": "Normalt link"}, "notice": {"message": "<PERSON><PERSON><PERSON>"}, "number_reviews": {"message": "Anmeldelser"}, "only_show_num": {"message": "Samlede produkter: $allnum$, Skjult: $hidenum2$", "placeholders": {"allnum": {"content": "$1"}, "hidenum2": {"content": "$2"}}}, "only_show_selected": {"message": "<PERSON><PERSON><PERSON> ikke markeret"}, "open": {"message": "<PERSON><PERSON>"}, "open_links": {"message": "Åbn links"}, "options_page_tab_check_links": {"message": "Tjek links"}, "options_page_tab_gernal": {"message": "Generel"}, "options_page_tab_notifications": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "options_page_tab_others": {"message": "<PERSON>"}, "options_page_tab_sbi": {"message": "<PERSON><PERSON><PERSON> e<PERSON>"}, "options_page_tab_shortcuts": {"message": "Genveje"}, "options_page_tab_shortcuts_title": {"message": "Skriftstørrelse til genveje"}, "options_page_tab_similar_products": {"message": "<PERSON><PERSON> produkter"}, "orange_rocket": {"message": "판매자로켓"}, "order_list_open_links_tip": {"message": "Flere produktlinks er ved at åbne"}, "order_list_sku_show_title": {"message": "Vis udvalgte varianter i de delte links"}, "orders_last30_days": {"message": "<PERSON>tal ordrer inden for de seneste 30 dage"}, "pTutorial_favorites_block1_desc1": {"message": "De produkter, du spores, er angivet her"}, "pTutorial_favorites_block1_title": {"message": "Foretrukne"}, "pTutorial_popup_block1_desc1": {"message": "En grøn etiket betyder, at der er prisfaldende produkter"}, "pTutorial_popup_block1_title": {"message": "Genveje og foretrukne"}, "pTutorial_price_history_block1_desc1": {"message": "<PERSON><PERSON> på \"<PERSON><PERSON> pris\", til<PERSON><PERSON><PERSON> produkter til favoritter. Når deres priser er faldet, modtager du underretninger"}, "pTutorial_price_history_block1_title": {"message": "Spor pris"}, "pTutorial_reviews_block1_desc1": {"message": "Købernes anmeldelser fra Itao og ægte fotos fra AliExpress feedback"}, "pTutorial_reviews_block1_title": {"message": "Anmeldelser"}, "pTutorial_reviews_block2_desc1": {"message": "Det er altid nyttigt at kontrollere anmeldelser fra andre købere"}, "pTutorial_same_products_block1_desc1": {"message": "Du kan sammenligne dem for at træffe det bedste valg"}, "pTutorial_same_products_block1_desc2": {"message": "<PERSON><PERSON> på 'Mere' for at \"Søg efter billede\""}, "pTutorial_same_products_block1_title": {"message": "<PERSON><PERSON> produkter"}, "pTutorial_same_products_by_image_search_block1_desc1": {"message": "Slip produktbillede der, og vælg en kategori"}, "pTutorial_same_products_by_image_search_block1_title": {"message": "<PERSON><PERSON><PERSON> e<PERSON>"}, "pTutorial_seller_analysis_block1_desc1": {"message": "Sælgers positive feedbackrate, feedback score og hvor længe sælgeren har været på markedet"}, "pTutorial_seller_analysis_block1_title": {"message": "<PERSON><PERSON><PERSON><PERSON> vurdering"}, "pTutorial_seller_analysis_block2_desc2": {"message": "Sælgers vurdering er baseret på 3 indekser: vare som beskrevet, Kommunikation Forsendelseshastighed"}, "pTutorial_seller_analysis_block3_desc3": {"message": "Vi bruger 3 farver og ikoner til at indikere sælgers tillidsniveauer"}, "page_count": {"message": "Antal sider"}, "pai_chu": {"message": "Udelukket"}, "pai_chu_HK_bu_yi_xia_xiaoshou": {"message": "Eksk<PERSON>r <PERSON>-beg<PERSON><PERSON><PERSON><PERSON>"}, "pai_chu_JP_bu_yi_xia_xiaoshou": {"message": "Ekskluder Japan-<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "pai_chu_KR_bu_yi_xia_xiaoshou": {"message": "Ekskluder Korea-Restricted"}, "pai_chu_KZ_bu_yi_xia_xiaoshou": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "pai_chu_MO_bu_yi_xia_xiaoshou": {"message": "<PERSON><PERSON>d <PERSON>-<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "pai_chu_RU_bu_yi_xia_xiaoshou": {"message": "Ekskluder Østeuropa-begrænset"}, "pai_chu_SA_bu_yi_xia_xiaoshou": {"message": "Ekskluder Saudi-Arabien-begrænset"}, "pai_chu_TW_bu_yi_xia_xiaoshou": {"message": "Ekskluder Taiwan-begrænset"}, "pai_chu_USA_bu_yi_xia_xiaoshou": {"message": "Udelad U.S.-beg<PERSON><PERSON><PERSON><PERSON>"}, "pai_chu_VN_bu_yi_xia_xiaoshou": {"message": "Ekskluder Vietnam-begræ<PERSON>t"}, "pai_chu_bu_yi_xia_xiaoshou": {"message": "<PERSON>ks<PERSON><PERSON><PERSON> begr<PERSON><PERSON><PERSON> varer"}, "payable_price_formula": {"message": "Pris + forsendelse + rabat"}, "pdd_check_retail_btn_txt": {"message": "<PERSON><PERSON><PERSON>"}, "pdd_pifa_to_retail_btn_txt": {"message": "Køb i detailhandlen"}, "pdp_copy_fail": {"message": "Kopiering mislykkedes!"}, "pdp_copy_success": {"message": "<PERSON><PERSON><PERSON> lykkedes!"}, "pdp_share_modal_subtitle": {"message": "<PERSON>, han/hun vil se dit valg."}, "pdp_share_modal_title": {"message": "Del dit valg"}, "pdp_share_screenshot": {"message": "<PERSON>"}, "pei_song": {"message": "Forsendelsesmetode"}, "pin_lei": {"message": "<PERSON><PERSON><PERSON>"}, "pin_zhi_ti_yan": {"message": "Produktkvalitet"}, "pin_zhi_ti_yan__desc": {"message": "Kvalitetsrefusionssats for sælgers butik"}, "pin_zhi_tui_kuan_lv": {"message": "Tilbagebetalingssats"}, "pin_zhi_tui_kuan_lv__desc": {"message": "<PERSON><PERSON> af ordrer, der kun er blevet refunderet og returneret inden for de seneste 30 dage"}, "ping_fen": {"message": "Bed<PERSON><PERSON>lse"}, "ping_jun_fa_huo_su_du": {"message": "Gennemsnitlig forsendelseshastighed"}, "pkgInfo_hide": {"message": "Logistik info: tænd/sluk"}, "pkgInfo_no_trace": {"message": "Ingen logistikoplysninger"}, "platform_name__1688": {"message": "1688"}, "platform_name__17zwd": {"message": "17zwd.com"}, "platform_name__51taoyang": {"message": "51taoyang.com"}, "platform_name__5ts": {"message": "5ts.com"}, "platform_name__91jf": {"message": "91jf.com"}, "platform_name__alibaba": {"message": "Alibaba.com"}, "platform_name__aliexpress": {"message": "AliExpress"}, "platform_name__amazon": {"message": "Amazon"}, "platform_name__bao66": {"message": "bao66.cn"}, "platform_name__chinagoods": {"message": "chinagoods.com"}, "platform_name__dhgate": {"message": "DHgate"}, "platform_name__e3hui": {"message": "e3hui.com"}, "platform_name__ebay": {"message": "Ebay"}, "platform_name__hznzcn": {"message": "hznzcn.com"}, "platform_name__jd": {"message": "JD"}, "platform_name__juyi5": {"message": "juyi5.cn"}, "platform_name__naver_shopping": {"message": "Naver Shopping"}, "platform_name__pinduoduo": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "platform_name__pinduoduo_pifa": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON> engros"}, "platform_name__shopee": {"message": "<PERSON>ee"}, "platform_name__sjxzw": {"message": "571xz.com"}, "platform_name__sooxie": {"message": "sooxie.com"}, "platform_name__taobao": {"message": "Taobao"}, "platform_name__taobao_lite": {"message": "Taobao Lite"}, "platform_name__vvic": {"message": "vvic.com"}, "platform_name__walmart": {"message": "Walmart"}, "platform_name__wsy": {"message": "wsy.com"}, "platform_name__xingfujie": {"message": "xingfujie.cn"}, "platform_name__yiwugo": {"message": "Yiwugo.com"}, "platform_name__yunchepin": {"message": "yunchepin.cn"}, "platform_name__zhaojiafang": {"message": "zhaojiafang.com"}, "popup_go_to_home": {"message": "<PERSON><PERSON><PERSON>"}, "popup_go_to_platform": {"message": "Gå til $name$", "placeholders": {"name": {"content": "$1"}}}, "popup_search_placeholder": {"message": "Jeg handler efter ..."}, "popup_track_package_btn_track": {"message": "SPORE"}, "popup_track_package_desc": {"message": "ALL-IN-ONE PAKKESPORING"}, "popup_track_package_search_placeholder": {"message": "Tracking nummer"}, "popup_translate_search_placeholder": {"message": "Oversæt og søg på $searchOn$", "placeholders": {"searchOn": {"content": "$1"}}}, "price_history": {"message": "<PERSON>ris historie"}, "price_history_chart_tip_ae": {"message": "Tip: <PERSON><PERSON><PERSON> af ordrer er det samlede antal ordrer siden lanceringen"}, "price_history_chart_tip_coupang": {"message": "Tip: <PERSON>upang sletter ordreoptællingen for svigagtige ordrer"}, "price_history_inm_1688_l1": {"message": "Installer venligst"}, "price_history_inm_1688_l2": {"message": "AliPrice Shopping Assistant for 1688"}, "price_history_panel_lowest_price": {"message": "Laveste pris:"}, "price_history_panel_tab_price_tracking": {"message": "<PERSON>ris historie"}, "price_history_panel_tab_seller_analysis": {"message": "Sælgeranalyse"}, "price_history_pro_modal_title": {"message": "Prishistorik & ordrehistorik"}, "privacy_consent__btn_agree": {"message": "Revidér samtykke til indsamling af data"}, "privacy_consent__btn_disable_all": {"message": "<PERSON><PERSON><PERSON> accepter"}, "privacy_consent__btn_enable_all": {"message": "<PERSON>ktiv<PERSON><PERSON> alle"}, "privacy_consent__btn_uninstall": {"message": "Fjer<PERSON>"}, "privacy_consent__desc_privacy": {"message": "<PERSON><PERSON><PERSON><PERSON>, at uden data eller cookies er nogle funktioner slået fra, fordi disse funktioner har brug for forklaring af data eller cookies, men du kan stadig bruge de andre funktioner."}, "privacy_consent__desc_privacy_L1": {"message": "Desværre fungerer det ikke uden data eller cookies, fordi vi har brug for forklaringen på data eller cookies."}, "privacy_consent__desc_privacy_L2": {"message": "<PERSON><PERSON> du ikke tillader os at indsamle disse oplysninger, skal du fjerne det."}, "privacy_consent__item_cookies_desc": {"message": "<PERSON><PERSON>, vi får kun dine valutadata i cookies, når vi handler online for at vise prishistorikken."}, "privacy_consent__item_cookies_title": {"message": "Nødvendige cookies"}, "privacy_consent__item_functional_desc_L1": {"message": "1. <PERSON><PERSON><PERSON><PERSON><PERSON> cookies i browseren for anonymt at identificere din computer eller enhed."}, "privacy_consent__item_functional_desc_L2": {"message": "2. Tilføj funktionelle data i tilføjelsesprogrammet for at arbejde med funktionen."}, "privacy_consent__item_functional_title": {"message": "Funktionelle og Analytics-cookies"}, "privacy_consent__more_desc": {"message": "<PERSON><PERSON><PERSON> opmærksom på, at vi ikke deler dine personlige data med andre virksomheder, og ingen annonceselskaber indsamler data via vores service."}, "privacy_consent__options__btn__desc": {"message": "For at bruge alle funktioner skal du tænde den."}, "privacy_consent__options__btn__label": {"message": "T<PERSON><PERSON> den"}, "privacy_consent__options__desc_L1": {"message": "Vi indsamler følgende data, der personligt identificerer dig:"}, "privacy_consent__options__desc_L2": {"message": "- cookies, vi modtager kun dine valutadata i cookies, når du handler online for at vise prishistorikken."}, "privacy_consent__options__desc_L3": {"message": "- og tilføj cookies i browseren for anonymt at identificere din computer eller enhed."}, "privacy_consent__options__desc_L4": {"message": "- andre anonyme data gør denne udvidelse mere bekvem."}, "privacy_consent__options__desc_L5": {"message": "Bemærk, at vi ikke deler dine personlige data med andre virk<PERSON>, og ingen annonceselskaber indsamler data via vores service."}, "privacy_consent__privacy_preferences": {"message": "Privacy præferencer"}, "privacy_consent__read_more": {"message": "<PERSON><PERSON>s mere >>"}, "privacy_consent__title_privacy": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "product_info": {"message": "Produkt info"}, "product_recommend__name": {"message": "<PERSON><PERSON> produkter"}, "product_research": {"message": "Produktforskning"}, "product_sub__email_desc": {"message": "E-mail med prisadvarsel"}, "product_sub__email_edit": {"message": "redigere"}, "product_sub__email_not_verified": {"message": "Bekræft venligst e-mail"}, "product_sub__email_required": {"message": "Angiv venligst e-mail"}, "product_sub__form_countdown": {"message": "Automatisk lukning efter $seconds$ sekunder", "placeholders": {"seconds": {"content": "$1"}}}, "product_sub__form_fail": {"message": "Kunne ikke tilføje påmindelse!"}, "product_sub__form_input_price": {"message": "<PERSON><PERSON><PERSON>"}, "product_sub__form_item_country": {"message": "nation"}, "product_sub__form_item_current_price": {"message": "Nuvæ<PERSON><PERSON> pris"}, "product_sub__form_item_duration": {"message": "spore"}, "product_sub__form_item_higher_price": {"message": "Eller pris>"}, "product_sub__form_item_invalid_higher_price": {"message": "Prisen skal være højere end $price$", "placeholders": {"price": {"content": "$1"}}}, "product_sub__form_item_invalid_lower_price": {"message": "Prisen skal være lavere end $price$", "placeholders": {"price": {"content": "$1"}}}, "product_sub__form_item_lower_price": {"message": "<PERSON><PERSON><PERSON> prisen <"}, "product_sub__form_submit": {"message": "Indsend"}, "product_sub__form_success": {"message": "Det lykkedes at tilføje påmindelse!"}, "product_sub__high_price_notify": {"message": "Giv mig besked om prisst<PERSON>inger"}, "product_sub__low_price_notify": {"message": "Giv mig besked om prisnedsættelser"}, "product_sub__modal_title": {"message": "Påmindelse om abonnementsprisændring"}, "province__an_hui": {"message": "<PERSON><PERSON>"}, "province__ao_men": {"message": "Macau"}, "province__bei_jing": {"message": "Beijing"}, "province__chong_qing": {"message": "Chongqing"}, "province__fu_jian": {"message": "Fujian"}, "province__gan_su": {"message": "Gansu"}, "province__guang_dong": {"message": "Guangdong"}, "province__guang_xi": {"message": "Guangxi"}, "province__gui_zhou": {"message": "Guizhou"}, "province__hai_nan": {"message": "Hainan"}, "province__he_bei": {"message": "Hebei"}, "province__he_nan": {"message": "<PERSON><PERSON>"}, "province__hei_long_jiang": {"message": "Heilongjiang"}, "province__hu_bei": {"message": "Hubei"}, "province__hu_nan": {"message": "Hunan"}, "province__ji_lin": {"message": "<PERSON><PERSON>"}, "province__jiang_su": {"message": "Jiangsu"}, "province__jiang_xi": {"message": "Jiangxi"}, "province__liao_ning": {"message": "Liaoning"}, "province__nei_meng_gu": {"message": "Inner Mongolia"}, "province__ning_xia": {"message": "Ningxia"}, "province__qing_hai": {"message": "Qinghai"}, "province__shan_dong": {"message": "Shandong"}, "province__shan_xi": {"message": "Shaanxi"}, "province__shang_hai": {"message": "Shanghai"}, "province__si_chuan": {"message": "Sichuan"}, "province__tai_wan": {"message": "Taiwan"}, "province__tian_jin": {"message": "Tianjin"}, "province__xi_zhang": {"message": "Tibet"}, "province__xiang_gang": {"message": "Hong Kong"}, "province__xin_jiang": {"message": "Xinjiang"}, "province__yun_nan": {"message": "Yunnan"}, "province__zhe_jiang": {"message": "Zhejiang"}, "purple_rocket": {"message": "로켓직구"}, "qi_ding_liang": {"message": "MOQ"}, "qi_ding_liang_qi_ding_jia": {"message": "MOQ & MOP"}, "qi_ye_mian_ji": {"message": "Virksomhedsområde"}, "qing_zhi_shao_xuan_ze_yi_ge_chan_pin": {"message": "<PERSON><PERSON><PERSON>g venligst mindst ét produkt"}, "qing_zhi_shao_xuan_ze_yi_ge_zi_duan": {"message": "<PERSON><PERSON><PERSON><PERSON> venligst mindst ét felt"}, "qu_deng_lu": {"message": "Log ind"}, "quan_guo_yan_xuan": {"message": "Globalt valg"}, "recommendation_popup_banner_btn_install": {"message": "Installer det"}, "recommendation_popup_banner_desc": {"message": "Vis prishistorik inden for 3/6 måneder, og prisfaldsmeddelelse"}, "region__all": {"message": "Alle regioner"}, "region__hai_wai": {"message": "Overseas"}, "region__hua_bei_qu": {"message": "North China"}, "region__hua_dong_qu": {"message": "East China"}, "region__hua_nan_qu": {"message": "South China"}, "region__hua_zhong_qu": {"message": "Central China"}, "region__jiang_zhe_lu": {"message": "Jiangsu, Zhejiang and Shanghai"}, "remove_web_limits": {"message": "Aktiver højreklik"}, "ren_zheng_gong_chang": {"message": "Cert<PERSON>ret fabrik"}, "ren_zheng_gong_ying_shang_nian_zhan": {"message": "<PERSON>r som certificeret leverandør"}, "required_to_aliprice_login": {"message": "Skal logge ind på AliPrice"}, "revenue_last30_days": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON> inden for de seneste 30 dage"}, "review_counts": {"message": "<PERSON><PERSON>"}, "rocket": {"message": "로켓"}, "ru_zhu_nian_xian": {"message": "Indgangsperiode"}, "sales_amount_last30_days": {"message": "Samlet salg inden for de seneste 30 dage"}, "sales_last30_days": {"message": "<PERSON><PERSON> inden for de seneste 30 dage"}, "sbi_alibaba_cate__accessories": {"message": "<PERSON><PERSON>h<PERSON><PERSON>"}, "sbi_alibaba_cate__aqfk": {"message": "Sikkerhed"}, "sbi_alibaba_cate__bags_cases": {"message": "<PERSON><PERSON> og kasser"}, "sbi_alibaba_cate__beauty": {"message": "Skønhed"}, "sbi_alibaba_cate__beverage": {"message": "<PERSON><PERSON>"}, "sbi_alibaba_cate__bgwh": {"message": "Kontorkultur"}, "sbi_alibaba_cate__bz": {"message": "<PERSON><PERSON>"}, "sbi_alibaba_cate__ccyj": {"message": "Køkkengrej"}, "sbi_alibaba_cate__clothes": {"message": "Beklædning"}, "sbi_alibaba_cate__cmgd": {"message": "Medieudsendelse"}, "sbi_alibaba_cate__coat_jacket": {"message": "Frakke og jakke"}, "sbi_alibaba_cate__consumer_electronics": {"message": "Forbrugerelektronik"}, "sbi_alibaba_cate__cryp": {"message": "Voksenprodukter"}, "sbi_alibaba_cate__csyp": {"message": "Sengeforing<PERSON>"}, "sbi_alibaba_cate__cwyy": {"message": "Havearbejde til kæledyr"}, "sbi_alibaba_cate__cysx": {"message": "Catering frisk"}, "sbi_alibaba_cate__dgdq": {"message": "Elektriker"}, "sbi_alibaba_cate__dl": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "sbi_alibaba_cate__dress_suits": {"message": "<PERSON><PERSON><PERSON> og dragter"}, "sbi_alibaba_cate__dszm": {"message": "Belysning"}, "sbi_alibaba_cate__dzqj": {"message": "Elektronisk apparat"}, "sbi_alibaba_cate__essb": {"message": "Brugt udstyr"}, "sbi_alibaba_cate__food": {"message": "Mad"}, "sbi_alibaba_cate__fspj": {"message": "T<PERSON><PERSON> og tilbehør"}, "sbi_alibaba_cate__furniture": {"message": "<PERSON><PERSON><PERSON>"}, "sbi_alibaba_cate__fzpg": {"message": "<PERSON><PERSON><PERSON>"}, "sbi_alibaba_cate__ghjq": {"message": "Personlig pleje"}, "sbi_alibaba_cate__gt": {"message": "<PERSON><PERSON><PERSON>"}, "sbi_alibaba_cate__gyp": {"message": "H<PERSON>ndværk"}, "sbi_alibaba_cate__hb": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "sbi_alibaba_cate__hfcz": {"message": "<PERSON><PERSON><PERSON><PERSON> makeup"}, "sbi_alibaba_cate__hg": {"message": "Kemisk industri"}, "sbi_alibaba_cate__jg": {"message": "Forarbejdning"}, "sbi_alibaba_cate__jianccai": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "sbi_alibaba_cate__jichuang": {"message": "<PERSON><PERSON> v<PERSON>ø<PERSON>"}, "sbi_alibaba_cate__jjry": {"message": "Husholdningens daglige brug"}, "sbi_alibaba_cate__jtys": {"message": "Transport"}, "sbi_alibaba_cate__jxsb": {"message": "Udstyr"}, "sbi_alibaba_cate__jxwj": {"message": "Mekanisk hardware"}, "sbi_alibaba_cate__jydq": {"message": "Husholdningsapparater"}, "sbi_alibaba_cate__jzjc": {"message": "Byggematerialer til boligforbedring"}, "sbi_alibaba_cate__jzjf": {"message": "Hjemmetekstiler"}, "sbi_alibaba_cate__mj": {"message": "Håndklæde"}, "sbi_alibaba_cate__myyp": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "sbi_alibaba_cate__nanz": {"message": "<PERSON><PERSON><PERSON>"}, "sbi_alibaba_cate__nvz": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "sbi_alibaba_cate__ny": {"message": "Energi"}, "sbi_alibaba_cate__others": {"message": "<PERSON>"}, "sbi_alibaba_cate__qcyp": {"message": "Auto tilbehør"}, "sbi_alibaba_cate__qmpj": {"message": "Autodele"}, "sbi_alibaba_cate__shoes": {"message": "<PERSON><PERSON>"}, "sbi_alibaba_cate__smdn": {"message": "Digital computer"}, "sbi_alibaba_cate__snqj": {"message": "Opbevaring og rengøring"}, "sbi_alibaba_cate__spjs": {"message": "<PERSON> drikke"}, "sbi_alibaba_cate__swfw": {"message": "Forretningsservice"}, "sbi_alibaba_cate__toys_hobbies": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "sbi_alibaba_cate__trousers_skirt": {"message": "Bukser & nederdel"}, "sbi_alibaba_cate__txcp": {"message": "Kommunikationsprodukter"}, "sbi_alibaba_cate__tz": {"message": "B<PERSON><PERSON>tøj"}, "sbi_alibaba_cate__underwear": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "sbi_alibaba_cate__wjgj": {"message": "Hardwareværktøjer"}, "sbi_alibaba_cate__xgpi": {"message": "<PERSON><PERSON><PERSON> tasker"}, "sbi_alibaba_cate__xmhz": {"message": "projektsamarbejde"}, "sbi_alibaba_cate__xs": {"message": "<PERSON><PERSON><PERSON>"}, "sbi_alibaba_cate__ydfs": {"message": "Sportstøj"}, "sbi_alibaba_cate__ydhw": {"message": "Udendørs sport"}, "sbi_alibaba_cate__yjkc": {"message": "Metallurgiske mineraler"}, "sbi_alibaba_cate__yqyb": {"message": "Instrumentering"}, "sbi_alibaba_cate__ys": {"message": "Print"}, "sbi_alibaba_cate__yyby": {"message": "Lægebehandling"}, "sbi_alibaba_cn_kj_90mjs": {"message": "<PERSON><PERSON> købere inden for de sidste 90 dage"}, "sbi_alibaba_cn_kj_90xsl": {"message": "Salgsvolumen inden for de sidste 90 dage"}, "sbi_alibaba_cn_kj_gjsj": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON> pris"}, "sbi_alibaba_cn_kj_gjwlyf": {"message": "Internationalt forsendelsesgebyr"}, "sbi_alibaba_cn_kj_gjyf": {"message": "Forsendelsesgebyr"}, "sbi_alibaba_cn_kj_gssj": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON> pris"}, "sbi_alibaba_cn_kj_lr": {"message": "Profit"}, "sbi_alibaba_cn_kj_lrgs": {"message": "Fortjeneste = estimeret pris x fortjenstmargen"}, "sbi_alibaba_cn_kj_pjfhsd": {"message": "Gennemsnitlig leveringshastighed"}, "sbi_alibaba_cn_kj_qtfy": {"message": "andet gebyr"}, "sbi_alibaba_cn_kj_qtfygs": {"message": "<PERSON> om<PERSON> = estimeret pris x andet omkostningsforhold"}, "sbi_alibaba_cn_kj_spjg": {"message": "<PERSON><PERSON>"}, "sbi_alibaba_cn_kj_spzl": {"message": "<PERSON><PERSON><PERSON>"}, "sbi_alibaba_cn_kj_szd": {"message": "Beliggenhed"}, "sbi_alibaba_cn_kj_unit_ge": {"message": "Stykker"}, "sbi_alibaba_cn_kj_unit_jian": {"message": "Stykker"}, "sbi_alibaba_cn_kj_unit_ke": {"message": "Gram"}, "sbi_alibaba_cn_kj_unit_ren": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "sbi_alibaba_cn_kj_unit_tai": {"message": "Stykker"}, "sbi_alibaba_cn_kj_unit_tao": {"message": "<PERSON><PERSON><PERSON>"}, "sbi_alibaba_cn_kj_unit_tian": {"message": "<PERSON><PERSON>"}, "sbi_alibaba_cn_kj_zwbj": {"message": "Ingen pris"}, "sbi_aliprice_alibaba_cn__jiage": {"message": "pris"}, "sbi_aliprice_alibaba_cn__keshou": {"message": "<PERSON><PERSON><PERSON> til salg"}, "sbi_aliprice_alibaba_cn__moren": {"message": "Standard"}, "sbi_aliprice_alibaba_cn__queding": {"message": "<PERSON>"}, "sbi_aliprice_alibaba_cn__xiaoliang": {"message": "<PERSON><PERSON>"}, "sbi_aliprice_alibaba_cn_cate__jiaju": {"message": "<PERSON><PERSON><PERSON>"}, "sbi_aliprice_alibaba_cn_cate__linghsi": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "sbi_aliprice_alibaba_cn_cate__meizhuang": {"message": "makeup"}, "sbi_aliprice_alibaba_cn_cate__neiyi": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "sbi_aliprice_alibaba_cn_cate__peishi": {"message": "<PERSON><PERSON>h<PERSON><PERSON>"}, "sbi_aliprice_alibaba_cn_cate__pinchui": {"message": "Flaske drink"}, "sbi_aliprice_alibaba_cn_cate__qita": {"message": "<PERSON>"}, "sbi_aliprice_alibaba_cn_cate__qunzhuang": {"message": "<PERSON><PERSON><PERSON>"}, "sbi_aliprice_alibaba_cn_cate__shangyi": {"message": "<PERSON><PERSON><PERSON>"}, "sbi_aliprice_alibaba_cn_cate__shuma": {"message": "Elektronik"}, "sbi_aliprice_alibaba_cn_cate__wanju": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "sbi_aliprice_alibaba_cn_cate__xiangbao": {"message": "Bagage"}, "sbi_aliprice_alibaba_cn_cate__xiazhuang": {"message": "Bunde"}, "sbi_aliprice_alibaba_cn_cate__xiezi": {"message": "sko"}, "sbi_aliprice_cate__apparel": {"message": "Beklædning"}, "sbi_aliprice_cate__automobiles_motorcycles": {"message": "Biler og motorcykler"}, "sbi_aliprice_cate__beauty_health": {"message": "Skønhed og sundhed"}, "sbi_aliprice_cate__cellphones_telecommunications": {"message": "Mobiltelefoner og telekommunikation"}, "sbi_aliprice_cate__computer_office": {"message": "Computer og kontor"}, "sbi_aliprice_cate__consumer_electronics": {"message": "Forbrugerelektronik"}, "sbi_aliprice_cate__education_office_supplies": {"message": "Uddannelse og kontorartikler"}, "sbi_aliprice_cate__electronic_components_supplies": {"message": "Elektroniske komponenter og forsyninger"}, "sbi_aliprice_cate__furniture": {"message": "<PERSON><PERSON><PERSON>"}, "sbi_aliprice_cate__hair_extensions_wigs": {"message": "Hårforlængelse og parykker"}, "sbi_aliprice_cate__home_garden": {"message": "Hus & have"}, "sbi_aliprice_cate__home_improvement": {"message": "Renovering af hjemmet"}, "sbi_aliprice_cate__jewelry_accessories": {"message": "Smykker og tilbehør"}, "sbi_aliprice_cate__luggage_bags": {"message": "Bagage og tasker"}, "sbi_aliprice_cate__mother_kids": {"message": "<PERSON><PERSON> og børn"}, "sbi_aliprice_cate__novelty_special_use": {"message": "Nyhed og speciel brug"}, "sbi_aliprice_cate__security_protection": {"message": "Sikkerhed og beskyttelse"}, "sbi_aliprice_cate__shoes": {"message": "<PERSON><PERSON>"}, "sbi_aliprice_cate__sports_entertainment": {"message": "Sport og underholdning"}, "sbi_aliprice_cate__toys_hobbies": {"message": "Legetøj og hobbyer"}, "sbi_aliprice_cate__watches": {"message": "<PERSON><PERSON>"}, "sbi_aliprice_cate__weddings_events": {"message": "Bryllupper & begivenheder"}, "sbi_btn_capture_txt": {"message": "<PERSON><PERSON>"}, "sbi_btn_source_now_txt": {"message": "<PERSON><PERSON> nu"}, "sbi_button__chat_with_me": {"message": "Chat med mig"}, "sbi_button__contact_supplier": {"message": "Kontakt"}, "sbi_button__hide_on_this_site": {"message": "Vis ikke på dette websted"}, "sbi_button__open_settings": {"message": "Kon<PERSON><PERSON><PERSON> søgning efter <PERSON>e"}, "sbi_capture_shortcut_tip": {"message": "eller tryk på \"Enter\" -tasten på tastaturet"}, "sbi_capturing_tip": {"message": "Optagelse"}, "sbi_composed_rating_45": {"message": "4,5 - 5,0 st<PERSON>ner"}, "sbi_crop_and_search": {"message": "<PERSON><PERSON><PERSON>"}, "sbi_crop_start": {"message": "Brug skærmbillede"}, "sbi_err_captcha_action": {"message": "Verificere"}, "sbi_err_captcha_for_alibaba_cn": {"message": "Har brug for bekræftelse, upload venligst et billede for at bekræfte. (Se $video_tutorial$ eller prøv at rydde cookies)", "placeholders": {"feedback": {"content": "$1"}, "video_tutorial": {"content": "$1"}}}, "sbi_err_captcha_for_aliprice": {"message": "Usædvanlig trafik. Bekræft venligst"}, "sbi_err_captcha_for_taobao": {"message": "Taobao beder dig om at bekræfte. Upload venligst et billede manuelt og søg for at bekræfte det. Denne fejl skyldes \"TaoBao-søgning efter billede\" ny bekræftelsespolitik, vi foresl<PERSON><PERSON>, at klagen bekræftes for hyppigt på Taobao $feedback$.", "placeholders": {"feedback": {"content": "$1"}}}, "sbi_err_captcha_for_taobao_feedback": {"message": "feedback"}, "sbi_err_captcha_msg": {"message": "$platform$ kræver, at du uploader et billede for at søge eller fuldfører sikkerhedsbekræftelse for at fjerne søgerestriktioner", "placeholders": {"platform": {"content": "$1"}}}, "sbi_err_checking_if_low_version": {"message": "Tjek om det er den nyeste version"}, "sbi_err_cookie_btn_clear": {"message": "Ryd cookies"}, "sbi_err_cookie_for_alibaba_cn": {"message": "Ryd 1688 cookies? (<PERSON><PERSON> for at logge på igen)"}, "sbi_err_desperate_feature_pdd": {"message": "Billedsøgningsfunktionen er blevet flyttet til Pinduoduo Search by Image extension."}, "sbi_err_hidden_skill_of_improve_search_rate": {"message": "<PERSON><PERSON><PERSON> forbedrer man succesraten for billedsøgning?"}, "sbi_err_img_undersize": {"message": "Billede > $imgMinimumSize$", "placeholders": {"imgMinimumSize": {"content": "$1"}}}, "sbi_err_login_required": {"message": "Log ind $loginSite$", "placeholders": {"loginSite": {"content": "$1"}}}, "sbi_err_login_required_action": {"message": "<PERSON>g på"}, "sbi_err_low_version": {"message": "Installer den nyeste version ($latestVersion$)", "placeholders": {"latestVersion": {"content": "$1"}}}, "sbi_err_low_version_action": {"message": "He<PERSON>"}, "sbi_err_need_help": {"message": "Brug for hjælp"}, "sbi_err_network": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, sørg for at du kan besøge webstedet"}, "sbi_err_not_low_version": {"message": "Den seneste version er installeret ($latestVersion$)", "placeholders": {"latestVersion": {"content": "$1"}}}, "sbi_err_try_again": {"message": "<PERSON><PERSON><PERSON><PERSON> igen"}, "sbi_err_try_again_action": {"message": "<PERSON><PERSON><PERSON><PERSON> igen"}, "sbi_err_visit_and_try": {"message": "Prøv igen, eller besøg $website$ for at prøve igen", "placeholders": {"website": {"content": "$1"}}}, "sbi_err_visit_site": {"message": "Besøg startsiden for $siteName$", "placeholders": {"siteName": {"content": "$1"}}}, "sbi_fail_tip": {"message": "Indlæsningen mislykkedes. Opdater siden og prøv igen."}, "sbi_kuajing_filter_area": {"message": "Areal"}, "sbi_kuajing_filter_au": {"message": "Australien"}, "sbi_kuajing_filter_btn_confirm": {"message": "Bekræfte"}, "sbi_kuajing_filter_de": {"message": "Tyskland"}, "sbi_kuajing_filter_destination_country": {"message": "Destinationsland"}, "sbi_kuajing_filter_es": {"message": "Spanien"}, "sbi_kuajing_filter_estimate": {"message": "Skøn"}, "sbi_kuajing_filter_estimate_price": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON> pris"}, "sbi_kuajing_filter_estimate_price_formula": {"message": "Estimeret prisformel = (råvarepris + international logistikfragt)/(1 - fortjenstmargen - andet omkostningsforhold)"}, "sbi_kuajing_filter_fr": {"message": "<PERSON><PERSON>"}, "sbi_kuajing_filter_kw_placeholder": {"message": "Indtast søgeord for at matche titlen"}, "sbi_kuajing_filter_logistics": {"message": "Logistik skabelon"}, "sbi_kuajing_filter_logistics_china_post": {"message": "Kina Post luftpost"}, "sbi_kuajing_filter_logistics_discount": {"message": "Logistik rabat"}, "sbi_kuajing_filter_logistics_epacket": {"message": "epacket"}, "sbi_kuajing_filter_logistics_price_formula": {"message": "International logistikfragt = (vægt x forsendelsespris + registreringsgebyr) x (1 - rabat)"}, "sbi_kuajing_filter_others_fee": {"message": "<PERSON><PERSON> gebyr"}, "sbi_kuajing_filter_profit_percent": {"message": "Overskudsgrad"}, "sbi_kuajing_filter_prop": {"message": "Egenskaber"}, "sbi_kuajing_filter_ru": {"message": "Rusland"}, "sbi_kuajing_filter_total": {"message": "Match $count$ lignende varer", "placeholders": {"count": {"content": "$1"}}}, "sbi_kuajing_filter_uk": {"message": "U.K"}, "sbi_kuajing_filter_usa": {"message": "Amerika"}, "sbi_login_punish_title__pdd_pifa": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON> engros"}, "sbi_msg_no_result": {"message": "Intet resultat fundet. Log ind på $loginSite$ eller prøv et andet billede", "placeholders": {"loginSite": {"content": "$1"}}}, "sbi_msg_no_result_check_support_page_l1": {"message": "Midlertidigt ikke tilgængelig til Safari, brug venligst $supportPage$.", "placeholders": {"supportPage": {"content": "$1"}}}, "sbi_msg_no_result_check_support_page_l2": {"message": "Chrome-browser og dens udvidelser"}, "sbi_msg_no_result_reinstall_l1": {"message": "Ingen resultater fundet, log ind på $loginSite$ eller prøv et andet billede, eller geninstaller $latestExtUrl$", "placeholders": {"loginSite": {"content": "$1"}, "latestExtUrl": {"content": "$2"}}}, "sbi_msg_no_result_reinstall_l2": {"message": "Den seneste version af", "placeholders": {}}, "sbi_search_by_selected_area": {"message": "<PERSON><PERSON>"}, "sbi_shipping_": {"message": "Forsendelse samme dag"}, "sbi_specify_category": {"message": "<PERSON><PERSON>:"}, "sbi_start_crop": {"message": "<PERSON><PERSON><PERSON><PERSON> o<PERSON>r<PERSON>"}, "sbi_store_name_alibabaCNKuaJing": {"message": "1688 oversøiske"}, "sbi_tutorial_btn_more": {"message": "<PERSON><PERSON><PERSON> m<PERSON>"}, "sbi_tutorial_find_coupons_on_taobao": {"message": "<PERSON>-k<PERSON><PERSON>"}, "sbi_txt__empty_retry": {"message": "Be<PERSON>ger, der blev ikke fundet nogen resultater. Prøv igen."}, "sbi_txt__min_order": {"message": "<PERSON><PERSON> best<PERSON>"}, "sbi_visiting": {"message": "Gennemse"}, "sbi_yiwugo__jiagexiangtan": {"message": "Kontakt sælger for prisen"}, "sbi_yiwugo__qigou": {"message": "$num$ stykker (MOQ)", "placeholders": {"num": {"content": "$1"}}}, "sbi_yiwugo__xing": {"message": "<PERSON><PERSON><PERSON>"}, "searchByImage_screenshot": {"message": "Et-klik screenshot"}, "searchByImage_search": {"message": "<PERSON><PERSON>g efter de samme varer med et enkelt klik"}, "searchByImage_size_type": {"message": "Filstørrelsen må ikke være større end $num$ MB, kun $type$", "placeholders": {"num": {"content": "$1"}, "type": {"content": "$2"}}}, "search_by_image_progress_analysing": {"message": "<PERSON><PERSON><PERSON><PERSON> billedet"}, "search_by_image_progress_searching": {"message": "<PERSON><PERSON><PERSON> efter produkter"}, "search_by_image_progress_sending": {"message": "<PERSON><PERSON><PERSON>e"}, "search_by_image_response_rate": {"message": "Svarprocent: $responsRate$ af købere, der kontaktede denne leverandør, modtog et svar inden for $responseInHour$ timer.", "placeholders": {"responsRate": {"content": "$1"}, "responseInHour": {"content": "$2"}}}, "search_by_keyword": {"message": "<PERSON><PERSON><PERSON> efter nø<PERSON>:"}, "select_country_language_modal_title_country": {"message": "Land"}, "select_country_language_modal_title_language": {"message": "Sp<PERSON>"}, "select_country_region_modal_title": {"message": "Vælg et land / en region"}, "select_language_modal_title": {"message": "<PERSON><PERSON><PERSON><PERSON> sprog:"}, "select_shop": {"message": "<PERSON><PERSON><PERSON><PERSON> but<PERSON>"}, "sellers_count": {"message": "Antal sælgere på den aktuelle side"}, "sellers_count_per_page": {"message": "Antal sælgere på den aktuelle side"}, "service_score": {"message": "Omfattende servicevurdering"}, "set_shortcut_keys": {"message": "Indstil genvejstaster"}, "setting_logo_title": {"message": "Shoppingassistent"}, "setting_modal_options_position_title": {"message": "Plug-in position"}, "setting_modal_options_position_value_left": {"message": "<PERSON><PERSON><PERSON> hjø<PERSON>"}, "setting_modal_options_position_value_right": {"message": "<PERSON><PERSON><PERSON><PERSON>jø<PERSON>"}, "setting_modal_options_theme_title": {"message": "<PERSON><PERSON> farve"}, "setting_modal_options_theme_value_dark": {"message": "<PERSON><PERSON><PERSON>"}, "setting_modal_options_theme_value_light": {"message": "Lys"}, "setting_modal_title": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "setting_options_country_title": {"message": "Land / region"}, "setting_options_hover_zoom_desc": {"message": "Hold musen over for at zoome ind"}, "setting_options_hover_zoom_title": {"message": "Hold musen over zoom"}, "setting_options_jd_coupon_desc": {"message": "Fundet kupon på JD.com"}, "setting_options_jd_coupon_title": {"message": "JD.com kupon"}, "setting_options_language_title": {"message": "Sp<PERSON>"}, "setting_options_price_drop_alert_desc": {"message": "<PERSON><PERSON>r prisen på produkter i Min favorit falder, modtager du push-besked."}, "setting_options_price_drop_alert_title": {"message": "Prisfald alarm"}, "setting_options_price_history_on_list_page_desc": {"message": "Vis prishistorik på produktsøgningssiden"}, "setting_options_price_history_on_list_page_title": {"message": "Prishistorik (listeside)"}, "setting_options_price_history_on_produt_page_desc": {"message": "Vis produkthistorik på siden med produktdetaljer"}, "setting_options_price_history_on_produt_page_title": {"message": "Prishistorik (side med detaljer)"}, "setting_options_sales_analysis_desc": {"message": "<PERSON><PERSON><PERSON> statistik over pris, salgsvolumen, antal sælgere og butikssalgsforhold på $platforms$ produktlistesiden", "placeholders": {"platforms": {"content": "$1"}}}, "setting_options_sales_analysis_title": {"message": "Salgsanalyse"}, "setting_options_save_success_msg": {"message": "Succes"}, "setting_options_tacking_price_title": {"message": "Alarm om prisændring"}, "setting_options_value_off": {"message": "Af"}, "setting_options_value_on": {"message": "På"}, "setting_pkg_quick_view_desc": {"message": "Support: 1688 & Taobao"}, "setting_saved_message": {"message": "Ændringerne blev gemt"}, "setting_section_enable_platform_title": {"message": "Tænd sluk"}, "setting_section_setting_title": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "setting_section_shortcuts_title": {"message": "Genveje"}, "settings_aliprice_agent__desc": {"message": "Vist på $platforms$ produktdetaljeside", "placeholders": {"platforms": {"content": "$1"}}}, "settings_aliprice_agent__title": {"message": "<PERSON>øb-for-mig"}, "settings_copy_link__desc": {"message": "Vises på produktdetaljesiden"}, "settings_copy_link__title": {"message": "<PERSON><PERSON><PERSON><PERSON> knap og Søg titel"}, "settings_currency_desc__for_detail": {"message": "Support 1688 produktdetaljer side"}, "settings_currency_desc__for_list": {"message": "<PERSON><PERSON><PERSON> e<PERSON> (inkluderer 1688/1688 overseas / Taobao)"}, "settings_currency_desc__for_sbi": {"message": "<PERSON><PERSON><PERSON><PERSON> pris"}, "settings_currency_desc_display_for_list": {"message": "<PERSON><PERSON> <PERSON> (inklusive 1688/1688 oversøisk/Taobao)"}, "settings_currency_rate_desc": {"message": "Opdatering af valutakurs fra \"$currencyRateFrom$\"", "placeholders": {"currencyRateFrom": {"content": "$1"}}}, "settings_currency_rate_from_boc": {"message": "Bank of China"}, "settings_download_images__desc": {"message": "Understøttelse af download af billeder fra $platforms$", "placeholders": {"platforms": {"content": "$1"}}}, "settings_download_images__title": {"message": "download billede knap"}, "settings_download_reviews__desc": {"message": "Vist på $platforms$ produktdetaljeside", "placeholders": {"platforms": {"content": "$1"}}}, "settings_download_reviews__title": {"message": "Download billeder af anmeldelser"}, "settings_google_translate_desc": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON> for at få google oversættelseslinje"}, "settings_google_translate_title": {"message": "webside oversættelse"}, "settings_historical_trend_desc": {"message": "Vises i nederste højre hjørne af billedet på produktlistesiden"}, "settings_modal_btn_more": {"message": "<PERSON><PERSON><PERSON> in<PERSON>"}, "settings_productInfo_desc": {"message": "Vis mere detaljeret produktinformation på produktlistesiden. Aktivering af dette kan øge computerens belastning og forårsage sideforsinkelse. Hvis det påvirker ydeevnen, anbe<PERSON><PERSON> det at deaktivere det."}, "settings_product_recommend__desc": {"message": "Vist under hovedbilledet på $platforms$ produktdetaljer-siden", "placeholders": {"platforms": {"content": "$1"}}}, "settings_product_recommend__name": {"message": "Produkt<PERSON> anbe<PERSON>les"}, "settings_research_desc": {"message": "<PERSON><PERSON><PERSON><PERSON> mere detaljerede oplysninger på produktlistesiden"}, "settings_sbi_add_to_list": {"message": "Føj til $listType$", "placeholders": {"listType": {"content": "$1"}}}, "settings_sbi_detail_page_icon_title_desc": {"message": "Miniaturebillede af billedsøgeresultat"}, "settings_sbi_remove_from_list": {"message": "Fjern fra $listType$", "placeholders": {"listType": {"content": "$1"}}}, "settings_search_by_image_add_to_blacklist": {"message": "F<PERSON>j til blokeringsliste"}, "settings_search_by_image_adjust_entrance_entrance": {"message": "<PERSON><PERSON> in<PERSON>"}, "settings_search_by_image_blacklist_desc": {"message": "Vis ikke ikonet på websteder på den sorte liste."}, "settings_search_by_image_blacklist_title": {"message": "Blokeringsliste"}, "settings_search_by_image_bottom_left": {"message": "Nederst til venstre"}, "settings_search_by_image_bottom_right": {"message": "Nederst til højre"}, "settings_search_by_image_clear_blacklist": {"message": "<PERSON><PERSON> blo<PERSON>ingslisten"}, "settings_search_by_image_detail_page_icon_title": {"message": "Miniaturebillede"}, "settings_search_by_image_detail_page_icon_val_large": {"message": "<PERSON><PERSON><PERSON>"}, "settings_search_by_image_detail_page_icon_val_small": {"message": "Mindre"}, "settings_search_by_image_display_button_desc": {"message": "Et klik på ikonet for at søge efter billede"}, "settings_search_by_image_display_button_title": {"message": "<PERSON><PERSON> p<PERSON>"}, "settings_search_by_image_sourece_websites_desc": {"message": "Find kildeproduktet på disse websteder"}, "settings_search_by_image_sourece_websites_title": {"message": "<PERSON><PERSON><PERSON> efter <PERSON>"}, "settings_search_by_image_top_left": {"message": "Øverst til venstre"}, "settings_search_by_image_top_right": {"message": "Øverst til højre"}, "settings_search_keyword_on_x__desc": {"message": "<PERSON><PERSON>g efter ord på $platform$", "placeholders": {"platform": {"content": "$1"}}}, "settings_search_keyword_on_x__title": {"message": "Vis ikonet $platform$, når ord er valgt", "placeholders": {"platform": {"content": "$1"}}}, "settings_similar_products_desc": {"message": "<PERSON><PERSON><PERSON><PERSON> at finde det samme produkt på disse websteder (maks. Til 5)"}, "settings_similar_products_title": {"message": "Find det samme produkt"}, "settings_toolbar_expand_title": {"message": "Minimer plugin"}, "settings_top_toolbar_desc": {"message": "<PERSON><PERSON><PERSON><PERSON>je øverst på siden"}, "settings_top_toolbar_title": {"message": "Søg bar"}, "settings_translate_search_desc": {"message": "Oversæt til kinesisk og søg"}, "settings_translate_search_title": {"message": "Flersp<PERSON><PERSON> søgning"}, "settings_translator_contextmenu_title": {"message": "<PERSON><PERSON> for at oversætte"}, "settings_translator_title": {"message": "Oversætte"}, "shai_xuan_dao_chu": {"message": "Filtrer til eksport"}, "shai_xuan_zi_duan": {"message": "<PERSON><PERSON><PERSON> felter"}, "shang_jia_shi_jian": {"message": "På hyldetid"}, "shang_pin_biao_ti": {"message": "produkt titel"}, "shang_pin_dui_bi": {"message": "Produktsammenligning"}, "shang_pin_lian_jie": {"message": "produkt link"}, "shang_pin_xin_xi": {"message": "Produkt info"}, "share_modal__content": {"message": "Del med dine venner"}, "share_modal__disable_for_while": {"message": "Jeg vil ikke dele noget"}, "share_modal__title": {"message": "Kan du lide $extensionName$?", "placeholders": {"extensionName": {"content": "$1"}}}, "sheng_yu_ku_cun": {"message": "Tilbage"}, "shi_fou_ke_ding_zhi": {"message": "Kan det tilpasses?"}, "shi_fou_ren_zheng_gong_ying_sha": {"message": "Cert<PERSON><PERSON> lever<PERSON>ø<PERSON>"}, "shi_fou_ren_zheng_gong_ying_shang_zong_shu": {"message": "Certifice<PERSON><PERSON>"}, "shi_fou_you_mao_yi_dan_bao": {"message": "Handelsforsikring"}, "shi_fou_you_mao_yi_dan_bao_zong_shu": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "shipping_fee": {"message": "Forsendelsesgebyr"}, "shop_followers": {"message": "Shop følgere"}, "shou_qi": {"message": "Mindre"}, "similar_products_warn_max_platforms": {"message": "Maks. Til 5"}, "sku_calc_price": {"message": "<PERSON><PERSON><PERSON><PERSON> pris"}, "sku_calc_price_settings": {"message": "Bere<PERSON>e prisindstillinger"}, "sku_formula": {"message": "Formel"}, "sku_formula_desc": {"message": "Formelbeskrivelse"}, "sku_formula_desc_text": {"message": "Understøtter komplekse matematiske formler, hvor den oprindelige pris er repræsenteret af A og fragten er repræsenteret af B\n\n<br/>\n\nUnderstøtter parenteser (), plus +, minus -, multiplikation * og division /\n\n<br/>\n\nEks<PERSON>pel:\n\n<br/>\n\n1. For at opnå 1,2 gange den oprindelige pris og derefter lægge fragten til, er formlen: A*1,2+B\n\n<br/>\n\n2. For at opnå den oprindelige pris plus 1 yuan, skal du gange med 1,2 gange, formlen er: (A+1)*1,2\n\n<br/>\n\n3. For at opnå den oprindelige pris plus 10 yuan, skal du gange med 1,2 gange og derefter trække 3 yuan fra, formlen er: (A+10)*1,2-3"}, "sku_in_stock": {"message": "På lager"}, "sku_invalid_formula_format": {"message": "Ugyldigt formelformat"}, "sku_inventory": {"message": "Lagerbeholdning"}, "sku_link_copy_fail": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>, SKU-specifikationer og attributter er ikke valgt"}, "sku_link_copy_success": {"message": "<PERSON><PERSON><PERSON>, varespecifikationer og attributter valgt"}, "sku_list": {"message": "SKU-liste"}, "sku_min_qrder_qty": {"message": "Minimum ordremængde"}, "sku_name": {"message": "SKU-navn"}, "sku_no": {"message": "Nr."}, "sku_original_price": {"message": "Oprindelig pris"}, "sku_price": {"message": "SKU-pris"}, "stop_track_time_label": {"message": "Sporingsfrist:"}, "suo_zai_di_qu": {"message": "Beliggenhed"}, "tab_pkg_quick_view": {"message": "Logistik monitor"}, "tab_product_details_price_history": {"message": "<PERSON>ris historie"}, "tab_product_details_reviews": {"message": "Foto anmeldelser"}, "tab_product_details_seller_analysis": {"message": "Sælgeranalyse"}, "tab_product_details_similar_products": {"message": "<PERSON><PERSON> produkter"}, "total_days_listed_per_product": {"message": "Sum af hyldedage ÷ Antal produkter"}, "total_items": {"message": "<PERSON>let antal produkter"}, "total_price_per_product": {"message": "Sum af priser ÷ Antal produkter"}, "total_rating_per_product": {"message": "Sum af vurderinger ÷ Antal produkter"}, "total_revenue": {"message": "Samlede indtægter"}, "total_revenue40_items": {"message": "Samlet omsætning for de $amount$ produkter på denne side", "placeholders": {"amount": {"content": "$1"}}}, "total_sales": {"message": "Samlet salg"}, "total_sales40_items": {"message": "Samlet salg af de $amount$ produkter på den aktuelle side", "placeholders": {"amount": {"content": "$1"}}}, "track_for_12_months": {"message": "Spor i: 1 år"}, "track_for_3_months": {"message": "Spor i: 3 måneder"}, "track_for_6_months": {"message": "Spor i: 6 måneder"}, "tracking_price_email_add_btn": {"message": "Tilføj e-mail"}, "tracking_price_email_edit_btn": {"message": "Rediger e-mail"}, "tracking_price_email_intro": {"message": "Vi giver dig besked via e-mail."}, "tracking_price_email_invalid": {"message": "Angiv en gyldig e-mail"}, "tracking_price_email_verified_desc": {"message": "Du kan nu modtage vores prisfaldalarm."}, "tracking_price_email_verified_title": {"message": "Bekræftet"}, "tracking_price_email_verify_desc_line1": {"message": "Vi har sendt et bekræftelseslink til din e-mail-adresse,"}, "tracking_price_email_verify_desc_line2": {"message": "tjek venligst din e-mail-indbakke."}, "tracking_price_email_verify_title": {"message": "Bekræft e-mail"}, "tracking_price_web_push_notification_intro": {"message": "På skrivebordet: <PERSON><PERSON><PERSON> kan overvåge ethvert produkt for dig og sende dig en Web Push Notification, når prisen ændres."}, "tracking_price_web_push_notification_title": {"message": "<PERSON> Push-medd<PERSON><PERSON>er"}, "translate_im__login_required": {"message": "Oversat af AliPrice, log venligst ind på $loginUrl$", "placeholders": {"loginUrl": {"content": "$1"}}}, "translate_im__paste_fail": {"message": "Oversat og kopieret til udklipsholderen, men på grund af begrænsningen i Aliwangwang skal du indsætte det manuelt!"}, "translate_im__send": {"message": "Oversæt og send"}, "translate_search": {"message": "Oversæt og søgning"}, "translation_originals_translated": {"message": "Original og kinesisk"}, "translation_translated": {"message": "kinesisk"}, "translator_btn_capture_txt": {"message": "Oversætte"}, "translator_language_auto_detect": {"message": "Automatisk detektering"}, "translator_language_detected": {"message": "Opdaget"}, "translator_language_search_placeholder": {"message": "<PERSON><PERSON><PERSON> sprog"}, "try_again": {"message": "<PERSON><PERSON><PERSON><PERSON> igen"}, "tu_pian_chi_cun": {"message": "Billedstørrelse:"}, "tu_pian_lian_jie": {"message": "Billedlink"}, "tui_huan_ti_yan": {"message": "Returoplevelse"}, "tui_huan_ti_yan__desc": {"message": "Vurder sælgeres eftersalgsindikatorer"}, "tutorial__show_all": {"message": "<PERSON>e funktioner"}, "tutorial_ae_popup_title": {"message": "Fastgør ud<PERSON>en, åbn Aliexpress"}, "tutorial_aliexpress_reviews_analysis": {"message": "AliExpress anmeldelsesanalyse"}, "tutorial_aliprice_agent_with1688_desc_l1": {"message": "Support USD"}, "tutorial_aliprice_agent_with1688_desc_l2": {"message": "Forsendelse til Sydkorea/Japan/Kina"}, "tutorial_aliprice_agent_with1688_title": {"message": "1688 understøtter køb i udlandet"}, "tutorial_auto_apply_coupon_title": {"message": "Anvend kupon automatisk"}, "tutorial_btn_end": {"message": "<PERSON><PERSON>"}, "tutorial_btn_example": {"message": "Eksempel"}, "tutorial_btn_see_more": {"message": "<PERSON><PERSON>"}, "tutorial_compare_products": {"message": "Sammenlign produkter"}, "tutorial_currency_convert_title": {"message": "Valuta <PERSON>gni<PERSON>"}, "tutorial_export_shopping_cart": {"message": "Eksporter som CSV, Support Taobao og 1688"}, "tutorial_export_shopping_cart_title": {"message": "Eksportvogn"}, "tutorial_price_history_pro": {"message": "Vises på produktdetaljesiden. Support Shopee, Lazada, Amazon, Ebay"}, "tutorial_price_history_pro_title": {"message": "Helår Prishistorik & Ordrehistorik"}, "tutorial_sbi_context_menu_screenshot_title": {"message": "Optag for at søge efter billede"}, "tutorial_translate_search": {"message": "Oversæt til søgning"}, "tutorial_translate_search_and_package_tracking": {"message": "Oversættelsessøgning og pakkesporing"}, "unit_bao": {"message": "stk"}, "unit_ben": {"message": "stk"}, "unit_bi": {"message": "Ordre:% s"}, "unit_chuang": {"message": "stk"}, "unit_dai": {"message": "stk"}, "unit_dui": {"message": "par"}, "unit_fen": {"message": "stk"}, "unit_ge": {"message": "stk"}, "unit_he": {"message": "stk"}, "unit_jian": {"message": "stk"}, "unit_li_fang_mi": {"message": "m³"}, "unit_ping": {"message": "stk"}, "unit_ping_fang_mi": {"message": "㎡"}, "unit_shuang": {"message": "par"}, "unit_tai": {"message": "stk"}, "unit_ti": {"message": "stk"}, "unit_tiao": {"message": "stk"}, "unit_xiang": {"message": "stk"}, "unit_zhang": {"message": "stk"}, "unit_zhi": {"message": "stk"}, "verify_contact_support": {"message": "Kontakt support"}, "verify_human_verification": {"message": "Menneskelig verifikation"}, "verify_unusual_access": {"message": "Usædvanlig adgang registreret"}, "view_history_clean_all": {"message": "Rengør alt"}, "view_history_clean_all_warring": {"message": "Ryd alle viste poster?"}, "view_history_clean_all_warring_title": {"message": "<PERSON><PERSON><PERSON>"}, "view_history_viewd": {"message": "har set"}, "website": {"message": "hje<PERSON><PERSON>"}, "weight": {"message": "<PERSON><PERSON><PERSON>"}, "wu_fa_huo_qu_dao_gai_shu_ju": {"message": "Kan ikke hente <PERSON>ene"}, "wu_liu_shi_xiao": {"message": "Forsendelse til tiden"}, "wu_liu_shi_xiao__desc": {"message": "48-timers indsamlingshastighed og opfyldelsesgrad for sælgers butik"}, "xia_dan_jia": {"message": "<PERSON><PERSON><PERSON> pris"}, "xian_xuan_ze_product_attributes": {"message": "Vælg produktattributter"}, "xiao_liang": {"message": "Omsætning"}, "xiao_liang_zhan_bi": {"message": "<PERSON><PERSON> af salgsvolumen"}, "xiao_shi": {"message": "$num$ Timer", "placeholders": {"num": {"content": "$1"}}}, "xiao_shou_e": {"message": "Indtægter"}, "xiao_shou_e_zhan_bi": {"message": "<PERSON>cent af o<PERSON>æ<PERSON>ningen"}, "xuan_zhong_x_tiao_ji_lu": {"message": "Vælg $amount$ poster", "placeholders": {"amoun": {"content": "$1"}, "amount": {"content": "$1"}}}, "yan_xuan": {"message": "<PERSON><PERSON>"}, "yi_ding_zai_zuo_ce": {"message": "Fastgjort"}, "yi_jia_zai_wan_suo_you_shang_pin": {"message": "Alle produkter er indlæst"}, "yi_nian_xiao_liang": {"message": "Årligt salg"}, "yi_nian_xiao_liang_zhan_bi": {"message": "Årlig salgsandel"}, "yi_nian_xiao_shou_e": {"message": "Årlig omsætning"}, "yi_nian_xiao_shou_e_zhan_bi": {"message": "Årlig omsætningsandel"}, "yi_shua_xin": {"message": "Forfrisket"}, "yin_cang_xiang_tong_dian": {"message": "sk<PERSON><PERSON> ligheder"}, "you_xiao_liang": {"message": "Med salgsvolumen"}, "yu_ji_dao_da_shi_jian": {"message": "Forventet ankomsttid"}, "yuan_gong_ren_shu": {"message": "<PERSON><PERSON>"}, "yue_cheng_jiao": {"message": "Månedlig <PERSON>n"}, "yue_dai_xiao": {"message": "Dropshipping"}, "yue_dai_xiao__desc": {"message": "Dropshipping-salg inden for de sidste 30 dage"}, "yue_dai_xiao_pai_xu__desc": {"message": "Dropshipping-salg inden for de sidste 30 dage, sorteret fra høj til lav"}, "yue_xiao_liang__desc": {"message": "Salgsvolumen inden for de seneste 30 dage"}, "zhan_kai": {"message": "<PERSON><PERSON>"}, "zhe_kou": {"message": "Rabat"}, "zhi_chi_yi_jian_dai_fa": {"message": "Dropshipping"}, "zhi_chi_yi_jian_dai_fa_bao_you": {"message": "<PERSON><PERSON><PERSON> fragt"}, "zhi_fu_ding_dan_shu": {"message": "<PERSON><PERSON><PERSON> or<PERSON>r"}, "zhi_fu_ding_dan_shu__desc": {"message": "Antal ordrer for dette produkt (30 dage)"}, "zhu_ce_xing_zhi": {"message": "Registrerings karakter"}, "zi_ding_yi_tiao_jian": {"message": "Brugerdefinerede betingelser"}, "zi_duan": {"message": "<PERSON><PERSON>"}, "zi_ti_xiao_liang": {"message": "Variation solgt"}, "zong_he_fu_wu_fen": {"message": "<PERSON><PERSON>ø<PERSON>e"}, "zong_he_fu_wu_fen__desc": {"message": "Samlet bedømmelse af sælgerservice"}, "zong_he_fu_wu_fen__short": {"message": "Bed<PERSON><PERSON>lse"}, "zong_he_ti_yan_fen": {"message": "Bed<PERSON><PERSON>lse"}, "zong_he_ti_yan_fen_3": {"message": "Under 4 stjerner"}, "zong_he_ti_yan_fen_4": {"message": "4 - 4,5 stjerner"}, "zong_he_ti_yan_fen_4_5": {"message": "4,5 - 5,0 st<PERSON>ner"}, "zong_he_ti_yan_fen_5": {"message": "5 stjerner"}, "zong_ku_cun": {"message": "Samlet beholdning"}, "zong_xiao_liang": {"message": "Samlet salg"}, "zui_jin_30D_3Min_xiang_ying_lv": {"message": "3 minutters svarp<PERSON>cent inden for de sidste 30 dage"}, "zui_jin_30D_48H_lan_shou_lv": {"message": "48H genopretningshastighed i de sidste 30 dage"}, "zui_jin_30D_48H_lv_yue_lv": {"message": "48H yd<PERSON><PERSON><PERSON> inden for de sidste 30 dage"}, "zui_jin_30D_jiao_yi_ji_lu": {"message": "Handelsrekord (30 dage)"}, "zui_jin_30D_jiao_yi_ji_lu__desc": {"message": "Handelsrekord (30 dage)"}, "zui_jin_30D_jiu_fen_lv": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> inden for de seneste 30 dage"}, "zui_jin_30D_pin_zhi_tui_kuan_lv": {"message": "Kvalitetsrefusionssats inden for de sidste 30 dage"}, "zui_jin_30D_zhi_fu_ding_dan_shu": {"message": "Antallet af betalingsordrer inden for de seneste 30 dage"}}