{"EXTENSION_NAME": {"message": "TODO: EXTENSION_NAME"}, "EXTENSION_DESCRIPTION": {"message": "TODO: EXTENSION_DESCRIPTION"}, "1688_cross_border_hot_selling": {"message": "1688 Pārrobežu karstā pā<PERSON> vieta"}, "1688_shi_li_ren_zheng": {"message": "1688 stiprības sertifik<PERSON>ts"}, "1_jian_qi_pi": {"message": "MOQ: 1"}, "1year_yi_shang": {"message": "Vairāk nekā 1 gads"}, "24H": {"message": "24H"}, "24H_fa_huo": {"message": "Piegāde 24 stundu laikā"}, "24H_lan_shou_lv": {"message": "24 stundu i<PERSON>ak<PERSON><PERSON> tarifs"}, "30D_shang_xin": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> jau<PERSON>i"}, "30d_sales": {"message": "$amount$ pārdots 30 dienu laikā", "placeholders": {"amount": {"content": "$1"}}}, "3Min_xiang_ying_lv": {"message": "Atbilde 3 minūšu laikā."}, "3Min_xiang_ying_lv__desc": {"message": "Vangvangas efektīvo atbilžu īpatsvars uz pircēja pieprasījuma ziņojumiem 3 minūšu laikā pēdējo 30 dienu laikā"}, "48H": {"message": "48H"}, "48H_fa_huo": {"message": "Piegāde 48 stundu laik<PERSON>"}, "48H_lan_shou_lv": {"message": "48 stun<PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> likme"}, "48H_lan_shou_lv__desc": {"message": "48 stundu laikā izņemtā pasūtījuma numura attiecība pret kopējo pasūtījumu skaitu"}, "48H_lv_yue_lv": {"message": "48 stundu veikts<PERSON><PERSON>jas rādītājs"}, "48H_lv_yue_lv__desc": {"message": "Paņemtā vai piegādātā pasūtījuma numura 48 stundu laikā attiecība pret kopējo pasūtījumu skaitu"}, "72H": {"message": "72H"}, "7D_shang_xin": {"message": "Iknedēļ<PERSON> jaunums"}, "7D_wu_li_you": {"message": "7 dienas bez aprūpes"}, "ABS_title_text": {"message": "<PERSON><PERSON><PERSON> ir i<PERSON><PERSON><PERSON><PERSON><PERSON>tā<PERSON>"}, "AC_title_text": {"message": "<PERSON><PERSON> ir emblēma Amazon's Choice"}, "A_title_text": {"message": "<PERSON><PERSON> i<PERSON> ir A+ satura lapa"}, "BS_title_text": {"message": "Šis ieraksts ir novērtēts kā $num$ vislabāk pārdotais kategorijā $type$", "placeholders": {"type": {"content": "$1"}, "num": {"content": "$2"}}}, "LTD_title_text": {"message": "LTD (ierobežota laika piedāvājums) no<PERSON><PERSON><PERSON>ē, ka <PERSON>is ieraksts ir daļa no \"7 dienu veici<PERSON>\" pas<PERSON><PERSON>ma"}, "NR_title_text": {"message": "Šis ieraksts ir novērtēts kā $num$ jaunais izdevums kategorijā $type$", "placeholders": {"type": {"content": "$1"}, "num": {"content": "$2"}}}, "SBV_title_text": {"message": "<PERSON><PERSON><PERSON> ierakstā ir videorek<PERSON>ma — PPC reklāma, kas parasti tiek rādīta meklēša<PERSON> rezultātu vidū"}, "SB_title_text": {"message": "<PERSON><PERSON><PERSON> iera<PERSON>tā ir z<PERSON><PERSON> re<PERSON> — PPC reklāma, kas parasti tiek rādīta meklēša<PERSON> rezultātu augšdaļā vai apakšā."}, "SP_title_text": {"message": "<PERSON><PERSON><PERSON> ierakstā ir sponsorētā produkta reklāma"}, "V_title_text": {"message": "<PERSON><PERSON> ir video ievads"}, "advanced_research": {"message": "Uzlabotā <PERSON>zpēte"}, "agent_ds1688___my_order": {"message": "<PERSON><PERSON><PERSON>"}, "agent_ds1688__add_to_cart": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "agent_ds1688__cart": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "agent_ds1688__desc": {"message": "Nodrošina 1688. Tas atbalsta tiešu iegādi no ārzemēm, ma<PERSON><PERSON><PERSON><PERSON> USD un piegādi uz jūsu tranzīta noliktavu Ķīnā."}, "agent_ds1688__freight": {"message": "Piegādes izmaksu kalk<PERSON>s"}, "agent_ds1688__help": {"message": "Palīdzī<PERSON>"}, "agent_ds1688__packages": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "agent_ds1688__profile": {"message": "Personīgais centrs"}, "agent_ds1688__warehouse": {"message": "<PERSON><PERSON>"}, "ai_comment_analysis_advantage": {"message": "Pros"}, "ai_comment_analysis_ai": {"message": "AI pārskata analīze"}, "ai_comment_analysis_available": {"message": "<PERSON><PERSON><PERSON>"}, "ai_comment_analysis_balance": {"message": "Nepietiek <PERSON>, l<PERSON><PERSON><PERSON>, papildiniet"}, "ai_comment_analysis_behavior": {"message": "Uzvedība"}, "ai_comment_analysis_characteristic": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "ai_comment_analysis_comment": {"message": "Produktam nav pietiekami daudz <PERSON>, lai i<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, izv<PERSON>lie<PERSON> produktu ar vairāk atsa<PERSON>."}, "ai_comment_analysis_consume": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "ai_comment_analysis_default": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "ai_comment_analysis_desire": {"message": "<PERSON><PERSON><PERSON>"}, "ai_comment_analysis_disadvantage": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "ai_comment_analysis_free": {"message": "Bezmaksas mēģinājumi"}, "ai_comment_analysis_freeNum": {"message": "Tiks izmantots 1 bezmaksas kredīts"}, "ai_comment_analysis_go_recharge": {"message": "Dodieties uz papildināšanu"}, "ai_comment_analysis_intelligence": {"message": "Inteliģenta pārskata analīze"}, "ai_comment_analysis_location": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> vieta"}, "ai_comment_analysis_motive": {"message": "Pirkuma motivācija"}, "ai_comment_analysis_network_error": {"message": "<PERSON><PERSON><PERSON> k<PERSON>. <PERSON><PERSON><PERSON><PERSON>, mēģiniet vēlre<PERSON>"}, "ai_comment_analysis_normal": {"message": "Fotoattēlu apskati"}, "ai_comment_analysis_number_reviews": {"message": "Atsauksmju skaits: $num$, paredzam<PERSON> pat<PERSON>: $consume$", "placeholders": {"num": {"content": "$1"}, "consume": {"content": "$2"}}}, "ai_comment_analysis_ordinary": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> k<PERSON>"}, "ai_comment_analysis_percentage": {"message": "<PERSON><PERSON><PERSON>"}, "ai_comment_analysis_problem": {"message": "<PERSON>b<PERSON><PERSON><PERSON> ar ma<PERSON>"}, "ai_comment_analysis_reanalysis": {"message": "Atkārtoti analizēt"}, "ai_comment_analysis_reason": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "ai_comment_analysis_recharge": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "ai_comment_analysis_recharged": {"message": "<PERSON><PERSON><PERSON>"}, "ai_comment_analysis_retry": {"message": "Mēģiniet vēlreiz"}, "ai_comment_analysis_scene": {"message": "<PERSON><PERSON><PERSON><PERSON> sce<PERSON>"}, "ai_comment_analysis_start": {"message": "Sāciet analizēt"}, "ai_comment_analysis_subject": {"message": "<PERSON><PERSON><PERSON>"}, "ai_comment_analysis_time": {"message": "<PERSON><PERSON><PERSON><PERSON> laiks"}, "ai_comment_analysis_tool": {"message": "AI rīks"}, "ai_comment_analysis_user_portrait": {"message": "Lieto<PERSON><PERSON><PERSON> profils"}, "ai_comment_analysis_welcome": {"message": "Laipni lūdzam AI pārskata analīzē"}, "ai_comment_analysis_year": {"message": "<PERSON><PERSON><PERSON><PERSON> no pagājušā gada"}, "ai_listing_Exclude_keywords": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON> at<PERSON>lēgv<PERSON><PERSON>"}, "ai_listing_Login_the_feature": {"message": "<PERSON>, ir <PERSON><PERSON><PERSON><PERSON><PERSON>"}, "ai_listing_aI_generation": {"message": "AI paaudze"}, "ai_listing_add_automatic": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "ai_listing_add_dictionary_new": {"message": "Izveidojiet jaunu bibliotēku"}, "ai_listing_add_enter_keywords": {"message": "Ievadiet atslēg<PERSON>us"}, "ai_listing_add_inputkey_selling": {"message": "Ievadiet pārdošanas punktu un nospiediet $key$, lai pabeig<PERSON> pie<PERSON>", "placeholders": {"key": {"content": "$1"}}}, "ai_listing_add_inputkey_warning": {"message": "Pārsniegts limits, līdz $amount$ pārdošanas punktiem", "placeholders": {"amount": {"content": "$1"}}}, "ai_listing_add_keywords": {"message": "Pievienojiet atslēgvārdus"}, "ai_listing_add_manually": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "ai_listing_add_selling": {"message": "Pievienojiet p<PERSON><PERSON> punk<PERSON>"}, "ai_listing_added_keywords": {"message": "Pievienoti atslēgvārdi"}, "ai_listing_added_successfully": {"message": "Veiks<PERSON><PERSON><PERSON>"}, "ai_listing_addexcluded_keywords": {"message": "Ievadiet iz<PERSON>lēgtos atslēgvārdus un nospiediet taustiņu <PERSON>, lai p<PERSON><PERSON><PERSON>."}, "ai_listing_adding_selling": {"message": "Pievienoti pārdoša<PERSON>"}, "ai_listing_addkeyword_enter": {"message": "Ievadiet atslēgas atribūta vārdus un nospiediet taustiņu <PERSON>, lai p<PERSON><PERSON><PERSON>"}, "ai_listing_ai_description": {"message": "AI apraksta vārdu bibliotēka"}, "ai_listing_ai_dictionary": {"message": "AI nosaukumu vārdu bibliotēka"}, "ai_listing_ai_title": {"message": "AI nosaukums"}, "ai_listing_aidescription_repeated": {"message": "AI apraksta vārda bibliotēkas nosaukumu nevar at<PERSON>ot"}, "ai_listing_aititle_repeated": {"message": "AI virsraksta vārda bibliotēkas nosaukumu nevar at<PERSON>"}, "ai_listing_data_comes_from": {"message": "<PERSON>ie dati nāk no:"}, "ai_listing_deleted_successfully": {"message": "Veiksmīgi izdzēsts"}, "ai_listing_dictionary_name": {"message": "Bibliotē<PERSON>"}, "ai_listing_edit_dictionary": {"message": "Mainīt bibliotēku..."}, "ai_listing_edit_word_library": {"message": "Rediģējiet vārdu bibliotēku"}, "ai_listing_enter_keywords": {"message": "Ievadiet atslēgvārdus un nospiediet $key$, lai p<PERSON><PERSON><PERSON>", "placeholders": {"key": {"content": "$1"}}}, "ai_listing_exceeded_maximum": {"message": "Ierobežoju<PERSON> ir <PERSON>, ma<PERSON><PERSON><PERSON><PERSON><PERSON> at<PERSON>lēgvārdu skaits $amount$", "placeholders": {"amount": {"content": "$1"}}}, "ai_listing_excluded_word_library": {"message": "Izslēgtā vārdu bibliotēka"}, "ai_listing_generate_characters": {"message": "Ģenerēt raks<PERSON><PERSON>mes"}, "ai_listing_generation_platform": {"message": "Paaudzes platforma"}, "ai_listing_help_optimize": {"message": "Palī<PERSON><PERSON>t man optimizēt produkta nosaukumu, s<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> nosauku<PERSON> ir"}, "ai_listing_include_selling": {"message": "Citi pārdošanas punkti ietver:"}, "ai_listing_included_keyword": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "ai_listing_included_keywords": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "ai_listing_input_selling": {"message": "Ievadiet p<PERSON><PERSON>"}, "ai_listing_input_selling_fit": {"message": "Ievadiet p<PERSON><PERSON><PERSON><PERSON>, lai tie atbilstu nosaukumam"}, "ai_listing_input_selling_please": {"message": "<PERSON><PERSON><PERSON><PERSON>, ievadiet p<PERSON><PERSON><PERSON><PERSON>"}, "ai_listing_intelligently_title": {"message": "Ievadiet i<PERSON><PERSON><PERSON><PERSON> saturu, lai gudri ģenerētu nosaukumu"}, "ai_listing_keyword_product_title": {"message": "Atslēgvārda produkta nosaukums"}, "ai_listing_keywords_repeated": {"message": "Ats<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> nevar <PERSON>"}, "ai_listing_listed_selling_points": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "ai_listing_long_title_1": {"message": "<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, produkta veidu, produkta <PERSON><PERSON> utt."}, "ai_listing_long_title_2": {"message": "Pamatojoties uz standarta produkta no<PERSON>, tiek pievienoti atslēgvārdi, kas veicina SEO."}, "ai_listing_long_title_3": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>, produkta veidam, produkta funkcijām un atslēgvārdiem ir iekļauti arī garie atslēgvārdi, lai sasniegtu augstāku rangu kon<PERSON>, <PERSON><PERSON><PERSON> v<PERSON>."}, "ai_listing_longtail_keyword_product_title": {"message": "<PERSON><PERSON><PERSON><PERSON> astes atslēgvārdu produkta nosaukums"}, "ai_listing_manually_enter": {"message": "<PERSON><PERSON><PERSON><PERSON> ievadiet..."}, "ai_listing_network_not_working": {"message": "Internets nav pieejams, lai piekļ<PERSON><PERSON> ChatGPT, ir nepieciešams VPN"}, "ai_listing_new_dictionary": {"message": "Izveidojiet jaunu vārdu bibliotēku..."}, "ai_listing_new_generate": {"message": "Ģenerēt"}, "ai_listing_optional_words": {"message": "<PERSON>zvē<PERSON> vārdi"}, "ai_listing_original_title": {"message": "Oriģinālais nosaukums"}, "ai_listing_other_keywords_included": {"message": "Iekļauti citi atslēgvārdi:"}, "ai_listing_please_again": {"message": "Lūdzu mēģiniet vēlreiz"}, "ai_listing_please_select": {"message": "Jums ir ģenerēti šā<PERSON>, l<PERSON><PERSON><PERSON>, atlasiet:"}, "ai_listing_product_category": {"message": "Produkta kategorija"}, "ai_listing_product_category_is": {"message": "Produktu kategorija ir"}, "ai_listing_product_category_to": {"message": "<PERSON><PERSON> kategorijai pieder prece?"}, "ai_listing_random_keywords": {"message": "Nejauši $amount$ atslēgvārdi", "placeholders": {"amount": {"content": "$1"}}}, "ai_listing_random_selling": {"message": "Nejauši $amount$ p<PERSON><PERSON><PERSON><PERSON>", "placeholders": {"amount": {"content": "$1"}}}, "ai_listing_randomize_keywords": {"message": "Randomizēt no vārdu bibliotēkas"}, "ai_listing_search_selling": {"message": "Mek<PERSON>ēt pēc p<PERSON><PERSON>"}, "ai_listing_select_product_categories": {"message": "Automātiski atlasīt produktu kategorija<PERSON>."}, "ai_listing_select_product_selling_points": {"message": "Automātiski atlasīt produktu pā<PERSON> punk<PERSON>"}, "ai_listing_select_word_library": {"message": "Izvēlieties vārdu bibliotēku"}, "ai_listing_selling": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "ai_listing_selling_ask": {"message": "<PERSON><PERSON>das citas prasības ir izvirz<PERSON> no<PERSON>ukumam?"}, "ai_listing_selling_optional": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "ai_listing_selling_repeat": {"message": "<PERSON><PERSON> ne<PERSON>"}, "ai_listing_set_excluded": {"message": "Iestatīt kā izslēgtu vārdu bibliotēku"}, "ai_listing_set_include_selling_points": {"message": "Iekļ<PERSON><PERSON><PERSON> p<PERSON><PERSON> punk<PERSON>"}, "ai_listing_set_included": {"message": "Iestatīt kā iekļautu vārdu bibliotēku"}, "ai_listing_set_selling_dictionary": {"message": "Iestatīt kā pārdošanas punktu bibliotēku"}, "ai_listing_standard_product_title": {"message": "Standarta produkta nosaukums"}, "ai_listing_translated_title": {"message": "Tulkots virsraksts"}, "ai_listing_visit_chatGPT": {"message": "Apmeklējiet ChatGPT"}, "ai_listing_what_other_keywords": {"message": "<PERSON><PERSON><PERSON> vēl atslēgv<PERSON><PERSON> ir nepiecieša<PERSON> virsrakstam?"}, "aliprice_coupons_apply_again": {"message": "Pieteikties vēlreiz"}, "aliprice_coupons_apply_coupons": {"message": "<PERSON><PERSON><PERSON>"}, "aliprice_coupons_apply_success": {"message": "Atrasts kupons: ietaupiet $amount$", "placeholders": {"amount": {"content": "$1"}}}, "aliprice_coupons_applying": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON> kodu test<PERSON>..."}, "aliprice_coupons_applying_desc": {"message": "Izrakstīšanās: $code$", "placeholders": {"code": {"content": "$1"}}}, "aliprice_coupons_back_to_checkout": {"message": "Turpiniet uz Checkout"}, "aliprice_coupons_found_coupons": {"message": "<PERSON><PERSON><PERSON> at<PERSON> k<PERSON> $amount$", "placeholders": {"amount": {"content": "$1"}}}, "aliprice_coupons_found_coupons_desc": {"message": "Vai esat gatavs norēķināties? <PERSON><PERSON><PERSON><PERSON>cināsimies, ka saņemat vislabāko cenu!"}, "aliprice_coupons_no_coupon_aviable": {"message": "<PERSON><PERSON> kodi nedarboj<PERSON>. Nav <PERSON>las probl<PERSON> — jūs jau sa<PERSON> v<PERSON>la<PERSON> cenu."}, "aliprice_coupons_toolbar_btn": {"message": "Saņem<PERSON> k<PERSON>us"}, "aliww_translate": {"message": "Aliwang<PERSON> t<PERSON><PERSON><PERSON><PERSON><PERSON> t<PERSON>s"}, "aliww_translate_supports": {"message": "Atbalsts: 1688 un Taobao"}, "amazon_extended_keywords_Keywords": {"message": "Atslēgvārdi"}, "amazon_extended_keywords_copy_all": {"message": "<PERSON><PERSON><PERSON><PERSON> visu"}, "amazon_extended_keywords_more": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "an_lei_ji_xiao_liang_pai_xu": {"message": "<PERSON><PERSON><PERSON><PERSON> pēc kumulatī<PERSON><PERSON><PERSON>"}, "an_lei_xing_cha_kan": {"message": "Tips"}, "an_yue_dai_xiao_pai_xu": {"message": "Reitings pēc dropshipping p<PERSON><PERSON><PERSON><PERSON>"}, "apra_btn__cat_name": {"message": "Atsauksm<PERSON>"}, "apra_chart__name": {"message": "Produktu p<PERSON><PERSON>šanas procenti pēc valsts"}, "apra_chart__update_at": {"message": "Atjaunin<PERSON><PERSON><PERSON> laiks $time$", "placeholders": {"time": {"content": "$1"}}}, "apra_name": {"message": "<PERSON><PERSON><PERSON> statistika"}, "auto_opening": {"message": "Automātiski tiks atvērts pēc $num$ sekundēm", "placeholders": {"num": {"content": "$1"}}}, "auto_page_next_x_pages": {"message": "Nākamās $autoPaging$ lapas", "placeholders": {"autoPaging": {"content": "$1"}}}, "average_days_listed": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON> plauktu dien<PERSON>s"}, "average_hui_fu_lv": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON> l<PERSON>"}, "average_ping_gong_ying_shang_deng_ji": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON> pie<PERSON><PERSON><PERSON><PERSON><PERSON> lī<PERSON>"}, "average_price": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON> cena"}, "average_qi_ding_liang": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "average_rating": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON> vē<PERSON>"}, "average_ren_zheng_gong_ying_nian_chang": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>adi"}, "average_revenue": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "average_revenue_per_product": {"message": "Kopējie ieņēmumi ÷ Produktu skaits"}, "average_sales": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON> a<PERSON>"}, "average_sales_per_product": {"message": "Ko<PERSON>ē<PERSON><PERSON> a<PERSON>jo<PERSON> ÷ Produktu skaits"}, "bao_han": {"message": "Satur"}, "bao_zheng_jin": {"message": "Marža"}, "bian_ti_shu": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "biao_ti": {"message": "Nosa<PERSON>ms"}, "blacklist_add_blacklist": {"message": "Bloķēt šo veikalu"}, "blacklist_address_incorrect": {"message": "Adrese nav pareiza. <PERSON><PERSON><PERSON><PERSON> to."}, "blacklist_blacked_out": {"message": "<PERSON><PERSON><PERSON><PERSON> ir bloķēts"}, "blacklist_blacklist": {"message": "Melnai<PERSON>"}, "blacklist_no_records_yet": {"message": "Vēl nav ierakstu!"}, "blue_rocket": {"message": "로켓배송"}, "brand_name": {"message": "Zī<PERSON>ls"}, "btn_aliprice_agent__daigou": {"message": "Pirkuma starpnieks"}, "btn_aliprice_agent__dropshipping": {"message": "Dropshipping"}, "btn_have_a_try": {"message": "Izmēģiniet"}, "btn_refresh": {"message": "<PERSON><PERSON>uno<PERSON>"}, "btn_try_it_now": {"message": "Pamēģini to tagad"}, "btn_txt_view_on_aliprice": {"message": "Skats vietnē AliPrice"}, "bu_bao_han": {"message": "Nesatur"}, "bulk_copy_links": {"message": "Lielapjoma kopēšanas sa<PERSON>"}, "bulk_copy_products": {"message": "Lielapjoma kopēšanas produkti"}, "cai_gou_zi_xun": {"message": "<PERSON><PERSON><PERSON>"}, "cai_gou_zi_xun__desc": {"message": "Pārdevēja trīs min<PERSON> atbildes reakcija"}, "can_ping_lei_xing": {"message": "Tips"}, "cao_zuo": {"message": "Darbība"}, "chan_pin_ID": {"message": "Produkta ID"}, "chan_pin_e_wai_xin_xi": {"message": "Produkta papildu informācija"}, "chan_pin_lian_jie": {"message": "Produkta saite"}, "cheng_li_shi_jian": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON> laiks"}, "city__aba": {"message": "Aba"}, "city__aksu": {"message": "<PERSON><PERSON><PERSON>"}, "city__alaer": {"message": "<PERSON><PERSON><PERSON>"}, "city__altai": {"message": "Altai"}, "city__alxaleague": {"message": "Alxa League"}, "city__ankang": {"message": "Ankan<PERSON>"}, "city__anqing": {"message": "Anqing"}, "city__anshan": {"message": "<PERSON><PERSON>"}, "city__anshun": {"message": "<PERSON><PERSON><PERSON>"}, "city__anyang": {"message": "Anyang"}, "city__baicheng": {"message": "Baicheng"}, "city__baise": {"message": "Baise"}, "city__baisha": {"message": "Baisha"}, "city__baishan": {"message": "Baishan"}, "city__baiyin": {"message": "<PERSON><PERSON><PERSON>"}, "city__baoding": {"message": "<PERSON>od<PERSON>"}, "city__baoji": {"message": "<PERSON><PERSON><PERSON>"}, "city__baoshan": {"message": "<PERSON><PERSON><PERSON>"}, "city__baoting": {"message": "Baoting"}, "city__baotou": {"message": "<PERSON><PERSON><PERSON>"}, "city__bayannur": {"message": "Bayannur"}, "city__bayinguoleng": {"message": "Bayinguoleng"}, "city__bazhong": {"message": "Bazhong"}, "city__beihai": {"message": "Beihai"}, "city__bengbu": {"message": "<PERSON><PERSON><PERSON>"}, "city__benxi": {"message": "Benxi"}, "city__bijie": {"message": "Bijie"}, "city__binzhou": {"message": "Binzhou"}, "city__bortala": {"message": "<PERSON><PERSON><PERSON>"}, "city__bozhou": {"message": "Bozhou"}, "city__cangzhou": {"message": "Cangzhou"}, "city__chamdo": {"message": "<PERSON><PERSON><PERSON>"}, "city__changchun": {"message": "<PERSON><PERSON><PERSON>"}, "city__changde": {"message": "<PERSON><PERSON>"}, "city__changhua": {"message": "Changhua"}, "city__changji": {"message": "Changji"}, "city__changjiang": {"message": "Changjiang"}, "city__changsha": {"message": "Changsha"}, "city__changzhi": {"message": "Changzhi"}, "city__changzhou": {"message": "Changzhou"}, "city__chaohu": {"message": "<PERSON><PERSON>"}, "city__chaoyang": {"message": "Chaoyang"}, "city__chaozhou": {"message": "Chaozhou"}, "city__chengde": {"message": "Chengde"}, "city__chengdu": {"message": "Chengdu"}, "city__chengmai": {"message": "<PERSON><PERSON><PERSON>"}, "city__chenzhou": {"message": "Chenzhou"}, "city__chiayi": {"message": "<PERSON><PERSON><PERSON>"}, "city__chifeng": {"message": "<PERSON><PERSON>"}, "city__chizhou": {"message": "Chizhou"}, "city__chongzuo": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__chuxiong": {"message": "Chuxiong"}, "city__chuzhou": {"message": "Chuzhou"}, "city__dali": {"message": "<PERSON><PERSON>"}, "city__dalian": {"message": "<PERSON><PERSON>"}, "city__dandong": {"message": "Dandong"}, "city__danzhou": {"message": "Danzhou"}, "city__daqing": {"message": "Daqing"}, "city__datong": {"message": "<PERSON><PERSON><PERSON>"}, "city__daxinganling": {"message": "<PERSON><PERSON>'<PERSON>ling"}, "city__dazhou": {"message": "Dazhou"}, "city__dehong": {"message": "<PERSON><PERSON>"}, "city__deyang": {"message": "Deyang"}, "city__dezhou": {"message": "Dezhou"}, "city__dingan": {"message": "Ding'an"}, "city__dingxi": {"message": "Dingxi"}, "city__diqing": {"message": "Diqing"}, "city__dongfang": {"message": "<PERSON><PERSON>g"}, "city__dongguan": {"message": "Dongguan"}, "city__dongying": {"message": "<PERSON><PERSON>"}, "city__enshi": {"message": "<PERSON><PERSON>"}, "city__ezhou": {"message": "Ezhou"}, "city__fangchenggang": {"message": "Fangchenggang"}, "city__foshan": {"message": "<PERSON><PERSON><PERSON>"}, "city__fushun": {"message": "<PERSON><PERSON><PERSON>"}, "city__fuxin": {"message": "Fuxin"}, "city__fuyang": {"message": "Fuyang"}, "city__fuzhou": {"message": "Fuzhou"}, "city__gannan": {"message": "<PERSON><PERSON><PERSON>"}, "city__ganzhou": {"message": "Ganzhou"}, "city__ganzi": {"message": "Ganzi"}, "city__guangan": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__guangyuan": {"message": "Guangyuan"}, "city__guangzhou": {"message": "Guangzhou"}, "city__guigang": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__guilin": {"message": "<PERSON><PERSON><PERSON>"}, "city__guiyang": {"message": "Guiyang"}, "city__guolok": {"message": "Guolok"}, "city__guyuan": {"message": "<PERSON><PERSON>"}, "city__haibei": {"message": "Haibei"}, "city__haidong": {"message": "Haidong"}, "city__haikou": {"message": "Hai<PERSON><PERSON>"}, "city__hainan": {"message": "Hainan"}, "city__haixi": {"message": "Haixi"}, "city__hami": {"message": "<PERSON><PERSON>"}, "city__handan": {"message": "<PERSON><PERSON>"}, "city__hangzhou": {"message": "Hangzhou"}, "city__hanzhong": {"message": "Hanzhong"}, "city__harbin": {"message": "<PERSON><PERSON><PERSON>"}, "city__hebi": {"message": "<PERSON><PERSON>"}, "city__hechi": {"message": "<PERSON><PERSON>"}, "city__hefei": {"message": "<PERSON><PERSON><PERSON>"}, "city__hegang": {"message": "<PERSON><PERSON><PERSON>"}, "city__heihe": {"message": "<PERSON><PERSON><PERSON>"}, "city__hengshui": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__hengyang": {"message": "Hengyang"}, "city__heyuan": {"message": "<PERSON><PERSON>"}, "city__heze": {"message": "<PERSON><PERSON>"}, "city__hezhou": {"message": "Hezhou"}, "city__hohhot": {"message": "Hohhot"}, "city__honghe": {"message": "<PERSON><PERSON>"}, "city__hongkongisland": {"message": "Hong Kong Island"}, "city__hotan": {"message": "<PERSON><PERSON>"}, "city__hsinchu": {"message": "<PERSON><PERSON><PERSON>"}, "city__huaian": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__huaibei": {"message": "<PERSON><PERSON><PERSON>"}, "city__huaihua": {"message": "Huaihua"}, "city__huaisouth": {"message": "Huai South"}, "city__hualien": {"message": "<PERSON><PERSON><PERSON>"}, "city__huanggang": {"message": "<PERSON><PERSON><PERSON>"}, "city__huangnan": {"message": "Huangnan"}, "city__huangshan": {"message": "<PERSON><PERSON>"}, "city__huangshi": {"message": "<PERSON><PERSON>"}, "city__huizhou": {"message": "Huizhou"}, "city__huludao": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__hulunbuir": {"message": "Hulunbuir"}, "city__huzhou": {"message": "Huzhou"}, "city__jiamusi": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__jian": {"message": "<PERSON><PERSON><PERSON>"}, "city__jiangmen": {"message": "Jiangmen"}, "city__jiaozuo": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__jiaxing": {"message": "Jiaxing"}, "city__jiayuguan": {"message": "Jiayuguan"}, "city__jieyang": {"message": "<PERSON><PERSON><PERSON>"}, "city__jilin": {"message": "<PERSON><PERSON>"}, "city__jinan": {"message": "<PERSON><PERSON>"}, "city__jinchang": {"message": "Jinchang"}, "city__jincheng": {"message": "Jincheng"}, "city__jingdezhen": {"message": "Jingdez<PERSON>"}, "city__jingmen": {"message": "Jingmen"}, "city__jingzhou": {"message": "Jingzhou"}, "city__jinhua": {"message": "Jinhua"}, "city__jining": {"message": "Jining"}, "city__jinzhong": {"message": "Jinzhong"}, "city__jinzhou": {"message": "Jinzhou"}, "city__jiujiang": {"message": "Jiujiang"}, "city__jiuquan": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__jixi": {"message": "Ji<PERSON>"}, "city__kaifeng": {"message": "Kaifeng"}, "city__kaohsiung": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__karamay": {"message": "<PERSON><PERSON><PERSON>"}, "city__kashgar": {"message": "Ka<PERSON><PERSON>"}, "city__keelung": {"message": "Keelung"}, "city__kinmen": {"message": "Kinmen"}, "city__kizilsu": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__kowloon": {"message": "Kowloon"}, "city__kunming": {"message": "Ku<PERSON><PERSON>"}, "city__laibin": {"message": "<PERSON><PERSON>"}, "city__laiwu": {"message": "<PERSON><PERSON>"}, "city__langfang": {"message": "<PERSON><PERSON><PERSON>"}, "city__lanzhou": {"message": "Lanzhou"}, "city__ledong": {"message": "<PERSON><PERSON>"}, "city__leshan": {"message": "<PERSON><PERSON>"}, "city__lhasa": {"message": "Lhasa"}, "city__liangshan": {"message": "Liangshan"}, "city__lianyungang": {"message": "Lianyungang"}, "city__liaocheng": {"message": "<PERSON><PERSON><PERSON>"}, "city__liaoyang": {"message": "<PERSON><PERSON><PERSON>"}, "city__liaoyuan": {"message": "<PERSON><PERSON><PERSON>"}, "city__lienchiang": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__lijiang": {"message": "Lijiang"}, "city__lincang": {"message": "Lincang"}, "city__linfen": {"message": "Linfen"}, "city__lingao": {"message": "Lingao"}, "city__lingshui": {"message": "<PERSON><PERSON><PERSON>"}, "city__linxia": {"message": "Lin<PERSON>"}, "city__linyi": {"message": "<PERSON><PERSON>"}, "city__lishui": {"message": "Li<PERSON><PERSON>"}, "city__liupanshui": {"message": "Liupanshu<PERSON>"}, "city__liuzhou": {"message": "Liuzhou"}, "city__longnan": {"message": "<PERSON><PERSON>"}, "city__longyan": {"message": "<PERSON><PERSON>"}, "city__loudi": {"message": "<PERSON><PERSON>"}, "city__luan": {"message": "<PERSON><PERSON><PERSON>"}, "city__luohe": {"message": "<PERSON><PERSON><PERSON>"}, "city__luoyang": {"message": "<PERSON><PERSON><PERSON>"}, "city__luzhou": {"message": "Luzhou"}, "city__lüliang": {"message": "<PERSON>眉liang"}, "city__maanshan": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__macauoutlyingislands": {"message": "Macau Outlying Islands"}, "city__macaupeninsula": {"message": "Macau Peninsula"}, "city__maoming": {"message": "<PERSON><PERSON>"}, "city__meishan": {"message": "<PERSON><PERSON>"}, "city__meizhou": {"message": "Meizhou"}, "city__mianyang": {"message": "Mianyang"}, "city__miaoli": {"message": "<PERSON><PERSON>"}, "city__mudanjiang": {"message": "Mudanjiang"}, "city__nagqu": {"message": "<PERSON>g<PERSON>u"}, "city__nanchang": {"message": "Nanchang"}, "city__nanchong": {"message": "<PERSON><PERSON><PERSON>"}, "city__nanjing": {"message": "Nanjing"}, "city__nanning": {"message": "Nanning"}, "city__nanping": {"message": "<PERSON><PERSON>"}, "city__nantong": {"message": "Nantong"}, "city__nantou": {"message": "<PERSON><PERSON><PERSON>"}, "city__nanyang": {"message": "Nanyang"}, "city__neijiang": {"message": "Neijiang"}, "city__newterritories": {"message": "New Territories"}, "city__ngari": {"message": "<PERSON><PERSON>"}, "city__ningbo": {"message": "Ningbo"}, "city__ningde": {"message": "<PERSON><PERSON><PERSON>"}, "city__nujiang": {"message": "Nujiang"}, "city__nyingchi": {"message": "Nyingchi"}, "city__ordos": {"message": "Ordos"}, "city__panjin": {"message": "Panjin"}, "city__panzhihua": {"message": "Pan Zhihua"}, "city__penghu": {"message": "<PERSON><PERSON><PERSON>"}, "city__pingdingshan": {"message": "Pingdingshan"}, "city__pingliang": {"message": "<PERSON><PERSON><PERSON>"}, "city__pingtung": {"message": "<PERSON><PERSON><PERSON>"}, "city__pingxiang": {"message": "<PERSON><PERSON><PERSON>"}, "city__puer": {"message": "<PERSON>u'er"}, "city__putian": {"message": "<PERSON><PERSON>"}, "city__puyang": {"message": "P<PERSON><PERSON>"}, "city__qiandongnan": {"message": "Qiandongnan"}, "city__qianjiang": {"message": "Qianjiang"}, "city__qiannan": {"message": "<PERSON><PERSON><PERSON>"}, "city__qianxinan": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__qingdao": {"message": "Qingdao"}, "city__qingyang": {"message": "Qingyang"}, "city__qingyuan": {"message": "Qingyuan"}, "city__qinhuangdao": {"message": "Qinhuangdao"}, "city__qinzhou": {"message": "Qinzhou"}, "city__qionghai": {"message": "Qionghai"}, "city__qiongzhong": {"message": "Qiongzhong"}, "city__qiqihar": {"message": "Qiqihar"}, "city__qitaihe": {"message": "<PERSON><PERSON><PERSON>"}, "city__quanzhou": {"message": "Quanzhou"}, "city__qujing": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__quzhou": {"message": "Quzhou"}, "city__rizhao": {"message": "<PERSON><PERSON><PERSON>"}, "city__sanmenxia": {"message": "Sanmenxia"}, "city__sanming": {"message": "Sanming"}, "city__sanya": {"message": "Sanya"}, "city__shangluo": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__shangqiu": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__shangrao": {"message": "<PERSON><PERSON><PERSON>"}, "city__shannan": {"message": "Shannan"}, "city__shantou": {"message": "<PERSON><PERSON><PERSON>"}, "city__shanwei": {"message": "Shanwei"}, "city__shaoguan": {"message": "Shaoguan"}, "city__shaoxing": {"message": "Shaoxing"}, "city__shaoyang": {"message": "Shaoyang"}, "city__shennongjiaforestarea": {"message": "Shennongjia Forest Area"}, "city__shenyang": {"message": "Shenyang"}, "city__shenzhen": {"message": "Shenzhen"}, "city__shigatse": {"message": "Shigat<PERSON>"}, "city__shihezi": {"message": "<PERSON><PERSON><PERSON>"}, "city__shijiazhuang": {"message": "Shijiazhuang"}, "city__shiyan": {"message": "<PERSON><PERSON>"}, "city__shizuishan": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__shuangyashan": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__shuozhou": {"message": "Shuozhou"}, "city__siping": {"message": "<PERSON><PERSON>"}, "city__songyuan": {"message": "Songyuan"}, "city__suihua": {"message": "Su<PERSON>ua"}, "city__suining": {"message": "Suining"}, "city__suizhou": {"message": "<PERSON><PERSON><PERSON>"}, "city__suqian": {"message": "<PERSON><PERSON><PERSON>"}, "city__suzhou": {"message": "Suzhou"}, "city__tacheng": {"message": "Tacheng"}, "city__taian": {"message": "Tai'an"}, "city__taichung": {"message": "Taichung"}, "city__tainan": {"message": "Tainan"}, "city__taipei": {"message": "Taipei"}, "city__taitung": {"message": "<PERSON><PERSON><PERSON>"}, "city__taiyuan": {"message": "Taiyuan"}, "city__taizhou": {"message": "Taizhou"}, "city__tangshan": {"message": "Tangshan"}, "city__taoyuan": {"message": "Taoyuan"}, "city__tianmen": {"message": "Tianmen"}, "city__tianshui": {"message": "<PERSON><PERSON><PERSON>"}, "city__tieling": {"message": "Tieling"}, "city__tongchuan": {"message": "<PERSON><PERSON><PERSON>"}, "city__tonghua": {"message": "Tonghua"}, "city__tongliao": {"message": "<PERSON><PERSON><PERSON>"}, "city__tongling": {"message": "Tongling"}, "city__tongren": {"message": "<PERSON><PERSON>"}, "city__tumushuke": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__tunchang": {"message": "Tunchang"}, "city__turpan": {"message": "<PERSON><PERSON><PERSON>"}, "city__ulanqab": {"message": "Ulanqab"}, "city__urumqi": {"message": "Urumqi"}, "city__wanning": {"message": "Wanning"}, "city__weifang": {"message": "<PERSON><PERSON><PERSON>"}, "city__weihai": {"message": "Weihai"}, "city__weinan": {"message": "Weinan"}, "city__wenchang": {"message": "Wenchang"}, "city__wenshan": {"message": "<PERSON><PERSON>"}, "city__wenzhou": {"message": "Wenzhou"}, "city__wuhai": {"message": "Wu<PERSON>"}, "city__wuhan": {"message": "<PERSON><PERSON>"}, "city__wuhu": {"message": "<PERSON><PERSON>"}, "city__wujiaqu": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__wuwei": {"message": "<PERSON><PERSON>"}, "city__wuxi": {"message": "Wuxi"}, "city__wuzhishan": {"message": "Wuzhishan"}, "city__wuzhong": {"message": "Wuzhong"}, "city__wuzhou": {"message": "Wuzhou"}, "city__xiamen": {"message": "Xiamen"}, "city__xian": {"message": "Xi'<PERSON>"}, "city__xiangfan": {"message": "<PERSON><PERSON><PERSON>"}, "city__xiangtan": {"message": "Xiangtan"}, "city__xiangxi": {"message": "Xiangxi"}, "city__xianning": {"message": "Xianning"}, "city__xiantao": {"message": "Xiantao"}, "city__xianyang": {"message": "<PERSON><PERSON><PERSON>"}, "city__xiaogan": {"message": "<PERSON><PERSON>"}, "city__xilingol": {"message": "Xilingol"}, "city__xinganleague": {"message": "Xing'an League"}, "city__xingtai": {"message": "Xingtai"}, "city__xining": {"message": "Xining"}, "city__xinxiang": {"message": "Xinxiang"}, "city__xinyang": {"message": "Xinyang"}, "city__xinyu": {"message": "Xinyu"}, "city__xinzhou": {"message": "Xinzhou"}, "city__xishuangbanna": {"message": "Xishuangbanna"}, "city__xuancheng": {"message": "Xuancheng"}, "city__xuchang": {"message": "Xuchang"}, "city__xuzhou": {"message": "Xuzhou"}, "city__yaan": {"message": "Ya'an"}, "city__yanan": {"message": "Yan'<PERSON>"}, "city__yanbian": {"message": "Yanbian"}, "city__yancheng": {"message": "Yancheng"}, "city__yangjiang": {"message": "Yangjiang"}, "city__yangquan": {"message": "<PERSON><PERSON><PERSON>"}, "city__yangzhou": {"message": "Yangzhou"}, "city__yantai": {"message": "Yantai"}, "city__yibin": {"message": "<PERSON><PERSON>"}, "city__yichang": {"message": "Yichang"}, "city__yichun": {"message": "<PERSON><PERSON><PERSON>"}, "city__yilan": {"message": "Yilan"}, "city__yili": {"message": "<PERSON><PERSON>"}, "city__yinchuan": {"message": "<PERSON><PERSON><PERSON>"}, "city__yingkou": {"message": "<PERSON><PERSON><PERSON>"}, "city__yingtan": {"message": "Yingtan"}, "city__yiwu": {"message": "<PERSON><PERSON>"}, "city__yiyang": {"message": "Yiyang"}, "city__yongzhou": {"message": "Yongzhou"}, "city__yueyang": {"message": "<PERSON><PERSON><PERSON>"}, "city__yulin": {"message": "Yulin"}, "city__yuncheng": {"message": "Yuncheng"}, "city__yunfu": {"message": "<PERSON><PERSON>"}, "city__yunlin": {"message": "<PERSON><PERSON>"}, "city__yushu": {"message": "Yushu"}, "city__yuxi": {"message": "Yuxi"}, "city__zaozhuang": {"message": "Zaozhuang"}, "city__zhangjiajie": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__zhangjiakou": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__zhangye": {"message": "<PERSON><PERSON>"}, "city__zhangzhou": {"message": "Zhangzhou"}, "city__zhanjiang": {"message": "Zhanjiang"}, "city__zhaoqing": {"message": "Zhaoqing"}, "city__zhaotong": {"message": "Zhaotong"}, "city__zhengzhou": {"message": "Zhengzhou"}, "city__zhenjiang": {"message": "Zhenjiang"}, "city__zhongshan": {"message": "Zhongshan"}, "city__zhongwei": {"message": "Zhongwei"}, "city__zhoukou": {"message": "<PERSON><PERSON><PERSON>"}, "city__zhoushan": {"message": "Zhoushan"}, "city__zhuhai": {"message": "Zhu<PERSON>"}, "city__zhumadian": {"message": "Zhumadian"}, "city__zhuzhou": {"message": "Zhuzhou"}, "city__zibo": {"message": "Zibo"}, "city__zigong": {"message": "Zigong"}, "city__ziyang": {"message": "<PERSON><PERSON><PERSON>"}, "city__zunyi": {"message": "<PERSON><PERSON><PERSON>"}, "click_copy_product_info": {"message": "Noklikšķiniet uz Kopēt produkta informāciju"}, "commmon_txt_expired": {"message": "<PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON>"}, "common__date_range_12m": {"message": "1 gads"}, "common__date_range_1m": {"message": "1 mēnesis"}, "common__date_range_1w": {"message": "1 nedēļa"}, "common__date_range_2w": {"message": "2 nedēļas"}, "common__date_range_3m": {"message": "3 mēneši"}, "common__date_range_3w": {"message": "3 nedēļas"}, "common__date_range_6m": {"message": "6 mēneši"}, "common_btn_cancel": {"message": "Atcelt"}, "common_btn_close": {"message": "Aizvērt"}, "common_btn_save": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "common_btn_setting": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "common_email": {"message": "E-pasts"}, "common_error_msg_no_data": {"message": "Nav datu"}, "common_error_msg_no_result": {"message": "Diemžēl rezultāts nav atrasts."}, "common_favorites": {"message": "<PERSON><PERSON><PERSON>"}, "common_feedback": {"message": "Atsauk<PERSON><PERSON>"}, "common_help": {"message": "Palīdzī<PERSON>"}, "common_loading": {"message": "<PERSON><PERSON>"}, "common_login": {"message": "Pieslēgties"}, "common_logout": {"message": "Izlogoties"}, "common_no": {"message": "Nē"}, "common_powered_by_aliprice": {"message": "Darbina AliPrice.com"}, "common_setting": {"message": "Iestatīšana"}, "common_sign_up": {"message": "Pierakstī<PERSON>"}, "common_system_upgrading_title": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "common_system_upgrading_txt": {"message": "<PERSON><PERSON><PERSON><PERSON>, mēģiniet vēlāk"}, "common_txt__currency": {"message": "<PERSON><PERSON><PERSON>"}, "common_txt__video_tutorial": {"message": "Video pamācība"}, "common_txt_ago_time": {"message": "pirms $time$ dienām", "placeholders": {"time": {"content": "$1"}}}, "common_txt_all_product": {"message": "visi"}, "common_txt_analysis": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "common_txt_basically_used": {"message": "Gandrīz nekad nav lietots"}, "common_txt_biaoti_link": {"message": "Virsraksts+Saite"}, "common_txt_biaoti_link_dian_pu": {"message": "Nosaukums+Saite+Veikala nosaukums"}, "common_txt_blacklist": {"message": "Bloķēšanas saraksts"}, "common_txt_cancel": {"message": "Atcelt"}, "common_txt_category": {"message": "Kategorija"}, "common_txt_chakan": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "common_txt_colors": {"message": "kr<PERSON><PERSON><PERSON>"}, "common_txt_confirm": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "common_txt_copied": {"message": "Nokopēts"}, "common_txt_copy": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "common_txt_copy_link": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "common_txt_copy_title": {"message": "<PERSON><PERSON><PERSON><PERSON> virsrak<PERSON>"}, "common_txt_copy_title__link": {"message": "Nokopējiet virs<PERSON>u un saiti"}, "common_txt_day": {"message": "<PERSON><PERSON><PERSON>"}, "common_txt_day__short": {"message": "D"}, "common_txt_delete": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "common_txt_dian_pu_link": {"message": "<PERSON><PERSON><PERSON><PERSON> veikala no<PERSON> + saiti"}, "common_txt_download": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "common_txt_downloaded": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "common_txt_export_as_csv": {"message": "Eksportēt Excel"}, "common_txt_export_as_txt": {"message": "Eksportēt Txt"}, "common_txt_fail": {"message": "Neizdoties"}, "common_txt_format": {"message": "<PERSON><PERSON><PERSON>"}, "common_txt_get": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "common_txt_incert_selection": {"message": "Apgriezt atlasi"}, "common_txt_install": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "common_txt_load_failed": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "common_txt_month": {"message": "mēnesis"}, "common_txt_more": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "common_txt_new_unused": {"message": "<PERSON><PERSON><PERSON><PERSON> jauns, ne<PERSON><PERSON><PERSON>"}, "common_txt_next": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "common_txt_no_limit": {"message": "Neierobežots"}, "common_txt_no_noticeable": {"message": "Nav redzamu skrāpējumu vai netīrumu"}, "common_txt_on_sale": {"message": "<PERSON><PERSON><PERSON>"}, "common_txt_opt_in_out": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "common_txt_order": {"message": "Pasūtījums"}, "common_txt_others": {"message": "Citi"}, "common_txt_overall_poor_condition": {"message": "Kopumā slikts stāvoklis"}, "common_txt_patterns": {"message": "<PERSON><PERSON><PERSON>"}, "common_txt_platform": {"message": "Platformas"}, "common_txt_please_select": {"message": "Lūdzu, izvēlieties"}, "common_txt_prev": {"message": "Iepriek<PERSON><PERSON><PERSON><PERSON>"}, "common_txt_price": {"message": "<PERSON><PERSON>"}, "common_txt_privacy_policy": {"message": "Privātuma politika"}, "common_txt_product_condition": {"message": "Produkta statuss"}, "common_txt_rating": {"message": "<PERSON><PERSON><PERSON>ē<PERSON><PERSON>"}, "common_txt_ratings": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "common_txt_reload": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "common_txt_reset": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "common_txt_review": {"message": "Pārskatīša<PERSON>"}, "common_txt_sale": {"message": "<PERSON><PERSON><PERSON>"}, "common_txt_same": {"message": "Tas pats"}, "common_txt_scratches_and_dirt": {"message": "Ar skrāpējumiem un netīrumiem"}, "common_txt_search_title": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "common_txt_select_all": {"message": "Izvēlēties visus"}, "common_txt_selected": {"message": "atlasīts"}, "common_txt_share": {"message": "Dalīties"}, "common_txt_sold": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "common_txt_sold_out": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "common_txt_some_scratches": {"message": "<PERSON><PERSON><PERSON> s<PERSON> un netīrumi"}, "common_txt_sort_by": {"message": "<PERSON><PERSON><PERSON><PERSON> pēc"}, "common_txt_state": {"message": "Statuss"}, "common_txt_success": {"message": "Panākums"}, "common_txt_sys_err": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "common_txt_today": {"message": "Šodien"}, "common_txt_total": {"message": "visi"}, "common_txt_unselect_all": {"message": "Apgriezt atlasi"}, "common_txt_upload_image": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> att<PERSON>"}, "common_txt_visit": {"message": "Apmeklējums"}, "common_txt_whitelist": {"message": "<PERSON><PERSON><PERSON> sa<PERSON>"}, "common_txt_wildberries": {"message": "Wildberries"}, "common_txt_year": {"message": "Gads"}, "common_yes": {"message": "Jā"}, "compare_tool_btn_clear_all": {"message": "Nodzēst visu"}, "compare_tool_btn_compare": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "compare_tool_btn_contact": {"message": "Kontakts"}, "compare_tool_tips_max_compared": {"message": "Pievienojiet līdz $maxComparedCount$", "placeholders": {"maxComparedCount": {"content": "$1"}}}, "configure_notifiactions": {"message": "Kon<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "contact_us": {"message": "Sa<PERSON>ies ar mums"}, "context_menu_screenshot_search": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>, lai meklētu pēc attēla"}, "context_menus_aliprice_search_by_image": {"message": "Meklēt attēlu vietnē AliPrice"}, "context_menus_goote_trans": {"message": "Tulkot lapu/Rādīt oriģinālu"}, "context_menus_search_by_image": {"message": "Meklēt pēc attēla vietnē $storeName$", "placeholders": {"storeName": {"content": "$1"}}}, "context_menus_search_by_image_capture": {"message": "Uzņemt vietnē $storeName$", "placeholders": {"storeName": {"content": "$1"}}}, "context_menus_translator": {"message": "U<PERSON>ņ<PERSON><PERSON>, lai tulkotu"}, "converter_modal_amount_placeholder": {"message": "Ievadiet summu šeit"}, "converter_modal_btn_convert": {"message": "konvertēt"}, "converter_modal_exchange_rate_source": {"message": "Dati nāk no $boc$ ārvalstu valūtas kursa Atjaunināšanas laiks: $updatedAt$", "placeholders": {"boc": {"content": "$1"}, "updatedAt": {"content": "$2"}}}, "converter_modal_name": {"message": "valūtas konvertēšana"}, "converter_modal_search_placeholder": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> val<PERSON>ta"}, "copy_all_contact_us_notice": {"message": "<PERSON><PERSON> vietne pa<PERSON><PERSON>k netiek at<PERSON>, <PERSON><PERSON><PERSON><PERSON>, sa<PERSON><PERSON><PERSON> ar mums"}, "copy_product_info": {"message": "Kopējiet produkta informāciju"}, "copy_suggest_search_kw": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "country__han_gou": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "country__ri_ben": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "country__yue_nan": {"message": "Vjetnama"}, "currency_convert__custom": {"message": "Pielāgot<PERSON> val<PERSON>tas k<PERSON>s"}, "currency_convert__sync_server": {"message": "Sinhronizēt serveri"}, "dang_ri_fa_huo": {"message": "<PERSON><PERSON><PERSON><PERSON> tajā pašā dienā"}, "dao_chu_quan_dian_shang_pin": {"message": "Eksportējiet visus veikala produktus"}, "dao_chu_wei_CSV": {"message": "Eksportēt"}, "dao_chu_zi_duan": {"message": "Eksporta lauki"}, "delivery_address": {"message": "<PERSON><PERSON><PERSON><PERSON> adrese"}, "delivery_company": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "di_zhi": {"message": "adrese"}, "dian_ji_cha_xun": {"message": "Noklikšķiniet, lai vaicātu"}, "dian_pu_ID": {"message": "Veikala ID"}, "dian_pu_di_zhi": {"message": "<PERSON>ei<PERSON><PERSON> adrese"}, "dian_pu_lian_jie": {"message": "Veikala saite"}, "dian_pu_ming": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "dian_pu_ming_cheng": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "dian_pu_shang_pin_zong_hsu": {"message": "Kopējais produktu skaits veikalā: $num$", "placeholders": {"num": {"content": "$1"}}}, "dian_pu_xin_xi": {"message": "Glabājiet <PERSON>"}, "ding_zai_zuo_ce": {"message": "pienaglots pa kreisi"}, "disable_old_version_tips_disable_btn_title": {"message": "Atspējot veco versiju"}, "download_image__SKU_variant_images": {"message": "SKU variantu attēli"}, "download_image__assume": {"message": "<PERSON><PERSON><PERSON><PERSON>, mums ir 2 att<PERSON><PERSON>, produkts1.jpg un produkts2.gif.\nimg_{$no$} tiks pārdēvēts par img_01.jpg, img_02.gif;\n{$group$}_{$no$} tiks pārdēvēta par main_image_01.jpg, main_image_02.gif;", "placeholders": {"no": {"content": "$3"}, "group": {"content": "$2"}}}, "download_image__batch_download": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "download_image__combined_image": {"message": "Komplektētā produkta detalizētais attēls"}, "download_image__continue_downloading": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "download_image__description_images": {"message": "<PERSON><PERSON><PERSON> att<PERSON>"}, "download_image__download_combined_image": {"message": "Lejupiel<PERSON><PERSON><PERSON>t kombinētā produkta detalizēto attēlu"}, "download_image__download_zip": {"message": "Lejupielādēt zip"}, "download_image__enlarge_check": {"message": "Atbalsta tikai JPEG, JPG, GIF un PNG attēlus, viena attēla maksim<PERSON> izmērs: 1600 * 1600"}, "download_image__enlarge_image": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON> att<PERSON>"}, "download_image__export": {"message": "Eksportēt"}, "download_image__height": {"message": "Augstums"}, "download_image__ignore_videos": {"message": "<PERSON>k<PERSON><PERSON> ir i<PERSON>, jo to nevar e<PERSON>t"}, "download_image__img_translate": {"message": "Attēlu tulkošana"}, "download_image__main_image": {"message": "galvenais attēls"}, "download_image__multi_folder": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "download_image__name": {"message": "leju<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> attēlu"}, "download_image__notice_content": {"message": "<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>kprogram<PERSON> lejupielādes iestatījumos neatzīmējiet izvēles rūtiņu \"J<PERSON><PERSON><PERSON><PERSON>, kur saglabāt katru failu pirms lejupielādes\"!!! Pretējā gadījumā būs daudz dialoglodziņu."}, "download_image__notice_ignore": {"message": "<PERSON><PERSON><PERSON>"}, "download_image__order_number": {"message": "{$no$} s<PERSON><PERSON><PERSON> numurs; {$group$} grupas nosaukums; {$date$} laikspiedols", "placeholders": {"no": {"content": "$1"}, "group": {"content": "$2"}, "date": {"content": "$3"}}}, "download_image__overview": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "download_image__prompt_download_zip": {"message": "Attē<PERSON> ir pār<PERSON>k <PERSON>, labāk tos lejupielādēt kā zip mapi."}, "download_image__rename": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "download_image__rule": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "download_image__single_folder": {"message": "Viena mape"}, "download_image__sku_image": {"message": "SKU attēli"}, "download_image__video": {"message": "video"}, "download_image__width": {"message": "Platums"}, "download_reviews__download_images": {"message": "Lejupielādēt pārskata attēlu"}, "download_reviews__dropdown_title": {"message": "Lejupielādēt pārskata attēlu"}, "download_reviews__export_csv": {"message": "eksportēt CSV"}, "download_reviews__no_images": {"message": "Lejupielādei pieejami 0 attēli"}, "download_reviews__no_reviews": {"message": "Nav recenzijas, ko lejupielād<PERSON>t!"}, "download_reviews__notice": {"message": "Padoms:"}, "download_reviews__notice__chrome_settings": {"message": "Iestatiet pārlūkprogrammu Chrome, lai pirms lejupiel<PERSON><PERSON>, kur saglab<PERSON>t katru failu, iestatiet uz “I<PERSON>lē<PERSON>”."}, "download_reviews__notice__wait": {"message": "Atkarībā no atsauksmju skaita gaidīša<PERSON> laiks var būt ilg<PERSON>ks"}, "download_reviews__pages_list__all": {"message": "Visi"}, "download_reviews__pages_list__page": {"message": "Iepriekšējās $page$ lapas", "placeholders": {"page": {"content": "$1"}}}, "download_reviews__pages_list_title": {"message": "Atlases diapazons"}, "export_shopping_cart__csv_filed__details_url": {"message": "Produkta saite"}, "export_shopping_cart__csv_filed__details_url_with_sku": {"message": "Echo SKU saite"}, "export_shopping_cart__csv_filed__images": {"message": "<PERSON><PERSON><PERSON><PERSON> saite"}, "export_shopping_cart__csv_filed__quantity": {"message": "Daudzums"}, "export_shopping_cart__csv_filed__sale_price": {"message": "<PERSON><PERSON>"}, "export_shopping_cart__csv_filed__specs": {"message": "Spec<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "export_shopping_cart__csv_filed__store_name": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "export_shopping_cart__csv_filed__store_url": {"message": "Veikala saite"}, "export_shopping_cart__csv_filed__title": {"message": "Produkta no<PERSON><PERSON>ms"}, "export_shopping_cart__export_btn": {"message": "Eksportēt"}, "export_shopping_cart__export_empty": {"message": "<PERSON><PERSON><PERSON><PERSON>, izv<PERSON>lieties produktu!"}, "fa_huo_shi_jian": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "favorite_add_email": {"message": "Pievienot e-pastu"}, "favorite_add_favorites": {"message": "<PERSON><PERSON><PERSON>"}, "favorite_added": {"message": "Pievienots"}, "favorite_btn_add": {"message": "<PERSON><PERSON><PERSON><PERSON>ājums par cenu kritumu."}, "favorite_btn_notify": {"message": "Trases cena"}, "favorite_cate_name_all": {"message": "Visi produkti"}, "favorite_current_price": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> cena"}, "favorite_due_date": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON> datums"}, "favorite_enable_notification": {"message": "<PERSON><PERSON><PERSON><PERSON>, iespējojiet e-pasta paziņojumus"}, "favorite_expired": {"message": "<PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON>"}, "favorite_go_to_enable": {"message": "<PERSON><PERSON><PERSON><PERSON>, lai ies<PERSON>"}, "favorite_msg_add_success": {"message": "Pievienots izlasei"}, "favorite_msg_del_success": {"message": "Izdzēsts no izlases"}, "favorite_msg_failure": {"message": "Neveiksme! Atsvaidziniet lapu un mēģiniet vēlreiz."}, "favorite_please_add_email": {"message": "<PERSON><PERSON><PERSON><PERSON>, pievienojiet e-pastu"}, "favorite_price_drop": {"message": "Uz leju"}, "favorite_price_rise": {"message": "<PERSON><PERSON> augšu"}, "favorite_price_untracked": {"message": "Cena nav izsekota"}, "favorite_saved_price": {"message": "Saglabātā cena"}, "favorite_stop_tracking": {"message": "Pārtrauciet izsekošanu"}, "favorite_sub_email_address": {"message": "Abonēšanas e-pasta adrese"}, "favorite_tracking_period": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> periods"}, "favorite_tracking_prices": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> cen<PERSON>"}, "favorite_verify_email": {"message": "Verificējiet e-pasta adresi"}, "favorites_list_remove_prompt_msg": {"message": "<PERSON>ai tiešām to i<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>?"}, "favorites_update_button": {"message": "Atjauniniet cena<PERSON> t<PERSON>"}, "fen_lei": {"message": "Kategorija"}, "fen_xia_yan_xuan": {"message": "Izplatītāja izvēle"}, "find_similar": {"message": "Atrodiet līdzīgus"}, "first_ali_price_date": {"message": "<PERSON><PERSON><PERSON>, kad pirmo reizi tvēra AliPrice r<PERSON>ļprogramma"}, "fooview_coupons_modal_no_data": {"message": "Nav k<PERSON>u"}, "fooview_coupons_modal_title": {"message": "<PERSON><PERSON><PERSON>"}, "fooview_favorites__product_sub__tooltip_l1": {"message": "Cena < $lowerPrice$", "placeholders": {"lowerPrice": {"content": "$1"}}}, "fooview_favorites__product_sub__tooltip_l2": {"message": "vai cena > $higherPrice$", "placeholders": {"higherPrice": {"content": "$1"}}}, "fooview_favorites__product_sub__tooltip_l3": {"message": "<PERSON><PERSON><PERSON><PERSON> laiks"}, "fooview_favorites_error_msg_no_favorites": {"message": "Pievienojiet šeit iecienītākos produktus, lai saņemtu brīdinājumu par cenu kritumu."}, "fooview_favorites_filter_latest": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "fooview_favorites_filter_price_drop": {"message": "cenas <PERSON>"}, "fooview_favorites_filter_price_up": {"message": "cenu pieaugums"}, "fooview_favorites_modal_title": {"message": "<PERSON><PERSON>"}, "fooview_favorites_modal_title_title": {"message": "Dodieties uz AliPrice Favorite"}, "fooview_favorites_track_price": {"message": "<PERSON> cenu"}, "fooview_price_history_app_price": {"message": "APP cena:"}, "fooview_price_history_title": {"message": "Cenu vēsture"}, "fooview_product_list_feedback": {"message": "Atsauk<PERSON><PERSON>"}, "fooview_product_list_orders": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "fooview_product_list_price": {"message": "<PERSON><PERSON>"}, "fooview_reviews_error_msg_no_review": {"message": "<PERSON><PERSON><PERSON>m atsauksmes par šo produktu."}, "fooview_reviews_filter_buyer_reviews": {"message": "Pircēju fotogrāfijas"}, "fooview_reviews_modal_title": {"message": "Atsauk<PERSON><PERSON>"}, "fooview_same_product_choose_category": {"message": "Izvēlieties kategoriju"}, "fooview_same_product_filter_feedback": {"message": "Atsauk<PERSON><PERSON>"}, "fooview_same_product_filter_orders": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "fooview_same_product_filter_price": {"message": "<PERSON><PERSON>"}, "fooview_same_product_filter_rating": {"message": "<PERSON><PERSON><PERSON>ē<PERSON><PERSON>"}, "fooview_same_product_modal_title": {"message": "Atrodiet to pašu produktu"}, "fooview_same_product_search_by_image": {"message": "Me<PERSON><PERSON><PERSON><PERSON> pēc attēla"}, "fooview_seller_analysis_modal_title": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "for_12_months": {"message": "Uz 1 gadu"}, "for_12_months_list_pro": {"message": "12 mēneši"}, "for_12_months_nei": {"message": "12 mēnešu laikā"}, "for_1_months": {"message": "1 mēnesis"}, "for_1_months_nei": {"message": "1 m<PERSON>ne<PERSON> laikā"}, "for_3_months": {"message": "3 mēnešus"}, "for_3_months_nei": {"message": "3 mēne<PERSON>u laikā"}, "for_6_months": {"message": "6 mēnešus"}, "for_6_months_nei": {"message": "6 mēne<PERSON>u laikā"}, "for_9_months": {"message": "9 mēneši"}, "for_9_months_nei": {"message": "9 mēne<PERSON>u laikā"}, "fu_gou_lv": {"message": "Atpirkšanas likme"}, "gao_liang_bu_tong_dian": {"message": "izcelt atšķirības"}, "gao_liang_guang_gao_chan_pin": {"message": "Izceliet reklāmas produktus"}, "geng_duo_xin_xi": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "geng_xin_shi_jian": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> laiks"}, "get_store_products_fail_tip": {"message": "Noklikšķiniet uz <PERSON><PERSON>, lai pārietu uz veri<PERSON><PERSON>, lai nod<PERSON><PERSON><PERSON>uvi"}, "gong_x_kuan_shang_pin": {"message": "Kopā produkti $amount$", "placeholders": {"amount": {"content": "$1"}}}, "gong_ying_shang": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "gong_ying_shang_ID": {"message": "Piegādātāja ID"}, "gong_ying_shang_deng_ji": {"message": "Piegād<PERSON><PERSON><PERSON><PERSON> vērtējums"}, "gong_ying_shang_nian_zhan": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> ir ve<PERSON>ks"}, "gong_ying_shang_xin_xi": {"message": "piegād<PERSON><PERSON><PERSON><PERSON>"}, "gong_ying_shang_zhu_ye_lian_jie": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> mā<PERSON>as saite"}, "green_rocket": {"message": "로켓프레시"}, "grey_rocket": {"message": "로켓설치"}, "gu_ji_shou_jia": {"message": "<PERSON><PERSON><PERSON><PERSON> cena"}, "guan_jian_zi": {"message": "Atslēgvārds"}, "guang_gao_chan_pin": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON> produktiem"}, "guang_gao_zhan_bi": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "guo_ji_wu_liu_yun_fei": {"message": "Starptautiskā piegādes maksa"}, "guo_lv_tiao_jian": {"message": "<PERSON><PERSON><PERSON>"}, "hao_ping_lv": {"message": "Pozitīvs vērtēju<PERSON>"}, "highest_price": {"message": "Augsts"}, "historical_trend": {"message": "Vēst<PERSON>skā tendence"}, "how_to_screenshot": {"message": "Tu<PERSON>t nospiestu peles kreiso pogu, lai atlas<PERSON><PERSON>, pieskarieties peles labās pogas vai Esc taustiņam, lai izietu no ekrānuzņēmuma"}, "howt_it_works": {"message": "<PERSON>ā tas strādā"}, "hui_fu_lv": {"message": "Reakcijas ātrums"}, "hui_tou_lv": {"message": "atgriešanas likme"}, "inquire_freightFee": {"message": "Kravu izmeklēšana"}, "inquire_freightFee_Yuan": {"message": "Krava/juaņa"}, "inquire_freightFee_province": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "inquire_freightFee_the": {"message": "<PERSON><PERSON><PERSON> cena ir $num$ , kas no<PERSON><PERSON><PERSON><PERSON>, ka reģionā ir bezma<PERSON> pieg<PERSON>.", "placeholders": {"num": {"content": "$1"}}}, "is_ad": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "jia_ge": {"message": "<PERSON><PERSON>"}, "jia_ge_dan_wei": {"message": "Vienība"}, "jia_ge_qu_shi": {"message": "tendence"}, "jia_zai_n_ge_shang_pin": {"message": "Ielādēt $num$ produktus", "placeholders": {"num": {"content": "$1"}}}, "jin_30_tian_xiao_liang_zhan_bi": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON> apjoma procentu<PERSON><PERSON><PERSON> daļa pēdējo 30 dienu laikā"}, "jin_30_tian_xiao_shou_e_zhan_bi": {"message": "<PERSON><PERSON>ņ<PERSON><PERSON><PERSON> procentu<PERSON><PERSON><PERSON> daļa pēdējo 30 dienu laikā"}, "jin_30d_xiao_liang": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "jin_30d_xiao_liang__desc": {"message": "Ko<PERSON><PERSON><PERSON><PERSON> p<PERSON><PERSON> apjoms pēdējo 30 dienu laik<PERSON>"}, "jin_30d_xiao_shou_e": {"message": "Apgrozījums"}, "jin_30d_xiao_shou_e__desc": {"message": "Kopējais apgrozījums pēdējo 30 dienu laikā"}, "jin_90_tian_mai_jia_shu": {"message": "<PERSON><PERSON><PERSON><PERSON> pē<PERSON><PERSON><PERSON> 90 dienu laikā"}, "jin_90_tian_xiao_shou_liang": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON> pē<PERSON><PERSON><PERSON> 90 dienu laikā"}, "jing_xuan_huo_yuan": {"message": "<PERSON><PERSON><PERSON> a<PERSON>"}, "jing_ying_mo_shi": {"message": "Biznesa modelis"}, "jing_ying_mo_shi__gong_chang": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "jiu_fen_jie_jue": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "jiu_fen_jie_jue__desc": {"message": "Pārdevēju ve<PERSON> strīdu uzska<PERSON>"}, "jiu_fen_lv": {"message": "<PERSON><PERSON><PERSON><PERSON> l<PERSON>"}, "jiu_fen_lv__desc": {"message": "Pēdējo 30 dienu laikā izpild<PERSON>to pasūtīju<PERSON> ar sūdz<PERSON><PERSON><PERSON><PERSON>, par kuriem atbild pārdevējs vai abas puses"}, "kai_dian_ri_qi": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> datums"}, "keywords": {"message": "Atslēgvārdi"}, "kua_jin_Select_pan_huo": {"message": "Pārrobežu atlase"}, "last15_days": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> 15 dienas"}, "last180_days": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> 180 dienas"}, "last30_days": {"message": "Pēdējo 30 dienu laik<PERSON>"}, "last360_days": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> 360 dienas"}, "last45_days": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> 45 dienas"}, "last60_days": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> 60 dienas"}, "last7_days": {"message": "Pēdējās 7 dienas"}, "last90_days": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> 90 dienas"}, "last_30d_sales": {"message": "Pēdējo 30 dienu <PERSON>"}, "lei_ji": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "lei_ji_xiao_liang": {"message": "Kopā"}, "lei_ji_xiao_liang__desc": {"message": "Visa izpārdošana pēc produkta uz plaukta"}, "lei_ji_xiao_liang_pai_xu__desc": {"message": "Kumulatīva<PERSON> pā<PERSON>nas apjoms pēdējo 30 dienu laik<PERSON>, sakārtots no augstākā līdz zemākajam"}, "lian_xi_fang_shi": {"message": "Kontaktinformācija"}, "list_time": {"message": "<PERSON><PERSON><PERSON><PERSON> da<PERSON>"}, "load_more": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "login_to_aliprice": {"message": "Piesakieties AliPrice"}, "long_link": {"message": "<PERSON><PERSON><PERSON> saite"}, "lowest_price": {"message": "Zems"}, "mai_jia_shu": {"message": "Pārdevēju skaits"}, "mao_li_lv": {"message": "<PERSON><PERSON><PERSON>"}, "mobile_view__dkxbqy": {"message": "Atveriet jaunu cilni"}, "mobile_view__sjdxq": {"message": "Sīkāka informācija lietotnē"}, "mobile_view__sjdxqy": {"message": "Detaļas lapa lietotnē"}, "mobile_view__smck": {"message": "<PERSON>an to <PERSON>"}, "mobile_view__smckms": {"message": "<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> kameru vai lie<PERSON>, lai skenētu un skatītu"}, "modified_failed": {"message": "Modifikā<PERSON><PERSON>"}, "modified_successfully": {"message": "Veiksmīgi p<PERSON>"}, "nav_btn_favorites": {"message": "<PERSON><PERSON>"}, "nav_btn_package": {"message": "Iepakojums"}, "nav_btn_product_info": {"message": "Par produktu"}, "nav_btn_viewed": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "no_suggest_search_kw": {"message": "Loading..."}, "none": {"message": "Nav"}, "normal_link": {"message": "Parasta saite"}, "notice": {"message": "m<PERSON><PERSON><PERSON><PERSON>"}, "number_reviews": {"message": "Atsauk<PERSON><PERSON>"}, "only_show_num": {"message": "Kopējais produktu skaits: $allnum$, slēptie: $hidenum2$", "placeholders": {"allnum": {"content": "$1"}, "hidenum2": {"content": "$2"}}}, "only_show_selected": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "open": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "open_links": {"message": "<PERSON><PERSON><PERSON><PERSON> saites"}, "options_page_tab_check_links": {"message": "Pārbaudiet saites"}, "options_page_tab_gernal": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "options_page_tab_notifications": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "options_page_tab_others": {"message": "Citi"}, "options_page_tab_sbi": {"message": "Me<PERSON><PERSON><PERSON><PERSON> pēc attēla"}, "options_page_tab_shortcuts": {"message": "Īsceļi"}, "options_page_tab_shortcuts_title": {"message": "<PERSON><PERSON>a lie<PERSON> saīsnēm"}, "options_page_tab_similar_products": {"message": "Tie paši produkti"}, "orange_rocket": {"message": "판매자로켓"}, "order_list_open_links_tip": {"message": "Drīzumā tiks atvērtas vairākas produktu saites"}, "order_list_sku_show_title": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON> at<PERSON>us kopīgotajās saitēs"}, "orders_last30_days": {"message": "Pasūtījumu skaits pēdējo 30 dienu laikā"}, "pTutorial_favorites_block1_desc1": {"message": "Šeit uzskaitīti jūsu izsekotie produkti"}, "pTutorial_favorites_block1_title": {"message": "<PERSON><PERSON><PERSON>"}, "pTutorial_popup_block1_desc1": {"message": "Za<PERSON>ā etiķete nozīmē, ka ir cenas pazemināti produkti"}, "pTutorial_popup_block1_title": {"message": "Saīsnes un izlase"}, "pTutorial_price_history_block1_desc1": {"message": "Noklikšķiniet uz “Track Price”, pievienojiet produktus izlasei. Kad viņu cenas pazeminā<PERSON>, jūs sa<PERSON> paziņoju<PERSON>"}, "pTutorial_price_history_block1_title": {"message": "Trases cena"}, "pTutorial_reviews_block1_desc1": {"message": "<PERSON><PERSON><PERSON><PERSON> atsauksmes no Itao un reālas fotogrāfijas no AliExpress atsauksmēm"}, "pTutorial_reviews_block1_title": {"message": "Atsauk<PERSON><PERSON>"}, "pTutorial_reviews_block2_desc1": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON> ir <PERSON><PERSON><PERSON><PERSON> p<PERSON>t citu pircēju atsa<PERSON>smes"}, "pTutorial_same_products_block1_desc1": {"message": "<PERSON><PERSON><PERSON> varat to<PERSON>, lai i<PERSON><PERSON><PERSON><PERSON> visla<PERSON><PERSON><PERSON> i<PERSON>"}, "pTutorial_same_products_block1_desc2": {"message": "<PERSON> mekl<PERSON>tu pēc attēla, noklikšķiniet uz Vair<PERSON>."}, "pTutorial_same_products_block1_title": {"message": "Tie paši produkti"}, "pTutorial_same_products_by_image_search_block1_desc1": {"message": "Nometiet tur produkta attēlu un izvēlieties kategoriju"}, "pTutorial_same_products_by_image_search_block1_title": {"message": "Me<PERSON><PERSON><PERSON><PERSON> pēc attēla"}, "pTutorial_seller_analysis_block1_desc1": {"message": "Pārdevēja pozitīvo atsauksmju līmenis, atsauksmes un cik ilgi pārdevējs ir bijis tirgū"}, "pTutorial_seller_analysis_block1_title": {"message": "Pārdevēja vērtējums"}, "pTutorial_seller_analysis_block2_desc2": {"message": "Pārdevēja vērtējums ir balstīts uz 3 rādītājiem: aprakst<PERSON>t<PERSON> prece, sazi<PERSON>as ātrums"}, "pTutorial_seller_analysis_block3_desc3": {"message": "<PERSON><PERSON>s <PERSON> 3 krā<PERSON><PERSON> un <PERSON>, lai norā<PERSON><PERSON><PERSON> pārdevēju uzticības līmeni"}, "page_count": {"message": "<PERSON><PERSON> skaits"}, "pai_chu": {"message": "<PERSON>z<PERSON>lē<PERSON><PERSON>"}, "pai_chu_HK_bu_yi_xia_xiaoshou": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> ierobežoto"}, "pai_chu_JP_bu_yi_xia_xiaoshou": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "pai_chu_KR_bu_yi_xia_xiaoshou": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "pai_chu_KZ_bu_yi_xia_xiaoshou": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "pai_chu_MO_bu_yi_xia_xiaoshou": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "pai_chu_RU_bu_yi_xia_xiaoshou": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> ierobežoto"}, "pai_chu_SA_bu_yi_xia_xiaoshou": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> Arābijas ierobežoto"}, "pai_chu_TW_bu_yi_xia_xiaoshou": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "pai_chu_USA_bu_yi_xia_xiaoshou": {"message": "Izslēgt ASV ierobežotu"}, "pai_chu_VN_bu_yi_xia_xiaoshou": {"message": "Izslē<PERSON> ierobežoto"}, "pai_chu_bu_yi_xia_xiaoshou": {"message": "Izslēdziet ierobežotos vienumus"}, "payable_price_formula": {"message": "Cena + piegāde + atlaide"}, "pdd_check_retail_btn_txt": {"message": "Pārbaudiet mazumtirdzniecību"}, "pdd_pifa_to_retail_btn_txt": {"message": "<PERSON><PERSON><PERSON><PERSON>z<PERSON>cī<PERSON>"}, "pdp_copy_fail": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON> ne<PERSON>!"}, "pdp_copy_success": {"message": "Ko<PERSON><PERSON><PERSON>na ir ve<PERSON>sm<PERSON>ga!"}, "pdp_share_modal_subtitle": {"message": "Kopīgojiet <PERSON>, viņ<PERSON>/viņa redzē<PERSON> jū<PERSON> izvēli."}, "pdp_share_modal_title": {"message": "Dalieties savā izvēlē"}, "pdp_share_screenshot": {"message": "Kopīgojiet ekrānuzņēmumu"}, "pei_song": {"message": "Pieg<PERSON><PERSON> metode"}, "pin_lei": {"message": "Kategorija"}, "pin_zhi_ti_yan": {"message": "Produkta <PERSON>"}, "pin_zhi_ti_yan__desc": {"message": "Pārdevēja veikala k<PERSON>itā<PERSON> atmaks<PERSON> likme"}, "pin_zhi_tui_kuan_lv": {"message": "<PERSON><PERSON><PERSON><PERSON> lik<PERSON>"}, "pin_zhi_tui_kuan_lv__desc": {"message": "To pas<PERSON><PERSON><PERSON><PERSON><PERSON>, par kuriem ir atmaksāta un atgriezta tikai pēdējo 30 dienu laikā"}, "ping_fen": {"message": "<PERSON><PERSON><PERSON>ē<PERSON><PERSON>"}, "ping_jun_fa_huo_su_du": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON> pieg<PERSON> ātrums"}, "pkgInfo_hide": {"message": "Loģistikas informācija: ieslēgts/izslēgts"}, "pkgInfo_no_trace": {"message": "Nav loģistikas informā<PERSON>jas"}, "platform_name__1688": {"message": "1688"}, "platform_name__17zwd": {"message": "17zwd.com"}, "platform_name__51taoyang": {"message": "51taoyang.com"}, "platform_name__5ts": {"message": "5ts.com"}, "platform_name__91jf": {"message": "91jf.com"}, "platform_name__alibaba": {"message": "Alibaba.com"}, "platform_name__aliexpress": {"message": "AliExpress"}, "platform_name__amazon": {"message": "Amazon"}, "platform_name__bao66": {"message": "bao66.cn"}, "platform_name__chinagoods": {"message": "chinagoods.com"}, "platform_name__dhgate": {"message": "DHgate"}, "platform_name__e3hui": {"message": "e3hui.com"}, "platform_name__ebay": {"message": "Ebay"}, "platform_name__hznzcn": {"message": "hznzcn.com"}, "platform_name__jd": {"message": "JD"}, "platform_name__juyi5": {"message": "juyi5.cn"}, "platform_name__naver_shopping": {"message": "Naver Shopping"}, "platform_name__pinduoduo": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "platform_name__pinduoduo_pifa": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>rdzniecī<PERSON>"}, "platform_name__shopee": {"message": "<PERSON>ee"}, "platform_name__sjxzw": {"message": "571xz.com"}, "platform_name__sooxie": {"message": "sooxie.com"}, "platform_name__taobao": {"message": "Taobao"}, "platform_name__taobao_lite": {"message": "Taobao Lite"}, "platform_name__vvic": {"message": "vvic.com"}, "platform_name__walmart": {"message": "Walmart"}, "platform_name__wsy": {"message": "wsy.com"}, "platform_name__xingfujie": {"message": "xingfujie.cn"}, "platform_name__yiwugo": {"message": "Yiwugo.com"}, "platform_name__yunchepin": {"message": "yunchepin.cn"}, "platform_name__zhaojiafang": {"message": "zhaojiafang.com"}, "popup_go_to_home": {"message": "<PERSON><PERSON><PERSON>"}, "popup_go_to_platform": {"message": "Dodieties uz $name$", "placeholders": {"name": {"content": "$1"}}}, "popup_search_placeholder": {"message": "<PERSON><PERSON> i<PERSON> ..."}, "popup_track_package_btn_track": {"message": "TRACK"}, "popup_track_package_desc": {"message": "IEPAKOJUMU VISU VIENĀS UZRAUDZĪBA"}, "popup_track_package_search_placeholder": {"message": "<PERSON>zse<PERSON>šanas numurs"}, "popup_translate_search_placeholder": {"message": "Tulkojiet un meklējiet vietnē $searchOn$", "placeholders": {"searchOn": {"content": "$1"}}}, "price_history": {"message": "Cenu vēsture"}, "price_history_chart_tip_ae": {"message": "Padoms. Pasūtījumu skaits ir kopējais pasūtījumu skaits kopš pala<PERSON>nas"}, "price_history_chart_tip_coupang": {"message": "Padoms. Coupang izdzēsīs krāpniecis<PERSON> pasūtījumu pasūtījumu skaitu"}, "price_history_inm_1688_l1": {"message": "<PERSON><PERSON><PERSON><PERSON>, instalējiet"}, "price_history_inm_1688_l2": {"message": "<PERSON><PERSON><PERSON> p<PERSON> viet<PERSON>i 1688"}, "price_history_panel_lowest_price": {"message": "<PERSON><PERSON>āk<PERSON> cena:"}, "price_history_panel_tab_price_tracking": {"message": "Cenu vēsture"}, "price_history_panel_tab_seller_analysis": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "price_history_pro_modal_title": {"message": "Cenu vēsture un pasūtījumu vēsture"}, "privacy_consent__btn_agree": {"message": "Atkārtoti apmeklējiet datu vāk<PERSON>"}, "privacy_consent__btn_disable_all": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "privacy_consent__btn_enable_all": {"message": "Iespējo<PERSON> visu"}, "privacy_consent__btn_uninstall": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "privacy_consent__desc_privacy": {"message": "Ņemiet vērā, ka bez datiem vai sīkfailiem dažas funkcijas tiks iz<PERSON>lēgtas, jo šīm funkcijām nepieciešams datu vai sīkdatņu skaidroju<PERSON>, taču jūs joprojām varat izmantot citas funkcijas."}, "privacy_consent__desc_privacy_L1": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON> bez datiem vai sīkfailiem tas nedar<PERSON>, jo mums ir nepie<PERSON> datu vai sīkdatņu skaidroju<PERSON>."}, "privacy_consent__desc_privacy_L2": {"message": "<PERSON>a j<PERSON><PERSON>t mums a<PERSON><PERSON><PERSON>, l<PERSON><PERSON><PERSON>, no<PERSON><PERSON><PERSON> to."}, "privacy_consent__item_cookies_desc": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, j<PERSON><PERSON> valūtas datus sīkfailos mēs iegūstam tikai tad, kad iepē<PERSON><PERSON> tiešsaistē, lai parādītu cenu vēsturi."}, "privacy_consent__item_cookies_title": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "privacy_consent__item_functional_desc_L1": {"message": "1. <PERSON><PERSON><PERSON><PERSON><PERSON> sīkfailus pārl<PERSON>kprogrammā, lai anonīmi identificētu jūsu datoru vai ier<PERSON>."}, "privacy_consent__item_functional_desc_L2": {"message": "2. <PERSON><PERSON><PERSON><PERSON><PERSON> funkcionālos datus pievienojumprogrammā darbam ar funkciju."}, "privacy_consent__item_functional_title": {"message": "Funkcionālās un Analytics sīk<PERSON>tnes"}, "privacy_consent__more_desc": {"message": "<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, ka mēs nedalāmies ar jūsu personas datiem ar citiem uzņēmumiem un neviens reklāmas uzņēmums ne<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> mūsu paka<PERSON>."}, "privacy_consent__options__btn__desc": {"message": "<PERSON> visas <PERSON><PERSON><PERSON>, jums tas ir j<PERSON><PERSON><PERSON><PERSON><PERSON>."}, "privacy_consent__options__btn__label": {"message": "<PERSON><PERSON><PERSON>ē<PERSON><PERSON><PERSON> to"}, "privacy_consent__options__desc_L1": {"message": "<PERSON><PERSON>s a<PERSON> da<PERSON>, kas jūs personiski identificē:"}, "privacy_consent__options__desc_L2": {"message": "- <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, mēs sa<PERSON>m jūsu valūtas datus sīkfailos tikai tad, kad jūs pē<PERSON>t <PERSON>, lai parādītu cenu vēsturi."}, "privacy_consent__options__desc_L3": {"message": "- un pārlūkprogrammā pievienojiet sī<PERSON>failus, lai anonīmi identificētu jūsu datoru vai ier<PERSON>."}, "privacy_consent__options__desc_L4": {"message": "- citi anonīmi dati padara šo paplaš<PERSON>āju<PERSON>."}, "privacy_consent__options__desc_L5": {"message": "<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, ka mēs nedalāmies ar jūsu personas datiem ar citiem uzņēmumiem un neviens reklāmas uzņēmums nevāc datus, <PERSON><PERSON><PERSON><PERSON><PERSON> mūsu paka<PERSON>."}, "privacy_consent__privacy_preferences": {"message": "Konfidencialitā<PERSON> preferences"}, "privacy_consent__read_more": {"message": "<PERSON><PERSON><PERSON> v<PERSON> >>"}, "privacy_consent__title_privacy": {"message": "Priv<PERSON><PERSON><PERSON>"}, "product_info": {"message": "Informācija par produktu"}, "product_recommend__name": {"message": "Tie paši produkti"}, "product_research": {"message": "Produktu izpēte"}, "product_sub__email_desc": {"message": "<PERSON><PERSON> br<PERSON>ma e-pasts"}, "product_sub__email_edit": {"message": "rediģēt"}, "product_sub__email_not_verified": {"message": "<PERSON><PERSON><PERSON><PERSON>, apstipriniet e-pastu"}, "product_sub__email_required": {"message": "<PERSON><PERSON><PERSON><PERSON>, norādiet e-pastu"}, "product_sub__form_countdown": {"message": "Automātiska aizvēršana pēc $seconds$ sekundēm", "placeholders": {"seconds": {"content": "$1"}}}, "product_sub__form_fail": {"message": "Neizdevā<PERSON> pievienot at<PERSON>!"}, "product_sub__form_input_price": {"message": "<PERSON><PERSON><PERSON> cena"}, "product_sub__form_item_country": {"message": "tauta"}, "product_sub__form_item_current_price": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> cena"}, "product_sub__form_item_duration": {"message": "trase"}, "product_sub__form_item_higher_price": {"message": "Vai cena>"}, "product_sub__form_item_invalid_higher_price": {"message": "Cenai ir jā<PERSON><PERSON>t <PERSON>l<PERSON> par $price$", "placeholders": {"price": {"content": "$1"}}}, "product_sub__form_item_invalid_lower_price": {"message": "Cenai ir jābūt zemākai par $price$", "placeholders": {"price": {"content": "$1"}}}, "product_sub__form_item_lower_price": {"message": "Kad cena <"}, "product_sub__form_submit": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "product_sub__form_success": {"message": "Atgādinājuma pie<PERSON>šana izdev<PERSON>!"}, "product_sub__high_price_notify": {"message": "<PERSON><PERSON>ņ<PERSON>jiet man par cenu pieaugumu"}, "product_sub__low_price_notify": {"message": "Paziņojiet man par cenu sa<PERSON>"}, "product_sub__modal_title": {"message": "Abonementa cenas mai<PERSON> at<PERSON>"}, "province__an_hui": {"message": "<PERSON><PERSON>"}, "province__ao_men": {"message": "Macau"}, "province__bei_jing": {"message": "Beijing"}, "province__chong_qing": {"message": "Chongqing"}, "province__fu_jian": {"message": "Fujian"}, "province__gan_su": {"message": "Gansu"}, "province__guang_dong": {"message": "Guangdong"}, "province__guang_xi": {"message": "Guangxi"}, "province__gui_zhou": {"message": "Guizhou"}, "province__hai_nan": {"message": "Hainan"}, "province__he_bei": {"message": "Hebei"}, "province__he_nan": {"message": "<PERSON><PERSON>"}, "province__hei_long_jiang": {"message": "Heilongjiang"}, "province__hu_bei": {"message": "Hubei"}, "province__hu_nan": {"message": "Hunan"}, "province__ji_lin": {"message": "<PERSON><PERSON>"}, "province__jiang_su": {"message": "Jiangsu"}, "province__jiang_xi": {"message": "Jiangxi"}, "province__liao_ning": {"message": "Liaoning"}, "province__nei_meng_gu": {"message": "Inner Mongolia"}, "province__ning_xia": {"message": "Ningxia"}, "province__qing_hai": {"message": "Qinghai"}, "province__shan_dong": {"message": "Shandong"}, "province__shan_xi": {"message": "Shaanxi"}, "province__shang_hai": {"message": "Shanghai"}, "province__si_chuan": {"message": "Sichuan"}, "province__tai_wan": {"message": "Taiwan"}, "province__tian_jin": {"message": "Tianjin"}, "province__xi_zhang": {"message": "Tibet"}, "province__xiang_gang": {"message": "Hong Kong"}, "province__xin_jiang": {"message": "Xinjiang"}, "province__yun_nan": {"message": "Yunnan"}, "province__zhe_jiang": {"message": "Zhejiang"}, "purple_rocket": {"message": "로켓직구"}, "qi_ding_liang": {"message": "MOQ"}, "qi_ding_liang_qi_ding_jia": {"message": "MOQ un MOP"}, "qi_ye_mian_ji": {"message": "Uzņēmuma zona"}, "qing_zhi_shao_xuan_ze_yi_ge_chan_pin": {"message": "<PERSON><PERSON><PERSON><PERSON>, atlasiet vismaz vienu produktu"}, "qing_zhi_shao_xuan_ze_yi_ge_zi_duan": {"message": "<PERSON><PERSON><PERSON><PERSON>, atlasiet vismaz vienu lauku"}, "qu_deng_lu": {"message": "Piesakieties"}, "quan_guo_yan_xuan": {"message": "Globālā iz<PERSON>"}, "recommendation_popup_banner_btn_install": {"message": "Instalējiet to"}, "recommendation_popup_banner_desc": {"message": "<PERSON>r<PERSON><PERSON><PERSON><PERSON> cenu vēsturi 3/6 mēnešu laikā un paziņojumu par cenu kritumu"}, "region__all": {"message": "Visi reģioni"}, "region__hai_wai": {"message": "Overseas"}, "region__hua_bei_qu": {"message": "North China"}, "region__hua_dong_qu": {"message": "East China"}, "region__hua_nan_qu": {"message": "South China"}, "region__hua_zhong_qu": {"message": "Central China"}, "region__jiang_zhe_lu": {"message": "Jiangsu, Zhejiang and Shanghai"}, "remove_web_limits": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> labo klikšķi"}, "ren_zheng_gong_chang": {"message": "Sertificēta rūpnīca"}, "ren_zheng_gong_ying_shang_nian_zhan": {"message": "<PERSON><PERSON> kā sertificēts piegādātājs"}, "required_to_aliprice_login": {"message": "Nepieciešams pieteikties AliPrice"}, "revenue_last30_days": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON> apjoms pēdējo 30 dienu laikā"}, "review_counts": {"message": "<PERSON>lek<PERSON><PERSON><PERSON> s<PERSON>"}, "rocket": {"message": "로켓"}, "ru_zhu_nian_xian": {"message": "<PERSON><PERSON><PERSON> periods"}, "sales_amount_last30_days": {"message": "Ko<PERSON><PERSON><PERSON><PERSON> p<PERSON><PERSON> apjoms pēdējo 30 dienu laik<PERSON>"}, "sales_last30_days": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON> pēd<PERSON><PERSON> 30 dienu laikā"}, "sbi_alibaba_cate__accessories": {"message": "<PERSON><PERSON><PERSON>"}, "sbi_alibaba_cate__aqfk": {"message": "Dr<PERSON><PERSON><PERSON><PERSON>"}, "sbi_alibaba_cate__bags_cases": {"message": "<PERSON><PERSON> un futrāļi"}, "sbi_alibaba_cate__beauty": {"message": "Skaistums"}, "sbi_alibaba_cate__beverage": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "sbi_alibaba_cate__bgwh": {"message": "<PERSON><PERSON><PERSON>"}, "sbi_alibaba_cate__bz": {"message": "Iepakojums"}, "sbi_alibaba_cate__ccyj": {"message": "Virtuves piederumi"}, "sbi_alibaba_cate__clothes": {"message": "Apģērbs"}, "sbi_alibaba_cate__cmgd": {"message": "Plašsaziņas līdzekļu apraide"}, "sbi_alibaba_cate__coat_jacket": {"message": "<PERSON><PERSON><PERSON><PERSON> un jaka"}, "sbi_alibaba_cate__consumer_electronics": {"message": "Elektronika"}, "sbi_alibaba_cate__cryp": {"message": "Produkti pieaugušajiem"}, "sbi_alibaba_cate__csyp": {"message": "Gultas oderes"}, "sbi_alibaba_cate__cwyy": {"message": "Mājdzīvnieku dārzkopība"}, "sbi_alibaba_cate__cysx": {"message": "Ēdināšana svaigā veidā"}, "sbi_alibaba_cate__dgdq": {"message": "Elektriķis"}, "sbi_alibaba_cate__dl": {"message": "Aktiermā<PERSON><PERSON>"}, "sbi_alibaba_cate__dress_suits": {"message": "Kleita un uzvalki"}, "sbi_alibaba_cate__dszm": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "sbi_alibaba_cate__dzqj": {"message": "Elektroniska ierīce"}, "sbi_alibaba_cate__essb": {"message": "Lietots aprīkojums"}, "sbi_alibaba_cate__food": {"message": "Ēdiens"}, "sbi_alibaba_cate__fspj": {"message": "Apģērbi un aksesuāri"}, "sbi_alibaba_cate__furniture": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "sbi_alibaba_cate__fzpg": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "sbi_alibaba_cate__ghjq": {"message": "<PERSON><PERSON><PERSON><PERSON> apr<PERSON><PERSON>"}, "sbi_alibaba_cate__gt": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "sbi_alibaba_cate__gyp": {"message": "Amatniecība"}, "sbi_alibaba_cate__hb": {"message": "Videi d<PERSON>"}, "sbi_alibaba_cate__hfcz": {"message": "<PERSON><PERSON> grims"}, "sbi_alibaba_cate__hg": {"message": "Ķīmiskā rūpnie<PERSON><PERSON>ba"}, "sbi_alibaba_cate__jg": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "sbi_alibaba_cate__jianccai": {"message": "Celtniecības materi<PERSON>"}, "sbi_alibaba_cate__jichuang": {"message": "Darbgaldi"}, "sbi_alibaba_cate__jjry": {"message": "Mājsaimniecības ikdienas lietošana"}, "sbi_alibaba_cate__jtys": {"message": "Transports"}, "sbi_alibaba_cate__jxsb": {"message": "Aprīkojums"}, "sbi_alibaba_cate__jxwj": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON> aparatūra"}, "sbi_alibaba_cate__jydq": {"message": "Māj<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "sbi_alibaba_cate__jzjc": {"message": "<PERSON><PERSON><PERSON> b<PERSON>"}, "sbi_alibaba_cate__jzjf": {"message": "<PERSON><PERSON><PERSON> teks<PERSON>"}, "sbi_alibaba_cate__mj": {"message": "<PERSON><PERSON><PERSON>"}, "sbi_alibaba_cate__myyp": {"message": "<PERSON><PERSON><PERSON><PERSON> preces"}, "sbi_alibaba_cate__nanz": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "sbi_alibaba_cate__nvz": {"message": "Si<PERSON>ie<PERSON><PERSON> apģērbs"}, "sbi_alibaba_cate__ny": {"message": "Enerģija"}, "sbi_alibaba_cate__others": {"message": "Citi"}, "sbi_alibaba_cate__qcyp": {"message": "Auto piederumi"}, "sbi_alibaba_cate__qmpj": {"message": "Auto daļas"}, "sbi_alibaba_cate__shoes": {"message": "<PERSON><PERSON><PERSON>"}, "sbi_alibaba_cate__smdn": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON> da<PERSON>"}, "sbi_alibaba_cate__snqj": {"message": "Uzglab<PERSON><PERSON><PERSON> un tīrīšana"}, "sbi_alibaba_cate__spjs": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "sbi_alibaba_cate__swfw": {"message": "B<PERSON><PERSON>a <PERSON>"}, "sbi_alibaba_cate__toys_hobbies": {"message": "Rotaļlieta"}, "sbi_alibaba_cate__trousers_skirt": {"message": "Bikses un svārki"}, "sbi_alibaba_cate__txcp": {"message": "Komunikācijas produkti"}, "sbi_alibaba_cate__tz": {"message": "Bērnu apģērbi"}, "sbi_alibaba_cate__underwear": {"message": "Apakšveļa"}, "sbi_alibaba_cate__wjgj": {"message": "<PERSON><PERSON>at<PERSON><PERSON> r<PERSON>"}, "sbi_alibaba_cate__xgpi": {"message": "<PERSON><PERSON> somas"}, "sbi_alibaba_cate__xmhz": {"message": "projektu sadarb<PERSON>ba"}, "sbi_alibaba_cate__xs": {"message": "Gumija"}, "sbi_alibaba_cate__ydfs": {"message": "Sporta apģērbs"}, "sbi_alibaba_cate__ydhw": {"message": "Āra sports"}, "sbi_alibaba_cate__yjkc": {"message": "Metalurģijas minerāli"}, "sbi_alibaba_cate__yqyb": {"message": "Instrumentācija"}, "sbi_alibaba_cate__ys": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "sbi_alibaba_cate__yyby": {"message": "Medicīniskā aprūpe"}, "sbi_alibaba_cn_kj_90mjs": {"message": "Pircēju skaits pēd<PERSON><PERSON><PERSON><PERSON> 90 dienās"}, "sbi_alibaba_cn_kj_90xsl": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON> apjoms pēdējo 90 dienu laikā"}, "sbi_alibaba_cn_kj_gjsj": {"message": "Paredzētā cena"}, "sbi_alibaba_cn_kj_gjwlyf": {"message": "Starptautiskā nosūtīšanas maksa"}, "sbi_alibaba_cn_kj_gjyf": {"message": "<PERSON><PERSON><PERSON><PERSON> maksa"}, "sbi_alibaba_cn_kj_gssj": {"message": "Paredzētā cena"}, "sbi_alibaba_cn_kj_lr": {"message": "Peļņa"}, "sbi_alibaba_cn_kj_lrgs": {"message": "Peļņa = aprēķinātā cena x peļ<PERSON>as norma"}, "sbi_alibaba_cn_kj_pjfhsd": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON> pieg<PERSON> ātrums"}, "sbi_alibaba_cn_kj_qtfy": {"message": "cita maksa"}, "sbi_alibaba_cn_kj_qtfygs": {"message": "Citas izmaksas = aprēķinātā cena x citu izmaksu at<PERSON>ba"}, "sbi_alibaba_cn_kj_spjg": {"message": "<PERSON><PERSON>"}, "sbi_alibaba_cn_kj_spzl": {"message": "<PERSON><PERSON><PERSON>"}, "sbi_alibaba_cn_kj_szd": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> vieta"}, "sbi_alibaba_cn_kj_unit_ge": {"message": "Gabali"}, "sbi_alibaba_cn_kj_unit_jian": {"message": "Gabali"}, "sbi_alibaba_cn_kj_unit_ke": {"message": "Gram"}, "sbi_alibaba_cn_kj_unit_ren": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "sbi_alibaba_cn_kj_unit_tai": {"message": "Gabali"}, "sbi_alibaba_cn_kj_unit_tao": {"message": "Komplekti"}, "sbi_alibaba_cn_kj_unit_tian": {"message": "<PERSON><PERSON>"}, "sbi_alibaba_cn_kj_zwbj": {"message": "Nav cenas"}, "sbi_aliprice_alibaba_cn__jiage": {"message": "cena"}, "sbi_aliprice_alibaba_cn__keshou": {"message": "<PERSON><PERSON><PERSON>"}, "sbi_aliprice_alibaba_cn__moren": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "sbi_aliprice_alibaba_cn__queding": {"message": "Protams"}, "sbi_aliprice_alibaba_cn__xiaoliang": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "sbi_aliprice_alibaba_cn_cate__jiaju": {"message": "mē<PERSON>es"}, "sbi_aliprice_alibaba_cn_cate__linghsi": {"message": "uzkodas"}, "sbi_aliprice_alibaba_cn_cate__meizhuang": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "sbi_aliprice_alibaba_cn_cate__neiyi": {"message": "Apakšveļa"}, "sbi_aliprice_alibaba_cn_cate__peishi": {"message": "<PERSON><PERSON><PERSON>"}, "sbi_aliprice_alibaba_cn_cate__pinchui": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "sbi_aliprice_alibaba_cn_cate__qita": {"message": "Citi"}, "sbi_aliprice_alibaba_cn_cate__qunzhuang": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "sbi_aliprice_alibaba_cn_cate__shangyi": {"message": "<PERSON><PERSON>"}, "sbi_aliprice_alibaba_cn_cate__shuma": {"message": "Elektronika"}, "sbi_aliprice_alibaba_cn_cate__wanju": {"message": "Rotaļlieta"}, "sbi_aliprice_alibaba_cn_cate__xiangbao": {"message": "Bagāža"}, "sbi_aliprice_alibaba_cn_cate__xiazhuang": {"message": "Apakšā"}, "sbi_aliprice_alibaba_cn_cate__xiezi": {"message": "apa<PERSON>"}, "sbi_aliprice_cate__apparel": {"message": "Apģērbs"}, "sbi_aliprice_cate__automobiles_motorcycles": {"message": "Vieglās automašīnas un motocikli"}, "sbi_aliprice_cate__beauty_health": {"message": "Skaistums un veselība"}, "sbi_aliprice_cate__cellphones_telecommunications": {"message": "Mobilie tālruņi un telekomunikācijas"}, "sbi_aliprice_cate__computer_office": {"message": "Datori un birojs"}, "sbi_aliprice_cate__consumer_electronics": {"message": "Elektronika"}, "sbi_aliprice_cate__education_office_supplies": {"message": "Izglītības un biroja piederumi"}, "sbi_aliprice_cate__electronic_components_supplies": {"message": "Elektroniskie komponenti un piederumi"}, "sbi_aliprice_cate__furniture": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "sbi_aliprice_cate__hair_extensions_wigs": {"message": "<PERSON><PERSON> un parūkas"}, "sbi_aliprice_cate__home_garden": {"message": "<PERSON><PERSON><PERSON> un dārz<PERSON>"}, "sbi_aliprice_cate__home_improvement": {"message": "<PERSON><PERSON><PERSON>"}, "sbi_aliprice_cate__jewelry_accessories": {"message": "Juvelierizstrādājumi un aksesuāri"}, "sbi_aliprice_cate__luggage_bags": {"message": "Bagāža un somas"}, "sbi_aliprice_cate__mother_kids": {"message": "<PERSON><PERSON><PERSON> un b<PERSON>rni"}, "sbi_aliprice_cate__novelty_special_use": {"message": "Jaunums un <PERSON><PERSON><PERSON>"}, "sbi_aliprice_cate__security_protection": {"message": "Drošība un aizsardzība"}, "sbi_aliprice_cate__shoes": {"message": "<PERSON><PERSON><PERSON>"}, "sbi_aliprice_cate__sports_entertainment": {"message": "Sports un izklaide"}, "sbi_aliprice_cate__toys_hobbies": {"message": "Rotaļlietas un vaļasprieki"}, "sbi_aliprice_cate__watches": {"message": "Pulksteņ<PERSON>"}, "sbi_aliprice_cate__weddings_events": {"message": "Kāzas un pasākumi"}, "sbi_btn_capture_txt": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "sbi_btn_source_now_txt": {"message": "Avots tagad"}, "sbi_button__chat_with_me": {"message": "<PERSON><PERSON>'' ar mani"}, "sbi_button__contact_supplier": {"message": "Kontakts"}, "sbi_button__hide_on_this_site": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> š<PERSON> viet<PERSON>"}, "sbi_button__open_settings": {"message": "Konfigurējiet me<PERSON> pēc attēla"}, "sbi_capture_shortcut_tip": {"message": "vai nospiediet tastatūras taustiņu \"Enter\""}, "sbi_capturing_tip": {"message": "Notveršana"}, "sbi_composed_rating_45": {"message": "4,5–5,0 zvaigz<PERSON>"}, "sbi_crop_and_search": {"message": "Meklēt"}, "sbi_crop_start": {"message": "Izmantot ekrānuzņēmumu"}, "sbi_err_captcha_action": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "sbi_err_captcha_for_alibaba_cn": {"message": "Nepie<PERSON><PERSON><PERSON> p<PERSON>. <PERSON><PERSON><PERSON><PERSON>, aug<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> att<PERSON>, lai pārbaud<PERSON>. (Skatiet $video_tutorial$ vai mēģiniet notīrīt sīkfailus)", "placeholders": {"feedback": {"content": "$1"}, "video_tutorial": {"content": "$1"}}}, "sbi_err_captcha_for_aliprice": {"message": "<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>"}, "sbi_err_captcha_for_taobao": {"message": "Taobao lūdz jūs pā<PERSON>. <PERSON><PERSON><PERSON><PERSON>, manu<PERSON><PERSON> aug<PERSON>et attēlu un meklējiet, lai to pārbaud<PERSON><PERSON>. <PERSON><PERSON> kļūda ir saistīta ar jauno verifikācijas politiku “TaoBao meklēšana pēc attēla”. Mēs iesakām pārāk bieži pārbaudīt sūdzību vietnē Taobao $feedback$.", "placeholders": {"feedback": {"content": "$1"}}}, "sbi_err_captcha_for_taobao_feedback": {"message": "at<PERSON>uk<PERSON><PERSON>"}, "sbi_err_captcha_msg": {"message": "$platform$ pieprasa augšupielādēt attēlu meklēša<PERSON> vai pabeigt droš<PERSON><PERSON> verifikā<PERSON>ju, lai noņemtu mekl<PERSON><PERSON> ierobežojumus", "placeholders": {"platform": {"content": "$1"}}}, "sbi_err_checking_if_low_version": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>, vai tā ir jaunākā versija"}, "sbi_err_cookie_btn_clear": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "sbi_err_cookie_for_alibaba_cn": {"message": "Vai dzēst 1688 sīkfailus? (Vai nepieciešams vēlreiz pieteikties)"}, "sbi_err_desperate_feature_pdd": {"message": "Attēlu meklēšanas funkcija ir pārvietota uz Pinduoduo Search by Image extension."}, "sbi_err_hidden_skill_of_improve_search_rate": {"message": "Kā uzlabot attēlu meklēša<PERSON> panākumu līmeni?"}, "sbi_err_img_undersize": {"message": "Attēls > $imgMinimumSize$", "placeholders": {"imgMinimumSize": {"content": "$1"}}}, "sbi_err_login_required": {"message": "Piesakieties $loginSite$", "placeholders": {"loginSite": {"content": "$1"}}}, "sbi_err_login_required_action": {"message": "Pieslēgties"}, "sbi_err_low_version": {"message": "Instalējiet jaunāko versiju ($latestVersion$)", "placeholders": {"latestVersion": {"content": "$1"}}}, "sbi_err_low_version_action": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "sbi_err_need_help": {"message": "Nepiecie<PERSON><PERSON>"}, "sbi_err_network": {"message": "<PERSON><PERSON><PERSON> k<PERSON>. <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, vai varat apmeklēt vietni"}, "sbi_err_not_low_version": {"message": "Ir instalēta jaunākā versija ($latestVersion$)", "placeholders": {"latestVersion": {"content": "$1"}}}, "sbi_err_try_again": {"message": "Mēģini vēlreiz"}, "sbi_err_try_again_action": {"message": "Mēģini vēlreiz"}, "sbi_err_visit_and_try": {"message": "Mēģiniet vēlreiz vai apmeklējiet vietni $website$, lai mēģinātu vēlreiz", "placeholders": {"website": {"content": "$1"}}}, "sbi_err_visit_site": {"message": "Apmeklējiet vietni $siteName$", "placeholders": {"siteName": {"content": "$1"}}}, "sbi_fail_tip": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> i<PERSON><PERSON>. <PERSON><PERSON><PERSON>, atsvaidziniet lapu un mēģiniet vēlreiz."}, "sbi_kuajing_filter_area": {"message": "<PERSON><PERSON>ī<PERSON>"}, "sbi_kuajing_filter_au": {"message": "Austrā<PERSON>ja"}, "sbi_kuajing_filter_btn_confirm": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "sbi_kuajing_filter_de": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "sbi_kuajing_filter_destination_country": {"message": "Galamērķa valsts"}, "sbi_kuajing_filter_es": {"message": "Spānija"}, "sbi_kuajing_filter_estimate": {"message": "Aprēķināt"}, "sbi_kuajing_filter_estimate_price": {"message": "<PERSON><PERSON><PERSON><PERSON> cena"}, "sbi_kuajing_filter_estimate_price_formula": {"message": "Paredzam<PERSON>s cenas formula = (preču cena + starptautiskā loģistikas krava)/(1 - peļņas norma - citu izmaksu attiecība)"}, "sbi_kuajing_filter_fr": {"message": "Francija"}, "sbi_kuajing_filter_kw_placeholder": {"message": "Ievadiet at<PERSON><PERSON><PERSON><PERSON>, kas atbilst nosaukumam"}, "sbi_kuajing_filter_logistics": {"message": "Loģistikas veidne"}, "sbi_kuajing_filter_logistics_china_post": {"message": "Ķīnas pasta gaisa pasts"}, "sbi_kuajing_filter_logistics_discount": {"message": "Loģistikas atlaide"}, "sbi_kuajing_filter_logistics_epacket": {"message": "e -pakete"}, "sbi_kuajing_filter_logistics_price_formula": {"message": "Starptautiskās loģistikas kravas = (svars x piegādes cena + reģistrācijas maksa) x (1 - atlaide)"}, "sbi_kuajing_filter_others_fee": {"message": "<PERSON><PERSON> maksa"}, "sbi_kuajing_filter_profit_percent": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>a"}, "sbi_kuajing_filter_prop": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "sbi_kuajing_filter_ru": {"message": "Krievija"}, "sbi_kuajing_filter_total": {"message": "Saskaņojiet $count$ līdzīgus vienumus", "placeholders": {"count": {"content": "$1"}}}, "sbi_kuajing_filter_uk": {"message": "Lielbritānija"}, "sbi_kuajing_filter_usa": {"message": "Amerika"}, "sbi_login_punish_title__pdd_pifa": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>rdzniecī<PERSON>"}, "sbi_msg_no_result": {"message": "Rezultāts nav atrasts,l<PERSON><PERSON><PERSON>,piesakieties $loginSite$ vai izmēģiniet citu attēlu", "placeholders": {"loginSite": {"content": "$1"}}}, "sbi_msg_no_result_check_support_page_l1": {"message": "Īslaicīgi nav pieejams pārlūkprogrammai Safari. <PERSON><PERSON><PERSON><PERSON>, izmantojiet $supportPage$.", "placeholders": {"supportPage": {"content": "$1"}}}, "sbi_msg_no_result_check_support_page_l2": {"message": "Chrome pārlūks un tā paplašinājumi"}, "sbi_msg_no_result_reinstall_l1": {"message": "Rezultāti nav atrasti. <PERSON><PERSON><PERSON><PERSON>, piesakieties vietnē $loginSite$ vai izmēģiniet citu attēlu vai pārinstalējiet jaunāko $latestExtUrl$ versiju", "placeholders": {"loginSite": {"content": "$1"}, "latestExtUrl": {"content": "$2"}}}, "sbi_msg_no_result_reinstall_l2": {"message": "Jaunākā versija", "placeholders": {}}, "sbi_search_by_selected_area": {"message": "Atlasītā teritorija"}, "sbi_shipping_": {"message": "<PERSON><PERSON><PERSON><PERSON> tajā pašā dienā"}, "sbi_specify_category": {"message": "<PERSON><PERSON><PERSON><PERSON> kate<PERSON>:"}, "sbi_start_crop": {"message": "Izvēlieties apgabalu"}, "sbi_store_name_alibabaCNKuaJing": {"message": "1688 aizjūras"}, "sbi_tutorial_btn_more": {"message": "<PERSON><PERSON><PERSON><PERSON> veidu"}, "sbi_tutorial_find_coupons_on_taobao": {"message": "Atrodiet Taobao kuponus"}, "sbi_txt__empty_retry": {"message": "Die<PERSON><PERSON><PERSON><PERSON> rezultāti netika atrasti. Lūdzu, mēģiniet vēlre<PERSON>."}, "sbi_txt__min_order": {"message": "<PERSON><PERSON>"}, "sbi_visiting": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "sbi_yiwugo__jiagexiangtan": {"message": "<PERSON> cenu, sa<PERSON><PERSON><PERSON> ar pārdev<PERSON>ju"}, "sbi_yiwugo__qigou": {"message": "$num$ gabali (MOQ)", "placeholders": {"num": {"content": "$1"}}}, "sbi_yiwugo__xing": {"message": "Zvaigznes"}, "searchByImage_screenshot": {"message": "Ekrānuzņēmums ar vienu klikšķi"}, "searchByImage_search": {"message": "Ar vienu klikšķi meklējiet tos pašus vienumus"}, "searchByImage_size_type": {"message": "<PERSON>aila lielums nedrīkst būt lielāks par $num$ MB, tikai $type$", "placeholders": {"num": {"content": "$1"}, "type": {"content": "$2"}}}, "search_by_image_progress_analysing": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "search_by_image_progress_searching": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON> produktus"}, "search_by_image_progress_sending": {"message": "<PERSON><PERSON> sūt<PERSON>ts attēls"}, "search_by_image_response_rate": {"message": "Atbildes līmenis: $responseRate$ pirc<PERSON><PERSON>, kuri <PERSON> ar <PERSON>, sa<PERSON><PERSON><PERSON> atbildi $responseInHour$ stundu laikā.", "placeholders": {"responseRate": {"content": "$1"}, "responseInHour": {"content": "$2"}}}, "search_by_keyword": {"message": "Mek<PERSON><PERSON><PERSON> pēc at<PERSON>lēgas vārda:"}, "select_country_language_modal_title_country": {"message": "Valsts"}, "select_country_language_modal_title_language": {"message": "Valoda"}, "select_country_region_modal_title": {"message": "Atlasiet valsti / reģionu"}, "select_language_modal_title": {"message": "Izvēlieties valodu:"}, "select_shop": {"message": "Atlasiet veikalu"}, "sellers_count": {"message": "Pārdevēju skaits pašreizē<PERSON><PERSON>"}, "sellers_count_per_page": {"message": "Pārdevēju skaits pašreizē<PERSON><PERSON>"}, "service_score": {"message": "Visaptver<PERSON><PERSON><PERSON> paka<PERSON> novērtēju<PERSON>"}, "set_shortcut_keys": {"message": "Iestatiet īsināju<PERSON>ņ<PERSON>"}, "setting_logo_title": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "setting_modal_options_position_title": {"message": "Iespraužama pozīcija"}, "setting_modal_options_position_value_left": {"message": "<PERSON><PERSON><PERSON> st<PERSON>"}, "setting_modal_options_position_value_right": {"message": "<PERSON><PERSON> st<PERSON>"}, "setting_modal_options_theme_title": {"message": "<PERSON><PERSON><PERSON>"}, "setting_modal_options_theme_value_dark": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "setting_modal_options_theme_value_light": {"message": "<PERSON><PERSON><PERSON>"}, "setting_modal_title": {"message": "Iestatījumi"}, "setting_options_country_title": {"message": "Valsts / reģions"}, "setting_options_hover_zoom_desc": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> pele<PERSON> k<PERSON>, lai tu<PERSON>"}, "setting_options_hover_zoom_title": {"message": "Virziet kursoru Zoom"}, "setting_options_jd_coupon_desc": {"message": "Atrasts kupons vietnē JD.com"}, "setting_options_jd_coupon_title": {"message": "JD.com kupons"}, "setting_options_language_title": {"message": "Valoda"}, "setting_options_price_drop_alert_desc": {"message": "Kad kritīsies produktu cena sada<PERSON>, j<PERSON><PERSON> pazi<PERSON>ju<PERSON>."}, "setting_options_price_drop_alert_title": {"message": "<PERSON><PERSON><PERSON><PERSON>ājums par cenu kritumu"}, "setting_options_price_history_on_list_page_desc": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>t cenu vēsturi produktu mekl<PERSON><PERSON> lapā"}, "setting_options_price_history_on_list_page_title": {"message": "<PERSON><PERSON> vēsture (sarak<PERSON> lapa)"}, "setting_options_price_history_on_produt_page_desc": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> produkta vēsturi produkta informācijas lapā"}, "setting_options_price_history_on_produt_page_title": {"message": "<PERSON>nu vēsture (detaļas lapa)"}, "setting_options_sales_analysis_desc": {"message": "$platforms$ produktu saraksta lapā atbalstīt statistikas datus par cenu, p<PERSON><PERSON><PERSON><PERSON> a<PERSON>, pārde<PERSON><PERSON><PERSON> skaitu un veikala pārdo<PERSON>nas attiecību.", "placeholders": {"platforms": {"content": "$1"}}}, "setting_options_sales_analysis_title": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "setting_options_save_success_msg": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "setting_options_tacking_price_title": {"message": "<PERSON><PERSON><PERSON>dinājums par cenu izmai<PERSON>m"}, "setting_options_value_off": {"message": "<PERSON>z<PERSON>lē<PERSON><PERSON>"}, "setting_options_value_on": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "setting_pkg_quick_view_desc": {"message": "Atbalsts: 1688 un Taobao"}, "setting_saved_message": {"message": "Izmaiņas veiksmīgi saglabātas"}, "setting_section_enable_platform_title": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "setting_section_setting_title": {"message": "Iestatījumi"}, "setting_section_shortcuts_title": {"message": "Īsceļi"}, "settings_aliprice_agent__desc": {"message": "Parādīts $platforms$ produkta informācijas lapā", "placeholders": {"platforms": {"content": "$1"}}}, "settings_aliprice_agent__title": {"message": "<PERSON><PERSON><PERSON><PERSON> man"}, "settings_copy_link__desc": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> produkta informācijas lapā"}, "settings_copy_link__title": {"message": "Poga Kopēt un Meklēt virsrakstu"}, "settings_currency_desc__for_detail": {"message": "Atbalsta 1688 produkta informācijas lapu"}, "settings_currency_desc__for_list": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON> pēc attēla (iekļaujot 1688/1688 aizjūras / Taobao)"}, "settings_currency_desc__for_sbi": {"message": "Izvēlieties cenu"}, "settings_currency_desc_display_for_list": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> attēlu mekl<PERSON> (tostarp 1688/1688 overseas/Taobao)"}, "settings_currency_rate_desc": {"message": "Valūtas kursa atjaunināšana no \"$currencyRateFrom$\"", "placeholders": {"currencyRateFrom": {"content": "$1"}}}, "settings_currency_rate_from_boc": {"message": "Ķīnas Banka"}, "settings_download_images__desc": {"message": "Atbalsts attēlu lejupielādei no $platforms$", "placeholders": {"platforms": {"content": "$1"}}}, "settings_download_images__title": {"message": "att<PERSON>la le<PERSON> poga"}, "settings_download_reviews__desc": {"message": "Parādīts $platforms$ produkta informācijas lapā", "placeholders": {"platforms": {"content": "$1"}}}, "settings_download_reviews__title": {"message": "Leju<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> apskates attēlus"}, "settings_google_translate_desc": {"message": "<PERSON>r p<PERSON><PERSON> labo pogu noklikšķiniet, lai i<PERSON><PERSON><PERSON> tul<PERSON> j<PERSON>"}, "settings_google_translate_title": {"message": "tī<PERSON><PERSON><PERSON>a lapu tul<PERSON>"}, "settings_historical_trend_desc": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>t produktu saraksta lapas attēla apakšējā labajā stūrī"}, "settings_modal_btn_more": {"message": "Vairāk iestatījumu"}, "settings_productInfo_desc": {"message": "Parādiet detalizētāku informāciju par produktu produktu saraksta lapā. Iespējojot to, var palielināties datora noslodze un izraisīt lapas aizkavi. Ja tas ietekmē veiktspēju, ieteicams to atspējot."}, "settings_product_recommend__desc": {"message": "Par<PERSON><PERSON><PERSON>ts zem galvenā attēla $platforms$ produkta informācijas lapā", "placeholders": {"platforms": {"content": "$1"}}}, "settings_product_recommend__name": {"message": "Ieteicamie produkti"}, "settings_research_desc": {"message": "Sīkāku informāciju mek<PERSON>ē<PERSON>et produktu saraksta lapā"}, "settings_sbi_add_to_list": {"message": "Pievienojiet $listType$", "placeholders": {"listType": {"content": "$1"}}}, "settings_sbi_detail_page_icon_title_desc": {"message": "Attēlu meklēšanas rezultātu sīktēls"}, "settings_sbi_remove_from_list": {"message": "Noņemt no $listType$", "placeholders": {"listType": {"content": "$1"}}}, "settings_search_by_image_add_to_blacklist": {"message": "Pievienot bloķēšanas sarakstam"}, "settings_search_by_image_adjust_entrance_entrance": {"message": "Pielāgojiet ieejas pozīciju"}, "settings_search_by_image_blacklist_desc": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> ikonu vietn<PERSON>, kas atrodas melnajā sarakst<PERSON>."}, "settings_search_by_image_blacklist_title": {"message": "Bloķēšanas saraksts"}, "settings_search_by_image_bottom_left": {"message": "Apakšā pa kreisi"}, "settings_search_by_image_bottom_right": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> labajā stūrī"}, "settings_search_by_image_clear_blacklist": {"message": "<PERSON><PERSON><PERSON><PERSON>t bloķēšanas sarakstu"}, "settings_search_by_image_detail_page_icon_title": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "settings_search_by_image_detail_page_icon_val_large": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "settings_search_by_image_detail_page_icon_val_small": {"message": "<PERSON><PERSON>ā<PERSON>"}, "settings_search_by_image_display_button_desc": {"message": "Vienu klikšķi uz ikonas, lai meklētu pēc attēla"}, "settings_search_by_image_display_button_title": {"message": "Ikona uz attēliem"}, "settings_search_by_image_sourece_websites_desc": {"message": "Atrodiet avota produktu šajās viet<PERSON>s"}, "settings_search_by_image_sourece_websites_title": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON> pēc attēla rezultāta"}, "settings_search_by_image_top_left": {"message": "Augšējais pa kreisi"}, "settings_search_by_image_top_right": {"message": "Augš<PERSON><PERSON><PERSON> pa labi"}, "settings_search_keyword_on_x__desc": {"message": "Meklējiet vārdus vietnē $platform$", "placeholders": {"platform": {"content": "$1"}}}, "settings_search_keyword_on_x__title": {"message": "Rādīt $platform$ ikonu, kad ir atlas<PERSON>ti vārdi", "placeholders": {"platform": {"content": "$1"}}}, "settings_similar_products_desc": {"message": "Mēģiniet atrast to pašu produktu šajās vietnēs (ne vairāk kā 5)"}, "settings_similar_products_title": {"message": "Atrodiet to pašu produktu"}, "settings_toolbar_expand_title": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "settings_top_toolbar_desc": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> josla lap<PERSON> aug<PERSON>"}, "settings_top_toolbar_title": {"message": "Me<PERSON><PERSON><PERSON><PERSON><PERSON> j<PERSON>"}, "settings_translate_search_desc": {"message": "Tulkojiet ķīniešu valodā un meklējiet"}, "settings_translate_search_title": {"message": "<PERSON>ud<PERSON><PERSON><PERSON><PERSON>"}, "settings_translator_contextmenu_title": {"message": "U<PERSON>ņ<PERSON><PERSON>, lai tulkotu"}, "settings_translator_title": {"message": "<PERSON><PERSON><PERSON>"}, "shai_xuan_dao_chu": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>, lai eksportētu"}, "shai_xuan_zi_duan": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> la<PERSON>"}, "shang_jia_shi_jian": {"message": "Uzglab<PERSON><PERSON><PERSON> laiks"}, "shang_pin_biao_ti": {"message": "produkta nosaukums"}, "shang_pin_dui_bi": {"message": "Produktu salīd<PERSON>"}, "shang_pin_lian_jie": {"message": "produkta saite"}, "shang_pin_xin_xi": {"message": "Informācija par produktu"}, "share_modal__content": {"message": "Dalieties ar draugiem"}, "share_modal__disable_for_while": {"message": "<PERSON>s negribu neko da<PERSON>"}, "share_modal__title": {"message": "Vai jums patīk $extensionName$?", "placeholders": {"extensionName": {"content": "$1"}}}, "sheng_yu_ku_cun": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "shi_fou_ke_ding_zhi": {"message": "Vai tas ir pielāgojams?"}, "shi_fou_ren_zheng_gong_ying_sha": {"message": "Sertificēts piegādātājs"}, "shi_fou_ren_zheng_gong_ying_shang_zong_shu": {"message": "Serti<PERSON><PERSON><PERSON> pie<PERSON>"}, "shi_fou_you_mao_yi_dan_bao": {"message": "Tirdzniecības garantija"}, "shi_fou_you_mao_yi_dan_bao_zong_shu": {"message": "Tirdzniecības garantijas"}, "shipping_fee": {"message": "<PERSON><PERSON><PERSON><PERSON> maksa"}, "shop_followers": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "shou_qi": {"message": "Ma<PERSON>ā<PERSON>"}, "similar_products_warn_max_platforms": {"message": "Maks. Līdz 5"}, "sku_calc_price": {"message": "Aprēķinātā cena"}, "sku_calc_price_settings": {"message": "Aprēķinātās cenas i<PERSON>"}, "sku_formula": {"message": "Formula"}, "sku_formula_desc": {"message": "Formulas apraksts"}, "sku_formula_desc_text": {"message": "Atbalsta sarežģītas matemātiskas formulas, kur sākotnējo cenu attēlo A un kravu pārvadājumus attēlo B\n\n<br/>\n\nAt<PERSON><PERSON> iekavas (), plus +, mīnus -, reiz<PERSON><PERSON><PERSON><PERSON> * un dalīšanu /\n\n<br/>\n\nPiemērs:\n\n<br/>\n\n1. <PERSON> i<PERSON>tu 1,2 reizes lielāku sākotnējo cenu un pēc tam pievienotu kravu pārvadājumus, formula ir: A*1,2+B\n\n<br/>\n\n2. <PERSON> iegūtu sākotnējo cenu plus 1 juaņa, tad reiziniet ar 1,2, formula ir: (A+1)*1,2\n\n<br/>\n\n3. <PERSON> iegūtu sākotnējo cenu plus 10 juaņas, tad reiziniet ar 1,2 un pēc tam atņemiet 3 juaņas, formula ir: (A+10)*1,2-3"}, "sku_in_stock": {"message": "Noliktavā"}, "sku_invalid_formula_format": {"message": "<PERSON><PERSON><PERSON>gs formulas formāts"}, "sku_inventory": {"message": "Inventārs"}, "sku_link_copy_fail": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, SKU specifikācijas un atribūti nav atlasīti"}, "sku_link_copy_success": {"message": "Veiksmīgi <PERSON>, atlasītas SKU specifikācijas un atribūti"}, "sku_list": {"message": "SKU saraksts"}, "sku_min_qrder_qty": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON> p<PERSON><PERSON> da<PERSON>"}, "sku_name": {"message": "SKU nosaukums"}, "sku_no": {"message": "Nr."}, "sku_original_price": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> cena"}, "sku_price": {"message": "SKU cena"}, "stop_track_time_label": {"message": "<PERSON>zse<PERSON>ša<PERSON> termiņ<PERSON>:"}, "suo_zai_di_qu": {"message": "<PERSON>ra<PERSON><PERSON><PERSON><PERSON> vieta"}, "tab_pkg_quick_view": {"message": "Loģistikas monitors"}, "tab_product_details_price_history": {"message": "Cenu vēsture"}, "tab_product_details_reviews": {"message": "Foto pārskati"}, "tab_product_details_seller_analysis": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "tab_product_details_similar_products": {"message": "Tie paši produkti"}, "total_days_listed_per_product": {"message": "Derīguma dienu summa ÷ Produktu skaits"}, "total_items": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON> produktu skaits"}, "total_price_per_product": {"message": "Cenu summa ÷ Preču skaits"}, "total_rating_per_product": {"message": "Vērtējumu summa ÷ Preču skaits"}, "total_revenue": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "total_revenue40_items": {"message": "Kopējie ieņēmumi no $amount$ produktiem pašreizējā lapā", "placeholders": {"amount": {"content": "$1"}}}, "total_sales": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON> a<PERSON>"}, "total_sales40_items": {"message": "Pašreizēj<PERSON> lapā esošo $amount$ produktu kopējais pārdošanas apjoms", "placeholders": {"amount": {"content": "$1"}}}, "track_for_12_months": {"message": "Trase: 1 gads"}, "track_for_3_months": {"message": "Trase: 3 mēne<PERSON>us"}, "track_for_6_months": {"message": "Trase: 6 mēne<PERSON>us"}, "tracking_price_email_add_btn": {"message": "Pievienot e-pastu"}, "tracking_price_email_edit_btn": {"message": "Rediģēt e-pastu"}, "tracking_price_email_intro": {"message": "<PERSON><PERSON><PERSON> jums paziņosim pa e-pastu."}, "tracking_price_email_invalid": {"message": "<PERSON><PERSON><PERSON><PERSON>, norādiet derīgu e-pastu"}, "tracking_price_email_verified_desc": {"message": "Tagad jūs varat saņemt brīdinājumu par cenu kritumu."}, "tracking_price_email_verified_title": {"message": "<PERSON><PERSON>ks<PERSON><PERSON><PERSON>"}, "tracking_price_email_verify_desc_line1": {"message": "Mēs esam nosūtījuši verifikācijas saiti uz jūsu e-pasta adresi,"}, "tracking_price_email_verify_desc_line2": {"message": "<PERSON><PERSON><PERSON><PERSON>, pārbaudiet savu e-pasta iesūtni."}, "tracking_price_email_verify_title": {"message": "Pārbaudiet e-pastu"}, "tracking_price_web_push_notification_intro": {"message": "Uz darbvirsmas: <PERSON><PERSON><PERSON> var pārraudzīt jebkuru produktu jūsu vietā un nosūtīt jums tīmekļa paziņojuma paziņ<PERSON>, tik<PERSON><PERSON><PERSON><PERSON> cena mainās."}, "tracking_price_web_push_notification_title": {"message": "Web Push paziņojumi"}, "translate_im__login_required": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, piesakieties vietnē $loginUrl$", "placeholders": {"loginUrl": {"content": "$1"}}}, "translate_im__paste_fail": {"message": "Tulkots un kopēts starpliktuvē, ta<PERSON>u Aliwangwang ierobežojumu dēļ tas ir jāielīmē manuāli!"}, "translate_im__send": {"message": "<PERSON><PERSON><PERSON> un sūt<PERSON>t"}, "translate_search": {"message": "Tulkot un meklējiet"}, "translation_originals_translated": {"message": "Oriģināls un ķīniešu"}, "translation_translated": {"message": "ķīniešu"}, "translator_btn_capture_txt": {"message": "<PERSON><PERSON><PERSON>"}, "translator_language_auto_detect": {"message": "Automātiska <PERSON>"}, "translator_language_detected": {"message": "Atklāts"}, "translator_language_search_placeholder": {"message": "Meklēšanas valoda"}, "try_again": {"message": "Mēģini vēlreiz"}, "tu_pian_chi_cun": {"message": "At<PERSON><PERSON><PERSON> i<PERSON>:"}, "tu_pian_lian_jie": {"message": "<PERSON><PERSON><PERSON><PERSON> saite"}, "tui_huan_ti_yan": {"message": "Atgriezties pieredze"}, "tui_huan_ti_yan__desc": {"message": "Novērtējiet pārdevēju pēcp<PERSON><PERSON><PERSON> rādītā<PERSON>s"}, "tutorial__show_all": {"message": "Visas funkcijas"}, "tutorial_ae_popup_title": {"message": "Piespraudiet paplaš<PERSON>ājumu, atveriet Aliexpress"}, "tutorial_aliexpress_reviews_analysis": {"message": "AliExpress pārskata analīze"}, "tutorial_aliprice_agent_with1688_desc_l1": {"message": "Atbalstiet USD"}, "tutorial_aliprice_agent_with1688_desc_l2": {"message": "<PERSON>gā<PERSON>/<PERSON>/kontinentālo Ķīnu"}, "tutorial_aliprice_agent_with1688_title": {"message": "1688 atbalsta pirkumus <PERSON>s"}, "tutorial_auto_apply_coupon_title": {"message": "Automātiski <PERSON>ē<PERSON> k<PERSON>u"}, "tutorial_btn_end": {"message": "Beigas"}, "tutorial_btn_example": {"message": "Piemē<PERSON>"}, "tutorial_btn_see_more": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "tutorial_compare_products": {"message": "Salīdziniet produktus"}, "tutorial_currency_convert_title": {"message": "Valūtas konvertēšana"}, "tutorial_export_shopping_cart": {"message": "Eksportējiet kā CSV, atbalstiet Taobao un 1688"}, "tutorial_export_shopping_cart_title": {"message": "Eksporta grozs"}, "tutorial_price_history_pro": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>ts produkta informācijas lapā.\nAtbalstiet Shopee, Lazada, Amazon, Ebay"}, "tutorial_price_history_pro_title": {"message": "Visa gada cenu vēsture un pasūtījumu vēsture"}, "tutorial_sbi_context_menu_screenshot_title": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>, lai meklētu pēc attēla"}, "tutorial_translate_search": {"message": "Tulk<PERSON> uz mek<PERSON>"}, "tutorial_translate_search_and_package_tracking": {"message": "Tulkojuma meklēšana un paku iz<PERSON>košana"}, "unit_bao": {"message": "gab"}, "unit_ben": {"message": "gab"}, "unit_bi": {"message": "p<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "unit_chuang": {"message": "gab"}, "unit_dai": {"message": "gab"}, "unit_dui": {"message": "p<PERSON>ri"}, "unit_fen": {"message": "gab"}, "unit_ge": {"message": "gab"}, "unit_he": {"message": "gab"}, "unit_jian": {"message": "gab"}, "unit_li_fang_mi": {"message": "m³"}, "unit_ping": {"message": "gab"}, "unit_ping_fang_mi": {"message": "㎡"}, "unit_shuang": {"message": "p<PERSON>ri"}, "unit_tai": {"message": "gab"}, "unit_ti": {"message": "gab"}, "unit_tiao": {"message": "gab"}, "unit_xiang": {"message": "gab"}, "unit_zhang": {"message": "gab"}, "unit_zhi": {"message": "gab"}, "verify_contact_support": {"message": "Sazinieties ar atbal<PERSON> dienestu"}, "verify_human_verification": {"message": "<PERSON>il<PERSON><PERSON><PERSON>"}, "verify_unusual_access": {"message": "Konstatēta neparasta <PERSON>"}, "view_history_clean_all": {"message": "Clean All"}, "view_history_clean_all_warring": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON> visus aps<PERSON> i<PERSON>?"}, "view_history_clean_all_warring_title": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "view_history_viewd": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "website": {"message": "t<PERSON>me<PERSON><PERSON><PERSON> vietne"}, "weight": {"message": "<PERSON><PERSON><PERSON>"}, "wu_fa_huo_qu_dao_gai_shu_ju": {"message": "<PERSON><PERSON><PERSON> da<PERSON>"}, "wu_liu_shi_xiao": {"message": "Sūtījums laikā"}, "wu_liu_shi_xiao__desc": {"message": "Pārdevēja veikala 48 stundu savākšanas ātrums un izpildes līmenis"}, "xia_dan_jia": {"message": "G<PERSON><PERSON><PERSON><PERSON> cena"}, "xian_xuan_ze_product_attributes": {"message": "Atlasiet produkta atribūtus"}, "xiao_liang": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON> a<PERSON>"}, "xiao_liang_zhan_bi": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON> apjoma procent<PERSON><PERSON><PERSON><PERSON>"}, "xiao_shi": {"message": "$num$ stundas", "placeholders": {"num": {"content": "$1"}}}, "xiao_shou_e": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "xiao_shou_e_zhan_bi": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> procent<PERSON><PERSON><PERSON><PERSON>"}, "xuan_zhong_x_tiao_ji_lu": {"message": "Atlasiet $amount$ ierakstus", "placeholders": {"amoun": {"content": "$1"}, "amount": {"content": "$1"}}}, "yan_xuan": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "yi_ding_zai_zuo_ce": {"message": "Piesprausts"}, "yi_jia_zai_wan_suo_you_shang_pin": {"message": "Visi produkti ielādēti"}, "yi_nian_xiao_liang": {"message": "<PERSON>kgad<PERSON><PERSON><PERSON> a<PERSON>"}, "yi_nian_xiao_liang_zhan_bi": {"message": "<PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON>"}, "yi_nian_xiao_shou_e": {"message": "Gada apgrozījums"}, "yi_nian_xiao_shou_e_zhan_bi": {"message": "<PERSON><PERSON> a<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "yi_shua_xin": {"message": "Atsvaidzināts"}, "yin_cang_xiang_tong_dian": {"message": "sl<PERSON><PERSON>"}, "you_xiao_liang": {"message": "<PERSON><PERSON> <PERSON><PERSON><PERSON> a<PERSON>"}, "yu_ji_dao_da_shi_jian": {"message": "<PERSON><PERSON><PERSON><PERSON> laiks"}, "yuan_gong_ren_shu": {"message": "<PERSON><PERSON><PERSON><PERSON> s<PERSON>ts"}, "yue_cheng_jiao": {"message": "<PERSON><PERSON><PERSON><PERSON> a<PERSON>"}, "yue_dai_xiao": {"message": "Dropshipping"}, "yue_dai_xiao__desc": {"message": "Dropshipping p<PERSON><PERSON><PERSON><PERSON> apjoms pēd<PERSON><PERSON> 30 dienu laikā"}, "yue_dai_xiao_pai_xu__desc": {"message": "Dropshipping p<PERSON><PERSON><PERSON><PERSON> apjomi pēd<PERSON><PERSON> 30 dienu laik<PERSON>, sak<PERSON><PERSON><PERSON>i no augstākā uz zemāko"}, "yue_xiao_liang__desc": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON> apjoms pēdējo 30 dienu laikā"}, "zhan_kai": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "zhe_kou": {"message": "<PERSON><PERSON><PERSON>"}, "zhi_chi_yi_jian_dai_fa": {"message": "Dropshipping"}, "zhi_chi_yi_jian_dai_fa_bao_you": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "zhi_fu_ding_dan_shu": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "zhi_fu_ding_dan_shu__desc": {"message": "Pasūtī<PERSON><PERSON> skaits šim produktam (30 dienas)"}, "zhu_ce_xing_zhi": {"message": "Reģistrācijas raksturs"}, "zi_ding_yi_tiao_jian": {"message": "Pie<PERSON>ā<PERSON><PERSON>"}, "zi_duan": {"message": "<PERSON><PERSON>"}, "zi_ti_xiao_liang": {"message": "Variācija p<PERSON>"}, "zong_he_fu_wu_fen": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON> reitings"}, "zong_he_fu_wu_fen__desc": {"message": "Pārdevēja pakalpojuma kopējais vērtējums"}, "zong_he_fu_wu_fen__short": {"message": "<PERSON><PERSON><PERSON>ē<PERSON><PERSON>"}, "zong_he_ti_yan_fen": {"message": "<PERSON><PERSON><PERSON>ē<PERSON><PERSON>"}, "zong_he_ti_yan_fen_3": {"message": "Zem 4 zvaigznēm"}, "zong_he_ti_yan_fen_4": {"message": "4-4,5 zvaigznes"}, "zong_he_ti_yan_fen_4_5": {"message": "4,5–5,0 zvaigz<PERSON>"}, "zong_he_ti_yan_fen_5": {"message": "5 zvaigznes"}, "zong_ku_cun": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON> in<PERSON>"}, "zong_xiao_liang": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON> a<PERSON>"}, "zui_jin_30D_3Min_xiang_ying_lv": {"message": "3 minūšu atbildes reakcija pēdējo 30 dienu laikā"}, "zui_jin_30D_48H_lan_shou_lv": {"message": "48 h at<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> rād<PERSON><PERSON><PERSON> pēd<PERSON><PERSON> 30 dienu laikā"}, "zui_jin_30D_48H_lv_yue_lv": {"message": "48 h veiktspējas rādīt<PERSON><PERSON><PERSON> pēd<PERSON><PERSON> 30 dienu laikā"}, "zui_jin_30D_jiao_yi_ji_lu": {"message": "Tirdzniecības rekords (30 dienas)"}, "zui_jin_30D_jiao_yi_ji_lu__desc": {"message": "Tirdzniecības rekords (30 dienas)"}, "zui_jin_30D_jiu_fen_lv": {"message": "<PERSON><PERSON><PERSON><PERSON> lī<PERSON> pēd<PERSON><PERSON> 30 dienu laikā"}, "zui_jin_30D_pin_zhi_tui_kuan_lv": {"message": "Kvalitatīva atmaksas likme pēdējo 30 dienu laikā"}, "zui_jin_30D_zhi_fu_ding_dan_shu": {"message": "Maksā<PERSON><PERSON> uzdevumu skaits pēdējo 30 dienu laikā"}}