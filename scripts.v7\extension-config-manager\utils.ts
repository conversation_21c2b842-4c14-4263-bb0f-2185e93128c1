/**
 * @fileoverview 扩展配置管理系统 v7 - 基础工具函数
 * @description 提供路径管理、自动字段生成、文件系统操作等基础工具函数
 */

import path from 'path';
import fs from 'fs/promises';
import { existsSync } from 'fs';
import type {
  WebstoreType,
  WebstoreCNType,
  VariantType,
  VariantChannel,
  ManifestVersionType,
  ProcessedPaths,
  I18nConfig,
  MergedI18nConfig,
  ManifestConfig,
  MergedManifestConfig,
} from './types.js';

// #region --- 路径相关工具 ---

/**
 * 获取项目根目录路径
 */
function getWorkspaceRoot(): string {
  // 假设脚本在 scripts.v7/ 目录下，项目根目录是上两级
  return path.resolve(__dirname, '../../');
}

/**
 * 获取插件根目录路径
 * @param extensionName 插件名称
 * @returns 插件根目录的绝对路径
 */
export function getExtensionRoot(extensionName: string): string {
  return path.join(getWorkspaceRoot(), 'packages', 'extensions', extensionName);
}

/**
 * 获取插件变体根目录路径
 * @param extensionName 插件名称
 * @returns 变体根目录的绝对路径
 */
export function getVariantsRoot(extensionName: string): string {
  return path.join(getExtensionRoot(extensionName), '.variants');
}

/**
 * 获取特定变体目录路径
 * @param extensionName 插件名称
 * @param variantTarget 变体目标标识
 * @returns 特定变体目录的绝对路径
 */
export function getVariantTargetRoot(extensionName: string, variantTarget: string): string {
  return path.join(getVariantsRoot(extensionName), variantTarget);
}

/**
 * 生成完整的路径信息对象
 * @param extensionName 插件名称
 * @param variantTarget 变体目标标识
 * @returns 包含所有相关路径的对象
 */
export function generateProcessedPaths(
  extensionName: string,
  variantTarget: string,
): ProcessedPaths {
  const workspace = getWorkspaceRoot();
  const extensionRoot = getExtensionRoot(extensionName);
  const variantsRoot = getVariantsRoot(extensionName);
  const variantTargetRoot = getVariantTargetRoot(extensionName, variantTarget);

  return {
    // 静态路径
    workspace,
    packages: path.join(workspace, 'packages'),
    extensions: path.join(workspace, 'packages', 'extensions'),
    shared: path.join(workspace, 'packages', 'shared'),
    sharedLocales: path.join(workspace, 'packages', 'shared', 'locales'),
    dist: path.join(workspace, 'dist'),
    release: path.join(workspace, 'release'),
    scripts: path.join(workspace, 'scripts.v7'),
    changelog: path.join(workspace, 'changelog'),

    // 插件相关路径
    extensionRoot,
    extensionRawConfig: path.join(extensionRoot, 'extension.config.ts'),
    extensionRawLocales: path.join(extensionRoot, 'locales'),
    extensionVariantsRoot: variantsRoot,
    extensionVariantTargetRoot: variantTargetRoot,
    extensionVariantTargetJSON: path.join(variantTargetRoot, 'extension.json'),
    extensionVariantTargetI18n: path.join(variantTargetRoot, 'i18n.json'),
    extensionVariantTargetLocales: path.join(variantsRoot, 'public', '_locales'),
    extensionManifestsRoot: path.join(extensionRoot, '.manifests'),
    extensionVariantTargetManifest: path.join(
      extensionRoot,
      '.manifests',
      `manifest.${variantTarget}.json`,
    ),
    extensionVariantTargetOutput: path.join(workspace, 'dist', extensionName, variantTarget),
  };
}

// #endregion

// #region --- 自动字段生成 ---

/**
 * 生成变体目标标识
 * @param webstore 目标浏览器
 * @param manifestVersion Manifest 版本
 * @param variantType 变体类型
 * @returns 变体目标标识，格式：{webstore}-mv{manifestVersion}-{variantType}
 *
 * @example
 * generateVariantTarget('chrome', 3, 'master') // 'chrome-mv3-master'
 * generateVariantTarget('firefox', 2, 'tm') // 'firefox-mv2-tm'
 */
export function generateVariantTarget(
  webstore: WebstoreType,
  manifestVersion: ManifestVersionType,
  variantType: VariantType,
): string {
  return `${webstore}-mv${manifestVersion}-${variantType}`;
}

/**
 * 生成变体渠道标识
 * @param webstore 目标浏览器
 * @param variantType 变体类型
 * @returns 变体渠道标识
 *
 * @example
 * generateVariantChannel('chrome', 'master') // 'chrome'
 * generateVariantChannel('chrome', 'offline') // 'chrome_offline'
 */
export function generateVariantChannel(
  webstore: WebstoreType,
  variantType: VariantType,
): VariantChannel {
  if (variantType === 'offline') {
    return `${webstore}_offline`;
  }
  return webstore;
}

/**
 * 生成浏览器商店中文标识映射
 * @param webstore 目标浏览器
 * @returns 浏览器商店中文标识
 *
 * @example
 * generateWebstoreCN('chrome') // 'e-c'
 * generateWebstoreCN('firefox') // 'e-f'
 */
export function generateWebstoreCN(webstore: WebstoreType): WebstoreCNType {
  const mapping: Record<WebstoreType, WebstoreCNType> = {
    chrome: 'e-c',
    firefox: 'e-f',
    opera: 'e-o',
    browser360: 'e-360',
    safari: 'e-s',
    adspower: 'e-ads',
    edge: 'e-edge',
  };

  return mapping[webstore] || webstore;
}

// #endregion

// #region --- 文件系统工具 ---

/**
 * 扫描插件的 locales 目录，获取所有支持的语言列表
 * @param extensionName 插件名称
 * @returns 语言代码列表
 *
 * @example
 * await scanLocalesDirectory('cookies_manager') // ['en', 'zh_CN', 'zh_TW']
 */
export async function scanLocalesDirectory(extensionName: string): Promise<string[]> {
  const localesDir = path.join(getExtensionRoot(extensionName), 'locales');

  try {
    if (!existsSync(localesDir)) {
      return [];
    }

    const files = await fs.readdir(localesDir);
    const locales = files
      .filter((file) => file.endsWith('.json'))
      .map((file) => path.basename(file, '.json'))
      .sort();

    return locales;
  } catch (error) {
    console.warn(`扫描语言目录失败: ${localesDir}`, error);
    return [];
  }
}

/**
 * 列出所有可用的插件
 * @returns 插件名称列表
 *
 * @example
 * await listExtensions() // ['cookies_manager', 'price_tracker', ...]
 */
export async function listExtensions(): Promise<string[]> {
  const extensionsDir = path.join(getWorkspaceRoot(), 'packages', 'extensions');

  try {
    if (!existsSync(extensionsDir)) {
      return [];
    }

    const entries = await fs.readdir(extensionsDir, { withFileTypes: true });
    const extensions: string[] = [];

    for (const entry of entries) {
      if (entry.isDirectory()) {
        const configPath = path.join(extensionsDir, entry.name, 'extension.config.ts');
        if (existsSync(configPath)) {
          extensions.push(entry.name);
        }
      }
    }

    return extensions.sort();
  } catch (error) {
    console.warn(`列出插件失败: ${extensionsDir}`, error);
    return [];
  }
}

/**
 * 检查插件是否存在
 * @param extensionName 插件名称
 * @returns 插件是否存在
 */
export function extensionExists(extensionName: string): boolean {
  const configPath = path.join(getExtensionRoot(extensionName), 'extension.config.ts');
  return existsSync(configPath);
}

/**
 * 检查插件配置文件是否存在
 * @param extensionName 插件名称
 * @returns 配置文件是否存在
 */
export function extensionConfigExists(extensionName: string): boolean {
  return extensionExists(extensionName);
}

// #endregion

// #region --- 配置合并工具 ---

/**
 * 合并 i18n 配置
 * @param base 基础 i18n 配置
 * @param variant 变体 i18n 配置
 * @param autoScannedLocales 自动扫描的语言列表
 * @returns 合并后的 i18n 配置
 */
export function mergeI18nConfig(
  base: I18nConfig = {},
  variant: Partial<I18nConfig> = {},
  autoScannedLocales: string[] = [],
): MergedI18nConfig {
  return {
    // 优先使用自动扫描的语言列表，然后是变体配置，最后是基础配置
    locales:
      autoScannedLocales.length > 0 ? autoScannedLocales : variant.locales || base.locales || [],

    // 其他配置项：变体配置覆盖基础配置
    includeKeys: variant.includeKeys || base.includeKeys,
    excludeKeys: variant.excludeKeys || base.excludeKeys,
    chromeOnlyLocales: variant.chromeOnlyLocales || base.chromeOnlyLocales,
    chromeOnlyKeys: variant.chromeOnlyKeys || base.chromeOnlyKeys,
  };
}

/**
 * 合并 Manifest 配置
 * @param base 基础 Manifest 配置
 * @param variant 变体 Manifest 配置
 * @returns 合并后的 Manifest 配置（不包含 manifest_version 和 default_locale）
 */
export function mergeManifestConfig(
  base: ManifestConfig = {},
  variant: Partial<ManifestConfig> = {},
): MergedManifestConfig {
  // 深度合并配置，变体配置覆盖基础配置
  const merged = { ...base, ...variant };

  // 移除会在其他地方处理的字段
  const { manifest_version, default_locale, ...cleanedManifest } = merged;

  return cleanedManifest as MergedManifestConfig;
}

// #endregion

// #region --- JSON 文件操作 ---

/**
 * 写入 JSON 文件
 * @param filePath 文件路径
 * @param data 要写入的数据
 * @param pretty 是否格式化输出
 */
export async function writeJsonFile(
  filePath: string,
  data: any,
  pretty: boolean = true,
): Promise<void> {
  const content = pretty ? JSON.stringify(data, null, 2) : JSON.stringify(data);
  await fs.mkdir(path.dirname(filePath), { recursive: true });
  await fs.writeFile(filePath, content, 'utf-8');
}

/**
 * 读取 JSON 文件
 * @param filePath 文件路径
 * @returns 解析后的 JSON 数据
 */
export async function readJsonFile<T = any>(filePath: string): Promise<T> {
  const content = await fs.readFile(filePath, 'utf-8');
  return JSON.parse(content);
}

/**
 * 删除文件
 * @param filePath 文件路径
 */
export async function deleteFile(filePath: string): Promise<void> {
  try {
    await fs.unlink(filePath);
  } catch (error: any) {
    if (error.code !== 'ENOENT') {
      throw error;
    }
  }
}

/**
 * 删除目录
 * @param dirPath 目录路径
 */
export async function deleteDirectory(dirPath: string): Promise<void> {
  try {
    await fs.rm(dirPath, { recursive: true, force: true });
  } catch (error: any) {
    if (error.code !== 'ENOENT') {
      throw error;
    }
  }
}

/**
 * 确保目录存在
 * @param dirPath 目录路径
 */
export async function ensureDirectoryExists(dirPath: string): Promise<void> {
  await fs.mkdir(dirPath, { recursive: true });
}

// #endregion
