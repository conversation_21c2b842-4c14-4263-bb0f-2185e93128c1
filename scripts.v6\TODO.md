# 插件配置管理模块 - 开发任务清单

## 项目概述

重写 `scripts/extension-config-manager/` 模块，实现一个全新的、健壮的浏览器扩展配置管理系统。

## 文件结构设计

```
scripts.v6/extension-config/
├── types.ts                    # 核心类型定义
├── constants.ts                # 常量和枚举定义  
├── paths.ts                    # 路径管理工具
├── validator.ts                # 配置验证器
├── define-config.ts            # defineExtensionConfig 核心函数
├── i18n-processor.ts           # 国际化处理器
├── manifest-processor.ts       # Manifest 配置处理器
├── file-generator.ts           # 文件生成器
├── utils.ts                    # 辅助工具函数
└── index.ts                    # 主入口和 API 导出
```

## 开发任务清单

### 阶段一：基础设施建设

- [x] **任务 0: 创建任务拆解文档** ✅
  - [x] 分析需求文档
  - [x] 设计文件结构
  - [x] 制定开发计划

- [ ] **任务 1: 设计核心类型系统** 🔄
  - [ ] 定义原始配置类型 (`ExtensionConfig`, `VariantConfig`, `I18nConfig`, `ManifestConfig`)
  - [ ] 定义处理后配置类型 (`ProcessedVariantConfig`, `ProcessedI18nConfig`, `ProcessedPaths`)
  - [ ] 定义验证相关类型 (`ValidationResult`, `ValidationError`)
  - [ ] 定义内部辅助类型 (`VariantInfo`, `LocaleMessages`, `ChromeMessage`)
  - [ ] 确保类型安全和一致性

- [ ] **任务 2: 实现常量和枚举定义**
  - [ ] 定义支持的浏览器商店类型
  - [ ] 定义变体类型和渠道类型
  - [ ] 定义 Manifest 版本类型
  - [ ] 定义各商店的更新 URL、扩展基础 URL 等常量
  - [ ] 定义默认的 Chrome 专用语言和键值模式

- [ ] **任务 3: 实现路径管理工具**
  - [ ] 创建统一的路径生成函数
  - [ ] 实现路径缓存机制
  - [ ] 支持相对路径和绝对路径转换
  - [ ] 提供路径验证和规范化功能

- [ ] **任务 4: 实现配置验证器**
  - [ ] 验证必填字段
  - [ ] 验证枚举值有效性
  - [ ] 验证 `variantTarget` 格式和唯一性
  - [ ] 提供详细的错误和警告信息
  - [ ] 支持分阶段验证

### 阶段二：核心处理逻辑

- [ ] **任务 5: 实现 defineExtensionConfig 核心函数**
  - [ ] 接收原始 `ExtensionConfig` 对象
  - [ ] 执行基础的配置扁平化与合并
  - [ ] 确定核心渠道元数据
  - [ ] 生成唯一的 `variantTarget` 标识
  - [ ] 返回初步处理结果
  - [ ] 集成配置验证

- [ ] **任务 6: 实现国际化处理器**
  - [ ] 自动扫描语言包目录
  - [ ] 实现语言包合并逻辑
  - [ ] 实现条件化文案解析
  - [ ] 实现文案过滤
  - [ ] 生成多种格式的语言包
  - [ ] 支持 Chrome 专用配置

- [ ] **任务 7: 实现 Manifest 配置处理器**
  - [ ] 实现深度合并逻辑
  - [ ] 实现权限分离
  - [ ] 自动填充字段
  - [ ] 根据 webstore 生成 update_url
  - [ ] 处理数组字段的去重合并

- [ ] **任务 8: 实现文件生成器**
  - [ ] 生成 extension.config.json
  - [ ] 生成 i18n.json (Vue I18n 格式)
  - [ ] 生成 _locales 目录 (Chrome 格式)
  - [ ] 生产模式下生成 manifest 备份
  - [ ] 支持原子性写入

### 阶段三：辅助功能和接口

- [ ] **任务 9: 实现辅助工具函数**
  - [ ] `listExtensionsByDir()`: 扫描扩展列表
  - [ ] `listExtensionVariants()`: 获取变体信息
  - [ ] `cleanExtensionConfigs()`: 清理缓存
  - [ ] `getProcessedExtensionConfig()`: 加载处理配置
  - [ ] 支持批量操作

- [ ] **任务 10: 实现主入口和 API 导出**
  - [ ] 创建 `index.ts` 主入口文件
  - [ ] 导出核心 API
  - [ ] 导出辅助 API
  - [ ] 导出工具 API
  - [ ] 导出所有类型定义

- [ ] **任务 11: 编写使用文档**
  - [ ] API 参考文档
  - [ ] 配置指南
  - [ ] 集成指南
  - [ ] 故障排除
  - [ ] 迁移指南

## 当前进度

- ✅ 需求分析和架构设计完成
- 🔄 正在进行：任务 1 - 设计核心类型系统
- ⏳ 待开始：任务 2-11

## 关键里程碑

1. **基础设施完成** (任务 1-4): 为后续开发提供坚实基础
2. **核心功能完成** (任务 5-6): 实现配置定义和国际化处理的核心逻辑
3. **完整功能完成** (任务 7-8): 实现 Manifest 处理和文件生成
4. **模块完成** (任务 9-11): 提供完整的 API 和文档

## 验收标准

每个任务完成后需要满足：
1. ✅ 代码通过 TypeScript 编译检查
2. ✅ 核心功能通过手动测试验证
3. ✅ 代码符合项目的编程规范
4. ✅ 提供必要的中文注释和文档
5. ✅ 与现有系统的集成测试通过

## 注意事项

- 这是对现有模块的**重写**，不是重构，可以参考但不要照搬
- 严格遵循 KISS 和 YAGNI 原则
- 优先实现 P0 核心功能，再考虑 P1/P2 功能
- 每完成一个任务都要更新此文档的进度
