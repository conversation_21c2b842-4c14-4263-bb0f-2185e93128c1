import path from 'node:path';
import fs from 'fs-extra';
import tailwindcss from '@tailwindcss/vite';
import browserslist from 'browserslist';
import { browserslistToTargets } from 'lightningcss';
import { defineConfig } from 'wxt';
import { SUPPORT_WEBSTORE } from './scripts/helpers';

const extensionVariantTargetConfigFilePath = process.env.VARIANT_TARGET_CONFIG_PATH || '';
if (!extensionVariantTargetConfigFilePath || !fs.existsSync(extensionVariantTargetConfigFilePath)) {
  throw new Error(`没有插件渠道包配置文件: ${extensionVariantTargetConfigFilePath}`);
}
const config = fs.readJsonSync(extensionVariantTargetConfigFilePath) || {};
if (!config.name || !config.variantTarget) {
  throw new Error('无效的插件渠道包配置文件。');
}

export default defineConfig({
  modules: ['@wxt-dev/module-vue', '@wxt-dev/auto-icons'],

  // root: config.paths.extensionRoot,
  srcDir: path.join(config.paths.extensionRoot, 'src'),
  // modulesDir: 'wxt-modules', // default: "modules"
  outDir: path.join(
    config.paths.workspace,
    '.output',
    process.env.NODE_ENV === 'production' ? 'release' : 'dist',
    config.name,
    config.version,
    config.variantTarget,
  ),
  outDirTemplate: '',
  publicDir: path.join(config.paths.extensionVariantTargetLocales, '../'),
  entrypointsDir: 'entrypoints',

  manifestVersion: config.manifestVersion,
  manifest: config.manifest,

  autoIcons: {
    enabled: true,
    baseIconPath: '../icons/128.png',
    sizes: [128, 48, 32, 16],
    grayscaleOnDevelopment: process.env.NODE_ENV === 'development',
  },
  vue: {
    vite: {
      template: {
        compilerOptions: {
          isCustomElement: (tag) => tag === 'iconify-icon',
        },
      },
    },
  },
  vite: () => ({
    plugins: [tailwindcss()],
    resolve: {
      alias: {
        '@shared': path.resolve(config.paths.shared),

        '@extensionVariantTargetI18nJSON': path.resolve(config.paths.extensionVariantTargetI18n),
      },
    },
    css: {
      transformer: 'lightningcss',
      lightningcss: {
        targets: browserslistToTargets(browserslist('>= 0.25%')),
      },
    },
    build: {
      cssMinify: 'lightningcss',
      // sourcemap: process.env.NODE_ENV === 'development',
    },
    define: {
      __IS_DEV__: JSON.stringify(import.meta.env.DEV),
      __IS_PROD__: JSON.stringify(import.meta.env.PROD),

      __IS_CHROME__: JSON.stringify(config.webstore === SUPPORT_WEBSTORE.chrome),
      __IS_EDGE__: JSON.stringify(config.webstore === SUPPORT_WEBSTORE.edge),
      __IS_FIREFOX__: JSON.stringify(config.webstore === SUPPORT_WEBSTORE.firefox),
      __IS_BROWSER_360__: JSON.stringify(config.webstore === SUPPORT_WEBSTORE.browser360),
      __IS_SAFARI__: JSON.stringify(config.webstore === SUPPORT_WEBSTORE.safari),
      __IS_ADSPOWER__: JSON.stringify(config.webstore === SUPPORT_WEBSTORE.adspower),
      __IS_OPERA__: JSON.stringify(config.webstore === SUPPORT_WEBSTORE.opera),
      __EXT_WEBSTORE__: JSON.stringify(config.webstore),
      __EXT_WEBSTORE_CN__: JSON.stringify(config.webstoreCN),

      __EXT_NAME__: JSON.stringify(config.name),
      __EXT_VERSION__: JSON.stringify(config.version),
      __EXT_MANIFEST_VERSION__: JSON.stringify(config.manifestVersion),
      __EXT_DEFAULT_LOCALE__: JSON.stringify(config.defaultLocale),
      __EXT_VARIANT_ID__: JSON.stringify(config.variantID),
      __EXT_VARIANT_NAME__: JSON.stringify(config.variantName),
      __EXT_VARIANT_TYPE__: JSON.stringify(config.variantType),
      __EXT_VARIANT_CHANNEL__: JSON.stringify(config.variantChannel),
      __EXT_VARIANT_TARGET__: JSON.stringify(config.variantTarget),
      __EXT_MEASUREMENT_ID__: JSON.stringify(config.measurementId),
    },
  }),

  // 不启动浏览器
  webExt: {
    disabled: true,
  },
});
