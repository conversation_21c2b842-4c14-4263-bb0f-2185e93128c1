/**
 * @fileoverview 配置文件生成器
 * @description 负责将处理后的配置写入磁盘，生成配置文件和语言包文件
 */

import fs from 'fs-extra';
import path from 'path';
import { generateI18n } from './i18n-processor.js';
import type { ProcessedVariantConfig } from './types.js';

/**
 * @description 为单个变体生成所有配置文件
 * @param variantConfig 处理后的单个变体配置
 *
 * 功能说明：
 * 1. 生成完整的 i18n 数据（包括 Vue 和 Chrome 格式）
 * 2. 将 i18n 数据填充回配置对象
 * 3. 清理并创建目标目录
 * 4. 写入 extension.config.json 文件
 * 5. 写入 i18n.json 文件
 * 6. 生成 Chrome 扩展的 _locales 目录结构
 */
async function generateVariantFiles(variantConfig: ProcessedVariantConfig): Promise<void> {
  // 1. 生成完整的 i18n 数据
  const i18nResult = generateI18n({
    extensionName: variantConfig.name,
    variantTarget: variantConfig.variantTarget,
    includeKeys: variantConfig.i18n.includeKeys,
    excludeKeys: variantConfig.i18n.excludeKeys,
    chromeOnlyLocales: variantConfig.i18n.chromeOnlyLocales,
    chromeOnlyKeys: variantConfig.i18n.chromeOnlyKeys,
  });

  // 2. 将 i18n 数据填充回配置对象
  const finalConfig = {
    ...variantConfig,
    i18n: i18nResult,
  };

  // 3. 确保目标目录不存在，如果存在则清理（避免文件覆盖）
  if (await fs.exists(finalConfig.paths.extensionVariantTargetRoot)) {
    await fs.remove(finalConfig.paths.extensionVariantTargetRoot);
  }
  await fs.ensureDir(finalConfig.paths.extensionVariantTargetRoot);

  // 4. 写入 extension.config.json
  await fs.writeJson(
    finalConfig.paths.extensionVariantTargetJSON,
    {
      ...finalConfig,
      i18n: {
        ...finalConfig.i18n,
        vueMessages: undefined, // 不需要写入 vueMessages
        chromeMessages: undefined, // 不需要写入 chromeMessages
      },
    },
    { spaces: 2 },
  );

  // 5. 写入 i18n.json (仅 Vue messages)
  await fs.writeJson(
    finalConfig.paths.extensionVariantTargetI18n,
    { messages: i18nResult.vueMessages },
    { spaces: 2 },
  );

  // 6. 写入 _locales/ 目录
  await fs.ensureDir(finalConfig.paths.extensionVariantTargetLocales);
  for (const [locale, messages] of Object.entries(i18nResult.chromeMessages)) {
    const localeDir = path.join(finalConfig.paths.extensionVariantTargetLocales, locale);
    await fs.ensureDir(localeDir);
    await fs.writeJson(path.join(localeDir, 'messages.json'), messages, { spaces: 2 });
  }
}

/**
 * 生成所有或指定变体的配置文件。
 * @param processedVariants - 已处理的变体配置对象。
 */
export async function generateExtensionConfigs(
  processedVariants: Record<string, ProcessedVariantConfig>,
): Promise<void> {
  for (const variantConfig of Object.values(processedVariants)) {
    await generateVariantFiles(variantConfig);
  }
}
