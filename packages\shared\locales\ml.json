{"EXTENSION_NAME": {"message": "TODO: EXTENSION_NAME"}, "EXTENSION_DESCRIPTION": {"message": "TODO: EXTENSION_DESCRIPTION"}, "1688_cross_border_hot_selling": {"message": "1688 ക്രോസ്-ബോർഡർ ഹോട്ട് സെല്ലിംഗ് സ്പോട്ട്"}, "1688_shi_li_ren_zheng": {"message": "1688 ശക്തി സർട്ടിഫിക്കേഷൻ"}, "1_jian_qi_pi": {"message": "MOQ: 1"}, "1year_yi_shang": {"message": "1 വർഷത്തിൽ കൂടുതൽ"}, "24H": {"message": "24H"}, "24H_fa_huo": {"message": "24 മണിക്കൂറിനുള്ളിൽ ഡെലിവറി"}, "24H_lan_shou_lv": {"message": "24-മണിക്കൂർ പാക്കേജിംഗ് നിരക്ക്"}, "30D_shang_xin": {"message": "പ്രതിമാസ പുതിയ വരവുകൾ"}, "30d_sales": {"message": "വിൽപ്പന അളവ്:$amount$", "placeholders": {"amount": {"content": "$1"}}}, "3Min_xiang_ying_lv": {"message": "3 മിനിറ്റിനുള്ളിൽ പ്രതികരണം."}, "3Min_xiang_ying_lv__desc": {"message": "കഴിഞ്ഞ 30 ദിവസങ്ങളിൽ 3 മിനിറ്റിനുള്ളിൽ വാങ്ങുന്നയാളുടെ അന്വേഷണ സന്ദേശങ്ങളോടുള്ള വാങ്‌വാങ്ങിൻ്റെ ഫലപ്രദമായ പ്രതികരണങ്ങളുടെ അനുപാതം"}, "48H": {"message": "48H"}, "48H_fa_huo": {"message": "48 മണിക്കൂറിനുള്ളിൽ ഡെലിവറി"}, "48H_lan_shou_lv": {"message": "48-മണിക്കൂർ പാക്കേജിംഗ് നിരക്ക്"}, "48H_lan_shou_lv__desc": {"message": "മൊത്തം ഓർഡറുകളുടെ എണ്ണവുമായി 48 മണിക്കൂറിനുള്ളിൽ പിക്കപ്പ് ഓർഡർ നമ്പറിൻ്റെ അനുപാതം"}, "48H_lv_yue_lv": {"message": "48 മണിക്കൂർ പ്രകടന നിരക്ക്"}, "48H_lv_yue_lv__desc": {"message": "മൊത്തം ഓർഡറുകളുടെ എണ്ണവുമായി 48 മണിക്കൂറിനുള്ളിൽ പിക്കപ്പ് അല്ലെങ്കിൽ ഡെലിവർ ചെയ്ത ഓർഡർ നമ്പറിൻ്റെ അനുപാതം"}, "72H": {"message": "72H"}, "7D_shang_xin": {"message": "ആഴ്ചതോറുമുള്ള പുതിയ വരവുകൾ"}, "7D_wu_li_you": {"message": "7 ദിവസത്തെ കെയർ ഫ്രീ"}, "ABS_title_text": {"message": "ഈ ലിസ്റ്റിംഗിൽ ഒരു ബ്രാൻഡ് സ്റ്റോറി ഉൾപ്പെടുന്നു"}, "AC_title_text": {"message": "ഈ ലിസ്റ്റിംഗിൽ Amazon's Choice ബാഡ്ജ് ഉണ്ട്"}, "A_title_text": {"message": "ഈ ലിസ്റ്റിന് A+ ഉള്ളടക്ക പേജുണ്ട്"}, "BS_title_text": {"message": "$type$ വിഭാഗത്തിലെ $num$ ബെസ്റ്റ് സെല്ലറായി ഈ ലിസ്റ്റിംഗ് റാങ്ക് ചെയ്യപ്പെട്ടിരിക്കുന്നു", "placeholders": {"type": {"content": "$1"}, "num": {"content": "$2"}}}, "LTD_title_text": {"message": "LTD (ലിമിറ്റഡ് ടൈം ഡീൽ) അർത്ഥമാക്കുന്നത് ഈ ലിസ്റ്റിംഗ് ഒരു \"7 ദിവസത്തെ പ്രമോഷൻ\" ഇവൻ്റിൻ്റെ ഭാഗമാണ് എന്നാണ്."}, "NR_title_text": {"message": "$type$ വിഭാഗത്തിലെ $num$ പുതിയ റിലീസായി ഈ ലിസ്റ്റിംഗ് റാങ്ക് ചെയ്യപ്പെട്ടിരിക്കുന്നു", "placeholders": {"type": {"content": "$1"}, "num": {"content": "$2"}}}, "SBV_title_text": {"message": "ഈ ലിസ്റ്റിംഗിൽ ഒരു വീഡിയോ പരസ്യമുണ്ട്, സാധാരണയായി തിരയൽ ഫലങ്ങളുടെ മധ്യത്തിൽ ദൃശ്യമാകുന്ന ഒരു തരം PPC പരസ്യം"}, "SB_title_text": {"message": "ഈ ലിസ്റ്റിംഗിന് ഒരു ബ്രാൻഡ് പരസ്യമുണ്ട്, സാധാരണയായി തിരയൽ ഫലങ്ങളുടെ മുകളിലോ താഴെയോ ദൃശ്യമാകുന്ന ഒരു തരം PPC പരസ്യം"}, "SP_title_text": {"message": "ഈ ലിസ്റ്റിംഗിൽ ഒരു സ്പോൺസർ ചെയ്ത ഉൽപ്പന്ന പരസ്യമുണ്ട്"}, "V_title_text": {"message": "ഈ ലിസ്റ്റിംഗിൽ ഒരു വീഡിയോ ആമുഖമുണ്ട്"}, "advanced_research": {"message": "വിപുലമായ ഗവേഷണം"}, "agent_ds1688___my_order": {"message": "എൻ്റെ ഉത്തരവുകൾ"}, "agent_ds1688__add_to_cart": {"message": "വിദേശ പർച്ചേസ്"}, "agent_ds1688__cart": {"message": "ഷോപ്പിംഗ് കാർട്ട്"}, "agent_ds1688__desc": {"message": "1688-ൽ നൽകിയത്. വിദേശത്ത് നിന്ന് നേരിട്ട് വാങ്ങൽ, USD-ൽ പണമടയ്ക്കൽ, ചൈനയിലെ നിങ്ങളുടെ ട്രാൻസിറ്റ് വെയർഹൗസിലേക്ക് ഡെലിവറി എന്നിവയെ ഇത് പിന്തുണയ്ക്കുന്നു."}, "agent_ds1688__freight": {"message": "ഷിപ്പിംഗ് ചെലവ് കാൽക്കുലേറ്റർ"}, "agent_ds1688__help": {"message": "സഹായം"}, "agent_ds1688__packages": {"message": "വേബിൽ"}, "agent_ds1688__profile": {"message": "വ്യക്തിഗത കേന്ദ്രം"}, "agent_ds1688__warehouse": {"message": "എൻ്റെ വെയർഹൗസ്"}, "ai_comment_analysis_advantage": {"message": "പ്രൊഫ"}, "ai_comment_analysis_ai": {"message": "AI അവലോകന വിശകലനം"}, "ai_comment_analysis_available": {"message": "ലഭ്യമാണ്"}, "ai_comment_analysis_balance": {"message": "അപര്യാപ്തമായ നാണയങ്ങൾ, ദയവായി ടോപ്പ് അപ്പ് ചെയ്യുക"}, "ai_comment_analysis_behavior": {"message": "പെരുമാറ്റം"}, "ai_comment_analysis_characteristic": {"message": "ആൾക്കൂട്ടത്തിൻ്റെ സവിശേഷതകൾ"}, "ai_comment_analysis_comment": {"message": "കൃത്യമായ നിഗമനങ്ങളിൽ എത്തിച്ചേരാൻ ഉൽപ്പന്നത്തിന് മതിയായ അവലോകനങ്ങൾ ഇല്ല, കൂടുതൽ അവലോകനങ്ങളുള്ള ഒരു ഉൽപ്പന്നം തിരഞ്ഞെടുക്കുക."}, "ai_comment_analysis_consume": {"message": "കണക്കാക്കിയ ഉപഭോഗം"}, "ai_comment_analysis_default": {"message": "ഡിഫോൾട്ട് അവലോകനങ്ങൾ"}, "ai_comment_analysis_desire": {"message": "ഉപഭോക്തൃ പ്രതീക്ഷകൾ"}, "ai_comment_analysis_disadvantage": {"message": "ദോഷങ്ങൾ"}, "ai_comment_analysis_free": {"message": "സ്വതന്ത്ര ശ്രമങ്ങൾ"}, "ai_comment_analysis_freeNum": {"message": "ഒരു സൗജന്യ ക്രെഡിറ്റ് ഉപയോഗിക്കും"}, "ai_comment_analysis_go_recharge": {"message": "മുകളിൽ പോകുക"}, "ai_comment_analysis_intelligence": {"message": "ബുദ്ധിപരമായ അവലോകന വിശകലനം"}, "ai_comment_analysis_location": {"message": "സ്ഥാനം"}, "ai_comment_analysis_motive": {"message": "വാങ്ങൽ പ്രചോദനം"}, "ai_comment_analysis_network_error": {"message": "നെറ്റ്‌വർക്ക് പിശക്, വീണ്ടും ശ്രമിക്കുക"}, "ai_comment_analysis_normal": {"message": "ഫോട്ടോ അവലോകനങ്ങൾ"}, "ai_comment_analysis_number_reviews": {"message": "അവലോകനങ്ങളുടെ എണ്ണം: $num$, കണക്കാക്കിയ ഉപഭോഗം: $consume$", "placeholders": {"num": {"content": "$1"}, "consume": {"content": "$2"}}}, "ai_comment_analysis_ordinary": {"message": "പൊതുവായ അഭിപ്രായങ്ങൾ"}, "ai_comment_analysis_percentage": {"message": "ശതമാനം"}, "ai_comment_analysis_problem": {"message": "പേയ്മെൻ്റുമായി ബന്ധപ്പെട്ട പ്രശ്നങ്ങൾ"}, "ai_comment_analysis_reanalysis": {"message": "വീണ്ടും വിശകലനം ചെയ്യുക"}, "ai_comment_analysis_reason": {"message": "കാരണം"}, "ai_comment_analysis_recharge": {"message": "ടോപ്പ് അപ്പ്"}, "ai_comment_analysis_recharged": {"message": "ഞാൻ ടോപ്പ് അപ്പ് ചെയ്തിട്ടുണ്ട്"}, "ai_comment_analysis_retry": {"message": "വീണ്ടും ശ്രമിക്കുക"}, "ai_comment_analysis_scene": {"message": "ഉപയോഗ രംഗം"}, "ai_comment_analysis_start": {"message": "വിശകലനം ആരംഭിക്കുക"}, "ai_comment_analysis_subject": {"message": "വിഷയങ്ങൾ"}, "ai_comment_analysis_time": {"message": "ഉപയോഗ സമയം"}, "ai_comment_analysis_tool": {"message": "AI ഉപകരണം"}, "ai_comment_analysis_user_portrait": {"message": "ഉപയോക്തൃ പ്രൊഫൈൽ"}, "ai_comment_analysis_welcome": {"message": "AI അവലോകന വിശകലനത്തിലേക്ക് സ്വാഗതം"}, "ai_comment_analysis_year": {"message": "കഴിഞ്ഞ വർഷത്തെ അഭിപ്രായങ്ങൾ"}, "ai_listing_Exclude_keywords": {"message": "കീവേഡുകൾ ഒഴികെ"}, "ai_listing_Login_the_feature": {"message": "ഫീച്ചറിന് ലോഗിൻ ചെയ്യേണ്ടതുണ്ട്"}, "ai_listing_aI_generation": {"message": "AI ജനറേഷൻ"}, "ai_listing_add_automatic": {"message": "ഓട്ടോമാറ്റിക്"}, "ai_listing_add_dictionary_new": {"message": "ഒരു പുതിയ ലൈബ്രറി സൃഷ്ടിക്കുക"}, "ai_listing_add_enter_keywords": {"message": "കീവേഡുകൾ നൽകുക"}, "ai_listing_add_inputkey_selling": {"message": "ചേർക്കുന്നത് പൂർത്തിയാക്കാൻ ഒരു വിൽപ്പന പോയിൻ്റ് നൽകി $key$ അമർത്തുക", "placeholders": {"key": {"content": "$1"}}}, "ai_listing_add_inputkey_warning": {"message": "പരിധി കവിഞ്ഞു, $amount$ വരെ വിൽപ്പന പോയിൻ്റുകൾ", "placeholders": {"amount": {"content": "$1"}}}, "ai_listing_add_keywords": {"message": "കീവേഡുകൾ ചേർക്കുക"}, "ai_listing_add_manually": {"message": "സ്വമേധയാ ചേർക്കുക"}, "ai_listing_add_selling": {"message": "വിൽപ്പന പോയിൻ്റുകൾ ചേർക്കുക"}, "ai_listing_added_keywords": {"message": "കീവേഡുകൾ ചേർത്തു"}, "ai_listing_added_successfully": {"message": "വിജയകരമായി ചേർത്തു"}, "ai_listing_addexcluded_keywords": {"message": "ഒഴിവാക്കിയ കീവേഡുകൾ നൽകുക, ചേർക്കുന്നത് പൂർത്തിയാക്കാൻ എൻ്റർ അമർത്തുക."}, "ai_listing_adding_selling": {"message": "വിൽപ്പന പോയിൻ്റുകൾ ചേർത്തു"}, "ai_listing_addkeyword_enter": {"message": "ചേർക്കുന്നത് പൂർത്തിയാക്കാൻ കീ ആട്രിബ്യൂട്ട് വാക്കുകൾ ടൈപ്പ് ചെയ്ത് എൻ്റർ അമർത്തുക"}, "ai_listing_ai_description": {"message": "AI വിവരണ വേഡ് ലൈബ്രറി"}, "ai_listing_ai_dictionary": {"message": "AI ടൈറ്റിൽ വേഡ് ലൈബ്രറി"}, "ai_listing_ai_title": {"message": "AI ശീർഷകം"}, "ai_listing_aidescription_repeated": {"message": "AI വിവരണ പദ ലൈബ്രറിയുടെ പേര് ആവർത്തിക്കാനാവില്ല"}, "ai_listing_aititle_repeated": {"message": "AI ടൈറ്റിൽ വേഡ് ലൈബ്രറിയുടെ പേര് ആവർത്തിക്കാൻ കഴിയില്ല"}, "ai_listing_data_comes_from": {"message": "ഈ ഡാറ്റ വരുന്നത്:"}, "ai_listing_deleted_successfully": {"message": "വിജയകരമായി ഇല്ലാതാക്കി"}, "ai_listing_dictionary_name": {"message": "ലൈബ്രറിയുടെ പേര്"}, "ai_listing_edit_dictionary": {"message": "ലൈബ്രറി പരിഷ്‌ക്കരിക്കുക..."}, "ai_listing_edit_word_library": {"message": "ലൈബ്രറി എന്ന വാക്ക് എഡിറ്റ് ചെയ്യുക"}, "ai_listing_enter_keywords": {"message": "ചേർക്കുന്നത് പൂർത്തിയാക്കാൻ കീവേഡുകൾ നൽകി $key$ അമർത്തുക", "placeholders": {"key": {"content": "$1"}}}, "ai_listing_exceeded_maximum": {"message": "പരിധി കവിഞ്ഞു, പരമാവധി $amount$ കീവേഡുകൾ", "placeholders": {"amount": {"content": "$1"}}}, "ai_listing_excluded_word_library": {"message": "ഒഴിവാക്കിയ വേഡ് ലൈബ്രറി"}, "ai_listing_generate_characters": {"message": "പ്രതീകങ്ങൾ സൃഷ്ടിക്കുക"}, "ai_listing_generation_platform": {"message": "ജനറേഷൻ പ്ലാറ്റ്ഫോം"}, "ai_listing_help_optimize": {"message": "ഉൽപ്പന്ന ശീർഷകം ഒപ്റ്റിമൈസ് ചെയ്യാൻ എന്നെ സഹായിക്കൂ, യഥാർത്ഥ ശീർഷകം"}, "ai_listing_include_selling": {"message": "മറ്റ് വിൽപ്പന പോയിൻ്റുകൾ ഉൾപ്പെടുന്നു:"}, "ai_listing_included_keyword": {"message": "കീവേഡുകൾ ഉൾപ്പെടുത്തിയിട്ടുണ്ട്"}, "ai_listing_included_keywords": {"message": "കീവേഡുകൾ ഉൾപ്പെടുത്തിയിട്ടുണ്ട്"}, "ai_listing_input_selling": {"message": "ഒരു വിൽപ്പന പോയിൻ്റ് നൽകുക"}, "ai_listing_input_selling_fit": {"message": "ശീർഷകവുമായി പൊരുത്തപ്പെടുന്നതിന് വിൽപ്പന പോയിൻ്റുകൾ നൽകുക"}, "ai_listing_input_selling_please": {"message": "വിൽപ്പന പോയിൻ്റുകൾ നൽകുക"}, "ai_listing_intelligently_title": {"message": "ശീർഷകം ബുദ്ധിപരമായി സൃഷ്ടിക്കാൻ ആവശ്യമായ ഉള്ളടക്കം മുകളിൽ നൽകുക"}, "ai_listing_keyword_product_title": {"message": "കീവേഡ് ഉൽപ്പന്ന ശീർഷകം"}, "ai_listing_keywords_repeated": {"message": "കീവേഡുകൾ ആവർത്തിക്കാൻ കഴിയില്ല"}, "ai_listing_listed_selling_points": {"message": "വിൽപ്പന പോയിൻ്റുകൾ ഉൾപ്പെടുന്നു"}, "ai_listing_long_title_1": {"message": "ബ്രാൻഡ് നാമം, ഉൽപ്പന്ന തരം, ഉൽപ്പന്ന സവിശേഷതകൾ മുതലായവ പോലുള്ള അടിസ്ഥാന വിവരങ്ങൾ അടങ്ങിയിരിക്കുന്നു."}, "ai_listing_long_title_2": {"message": "സ്റ്റാൻഡേർഡ് ഉൽപ്പന്ന ശീർഷകത്തിൻ്റെ അടിസ്ഥാനത്തിൽ, SEO-യ്ക്ക് അനുയോജ്യമായ കീവേഡുകൾ ചേർക്കുന്നു."}, "ai_listing_long_title_3": {"message": "ബ്രാൻഡ് നാമം, ഉൽപ്പന്ന തരം, ഉൽപ്പന്ന സവിശേഷതകൾ, കീവേഡുകൾ എന്നിവ അടങ്ങിയിരിക്കുന്നതിനു പുറമേ, നിർദ്ദിഷ്ട, സെഗ്മെൻ്റഡ് തിരയൽ അന്വേഷണങ്ങളിൽ ഉയർന്ന റാങ്കിംഗ് നേടുന്നതിന് ലോംഗ്-ടെയിൽ കീവേഡുകളും ഉൾപ്പെടുത്തിയിട്ടുണ്ട്."}, "ai_listing_longtail_keyword_product_title": {"message": "ലോംഗ്-ടെയിൽ കീവേഡ് ഉൽപ്പന്ന ശീർഷകം"}, "ai_listing_manually_enter": {"message": "സ്വമേധയാ നൽകുക..."}, "ai_listing_network_not_working": {"message": "ഇൻ്റർനെറ്റ് ലഭ്യമല്ല, ChatGPT ആക്‌സസ് ചെയ്യാൻ VPN ആവശ്യമാണ്"}, "ai_listing_new_dictionary": {"message": "ഒരു പുതിയ വേഡ് ലൈബ്രറി സൃഷ്‌ടിക്കുക..."}, "ai_listing_new_generate": {"message": "സൃഷ്ടിക്കുക"}, "ai_listing_optional_words": {"message": "ഓപ്ഷണൽ വാക്കുകൾ"}, "ai_listing_original_title": {"message": "യഥാർത്ഥ തലക്കെട്ട്"}, "ai_listing_other_keywords_included": {"message": "മറ്റ് കീവേഡുകൾ ഉൾപ്പെടുന്നു:"}, "ai_listing_please_again": {"message": "ദയവായി വീണ്ടും ശ്രമിക്കുക"}, "ai_listing_please_select": {"message": "ഇനിപ്പറയുന്ന ശീർഷകങ്ങൾ നിങ്ങൾക്കായി സൃഷ്ടിച്ചിരിക്കുന്നു, ദയവായി തിരഞ്ഞെടുക്കുക:"}, "ai_listing_product_category": {"message": "ഉൽപ്പന്ന വിഭാഗം"}, "ai_listing_product_category_is": {"message": "ഉൽപ്പന്ന വിഭാഗമാണ്"}, "ai_listing_product_category_to": {"message": "ഉൽപ്പന്നം ഏത് വിഭാഗത്തിൽ പെടുന്നു?"}, "ai_listing_random_keywords": {"message": "ക്രമരഹിതമായ $amount$ കീവേഡുകൾ", "placeholders": {"amount": {"content": "$1"}}}, "ai_listing_random_selling": {"message": "ക്രമരഹിതമായ $amount$ വിൽപ്പന പോയിൻ്റുകൾ", "placeholders": {"amount": {"content": "$1"}}}, "ai_listing_randomize_keywords": {"message": "ലൈബ്രറി എന്ന വാക്കിൽ നിന്ന് ക്രമരഹിതമാക്കുക"}, "ai_listing_search_selling": {"message": "വിൽപ്പന പോയിൻ്റ് ഉപയോഗിച്ച് തിരയുക"}, "ai_listing_select_product_categories": {"message": "ഉൽപ്പന്ന വിഭാഗങ്ങൾ സ്വയമേവ തിരഞ്ഞെടുക്കുക."}, "ai_listing_select_product_selling_points": {"message": "ഉൽപ്പന്ന വിൽപ്പന പോയിൻ്റുകൾ സ്വയമേവ തിരഞ്ഞെടുക്കുക"}, "ai_listing_select_word_library": {"message": "വേഡ് ലൈബ്രറി തിരഞ്ഞെടുക്കുക"}, "ai_listing_selling": {"message": "വിൽപ്പന പോയിൻ്റുകൾ"}, "ai_listing_selling_ask": {"message": "ശീർഷകത്തിന് മറ്റ് എന്ത് വിൽപ്പന പോയിൻ്റ് ആവശ്യകതകളുണ്ട്?"}, "ai_listing_selling_optional": {"message": "ഓപ്ഷണൽ വിൽപ്പന പോയിൻ്റുകൾ"}, "ai_listing_selling_repeat": {"message": "പോയിൻ്റുകൾ ഡ്യൂപ്ലിക്കേറ്റ് ചെയ്യാൻ കഴിയില്ല"}, "ai_listing_set_excluded": {"message": "ഒഴിവാക്കിയ വേഡ് ലൈബ്രറിയായി സജ്ജീകരിക്കുക"}, "ai_listing_set_include_selling_points": {"message": "വിൽപ്പന പോയിൻ്റുകൾ ഉൾപ്പെടുത്തുക"}, "ai_listing_set_included": {"message": "ഉൾപ്പെടുത്തിയ വേഡ് ലൈബ്രറി ആയി സജ്ജീകരിക്കുക"}, "ai_listing_set_selling_dictionary": {"message": "സെല്ലിംഗ് പോയിൻ്റ് ലൈബ്രറിയായി സജ്ജീകരിക്കുക"}, "ai_listing_standard_product_title": {"message": "സ്റ്റാൻഡേർഡ് ഉൽപ്പന്ന ശീർഷകം"}, "ai_listing_translated_title": {"message": "വിവർത്തനം ചെയ്ത ശീർഷകം"}, "ai_listing_visit_chatGPT": {"message": "ChatGPT സന്ദർശിക്കുക"}, "ai_listing_what_other_keywords": {"message": "ശീർഷകത്തിന് മറ്റ് എന്ത് കീവേഡുകൾ ആവശ്യമാണ്?"}, "aliprice_coupons_apply_again": {"message": "വീണ്ടും അപേക്ഷിക്കുക"}, "aliprice_coupons_apply_coupons": {"message": "കൂപ്പണുകൾ പ്രയോഗിക്കുക"}, "aliprice_coupons_apply_success": {"message": "കൂപ്പൺ കണ്ടെത്തി: $amount$ സംരക്ഷിക്കുക", "placeholders": {"amount": {"content": "$1"}}}, "aliprice_coupons_applying": {"message": "മികച്ച ഡീലുകൾക്കായുള്ള ടെസ്റ്റിംഗ് കോഡുകൾ..."}, "aliprice_coupons_applying_desc": {"message": "ചെക്ക് ഔട്ട് ചെയ്യുന്നു: $code$", "placeholders": {"code": {"content": "$1"}}}, "aliprice_coupons_back_to_checkout": {"message": "ചെക്ക്ഔട്ടിൽ തുടരുക"}, "aliprice_coupons_found_coupons": {"message": "ഞങ്ങൾ $amount$ കൂപ്പണുകൾ കണ്ടെത്തി", "placeholders": {"amount": {"content": "$1"}}}, "aliprice_coupons_found_coupons_desc": {"message": "ചെക്ക്ഔട്ട് ചെയ്യാൻ തയ്യാറാണോ? നിങ്ങൾക്ക് മികച്ച വില ലഭിക്കുമെന്ന് ഉറപ്പാക്കാം!"}, "aliprice_coupons_no_coupon_aviable": {"message": "ആ കോഡുകൾ പ്രവർത്തിച്ചില്ല. വലിയ കാര്യമില്ല - നിങ്ങൾക്ക് ഇതിനകം തന്നെ മികച്ച വില ലഭിക്കുന്നു."}, "aliprice_coupons_toolbar_btn": {"message": "കൂപ്പണുകൾ നേടുക"}, "aliww_translate": {"message": "അലിവാങ്‌വാങ് ചാറ്റ് വിവർത്തകൻ"}, "aliww_translate_supports": {"message": "പിന്തുണ: 1688 & Taobao"}, "amazon_extended_keywords_Keywords": {"message": "കീവേഡുകൾ"}, "amazon_extended_keywords_copy_all": {"message": "എല്ലാം പകർത്തുക"}, "amazon_extended_keywords_more": {"message": "കൂടുതൽ"}, "an_lei_ji_xiao_liang_pai_xu": {"message": "സഞ്ചിത വിൽപ്പന പ്രകാരം അടുക്കുക"}, "an_lei_xing_cha_kan": {"message": "തരം"}, "an_yue_dai_xiao_pai_xu": {"message": "ഡ്രോപ്പ്ഷിപ്പിംഗ് വിൽപ്പന പ്രകാരം റാങ്കിംഗ്"}, "apra_btn__cat_name": {"message": "അവലോകന വിശകലനം"}, "apra_chart__name": {"message": "രാജ്യം അനുസരിച്ച് ഉൽപ്പന്ന വിൽപ്പനയുടെ ശതമാനം"}, "apra_chart__update_at": {"message": "അപ്‌ഡേറ്റ് സമയം $time$", "placeholders": {"time": {"content": "$1"}}}, "apra_name": {"message": "രാജ്യങ്ങളുടെ വിൽപ്പന സ്ഥിതിവിവരക്കണക്കുകൾ"}, "auto_opening": {"message": "$num$ സെക്കൻഡിനുള്ളിൽ സ്വയമേവ തുറക്കുന്നു", "placeholders": {"num": {"content": "$1"}}}, "auto_page_next_x_pages": {"message": "അടുത്ത $autoPaging$ പേജുകൾ", "placeholders": {"autoPaging": {"content": "$1"}}}, "average_days_listed": {"message": "ഷെൽഫ് ദിവസങ്ങളിൽ ശരാശരി"}, "average_hui_fu_lv": {"message": "ശരാശരി മറുപടി നിരക്ക്"}, "average_ping_gong_ying_shang_deng_ji": {"message": "ശരാശരി വിതരണ നില"}, "average_price": {"message": "ശരാശരി വില"}, "average_qi_ding_liang": {"message": "ശരാശരി MOQ"}, "average_rating": {"message": "ശരാശരി റേറ്റിംഗ്"}, "average_ren_zheng_gong_ying_nian_chang": {"message": "സർട്ടിഫിക്കേഷൻ്റെ ശരാശരി വർഷങ്ങൾ"}, "average_revenue": {"message": "ശരാശരി വരുമാനം"}, "average_revenue_per_product": {"message": "മൊത്തം വരുമാനം ÷ ഉൽപ്പന്നങ്ങളുടെ എണ്ണം"}, "average_sales": {"message": "ശരാശരി വിൽപ്പന"}, "average_sales_per_product": {"message": "മൊത്തം വിൽപ്പന ÷ ഉൽപ്പന്നങ്ങളുടെ എണ്ണം"}, "bao_han": {"message": "അടങ്ങിയിരിക്കുന്നു"}, "bao_zheng_jin": {"message": "മാർജിൻ"}, "bian_ti_shu": {"message": "വ്യതിയാനങ്ങൾ"}, "biao_ti": {"message": "തലക്കെട്ട്"}, "blacklist_add_blacklist": {"message": "ഈ സ്റ്റോർ തടയുക"}, "blacklist_address_incorrect": {"message": "വിലാസം തെറ്റാണ്. ദയവായി ഇത് പരിശോധിക്കുക."}, "blacklist_blacked_out": {"message": "സ്റ്റോർ ബ്ലോക്ക് ചെയ്തു"}, "blacklist_blacklist": {"message": "കരിമ്പട്ടിക"}, "blacklist_no_records_yet": {"message": "ഇതുവരെ റെക്കോർഡൊന്നുമില്ല!"}, "blue_rocket": {"message": "로켓배송"}, "brand_name": {"message": "ബ്രാൻഡ്"}, "btn_aliprice_agent__daigou": {"message": "വാങ്ങൽ ഇടനിലക്കാരൻ"}, "btn_aliprice_agent__dropshipping": {"message": "ഡ്രോപ്പ്ഷിപ്പിംഗ്"}, "btn_have_a_try": {"message": "ഒന്ന് ശ്രമിച്ചുനോക്കൂ"}, "btn_refresh": {"message": "പുതുക്കുക"}, "btn_try_it_now": {"message": "ഇപ്പോൾ ശ്രമിക്കുക"}, "btn_txt_view_on_aliprice": {"message": "അലിപ്രൈസിൽ കാണുക"}, "bu_bao_han": {"message": "അടങ്ങിയിട്ടില്ല"}, "bulk_copy_links": {"message": "ബൾക്ക് കോപ്പി ലിങ്കുകൾ"}, "bulk_copy_products": {"message": "ബൾക്ക് കോപ്പി ഉൽപ്പന്നങ്ങൾ"}, "cai_gou_zi_xun": {"message": "കസ്റ്റമർ സർവീസ്"}, "cai_gou_zi_xun__desc": {"message": "വിൽപ്പനക്കാരൻ്റെ മൂന്ന് മിനിറ്റ് പ്രതികരണ നിരക്ക്"}, "can_ping_lei_xing": {"message": "ടൈപ്പ് ചെയ്യുക"}, "cao_zuo": {"message": "ഓപ്പറേഷൻ"}, "chan_pin_ID": {"message": "ഉൽപ്പന്ന ഐഡി"}, "chan_pin_e_wai_xin_xi": {"message": "ഉൽപ്പന്നം അധിക വിവരങ്ങൾ"}, "chan_pin_lian_jie": {"message": "ഉൽപ്പന്ന ലിങ്ക്"}, "cheng_li_shi_jian": {"message": "സ്ഥാപന സമയം"}, "city__aba": {"message": "Aba"}, "city__aksu": {"message": "<PERSON><PERSON><PERSON>"}, "city__alaer": {"message": "<PERSON><PERSON><PERSON>"}, "city__altai": {"message": "Altai"}, "city__alxaleague": {"message": "Alxa League"}, "city__ankang": {"message": "Ankan<PERSON>"}, "city__anqing": {"message": "Anqing"}, "city__anshan": {"message": "<PERSON><PERSON>"}, "city__anshun": {"message": "<PERSON><PERSON><PERSON>"}, "city__anyang": {"message": "Anyang"}, "city__baicheng": {"message": "Baicheng"}, "city__baise": {"message": "Baise"}, "city__baisha": {"message": "Baisha"}, "city__baishan": {"message": "Baishan"}, "city__baiyin": {"message": "<PERSON><PERSON><PERSON>"}, "city__baoding": {"message": "<PERSON>od<PERSON>"}, "city__baoji": {"message": "<PERSON><PERSON><PERSON>"}, "city__baoshan": {"message": "<PERSON><PERSON><PERSON>"}, "city__baoting": {"message": "Baoting"}, "city__baotou": {"message": "<PERSON><PERSON><PERSON>"}, "city__bayannur": {"message": "Bayannur"}, "city__bayinguoleng": {"message": "Bayinguoleng"}, "city__bazhong": {"message": "Bazhong"}, "city__beihai": {"message": "Beihai"}, "city__bengbu": {"message": "<PERSON><PERSON><PERSON>"}, "city__benxi": {"message": "Benxi"}, "city__bijie": {"message": "Bijie"}, "city__binzhou": {"message": "Binzhou"}, "city__bortala": {"message": "<PERSON><PERSON><PERSON>"}, "city__bozhou": {"message": "Bozhou"}, "city__cangzhou": {"message": "Cangzhou"}, "city__chamdo": {"message": "<PERSON><PERSON><PERSON>"}, "city__changchun": {"message": "<PERSON><PERSON><PERSON>"}, "city__changde": {"message": "<PERSON><PERSON>"}, "city__changhua": {"message": "Changhua"}, "city__changji": {"message": "Changji"}, "city__changjiang": {"message": "Changjiang"}, "city__changsha": {"message": "Changsha"}, "city__changzhi": {"message": "Changzhi"}, "city__changzhou": {"message": "Changzhou"}, "city__chaohu": {"message": "<PERSON><PERSON>"}, "city__chaoyang": {"message": "Chaoyang"}, "city__chaozhou": {"message": "Chaozhou"}, "city__chengde": {"message": "Chengde"}, "city__chengdu": {"message": "Chengdu"}, "city__chengmai": {"message": "<PERSON><PERSON><PERSON>"}, "city__chenzhou": {"message": "Chenzhou"}, "city__chiayi": {"message": "<PERSON><PERSON><PERSON>"}, "city__chifeng": {"message": "<PERSON><PERSON>"}, "city__chizhou": {"message": "Chizhou"}, "city__chongzuo": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__chuxiong": {"message": "Chuxiong"}, "city__chuzhou": {"message": "Chuzhou"}, "city__dali": {"message": "<PERSON><PERSON>"}, "city__dalian": {"message": "<PERSON><PERSON>"}, "city__dandong": {"message": "Dandong"}, "city__danzhou": {"message": "Danzhou"}, "city__daqing": {"message": "Daqing"}, "city__datong": {"message": "<PERSON><PERSON><PERSON>"}, "city__daxinganling": {"message": "<PERSON><PERSON>'<PERSON>ling"}, "city__dazhou": {"message": "Dazhou"}, "city__dehong": {"message": "<PERSON><PERSON>"}, "city__deyang": {"message": "Deyang"}, "city__dezhou": {"message": "Dezhou"}, "city__dingan": {"message": "Ding'an"}, "city__dingxi": {"message": "Dingxi"}, "city__diqing": {"message": "Diqing"}, "city__dongfang": {"message": "<PERSON><PERSON>g"}, "city__dongguan": {"message": "Dongguan"}, "city__dongying": {"message": "<PERSON><PERSON>"}, "city__enshi": {"message": "<PERSON><PERSON>"}, "city__ezhou": {"message": "Ezhou"}, "city__fangchenggang": {"message": "Fangchenggang"}, "city__foshan": {"message": "<PERSON><PERSON><PERSON>"}, "city__fushun": {"message": "<PERSON><PERSON><PERSON>"}, "city__fuxin": {"message": "Fuxin"}, "city__fuyang": {"message": "Fuyang"}, "city__fuzhou": {"message": "Fuzhou"}, "city__gannan": {"message": "<PERSON><PERSON><PERSON>"}, "city__ganzhou": {"message": "Ganzhou"}, "city__ganzi": {"message": "Ganzi"}, "city__guangan": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__guangyuan": {"message": "Guangyuan"}, "city__guangzhou": {"message": "Guangzhou"}, "city__guigang": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__guilin": {"message": "<PERSON><PERSON><PERSON>"}, "city__guiyang": {"message": "Guiyang"}, "city__guolok": {"message": "Guolok"}, "city__guyuan": {"message": "<PERSON><PERSON>"}, "city__haibei": {"message": "Haibei"}, "city__haidong": {"message": "Haidong"}, "city__haikou": {"message": "Hai<PERSON><PERSON>"}, "city__hainan": {"message": "Hainan"}, "city__haixi": {"message": "Haixi"}, "city__hami": {"message": "<PERSON><PERSON>"}, "city__handan": {"message": "<PERSON><PERSON>"}, "city__hangzhou": {"message": "Hangzhou"}, "city__hanzhong": {"message": "Hanzhong"}, "city__harbin": {"message": "<PERSON><PERSON><PERSON>"}, "city__hebi": {"message": "<PERSON><PERSON>"}, "city__hechi": {"message": "<PERSON><PERSON>"}, "city__hefei": {"message": "<PERSON><PERSON><PERSON>"}, "city__hegang": {"message": "<PERSON><PERSON><PERSON>"}, "city__heihe": {"message": "<PERSON><PERSON><PERSON>"}, "city__hengshui": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__hengyang": {"message": "Hengyang"}, "city__heyuan": {"message": "<PERSON><PERSON>"}, "city__heze": {"message": "<PERSON><PERSON>"}, "city__hezhou": {"message": "Hezhou"}, "city__hohhot": {"message": "Hohhot"}, "city__honghe": {"message": "<PERSON><PERSON>"}, "city__hongkongisland": {"message": "Hong Kong Island"}, "city__hotan": {"message": "<PERSON><PERSON>"}, "city__hsinchu": {"message": "<PERSON><PERSON><PERSON>"}, "city__huaian": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__huaibei": {"message": "<PERSON><PERSON><PERSON>"}, "city__huaihua": {"message": "Huaihua"}, "city__huaisouth": {"message": "Huai South"}, "city__hualien": {"message": "<PERSON><PERSON><PERSON>"}, "city__huanggang": {"message": "<PERSON><PERSON><PERSON>"}, "city__huangnan": {"message": "Huangnan"}, "city__huangshan": {"message": "<PERSON><PERSON>"}, "city__huangshi": {"message": "<PERSON><PERSON>"}, "city__huizhou": {"message": "Huizhou"}, "city__huludao": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__hulunbuir": {"message": "Hulunbuir"}, "city__huzhou": {"message": "Huzhou"}, "city__jiamusi": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__jian": {"message": "<PERSON><PERSON><PERSON>"}, "city__jiangmen": {"message": "Jiangmen"}, "city__jiaozuo": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__jiaxing": {"message": "Jiaxing"}, "city__jiayuguan": {"message": "Jiayuguan"}, "city__jieyang": {"message": "<PERSON><PERSON><PERSON>"}, "city__jilin": {"message": "<PERSON><PERSON>"}, "city__jinan": {"message": "<PERSON><PERSON>"}, "city__jinchang": {"message": "Jinchang"}, "city__jincheng": {"message": "Jincheng"}, "city__jingdezhen": {"message": "Jingdez<PERSON>"}, "city__jingmen": {"message": "Jingmen"}, "city__jingzhou": {"message": "Jingzhou"}, "city__jinhua": {"message": "Jinhua"}, "city__jining": {"message": "Jining"}, "city__jinzhong": {"message": "Jinzhong"}, "city__jinzhou": {"message": "Jinzhou"}, "city__jiujiang": {"message": "Jiujiang"}, "city__jiuquan": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__jixi": {"message": "Ji<PERSON>"}, "city__kaifeng": {"message": "Kaifeng"}, "city__kaohsiung": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__karamay": {"message": "<PERSON><PERSON><PERSON>"}, "city__kashgar": {"message": "Ka<PERSON><PERSON>"}, "city__keelung": {"message": "Keelung"}, "city__kinmen": {"message": "Kinmen"}, "city__kizilsu": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__kowloon": {"message": "Kowloon"}, "city__kunming": {"message": "Ku<PERSON><PERSON>"}, "city__laibin": {"message": "<PERSON><PERSON>"}, "city__laiwu": {"message": "<PERSON><PERSON>"}, "city__langfang": {"message": "<PERSON><PERSON><PERSON>"}, "city__lanzhou": {"message": "Lanzhou"}, "city__ledong": {"message": "<PERSON><PERSON>"}, "city__leshan": {"message": "<PERSON><PERSON>"}, "city__lhasa": {"message": "Lhasa"}, "city__liangshan": {"message": "Liangshan"}, "city__lianyungang": {"message": "Lianyungang"}, "city__liaocheng": {"message": "<PERSON><PERSON><PERSON>"}, "city__liaoyang": {"message": "<PERSON><PERSON><PERSON>"}, "city__liaoyuan": {"message": "<PERSON><PERSON><PERSON>"}, "city__lienchiang": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__lijiang": {"message": "Lijiang"}, "city__lincang": {"message": "Lincang"}, "city__linfen": {"message": "Linfen"}, "city__lingao": {"message": "Lingao"}, "city__lingshui": {"message": "<PERSON><PERSON><PERSON>"}, "city__linxia": {"message": "Lin<PERSON>"}, "city__linyi": {"message": "<PERSON><PERSON>"}, "city__lishui": {"message": "Li<PERSON><PERSON>"}, "city__liupanshui": {"message": "Liupanshu<PERSON>"}, "city__liuzhou": {"message": "Liuzhou"}, "city__longnan": {"message": "<PERSON><PERSON>"}, "city__longyan": {"message": "<PERSON><PERSON>"}, "city__loudi": {"message": "<PERSON><PERSON>"}, "city__luan": {"message": "<PERSON><PERSON><PERSON>"}, "city__luohe": {"message": "<PERSON><PERSON><PERSON>"}, "city__luoyang": {"message": "<PERSON><PERSON><PERSON>"}, "city__luzhou": {"message": "Luzhou"}, "city__lüliang": {"message": "<PERSON>眉liang"}, "city__maanshan": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__macauoutlyingislands": {"message": "Macau Outlying Islands"}, "city__macaupeninsula": {"message": "Macau Peninsula"}, "city__maoming": {"message": "<PERSON><PERSON>"}, "city__meishan": {"message": "<PERSON><PERSON>"}, "city__meizhou": {"message": "Meizhou"}, "city__mianyang": {"message": "Mianyang"}, "city__miaoli": {"message": "<PERSON><PERSON>"}, "city__mudanjiang": {"message": "Mudanjiang"}, "city__nagqu": {"message": "<PERSON>g<PERSON>u"}, "city__nanchang": {"message": "Nanchang"}, "city__nanchong": {"message": "<PERSON><PERSON><PERSON>"}, "city__nanjing": {"message": "Nanjing"}, "city__nanning": {"message": "Nanning"}, "city__nanping": {"message": "<PERSON><PERSON>"}, "city__nantong": {"message": "Nantong"}, "city__nantou": {"message": "<PERSON><PERSON><PERSON>"}, "city__nanyang": {"message": "Nanyang"}, "city__neijiang": {"message": "Neijiang"}, "city__newterritories": {"message": "New Territories"}, "city__ngari": {"message": "<PERSON><PERSON>"}, "city__ningbo": {"message": "Ningbo"}, "city__ningde": {"message": "<PERSON><PERSON><PERSON>"}, "city__nujiang": {"message": "Nujiang"}, "city__nyingchi": {"message": "Nyingchi"}, "city__ordos": {"message": "Ordos"}, "city__panjin": {"message": "Panjin"}, "city__panzhihua": {"message": "Pan Zhihua"}, "city__penghu": {"message": "<PERSON><PERSON><PERSON>"}, "city__pingdingshan": {"message": "Pingdingshan"}, "city__pingliang": {"message": "<PERSON><PERSON><PERSON>"}, "city__pingtung": {"message": "<PERSON><PERSON><PERSON>"}, "city__pingxiang": {"message": "<PERSON><PERSON><PERSON>"}, "city__puer": {"message": "<PERSON>u'er"}, "city__putian": {"message": "<PERSON><PERSON>"}, "city__puyang": {"message": "P<PERSON><PERSON>"}, "city__qiandongnan": {"message": "Qiandongnan"}, "city__qianjiang": {"message": "Qianjiang"}, "city__qiannan": {"message": "<PERSON><PERSON><PERSON>"}, "city__qianxinan": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__qingdao": {"message": "Qingdao"}, "city__qingyang": {"message": "Qingyang"}, "city__qingyuan": {"message": "Qingyuan"}, "city__qinhuangdao": {"message": "Qinhuangdao"}, "city__qinzhou": {"message": "Qinzhou"}, "city__qionghai": {"message": "Qionghai"}, "city__qiongzhong": {"message": "Qiongzhong"}, "city__qiqihar": {"message": "Qiqihar"}, "city__qitaihe": {"message": "<PERSON><PERSON><PERSON>"}, "city__quanzhou": {"message": "Quanzhou"}, "city__qujing": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__quzhou": {"message": "Quzhou"}, "city__rizhao": {"message": "<PERSON><PERSON><PERSON>"}, "city__sanmenxia": {"message": "Sanmenxia"}, "city__sanming": {"message": "Sanming"}, "city__sanya": {"message": "Sanya"}, "city__shangluo": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__shangqiu": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__shangrao": {"message": "<PERSON><PERSON><PERSON>"}, "city__shannan": {"message": "Shannan"}, "city__shantou": {"message": "<PERSON><PERSON><PERSON>"}, "city__shanwei": {"message": "Shanwei"}, "city__shaoguan": {"message": "Shaoguan"}, "city__shaoxing": {"message": "Shaoxing"}, "city__shaoyang": {"message": "Shaoyang"}, "city__shennongjiaforestarea": {"message": "Shennongjia Forest Area"}, "city__shenyang": {"message": "Shenyang"}, "city__shenzhen": {"message": "Shenzhen"}, "city__shigatse": {"message": "Shigat<PERSON>"}, "city__shihezi": {"message": "<PERSON><PERSON><PERSON>"}, "city__shijiazhuang": {"message": "Shijiazhuang"}, "city__shiyan": {"message": "<PERSON><PERSON>"}, "city__shizuishan": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__shuangyashan": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__shuozhou": {"message": "Shuozhou"}, "city__siping": {"message": "<PERSON><PERSON>"}, "city__songyuan": {"message": "Songyuan"}, "city__suihua": {"message": "Su<PERSON>ua"}, "city__suining": {"message": "Suining"}, "city__suizhou": {"message": "<PERSON><PERSON><PERSON>"}, "city__suqian": {"message": "<PERSON><PERSON><PERSON>"}, "city__suzhou": {"message": "Suzhou"}, "city__tacheng": {"message": "Tacheng"}, "city__taian": {"message": "Tai'an"}, "city__taichung": {"message": "Taichung"}, "city__tainan": {"message": "Tainan"}, "city__taipei": {"message": "Taipei"}, "city__taitung": {"message": "<PERSON><PERSON><PERSON>"}, "city__taiyuan": {"message": "Taiyuan"}, "city__taizhou": {"message": "Taizhou"}, "city__tangshan": {"message": "Tangshan"}, "city__taoyuan": {"message": "Taoyuan"}, "city__tianmen": {"message": "Tianmen"}, "city__tianshui": {"message": "<PERSON><PERSON><PERSON>"}, "city__tieling": {"message": "Tieling"}, "city__tongchuan": {"message": "<PERSON><PERSON><PERSON>"}, "city__tonghua": {"message": "Tonghua"}, "city__tongliao": {"message": "<PERSON><PERSON><PERSON>"}, "city__tongling": {"message": "Tongling"}, "city__tongren": {"message": "<PERSON><PERSON>"}, "city__tumushuke": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__tunchang": {"message": "Tunchang"}, "city__turpan": {"message": "<PERSON><PERSON><PERSON>"}, "city__ulanqab": {"message": "Ulanqab"}, "city__urumqi": {"message": "Urumqi"}, "city__wanning": {"message": "Wanning"}, "city__weifang": {"message": "<PERSON><PERSON><PERSON>"}, "city__weihai": {"message": "Weihai"}, "city__weinan": {"message": "Weinan"}, "city__wenchang": {"message": "Wenchang"}, "city__wenshan": {"message": "<PERSON><PERSON>"}, "city__wenzhou": {"message": "Wenzhou"}, "city__wuhai": {"message": "Wu<PERSON>"}, "city__wuhan": {"message": "<PERSON><PERSON>"}, "city__wuhu": {"message": "<PERSON><PERSON>"}, "city__wujiaqu": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__wuwei": {"message": "<PERSON><PERSON>"}, "city__wuxi": {"message": "Wuxi"}, "city__wuzhishan": {"message": "Wuzhishan"}, "city__wuzhong": {"message": "Wuzhong"}, "city__wuzhou": {"message": "Wuzhou"}, "city__xiamen": {"message": "Xiamen"}, "city__xian": {"message": "Xi'<PERSON>"}, "city__xiangfan": {"message": "<PERSON><PERSON><PERSON>"}, "city__xiangtan": {"message": "Xiangtan"}, "city__xiangxi": {"message": "Xiangxi"}, "city__xianning": {"message": "Xianning"}, "city__xiantao": {"message": "Xiantao"}, "city__xianyang": {"message": "<PERSON><PERSON><PERSON>"}, "city__xiaogan": {"message": "<PERSON><PERSON>"}, "city__xilingol": {"message": "Xilingol"}, "city__xinganleague": {"message": "Xing'an League"}, "city__xingtai": {"message": "Xingtai"}, "city__xining": {"message": "Xining"}, "city__xinxiang": {"message": "Xinxiang"}, "city__xinyang": {"message": "Xinyang"}, "city__xinyu": {"message": "Xinyu"}, "city__xinzhou": {"message": "Xinzhou"}, "city__xishuangbanna": {"message": "Xishuangbanna"}, "city__xuancheng": {"message": "Xuancheng"}, "city__xuchang": {"message": "Xuchang"}, "city__xuzhou": {"message": "Xuzhou"}, "city__yaan": {"message": "Ya'an"}, "city__yanan": {"message": "Yan'<PERSON>"}, "city__yanbian": {"message": "Yanbian"}, "city__yancheng": {"message": "Yancheng"}, "city__yangjiang": {"message": "Yangjiang"}, "city__yangquan": {"message": "<PERSON><PERSON><PERSON>"}, "city__yangzhou": {"message": "Yangzhou"}, "city__yantai": {"message": "Yantai"}, "city__yibin": {"message": "<PERSON><PERSON>"}, "city__yichang": {"message": "Yichang"}, "city__yichun": {"message": "<PERSON><PERSON><PERSON>"}, "city__yilan": {"message": "Yilan"}, "city__yili": {"message": "<PERSON><PERSON>"}, "city__yinchuan": {"message": "<PERSON><PERSON><PERSON>"}, "city__yingkou": {"message": "<PERSON><PERSON><PERSON>"}, "city__yingtan": {"message": "Yingtan"}, "city__yiwu": {"message": "<PERSON><PERSON>"}, "city__yiyang": {"message": "Yiyang"}, "city__yongzhou": {"message": "Yongzhou"}, "city__yueyang": {"message": "<PERSON><PERSON><PERSON>"}, "city__yulin": {"message": "Yulin"}, "city__yuncheng": {"message": "Yuncheng"}, "city__yunfu": {"message": "<PERSON><PERSON>"}, "city__yunlin": {"message": "<PERSON><PERSON>"}, "city__yushu": {"message": "Yushu"}, "city__yuxi": {"message": "Yuxi"}, "city__zaozhuang": {"message": "Zaozhuang"}, "city__zhangjiajie": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__zhangjiakou": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__zhangye": {"message": "<PERSON><PERSON>"}, "city__zhangzhou": {"message": "Zhangzhou"}, "city__zhanjiang": {"message": "Zhanjiang"}, "city__zhaoqing": {"message": "Zhaoqing"}, "city__zhaotong": {"message": "Zhaotong"}, "city__zhengzhou": {"message": "Zhengzhou"}, "city__zhenjiang": {"message": "Zhenjiang"}, "city__zhongshan": {"message": "Zhongshan"}, "city__zhongwei": {"message": "Zhongwei"}, "city__zhoukou": {"message": "<PERSON><PERSON><PERSON>"}, "city__zhoushan": {"message": "Zhoushan"}, "city__zhuhai": {"message": "Zhu<PERSON>"}, "city__zhumadian": {"message": "Zhumadian"}, "city__zhuzhou": {"message": "Zhuzhou"}, "city__zibo": {"message": "Zibo"}, "city__zigong": {"message": "Zigong"}, "city__ziyang": {"message": "<PERSON><PERSON><PERSON>"}, "city__zunyi": {"message": "<PERSON><PERSON><PERSON>"}, "click_copy_product_info": {"message": "ഉൽപ്പന്ന വിവരം പകർത്തുക ക്ലിക്കുചെയ്യുക"}, "commmon_txt_expired": {"message": "കാലഹരണപ്പെട്ടു"}, "common__date_range_12m": {"message": "1 വർഷം"}, "common__date_range_1m": {"message": "1 മാസം"}, "common__date_range_1w": {"message": "1 ആഴ്ച"}, "common__date_range_2w": {"message": "2 ആഴ്ച"}, "common__date_range_3m": {"message": "3 മാസം"}, "common__date_range_3w": {"message": "3 ആഴ്ച"}, "common__date_range_6m": {"message": "6 മാസം"}, "common_btn_cancel": {"message": "റദ്ദാക്കുക"}, "common_btn_close": {"message": "അടയ്‌ക്കുക"}, "common_btn_save": {"message": "രക്ഷിക്കും"}, "common_btn_setting": {"message": "സജ്ജമാക്കുക"}, "common_email": {"message": "ഇമെയിൽ"}, "common_error_msg_no_data": {"message": "ഡാറ്റാ ഇല്ല"}, "common_error_msg_no_result": {"message": "ക്ഷമിക്കണം, ഫലമൊന്നും കണ്ടെത്തിയില്ല."}, "common_favorites": {"message": "പ്രിയങ്കരങ്ങൾ"}, "common_feedback": {"message": "ഫീഡ്‌ബാക്ക്"}, "common_help": {"message": "സഹായിക്കൂ"}, "common_loading": {"message": "ലോഡിംഗ്"}, "common_login": {"message": "ലോഗിൻ"}, "common_logout": {"message": "ലോഗൗട്ട്"}, "common_no": {"message": "ഇല്ല"}, "common_powered_by_aliprice": {"message": "പ്രായോജകർAliPrice.com"}, "common_setting": {"message": "ക്രമീകരിക്കുന്നു"}, "common_sign_up": {"message": "സൈൻ അപ്പ് ചെയ്യുക"}, "common_system_upgrading_title": {"message": "സിസ്റ്റം നവീകരിക്കുന്നു"}, "common_system_upgrading_txt": {"message": "ദയവായി പിന്നീട് ശ്രമിക്കുക"}, "common_txt__currency": {"message": "കറൻസി"}, "common_txt__video_tutorial": {"message": "വീഡിയോ ട്യൂട്ടോറിയൽ"}, "common_txt_ago_time": {"message": "$time$ ദിവസം മുമ്പ്", "placeholders": {"time": {"content": "$1"}}}, "common_txt_all_product": {"message": "എല്ലാം"}, "common_txt_analysis": {"message": "വിശകലനം"}, "common_txt_basically_used": {"message": "ഒരിക്കലും ഉപയോഗിച്ചിട്ടില്ലാത്തത്"}, "common_txt_biaoti_link": {"message": "ടൈറ്റിൽ+ലിങ്ക്"}, "common_txt_biaoti_link_dian_pu": {"message": "ടൈറ്റിൽ+ലിങ്ക്+സ്റ്റോർ നാമം"}, "common_txt_blacklist": {"message": "ബ്ലോക്ക്‌ലിസ്റ്റ്"}, "common_txt_cancel": {"message": "റദ്ദാക്കുക"}, "common_txt_category": {"message": "വിഭാഗം"}, "common_txt_chakan": {"message": "ചെക്ക്"}, "common_txt_colors": {"message": "നിറങ്ങൾ"}, "common_txt_confirm": {"message": "സ്ഥിരീകരിക്കുക"}, "common_txt_copied": {"message": "പകർത്തി"}, "common_txt_copy": {"message": "പകർത്തുക"}, "common_txt_copy_link": {"message": "ലിങ്ക് പകർത്തുക"}, "common_txt_copy_title": {"message": "പേര് പകർത്തുക"}, "common_txt_copy_title__link": {"message": "ശീർഷകവും ലിങ്കും പകർത്തുക"}, "common_txt_day": {"message": "ആകാശം"}, "common_txt_day__short": {"message": "D"}, "common_txt_delete": {"message": "ഇല്ലാതാക്കുക"}, "common_txt_dian_pu_link": {"message": "സ്റ്റോറിൻ്റെ പേര് + ലിങ്ക് പകർത്തുക"}, "common_txt_download": {"message": "ഡൗൺലോഡ്"}, "common_txt_downloaded": {"message": "ഡൗൺലോഡ്"}, "common_txt_export_as_csv": {"message": "എക്സ്പോർട്ട് എക്സൽ"}, "common_txt_export_as_txt": {"message": "എക്സ്പോർട്ട് ടെക്സ്റ്റ്"}, "common_txt_fail": {"message": "പരാജയപ്പെടുക"}, "common_txt_format": {"message": "ഫോർമാറ്റ്"}, "common_txt_get": {"message": "ലഭിക്കും"}, "common_txt_incert_selection": {"message": "തിരഞ്ഞെടുപ്പ് വിപരീതമാക്കുക"}, "common_txt_install": {"message": "ഇൻസ്റ്റാൾ ചെയ്യുക"}, "common_txt_load_failed": {"message": "ലോഡുചെയ്യുന്നതിൽ പരാജയപ്പെട്ടു"}, "common_txt_month": {"message": "മാസം"}, "common_txt_more": {"message": "കൂടുതൽ"}, "common_txt_new_unused": {"message": "പുത്തൻ, ഉപയോഗിക്കാത്തത്"}, "common_txt_next": {"message": "അടുത്തത്"}, "common_txt_no_limit": {"message": "പരിധിയില്ലാത്ത"}, "common_txt_no_noticeable": {"message": "ദൃശ്യമായ പോറലുകളോ അഴുക്കോ ഇല്ല"}, "common_txt_on_sale": {"message": "ലഭ്യമാണ്"}, "common_txt_opt_in_out": {"message": "ഓൺ/ഓഫ്"}, "common_txt_order": {"message": "ഓർഡർ"}, "common_txt_others": {"message": "മറ്റുള്ളവർ"}, "common_txt_overall_poor_condition": {"message": "മൊത്തത്തിലുള്ള മോശം അവസ്ഥ"}, "common_txt_patterns": {"message": "പാറ്റേണുകൾ"}, "common_txt_platform": {"message": "പ്ലാറ്റ്ഫോമുകൾ"}, "common_txt_please_select": {"message": "ദയവായി തിരഞ്ഞെടുക്കുക"}, "common_txt_prev": {"message": "മുമ്പത്തെ"}, "common_txt_price": {"message": "വില"}, "common_txt_privacy_policy": {"message": "സ്വകാര്യതാനയം"}, "common_txt_product_condition": {"message": "ഉൽപ്പന്ന നില"}, "common_txt_rating": {"message": "റേറ്റിംഗ്"}, "common_txt_ratings": {"message": "റേറ്റിംഗുകൾ"}, "common_txt_reload": {"message": "വീണ്ടും ലോഡുചെയ്യുക"}, "common_txt_reset": {"message": "പുനഃസജ്ജമാക്കുക"}, "common_txt_review": {"message": "അവലോകനം"}, "common_txt_sale": {"message": "ലഭ്യമാണ്"}, "common_txt_same": {"message": "അതേ"}, "common_txt_scratches_and_dirt": {"message": "പോറലുകളും അഴുക്കും കൊണ്ട്"}, "common_txt_search_title": {"message": "ശീർഷകം തിരയുക"}, "common_txt_select_all": {"message": "എല്ലാം തിരഞ്ഞെടുക്കുക"}, "common_txt_selected": {"message": "തിരഞ്ഞെടുത്തു"}, "common_txt_share": {"message": "പങ്കിടുക"}, "common_txt_sold": {"message": "വിറ്റു"}, "common_txt_sold_out": {"message": "വിറ്റുതീർത്തു"}, "common_txt_some_scratches": {"message": "ചില പോറലുകളും അഴുക്കും"}, "common_txt_sort_by": {"message": "ഇങ്ങനെ അടുക്കുക"}, "common_txt_state": {"message": "പദവി"}, "common_txt_success": {"message": "പിന്തുടര്ച്ച"}, "common_txt_sys_err": {"message": "സിസ്റ്റം പിശക്"}, "common_txt_today": {"message": "ഇന്ന്"}, "common_txt_total": {"message": "എല്ലാം"}, "common_txt_unselect_all": {"message": "വിപരീത തിരഞ്ഞെടുപ്പ്"}, "common_txt_upload_image": {"message": "ചിത്രം അപ്ലോഡ് ചെയ്യുക"}, "common_txt_visit": {"message": "സന്ദർശിക്കുക"}, "common_txt_whitelist": {"message": "വൈറ്റ്ലിസ്റ്റ്"}, "common_txt_wildberries": {"message": "Wildberries"}, "common_txt_year": {"message": "വര്ഷം"}, "common_yes": {"message": "അതെ"}, "compare_tool_btn_clear_all": {"message": "എല്ലാം മായ്ക്കുക"}, "compare_tool_btn_compare": {"message": "താരതമ്യം ചെയ്യുക"}, "compare_tool_btn_contact": {"message": "ബന്ധപ്പെടുക"}, "compare_tool_tips_max_compared": {"message": "$maxComparedCount$ വരെ ചേർക്കുക", "placeholders": {"maxComparedCount": {"content": "$1"}}}, "configure_notifiactions": {"message": "അറിയിപ്പുകൾ കോൺഫിഗർ ചെയ്യുക"}, "contact_us": {"message": "ഞങ്ങളെ സമീപിക്കുക"}, "context_menu_screenshot_search": {"message": "ചിത്രം ഉപയോഗിച്ച് തിരയാൻ ക്യാപ്ചർ ചെയ്യുക"}, "context_menus_aliprice_search_by_image": {"message": "അലിപ്രൈസിൽ ചിത്രം തിരയുക"}, "context_menus_goote_trans": {"message": "പേജ് വിവർത്തനം ചെയ്യുക/ഒറിജിനൽ കാണിക്കുക"}, "context_menus_search_by_image": {"message": "Image $storeName$ on ൽ ഇമേജ് പ്രകാരം തിരയുക", "placeholders": {"storeName": {"content": "$1"}}}, "context_menus_search_by_image_capture": {"message": "$storeName$ ലേക്ക് ക്യാപ്‌ചർ ചെയ്യുക", "placeholders": {"storeName": {"content": "$1"}}}, "context_menus_translator": {"message": "വിവർത്തനം ചെയ്യാൻ ക്യാപ്ചർ ചെയ്യുക"}, "converter_modal_amount_placeholder": {"message": "തുക ഇവിടെ രേഖപ്പെടുത്തുക"}, "converter_modal_btn_convert": {"message": "മാറ്റുക"}, "converter_modal_exchange_rate_source": {"message": "$boc$ വിദേശ വിനിമയ നിരക്കിൽ നിന്നാണ് ഡാറ്റ വരുന്നത് അപ്ഡേറ്റ് സമയം: $updatedAt$", "placeholders": {"boc": {"content": "$1"}, "updatedAt": {"content": "$2"}}}, "converter_modal_name": {"message": "നാണയ പരിവര്ത്തനം"}, "converter_modal_search_placeholder": {"message": "കറൻസി തിരയുക"}, "copy_all_contact_us_notice": {"message": "ഈ സൈറ്റ് ഇപ്പോൾ പിന്തുണയ്ക്കുന്നില്ല, ദയവായി ഞങ്ങളെ ബന്ധപ്പെടുക"}, "copy_product_info": {"message": "ഉൽപ്പന്ന വിവരം പകർത്തുക"}, "copy_suggest_search_kw": {"message": "ഡ്രോപ്പ്ഡൗൺ ലിസ്റ്റുകൾ പകർത്തുക"}, "country__han_gou": {"message": "ദക്ഷിണ കൊറിയ"}, "country__ri_ben": {"message": "ജപ്പാൻ"}, "country__yue_nan": {"message": "വിയറ്റ്നാം"}, "currency_convert__custom": {"message": "ഇഷ്‌ടാനുസൃത വിനിമയ നിരക്ക്"}, "currency_convert__sync_server": {"message": "സെർവർ സമന്വയിപ്പിക്കുക"}, "dang_ri_fa_huo": {"message": "അതേ ദിവസം ഷിപ്പിംഗ്"}, "dao_chu_quan_dian_shang_pin": {"message": "എല്ലാ സ്റ്റോർ ഉൽപ്പന്നങ്ങളും കയറ്റുമതി ചെയ്യുക"}, "dao_chu_wei_CSV": {"message": "കയറ്റുമതി"}, "dao_chu_zi_duan": {"message": "ഫീൽഡുകൾ കയറ്റുമതി ചെയ്യുക"}, "delivery_address": {"message": "ഷിപ്പിംഗ് വിലാസം"}, "delivery_company": {"message": "ഡെലിവറി കമ്പനി"}, "di_zhi": {"message": "വിലാസം"}, "dian_ji_cha_xun": {"message": "അന്വേഷിക്കാൻ ക്ലിക്ക് ചെയ്യുക"}, "dian_pu_ID": {"message": "സ്റ്റോർ ഐഡി"}, "dian_pu_di_zhi": {"message": "സ്റ്റോർ വിലാസം"}, "dian_pu_lian_jie": {"message": "സ്റ്റോർ ലിങ്ക്"}, "dian_pu_ming": {"message": "സ്റ്റോറിന്റെ പേര്"}, "dian_pu_ming_cheng": {"message": "സ്റ്റോറിന്റെ പേര്"}, "dian_pu_shang_pin_zong_hsu": {"message": "സ്റ്റോറിലെ മൊത്തം ഉൽപ്പന്നങ്ങളുടെ എണ്ണം: $num$", "placeholders": {"num": {"content": "$1"}}}, "dian_pu_xin_xi": {"message": "വിവരങ്ങൾ സംഭരിക്കുക"}, "ding_zai_zuo_ce": {"message": "ഇടതുവശത്ത് ആണിയടിച്ചു"}, "disable_old_version_tips_disable_btn_title": {"message": "പഴയ പതിപ്പ് അപ്രാപ്‌തമാക്കുക"}, "download_image__SKU_variant_images": {"message": "SKU വേരിയൻ്റ് ചിത്രങ്ങൾ"}, "download_image__assume": {"message": "ഉദാഹരണത്തിന്, ഞങ്ങൾക്ക് 2 ചിത്രങ്ങൾ ഉണ്ട്, product1.jpg, product2.gif.\nimg_{$no$} എന്നതിനെ img_01.jpg, img_02.gif എന്ന് പുനർനാമകരണം ചെയ്യും;\n{$group$}_{$no$} എന്നത് main_image_01.jpg, main_image_02.gif എന്ന് പുനർനാമകരണം ചെയ്യും;", "placeholders": {"no": {"content": "$3"}, "group": {"content": "$2"}}}, "download_image__batch_download": {"message": "ബാച്ച് ഡൗൺലോഡ്"}, "download_image__combined_image": {"message": "സംയോജിത ഉൽപ്പന്ന വിശദാംശ ചിത്രം"}, "download_image__continue_downloading": {"message": "ഡൗൺലോഡ് ചെയ്യുന്നത് തുടരുക"}, "download_image__description_images": {"message": "വിവരണ ചിത്രങ്ങൾ"}, "download_image__download_combined_image": {"message": "സംയോജിത ഉൽപ്പന്ന വിശദാംശ ചിത്രം ഡൗൺലോഡ് ചെയ്യുക"}, "download_image__download_zip": {"message": "സിപ്പ് ഡൗൺലോഡ് ചെയ്യുക"}, "download_image__enlarge_check": {"message": "JPEG, JPG, GIF, PNG ചിത്രങ്ങൾ മാത്രം പിന്തുണയ്ക്കുന്നു, ഒരൊറ്റ ചിത്രത്തിൻ്റെ പരമാവധി വലുപ്പം: 1600 * 1600"}, "download_image__enlarge_image": {"message": "ചിത്രം വലുതാക്കുക"}, "download_image__export": {"message": "കയറ്റുമതി"}, "download_image__height": {"message": "ഉയരം"}, "download_image__ignore_videos": {"message": "കയറ്റുമതി ചെയ്യാൻ കഴിയാത്തതിനാൽ വീഡിയോ അവഗണിക്കപ്പെട്ടു"}, "download_image__img_translate": {"message": "ഇമേജ് വിവർത്തനം"}, "download_image__main_image": {"message": "പ്രധാന ചിത്രം"}, "download_image__multi_folder": {"message": "മൾട്ടി ഫോൾഡർ"}, "download_image__name": {"message": "ചിത്രം ഡൗൺലോഡ് ചെയ്യുക"}, "download_image__notice_content": {"message": "നിങ്ങളുടെ ബ്രൗസറിൻ്റെ ഡൗൺലോഡ് ക്രമീകരണങ്ങളിൽ \"ഡൗൺലോഡ് ചെയ്യുന്നതിന് മുമ്പ് ഓരോ ഫയലും എവിടെ സേവ് ചെയ്യണമെന്ന് ചോദിക്കുക\" എന്നത് ദയവായി പരിശോധിക്കരുത്!!! അല്ലെങ്കിൽ ധാരാളം ഡയലോഗ് ബോക്സുകൾ ഉണ്ടാകും."}, "download_image__notice_ignore": {"message": "ഈ സന്ദേശത്തിനായി വീണ്ടും ആവശ്യപ്പെടരുത്"}, "download_image__order_number": {"message": "{$no$} സീരിയൽ നമ്പർ; {$group$} ഗ്രൂപ്പിൻ്റെ പേര്; {$date$} ടൈംസ്റ്റാമ്പ്", "placeholders": {"no": {"content": "$1"}, "group": {"content": "$2"}, "date": {"content": "$3"}}}, "download_image__overview": {"message": "അവലോകനം"}, "download_image__prompt_download_zip": {"message": "വളരെയധികം ചിത്രങ്ങളുണ്ട്, അവ ഒരു zip ഫോൾഡറായി ഡൗൺലോഡ് ചെയ്യുന്നതാണ് നല്ലത്."}, "download_image__rename": {"message": "പേരുമാറ്റുക"}, "download_image__rule": {"message": "പേരിടൽ നിയമങ്ങൾ"}, "download_image__single_folder": {"message": "ഒറ്റ ഫോൾഡർ"}, "download_image__sku_image": {"message": "SKU ചിത്രങ്ങൾ"}, "download_image__video": {"message": "വീഡിയോ"}, "download_image__width": {"message": "വീതി"}, "download_reviews__download_images": {"message": "അവലോകന ചിത്രം ഡൗൺലോഡ് ചെയ്യുക"}, "download_reviews__dropdown_title": {"message": "അവലോകന ചിത്രം ഡൗൺലോഡ് ചെയ്യുക"}, "download_reviews__export_csv": {"message": "CSV കയറ്റുമതി ചെയ്യുക"}, "download_reviews__no_images": {"message": "0 ചിത്രങ്ങൾ ഡൗൺലോഡ് ചെയ്യാൻ ലഭ്യമാണ്"}, "download_reviews__no_reviews": {"message": "ഡൗൺലോഡ് ചെയ്യാൻ റിവ്യൂ ഒന്നുമില്ല!"}, "download_reviews__notice": {"message": "നുറുങ്ങ്:"}, "download_reviews__notice__chrome_settings": {"message": "ഡൗൺലോഡ് ചെയ്യുന്നതിന് മുമ്പ് ഓരോ ഫയലും എവിടെ സംരക്ഷിക്കണമെന്ന് ചോദിക്കാൻ Chrome ബ്രൗസർ സജ്ജമാക്കുക, \"ഓഫ്\" എന്ന് സജ്ജമാക്കുക"}, "download_reviews__notice__wait": {"message": "അവലോകനങ്ങളുടെ എണ്ണം അനുസരിച്ച്, കാത്തിരിപ്പ് സമയം കൂടുതലായിരിക്കാം"}, "download_reviews__pages_list__all": {"message": "എല്ലാം"}, "download_reviews__pages_list__page": {"message": "മുമ്പത്തെ $page$ പേജുകൾ", "placeholders": {"page": {"content": "$1"}}}, "download_reviews__pages_list_title": {"message": "തിരഞ്ഞെടുക്കൽ ശ്രേണി"}, "export_shopping_cart__csv_filed__details_url": {"message": "ഉൽപ്പന്ന ലിങ്ക്"}, "export_shopping_cart__csv_filed__details_url_with_sku": {"message": "എക്കോ SKU ലിങ്ക്"}, "export_shopping_cart__csv_filed__images": {"message": "ചിത്ര ലിങ്ക്"}, "export_shopping_cart__csv_filed__quantity": {"message": "അളവ്"}, "export_shopping_cart__csv_filed__sale_price": {"message": "വില"}, "export_shopping_cart__csv_filed__specs": {"message": "സ്പെസിഫിക്കേഷനുകൾ"}, "export_shopping_cart__csv_filed__store_name": {"message": "സ്റ്റോറിന്റെ പേര്"}, "export_shopping_cart__csv_filed__store_url": {"message": "സ്റ്റോർ ലിങ്ക്"}, "export_shopping_cart__csv_filed__title": {"message": "ഉത്പന്നത്തിന്റെ പേര്"}, "export_shopping_cart__export_btn": {"message": "കയറ്റുമതി"}, "export_shopping_cart__export_empty": {"message": "ദയവായി ഒരു ഉൽപ്പന്നം തിരഞ്ഞെടുക്കുക!"}, "fa_huo_shi_jian": {"message": "ഷിപ്പിംഗ്"}, "favorite_add_email": {"message": "ഇമെയിൽ ചേർക്കുക"}, "favorite_add_favorites": {"message": "പ്രിയപ്പെട്ടവയിലേക്ക് ചേർക്കുക"}, "favorite_added": {"message": "ചേർത്തു"}, "favorite_btn_add": {"message": "വില ഡ്രോപ്പ് അലേർട്ട്."}, "favorite_btn_notify": {"message": "ട്രാക്ക് വില"}, "favorite_cate_name_all": {"message": "എല്ലാ ഉൽപ്പന്നങ്ങളും"}, "favorite_current_price": {"message": "നിലവിലെ വില"}, "favorite_due_date": {"message": "അവസാന തീയതി"}, "favorite_enable_notification": {"message": "ദയവായി ഇമെയിൽ അറിയിപ്പ് പ്രാപ്തമാക്കുക"}, "favorite_expired": {"message": "കാലഹരണപ്പെട്ടു"}, "favorite_go_to_enable": {"message": "പ്രാപ്തമാക്കുക എന്നതിലേക്ക് പോകുക"}, "favorite_msg_add_success": {"message": "പ്രിയങ്കരങ്ങളിലേക്ക് ചേർത്തു"}, "favorite_msg_del_success": {"message": "പ്രിയങ്കരങ്ങളിൽ നിന്ന് ഇല്ലാതാക്കി"}, "favorite_msg_failure": {"message": "പരാജയപ്പെട്ടു! പേജ് പുതുക്കി വീണ്ടും ശ്രമിക്കുക."}, "favorite_please_add_email": {"message": "ദയവായി ഇമെയിൽ ചേർക്കുക"}, "favorite_price_drop": {"message": "താഴേക്ക്"}, "favorite_price_rise": {"message": "മുകളിലേക്ക്"}, "favorite_price_untracked": {"message": "വില ട്രാക്ക് ചെയ്തിട്ടില്ല"}, "favorite_saved_price": {"message": "സംരക്ഷിച്ച വില"}, "favorite_stop_tracking": {"message": "ട്രാക്കിംഗ് നിർത്തുക"}, "favorite_sub_email_address": {"message": "സബ്സ്ക്രിപ്ഷൻ ഇമെയിൽ വിലാസം"}, "favorite_tracking_period": {"message": "ട്രാക്കിംഗ് കാലയളവ്"}, "favorite_tracking_prices": {"message": "വിലകൾ ട്രാക്ക് ചെയ്യുന്നു"}, "favorite_verify_email": {"message": "ഇമെയിൽ വിലാസം പരിശോധിക്കുക"}, "favorites_list_remove_prompt_msg": {"message": "ഇത് ഇല്ലാതാക്കുമെന്ന് നിങ്ങൾക്ക് ഉറപ്പാണോ?"}, "favorites_update_button": {"message": "ഇപ്പോൾ വിലകൾ അപ്ഡേറ്റ് ചെയ്യുക"}, "fen_lei": {"message": "വിഭാഗം"}, "fen_xia_yan_xuan": {"message": "വിതരണക്കാരന്റെ ചോയ്‌സ്"}, "find_similar": {"message": "സമാനമായത് കണ്ടെത്തുക"}, "first_ali_price_date": {"message": "AliPrice ക്രാളർ ആദ്യമായി പിടിച്ചെടുത്ത തീയതി"}, "fooview_coupons_modal_no_data": {"message": "കൂപ്പണുകളൊന്നുമില്ല"}, "fooview_coupons_modal_title": {"message": "കൂപ്പണുകൾ"}, "fooview_favorites__product_sub__tooltip_l1": {"message": "വില < $lowerPrice$", "placeholders": {"lowerPrice": {"content": "$1"}}}, "fooview_favorites__product_sub__tooltip_l2": {"message": "അല്ലെങ്കിൽ വില > $higherPrice$", "placeholders": {"higherPrice": {"content": "$1"}}}, "fooview_favorites__product_sub__tooltip_l3": {"message": "ഡെഡ്ലൈൻ"}, "fooview_favorites_error_msg_no_favorites": {"message": "പ്രൈസ് ഡ്രോപ്പ് അലേർട്ട് ലഭിക്കുന്നതിന് പ്രിയപ്പെട്ട ഉൽപ്പന്നങ്ങൾ ഇവിടെ ചേർക്കുക."}, "fooview_favorites_filter_latest": {"message": "ഏറ്റവും പുതിയ"}, "fooview_favorites_filter_price_drop": {"message": "വില കുറച്ചു"}, "fooview_favorites_filter_price_up": {"message": "വില വർദ്ധനവ്"}, "fooview_favorites_modal_title": {"message": "എന്റെ പ്രിയങ്കരങ്ങൾ"}, "fooview_favorites_modal_title_title": {"message": "അലിപ്രൈസ് പ്രിയങ്കരത്തിലേക്ക് പോകുക"}, "fooview_favorites_track_price": {"message": "വില ട്രാക്കുചെയ്യുന്നതിന്"}, "fooview_price_history_app_price": {"message": "APP വില:"}, "fooview_price_history_title": {"message": "വില ചരിത്രം"}, "fooview_product_list_feedback": {"message": "ഫീഡ്‌ബാക്ക്"}, "fooview_product_list_orders": {"message": "ഓർഡറുകൾ"}, "fooview_product_list_price": {"message": "വില"}, "fooview_reviews_error_msg_no_review": {"message": "ഈ ഉൽപ്പന്നത്തിനായി ഞങ്ങൾ അവലോകനങ്ങളൊന്നും കണ്ടെത്തിയില്ല."}, "fooview_reviews_filter_buyer_reviews": {"message": "വാങ്ങുന്നവരുടെ ഫോട്ടോകൾ"}, "fooview_reviews_modal_title": {"message": "അവലോകനങ്ങൾ"}, "fooview_same_product_choose_category": {"message": "വിഭാഗം തിരഞ്ഞെടുക്കുക"}, "fooview_same_product_filter_feedback": {"message": "ഫീഡ്‌ബാക്ക്"}, "fooview_same_product_filter_orders": {"message": "ഓർഡറുകൾ"}, "fooview_same_product_filter_price": {"message": "വില"}, "fooview_same_product_filter_rating": {"message": "റേറ്റിംഗ്"}, "fooview_same_product_modal_title": {"message": "സമാന ഉൽപ്പന്നം കണ്ടെത്തുക"}, "fooview_same_product_search_by_image": {"message": "ഇമേജ് പ്രകാരം തിരയുക"}, "fooview_seller_analysis_modal_title": {"message": "വിൽപ്പനക്കാരന്റെ വിശകലനം"}, "for_12_months": {"message": "1 വർഷത്തേക്ക്"}, "for_12_months_list_pro": {"message": "12 മാസം"}, "for_12_months_nei": {"message": "12 മാസത്തിനുള്ളിൽ"}, "for_1_months": {"message": "1 മാസം"}, "for_1_months_nei": {"message": "1 മാസത്തിനുള്ളിൽ"}, "for_3_months": {"message": "3 മാസത്തേക്ക്"}, "for_3_months_nei": {"message": "3 മാസത്തിനുള്ളിൽ"}, "for_6_months": {"message": "6 മാസത്തേക്ക്"}, "for_6_months_nei": {"message": "6 മാസത്തിനുള്ളിൽ"}, "for_9_months": {"message": "9 മാസം"}, "for_9_months_nei": {"message": "9 മാസത്തിനുള്ളിൽ"}, "fu_gou_lv": {"message": "റീപർച്ചേസ് നിരക്ക്"}, "gao_liang_bu_tong_dian": {"message": "വ്യത്യാസങ്ങൾ ഹൈലൈറ്റ് ചെയ്യുക"}, "gao_liang_guang_gao_chan_pin": {"message": "പരസ്യ ഉൽപ്പന്നങ്ങൾ ഹൈലൈറ്റ് ചെയ്യുക"}, "geng_duo_xin_xi": {"message": "കൂടുതൽ വിവരങ്ങൾ"}, "geng_xin_shi_jian": {"message": "അപ്ഡേറ്റ് സമയം"}, "gong_x_kuan_shang_pin": {"message": "മൊത്തം $amount$ ഉൽപ്പന്നങ്ങൾ", "placeholders": {"amount": {"content": "$1"}}}, "gong_ying_shang": {"message": "വിതരണക്കാരൻ"}, "gong_ying_shang_ID": {"message": "വിതരണക്കാരൻ ഐഡി"}, "gong_ying_shang_deng_ji": {"message": "വിതരണക്കാരന്റെ റേറ്റിംഗ്"}, "gong_ying_shang_nian_zhan": {"message": "വിതരണക്കാരൻ പഴയതാണ്"}, "gong_ying_shang_xin_xi": {"message": "വിതരണക്കാരന്റെ വിവരങ്ങൾ"}, "gong_ying_shang_zhu_ye_lian_jie": {"message": "വിതരണക്കാരന്റെ ഹോംപേജ് ലിങ്ക്"}, "green_rocket": {"message": "로켓프레시"}, "grey_rocket": {"message": "로켓설치"}, "gu_ji_shou_jia": {"message": "കണക്കാക്കിയ വിൽപ്പന വില"}, "guan_jian_zi": {"message": "കീവേഡ്"}, "guang_gao_chan_pin": {"message": "പരസ്യം. ഉൽപ്പന്നങ്ങൾ"}, "guang_gao_zhan_bi": {"message": "പരസ്യം. അനുപാതം"}, "guo_ji_wu_liu_yun_fei": {"message": "അന്താരാഷ്ട്ര ഷിപ്പിംഗ് ഫീസ്"}, "guo_lv_tiao_jian": {"message": "<PERSON><PERSON><PERSON>"}, "hao_ping_lv": {"message": "പോസിറ്റീവ് റേറ്റിംഗ്"}, "highest_price": {"message": "ഉയർന്ന"}, "historical_trend": {"message": "ചരിത്രപരമായ പ്രവണത"}, "how_to_screenshot": {"message": "ഏരിയ തിരഞ്ഞെടുക്കാൻ ഇടത് മൌസ് ബട്ടൺ അമർത്തിപ്പിടിക്കുക, സ്ക്രീൻഷോട്ടിൽ നിന്ന് പുറത്തുകടക്കാൻ വലത് മൗസ് ബട്ടണിലോ Esc കീയിലോ ടാപ്പ് ചെയ്യുക"}, "howt_it_works": {"message": "ഇത് എങ്ങനെ പ്രവർത്തിക്കുന്നു"}, "hui_fu_lv": {"message": "പ്രതികരണ നിരക്ക്"}, "hui_tou_lv": {"message": "റിട്ടേൺ നിരക്ക്"}, "inquire_freightFee": {"message": "ചരക്ക് അന്വേഷണം"}, "inquire_freightFee_Yuan": {"message": "ചരക്ക്/യുവാൻ"}, "inquire_freightFee_province": {"message": "പ്രവിശ്യ"}, "inquire_freightFee_the": {"message": "ചരക്ക് $num$ ആണ്, അതായത് പ്രദേശത്തിന് സൗജന്യ ഷിപ്പിംഗ് ഉണ്ട്.", "placeholders": {"num": {"content": "$1"}}}, "is_ad": {"message": "പരസ്യം."}, "jia_ge": {"message": "വില"}, "jia_ge_dan_wei": {"message": "യൂണിറ്റ്"}, "jia_ge_qu_shi": {"message": "പ്രവണത"}, "jia_zai_n_ge_shang_pin": {"message": "$num$ ഉൽപ്പന്നങ്ങൾ ലോഡ് ചെയ്യുക", "placeholders": {"num": {"content": "$1"}}}, "jin_30_tian_xiao_liang_zhan_bi": {"message": "കഴിഞ്ഞ 30 ദിവസങ്ങളിലെ വിൽപ്പന അളവിൻ്റെ ശതമാനം"}, "jin_30_tian_xiao_shou_e_zhan_bi": {"message": "കഴിഞ്ഞ 30 ദിവസത്തെ വരുമാനത്തിൻ്റെ ശതമാനം"}, "jin_30d_xiao_liang": {"message": "വിൽപ്പന"}, "jin_30d_xiao_liang__desc": {"message": "കഴിഞ്ഞ 30 ദിവസങ്ങളിലെ മൊത്തം വിൽപ്പന"}, "jin_30d_xiao_shou_e": {"message": "വിറ്റുവരവ്"}, "jin_30d_xiao_shou_e__desc": {"message": "കഴിഞ്ഞ 30 ദിവസത്തെ മൊത്തം വിറ്റുവരവ്"}, "jin_90_tian_mai_jia_shu": {"message": "കഴിഞ്ഞ 90 ദിവസങ്ങളിൽ വാങ്ങുന്നവർ"}, "jin_90_tian_xiao_shou_liang": {"message": "കഴിഞ്ഞ 90 ദിവസങ്ങളിലെ വിൽപ്പന"}, "jing_xuan_huo_yuan": {"message": "തിരഞ്ഞെടുത്ത ഉറവിടങ്ങൾ"}, "jing_ying_mo_shi": {"message": "ബിസിനസ് രീീതി"}, "jing_ying_mo_shi__gong_chang": {"message": "നിർമ്മാതാവ്"}, "jiu_fen_jie_jue": {"message": "തർക്ക പരിഹാരം"}, "jiu_fen_jie_jue__desc": {"message": "വിൽപ്പനക്കാരുടെ സ്റ്റോർ അവകാശ തർക്കങ്ങളുടെ അക്കൗണ്ടിംഗ്"}, "jiu_fen_lv": {"message": "തർക്ക നിരക്ക്"}, "jiu_fen_lv__desc": {"message": "കഴിഞ്ഞ 30 ദിവസത്തിനുള്ളിൽ പൂർത്തിയാക്കിയ പരാതികളുമൊത്തുള്ള ഓർഡറുകളുടെ അനുപാതം വിൽപ്പനക്കാരൻ്റെയോ ഇരു കക്ഷികളുടെയും ഉത്തരവാദിത്തമാണെന്ന് വിലയിരുത്തപ്പെടുന്നു"}, "kai_dian_ri_qi": {"message": "തുറക്കുന്ന തീയതി"}, "keywords": {"message": "കീവേഡുകൾ"}, "kua_jin_Select_pan_huo": {"message": "ക്രോസ്-ബോർഡർ സെലക്ഷൻ"}, "last15_days": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> 15 dienas"}, "last180_days": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> 180 dienas"}, "last30_days": {"message": "കഴിഞ്ഞ 30 ദിവസങ്ങളിൽ"}, "last360_days": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> 360 dienas"}, "last45_days": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> 45 dienas"}, "last60_days": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> 60 dienas"}, "last7_days": {"message": "Pēdējās 7 dienas"}, "last90_days": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> 90 dienas"}, "last_30d_sales": {"message": "കഴിഞ്ഞ 30 ദിവസത്തെ വിൽപ്പന"}, "lei_ji": {"message": "ക്യുമുലേറ്റീവ്"}, "lei_ji_xiao_liang": {"message": "ആകെ"}, "lei_ji_xiao_liang__desc": {"message": "ഉൽപ്പന്നത്തിന് ശേഷമുള്ള എല്ലാ വിൽപ്പനകളും ഷെൽഫിൽ"}, "lei_ji_xiao_liang_pai_xu__desc": {"message": "കഴിഞ്ഞ 30 ദിവസങ്ങളിലെ ക്യുമുലേറ്റീവ് വിൽപ്പന അളവ്, ഉയർന്നതിൽ നിന്ന് താഴ്ന്നതിലേക്ക് അടുക്കി"}, "lian_xi_fang_shi": {"message": "ബന്ധപ്പെടാനുള്ള വിവരങ്ങൾ"}, "list_time": {"message": "ഷെൽഫ് തീയതിയിൽ"}, "load_more": {"message": "കൂടുതൽ ലോഡ് ചെയ്യുക"}, "login_to_aliprice": {"message": "AliPrice-ലേക്ക് ലോഗിൻ ചെയ്യുക"}, "long_link": {"message": "നീണ്ട ലിങ്ക്"}, "lowest_price": {"message": "താഴ്ന്നത്"}, "mai_jia_shu": {"message": "വിൽപ്പനക്കാരുടെ എണ്ണം"}, "mao_li_lv": {"message": "മൊത്തം മാർജിൻ"}, "mobile_view__dkxbqy": {"message": "ഒരു പുതിയ ടാബ് തുറക്കുക"}, "mobile_view__sjdxq": {"message": "ആപ്പിലെ വിശദാംശങ്ങൾ"}, "mobile_view__sjdxqy": {"message": "ആപ്പിലെ വിശദമായ പേജ്"}, "mobile_view__smck": {"message": "കാണുന്നതിന് സ്കാൻ ചെയ്യുക"}, "mobile_view__smckms": {"message": "സ്കാൻ ചെയ്യാനും കാണാനും ക്യാമറയോ ആപ്പോ ഉപയോഗിക്കുക"}, "modified_failed": {"message": "പരിഷ്ക്കരണം പരാജയപ്പെട്ടു"}, "modified_successfully": {"message": "വിജയകരമായി പരിഷ്കരിച്ചു"}, "nav_btn_favorites": {"message": "എന്റെ ശേഖരങ്ങൾ"}, "nav_btn_package": {"message": "പാക്കേജ്"}, "nav_btn_product_info": {"message": "ഉൽപ്പന്നത്തെക്കുറിച്ച്"}, "nav_btn_viewed": {"message": "കണ്ടു"}, "no_suggest_search_kw": {"message": "Loading..."}, "none": {"message": "ഇല്ല"}, "normal_link": {"message": "സാധാരണ ലിങ്ക്"}, "notice": {"message": "സൂചന"}, "number_reviews": {"message": "അവലോകനങ്ങൾ"}, "only_show_num": {"message": "ആകെ ഉൽപ്പന്നങ്ങൾ: $allnum$, മറച്ചത്: $hidenum2$", "placeholders": {"allnum": {"content": "$1"}, "hidenum2": {"content": "$2"}}}, "only_show_selected": {"message": "അൺചെക്ക് ചെയ്‌തത് നീക്കം ചെയ്യുക"}, "open": {"message": "തുറക്കുക"}, "open_links": {"message": "ലിങ്കുകൾ തുറക്കുക"}, "options_page_tab_check_links": {"message": "ലിങ്കുകൾ പരിശോധിക്കുക"}, "options_page_tab_gernal": {"message": "ജനറൽ"}, "options_page_tab_notifications": {"message": "അറിയിപ്പുകൾ"}, "options_page_tab_others": {"message": "മറ്റുള്ളവർ"}, "options_page_tab_sbi": {"message": "ഇമേജ് പ്രകാരം തിരയുക"}, "options_page_tab_shortcuts": {"message": "കുറുക്കുവഴികൾ"}, "options_page_tab_shortcuts_title": {"message": "കുറുക്കുവഴികൾക്കുള്ള ഫോണ്ട് വലുപ്പം"}, "options_page_tab_similar_products": {"message": "ഒരേ ഉൽപ്പന്നങ്ങൾ"}, "orange_rocket": {"message": "판매자로켓"}, "order_list_open_links_tip": {"message": "ഒന്നിലധികം ഉൽപ്പന്ന ലിങ്കുകൾ തുറക്കാൻ പോകുന്നു"}, "order_list_sku_show_title": {"message": "പങ്കിട്ട ലിങ്കുകളിൽ തിരഞ്ഞെടുത്ത വേരിയൻ്റുകൾ കാണിക്കുക"}, "orders_last30_days": {"message": "കഴിഞ്ഞ 30 ദിവസങ്ങളിലെ ഓർഡറുകളുടെ എണ്ണം"}, "pTutorial_favorites_block1_desc1": {"message": "നിങ്ങൾ ട്രാക്കുചെയ്ത ഉൽപ്പന്നങ്ങൾ ഇവിടെ പട്ടികപ്പെടുത്തിയിട്ടുണ്ട്"}, "pTutorial_favorites_block1_title": {"message": "പ്രിയങ്കരങ്ങൾ"}, "pTutorial_popup_block1_desc1": {"message": "ഒരു പച്ച ലേബൽ എന്നതിനർത്ഥം വില കുറഞ്ഞ ഉൽപ്പന്നങ്ങളുണ്ടെന്നാണ്"}, "pTutorial_popup_block1_title": {"message": "കുറുക്കുവഴികളും പ്രിയങ്കരങ്ങളും"}, "pTutorial_price_history_block1_desc1": {"message": "വില ട്രാക്കുചെയ്യുക ക്ലിക്കുചെയ്യുക, പ്രിയങ്കരങ്ങളിലേക്ക് ഉൽപ്പന്നങ്ങൾ ചേർക്കുക. അവയുടെ വില കുറഞ്ഞു കഴിഞ്ഞാൽ, നിങ്ങൾക്ക് അറിയിപ്പുകൾ ലഭിക്കും"}, "pTutorial_price_history_block1_title": {"message": "ട്രാക്ക് വില"}, "pTutorial_reviews_block1_desc1": {"message": "ഇറ്റാവോയിൽ നിന്നുള്ള വാങ്ങുന്നവരുടെ അവലോകനങ്ങളും അലിഎക്സ്പ്രസ്സ് ഫീഡ്‌ബാക്കിൽ നിന്നുള്ള യഥാർത്ഥ ഫോട്ടോകളും"}, "pTutorial_reviews_block1_title": {"message": "അവലോകനങ്ങൾ"}, "pTutorial_reviews_block2_desc1": {"message": "മറ്റ് വാങ്ങുന്നവരിൽ നിന്നുള്ള അവലോകനങ്ങൾ പരിശോധിക്കുന്നത് എല്ലായ്പ്പോഴും സഹായകരമാണ്"}, "pTutorial_same_products_block1_desc1": {"message": "മികച്ച തിരഞ്ഞെടുപ്പ് നടത്താൻ നിങ്ങൾക്ക് അവ താരതമ്യം ചെയ്യാം"}, "pTutorial_same_products_block1_desc2": {"message": "ഇമേജ് പ്രകാരം തിരയുക എന്നതിലേക്ക് 'കൂടുതൽ' ക്ലിക്കുചെയ്യുക"}, "pTutorial_same_products_block1_title": {"message": "ഒരേ ഉൽപ്പന്നങ്ങൾ"}, "pTutorial_same_products_by_image_search_block1_desc1": {"message": "ഉൽപ്പന്ന ഇമേജ് അവിടെ ഉപേക്ഷിച്ച് ഒരു വിഭാഗം തിരഞ്ഞെടുക്കുക"}, "pTutorial_same_products_by_image_search_block1_title": {"message": "ഇമേജ് പ്രകാരം തിരയുക"}, "pTutorial_seller_analysis_block1_desc1": {"message": "വിൽപ്പനക്കാരന്റെ പോസിറ്റീവ് ഫീഡ്‌ബാക്ക് നിരക്ക്, ഫീഡ്‌ബാക്ക് സ്‌കോറുകൾ, വിൽപ്പനക്കാരൻ വിപണിയിൽ എത്ര കാലമായി"}, "pTutorial_seller_analysis_block1_title": {"message": "വിൽപ്പനക്കാരന്റെ റേറ്റിംഗ്"}, "pTutorial_seller_analysis_block2_desc2": {"message": "വിൽപ്പനക്കാരന്റെ റേറ്റിംഗ് 3 സൂചികകളെ അടിസ്ഥാനമാക്കിയുള്ളതാണ്: വിവരിച്ച ഇനം, ആശയവിനിമയ ഷിപ്പിംഗ് വേഗത"}, "pTutorial_seller_analysis_block3_desc3": {"message": "വിൽപ്പനക്കാരുടെ വിശ്വാസ്യത നില സൂചിപ്പിക്കാൻ ഞങ്ങൾ 3 നിറങ്ങളും ഐക്കണുകളും ഉപയോഗിക്കുന്നു"}, "page_count": {"message": "പേജുകളുടെ എണ്ണം"}, "pai_chu": {"message": "ഒഴിവാക്കി"}, "pai_chu_HK_bu_yi_xia_xiaoshou": {"message": "ഹോങ്കോംഗ്-നിയന്ത്രിത ഒഴിവാക്കുക"}, "pai_chu_JP_bu_yi_xia_xiaoshou": {"message": "ജപ്പാൻ-നിയന്ത്രിത ഒഴിവാക്കുക"}, "pai_chu_KR_bu_yi_xia_xiaoshou": {"message": "കൊറിയ-നിയന്ത്രിത ഒഴിവാക്കുക"}, "pai_chu_KZ_bu_yi_xia_xiaoshou": {"message": "കസാക്കിസ്ഥാൻ-നിയന്ത്രിത ഒഴിവാക്കുക"}, "pai_chu_MO_bu_yi_xia_xiaoshou": {"message": "മക്കാവു-നിയന്ത്രിത ഒഴിവാക്കുക"}, "pai_chu_RU_bu_yi_xia_xiaoshou": {"message": "കിഴക്കൻ യൂറോപ്പ്-നിയന്ത്രിത ഒഴിവാക്കുക"}, "pai_chu_SA_bu_yi_xia_xiaoshou": {"message": "സൗദി അറേബ്യ-നിയന്ത്രിത ഒഴിവാക്കുക"}, "pai_chu_TW_bu_yi_xia_xiaoshou": {"message": "തായ്‌വാൻ-നിയന്ത്രിത ഒഴിവാക്കുക"}, "pai_chu_USA_bu_yi_xia_xiaoshou": {"message": "യുഎസ്-നിയന്ത്രിത ഒഴിവാക്കുക"}, "pai_chu_VN_bu_yi_xia_xiaoshou": {"message": "വിയറ്റ്നാം-നിയന്ത്രിത ഒഴിവാക്കുക"}, "pai_chu_bu_yi_xia_xiaoshou": {"message": "നിയന്ത്രിത ഇനങ്ങൾ ഒഴിവാക്കുക"}, "payable_price_formula": {"message": "വില + ഷിപ്പിംഗ് + കിഴിവ്"}, "pdd_check_retail_btn_txt": {"message": "റീട്ടെയിൽ പരിശോധിക്കുക"}, "pdd_pifa_to_retail_btn_txt": {"message": "ചില്ലറ വിൽപ്പനയിൽ വാങ്ങുക"}, "pdp_copy_fail": {"message": "പകർത്താനായില്ല!"}, "pdp_copy_success": {"message": "പകർപ്പ് വിജയിച്ചു!"}, "pdp_share_modal_subtitle": {"message": "സ്ക്രീൻഷോട്ട് പങ്കിടുക, അവൻ/അവൾ നിങ്ങളുടെ ചോയ്സ് കാണും."}, "pdp_share_modal_title": {"message": "നിങ്ങളുടെ തിരഞ്ഞെടുപ്പ് പങ്കിടുക"}, "pdp_share_screenshot": {"message": "സ്ക്രീൻഷോട്ട് പങ്കിടുക"}, "pei_song": {"message": "ഷിപ്പിംഗ് മാർഗം"}, "pin_lei": {"message": "വിഭാഗം"}, "pin_zhi_ti_yan": {"message": "ഉൽപ്പന്ന നിലവാരം"}, "pin_zhi_ti_yan__desc": {"message": "വിൽപ്പനക്കാരൻ്റെ സ്റ്റോറിൻ്റെ ഗുണനിലവാരമുള്ള റീഫണ്ട് നിരക്ക്"}, "pin_zhi_tui_kuan_lv": {"message": "റീഫണ്ട് നിരക്ക്"}, "pin_zhi_tui_kuan_lv__desc": {"message": "കഴിഞ്ഞ 30 ദിവസത്തിനുള്ളിൽ റീഫണ്ട് ചെയ്യുകയും തിരികെ നൽകുകയും ചെയ്ത ഓർഡറുകളുടെ അനുപാതം"}, "ping_fen": {"message": "റേറ്റിംഗ്"}, "ping_jun_fa_huo_su_du": {"message": "ശരാശരി ഷിപ്പിംഗ് വേഗത"}, "pkgInfo_hide": {"message": "ലോജിസ്റ്റിക്സ് വിവരം: ഓൺ/ഓഫ്"}, "pkgInfo_no_trace": {"message": "ലോജിസ്റ്റിക്സ് വിവരങ്ങളൊന്നുമില്ല"}, "platform_name__1688": {"message": "1688"}, "platform_name__17zwd": {"message": "17zwd.com"}, "platform_name__51taoyang": {"message": "51taoyang.com"}, "platform_name__5ts": {"message": "5ts.com"}, "platform_name__91jf": {"message": "91jf.com"}, "platform_name__alibaba": {"message": "Alibaba.com"}, "platform_name__aliexpress": {"message": "AliExpress"}, "platform_name__amazon": {"message": "Amazon"}, "platform_name__bao66": {"message": "bao66.cn"}, "platform_name__chinagoods": {"message": "chinagoods.com"}, "platform_name__dhgate": {"message": "ഡിഎച്ച് ഗേറ്റ്"}, "platform_name__e3hui": {"message": "e3hui.com"}, "platform_name__ebay": {"message": "ഇബേ"}, "platform_name__hznzcn": {"message": "hznzcn.com"}, "platform_name__jd": {"message": "JD"}, "platform_name__juyi5": {"message": "juyi5.cn"}, "platform_name__naver_shopping": {"message": "Naver Shopping"}, "platform_name__pinduoduo": {"message": "പിൻഡുവോഡുവോ"}, "platform_name__pinduoduo_pifa": {"message": "Pinduoduo മൊത്തവ്യാപാരം"}, "platform_name__shopee": {"message": "ഷോപ്പി"}, "platform_name__sjxzw": {"message": "571xz.com"}, "platform_name__sooxie": {"message": "sooxie.com"}, "platform_name__taobao": {"message": "താവോബാവോ"}, "platform_name__taobao_lite": {"message": "Taobao Lite"}, "platform_name__vvic": {"message": "vvic.com"}, "platform_name__walmart": {"message": "വാൾമാർട്ട്"}, "platform_name__wsy": {"message": "wsy.com"}, "platform_name__xingfujie": {"message": "xingfujie.cn"}, "platform_name__yiwugo": {"message": "Yiwugo.com"}, "platform_name__yunchepin": {"message": "yunchepin.cn"}, "platform_name__zhaojiafang": {"message": "zhaojiafang.com"}, "popup_go_to_home": {"message": "വീട്"}, "popup_go_to_platform": {"message": "അലിപ്രൈസിലേക്ക് പോകുക"}, "popup_search_placeholder": {"message": "ഞാൻ ഷോപ്പിംഗ് നടത്തുന്നു ..."}, "popup_track_package_btn_track": {"message": "ട്രാക്ക്"}, "popup_track_package_desc": {"message": "എല്ലാ ഇൻ-വൺ പാക്കേജ് ട്രാക്കിംഗ്"}, "popup_track_package_search_placeholder": {"message": "ട്രാക്കിംഗ് നമ്പർ"}, "popup_translate_search_placeholder": {"message": "$searchOn$ എന്നതിൽ വിവർത്തനം ചെയ്യുകയും തിരയുകയും ചെയ്യുക", "placeholders": {"searchOn": {"content": "$1"}}}, "price_history": {"message": "വില ചരിത്രം"}, "price_history_chart_tip_ae": {"message": "നുറുങ്ങ്: സമാരംഭിച്ചതിന് ശേഷമുള്ള ഓർഡറുകളുടെ ക്യുമുലേറ്റീവ് സംഖ്യയാണ് ഓർഡറുകളുടെ എണ്ണം"}, "price_history_chart_tip_coupang": {"message": "നുറുങ്ങ്: വഞ്ചനാപരമായ ഓർഡറുകളുടെ ഓർഡർ എണ്ണം Coupang ഇല്ലാതാക്കും"}, "price_history_inm_1688_l1": {"message": "ഇൻസ്റ്റാൾ ചെയ്യുക"}, "price_history_inm_1688_l2": {"message": "1688 നായുള്ള അലിപ്രൈസ് ഷോപ്പിംഗ് അസിസ്റ്റന്റ്"}, "price_history_panel_lowest_price": {"message": "ഏറ്റവും കുറഞ്ഞ വില:"}, "price_history_panel_tab_price_tracking": {"message": "വില ചരിത്രം"}, "price_history_panel_tab_seller_analysis": {"message": "വിൽപ്പനക്കാരന്റെ വിശകലനം"}, "price_history_pro_modal_title": {"message": "വില ചരിത്രവും ഓർഡർ ചരിത്രവും"}, "privacy_consent__btn_agree": {"message": "ഡാറ്റ ശേഖരണ സമ്മതം വീണ്ടും സന്ദർശിക്കുക"}, "privacy_consent__btn_disable_all": {"message": "സ്വീകരിക്കില്ല"}, "privacy_consent__btn_enable_all": {"message": "എല്ലാം പ്രാപ്തമാക്കുക"}, "privacy_consent__btn_uninstall": {"message": "നീക്കംചെയ്യുക"}, "privacy_consent__desc_privacy": {"message": "ശ്രദ്ധിക്കുക, ഡാറ്റയോ കുക്കികളോ ഇല്ലാതെ ചില ഫംഗ്ഷനുകൾ ഓഫാകും കാരണം ആ ഫംഗ്ഷനുകൾക്ക് ഡാറ്റയുടെയോ കുക്കികളുടെയോ വിശദീകരണം ആവശ്യമാണ്, എന്നാൽ നിങ്ങൾക്ക് ഇപ്പോഴും മറ്റ് ഫംഗ്ഷനുകൾ ഉപയോഗിക്കാൻ കഴിയും."}, "privacy_consent__desc_privacy_L1": {"message": "നിർഭാഗ്യവശാൽ, ഡാറ്റയോ കുക്കികളോ ഇല്ലാതെ ഇത് പ്രവർത്തിക്കില്ല കാരണം ഞങ്ങൾക്ക് ഡാറ്റയുടെയോ കുക്കികളുടെയോ വിശദീകരണം ആവശ്യമാണ്."}, "privacy_consent__desc_privacy_L2": {"message": "ഈ വിവരങ്ങൾ‌ ശേഖരിക്കാൻ നിങ്ങൾ‌ ഞങ്ങളെ അനുവദിക്കുന്നില്ലെങ്കിൽ‌, ദയവായി അത് നീക്കംചെയ്യുക."}, "privacy_consent__item_cookies_desc": {"message": "കുക്കി, വില ചരിത്രം കാണിക്കുന്നതിന് ഓൺ‌ലൈൻ ഷോപ്പിംഗ് നടത്തുമ്പോൾ മാത്രമേ നിങ്ങളുടെ കറൻസി ഡാറ്റ കുക്കികളിൽ ലഭിക്കുകയുള്ളൂ."}, "privacy_consent__item_cookies_title": {"message": "ആവശ്യമായ കുക്കികൾ"}, "privacy_consent__item_functional_desc_L1": {"message": "1. നിങ്ങളുടെ കമ്പ്യൂട്ടറോ ഉപകരണമോ അജ്ഞാതമായി തിരിച്ചറിയുന്നതിന് ബ്രൗസറിൽ കുക്കികൾ ചേർക്കുക."}, "privacy_consent__item_functional_desc_L2": {"message": "2. ഫംഗ്ഷനോടൊപ്പം പ്രവർത്തിക്കാൻ ആഡ്-ഓൺ ഫംഗ്ഷണൽ ഡാറ്റ ചേർക്കുക."}, "privacy_consent__item_functional_title": {"message": "പ്രവർത്തനപരവും അനലിറ്റിക്സ് കുക്കികളും"}, "privacy_consent__more_desc": {"message": "നിങ്ങളുടെ സ്വകാര്യ ഡാറ്റ ഞങ്ങൾ മറ്റ് കമ്പനികളുമായി പങ്കിടുന്നില്ലെന്നും പരസ്യ കമ്പനികളൊന്നും ഞങ്ങളുടെ സേവനത്തിലൂടെ ഡാറ്റ ശേഖരിക്കുന്നില്ലെന്നും ദയവായി അറിയുക."}, "privacy_consent__options__btn__desc": {"message": "എല്ലാ സവിശേഷതകളും ഉപയോഗിക്കുന്നതിന്, നിങ്ങൾ അത് ഓണാക്കേണ്ടതുണ്ട്."}, "privacy_consent__options__btn__label": {"message": "അത് ഓണാക്കുക"}, "privacy_consent__options__desc_L1": {"message": "നിങ്ങളെ വ്യക്തിപരമായി തിരിച്ചറിയുന്ന ഇനിപ്പറയുന്ന ഡാറ്റ ഞങ്ങൾ ശേഖരിക്കും:"}, "privacy_consent__options__desc_L2": {"message": "- കുക്കികൾ, വില ചരിത്രം കാണിക്കുന്നതിന് നിങ്ങൾ ഓൺലൈനിൽ ഷോപ്പിംഗ് നടത്തുമ്പോൾ മാത്രമേ നിങ്ങളുടെ കറൻസി ഡാറ്റ കുക്കികളിൽ ലഭിക്കുകയുള്ളൂ."}, "privacy_consent__options__desc_L3": {"message": "- നിങ്ങളുടെ കമ്പ്യൂട്ടറോ ഉപകരണമോ അജ്ഞാതമായി തിരിച്ചറിയുന്നതിന് ബ്ര browser സറിൽ കുക്കികൾ ചേർക്കുക."}, "privacy_consent__options__desc_L4": {"message": "- മറ്റ് അജ്ഞാത ഡാറ്റ ഈ വിപുലീകരണത്തെ കൂടുതൽ സൗകര്യപ്രദമാക്കുന്നു."}, "privacy_consent__options__desc_L5": {"message": "നിങ്ങളുടെ സ്വകാര്യ ഡാറ്റ ഞങ്ങൾ മറ്റ് കമ്പനികളുമായി പങ്കിടുന്നില്ലെന്നും പരസ്യ കമ്പനികളൊന്നും ഞങ്ങളുടെ സേവനത്തിലൂടെ ഡാറ്റ ശേഖരിക്കുന്നില്ലെന്നും ദയവായി ശ്രദ്ധിക്കുക."}, "privacy_consent__privacy_preferences": {"message": "സ്വകാര്യത മുൻഗണനകൾ"}, "privacy_consent__read_more": {"message": "കൂടുതൽ വായിക്കുക >>"}, "privacy_consent__title_privacy": {"message": "സ്വകാര്യത"}, "product_info": {"message": "Informācija par produktu"}, "product_recommend__name": {"message": "ഒരേ ഉൽപ്പന്നങ്ങൾ"}, "product_research": {"message": "ഉൽപ്പന്ന ഗവേഷണം"}, "product_sub__email_desc": {"message": "വില മുന്നറിയിപ്പ് ഇമെയിൽ"}, "product_sub__email_edit": {"message": "തിരുത്തുക"}, "product_sub__email_not_verified": {"message": "ഇമെയിൽ പരിശോധിച്ചുറപ്പിക്കുക"}, "product_sub__email_required": {"message": "ദയവായി ഇമെയിൽ നൽകുക"}, "product_sub__form_countdown": {"message": "$seconds$ സെക്കൻഡുകൾക്ക് ശേഷം സ്വയമേവ അടയ്ക്കുക", "placeholders": {"seconds": {"content": "$1"}}}, "product_sub__form_fail": {"message": "റിമൈൻഡർ ചേർക്കുന്നതിൽ പരാജയപ്പെട്ടു!"}, "product_sub__form_input_price": {"message": "ഇൻപുട്ട് വില"}, "product_sub__form_item_country": {"message": "രാജ്യം"}, "product_sub__form_item_current_price": {"message": "ഇപ്പോഴത്തെ വില"}, "product_sub__form_item_duration": {"message": "ട്രാക്ക്"}, "product_sub__form_item_higher_price": {"message": "അല്ലെങ്കിൽ വില>"}, "product_sub__form_item_invalid_higher_price": {"message": "വില $price$-നേക്കാൾ കൂടുതലായിരിക്കണം", "placeholders": {"price": {"content": "$1"}}}, "product_sub__form_item_invalid_lower_price": {"message": "വില $price$-നേക്കാൾ കുറവായിരിക്കണം", "placeholders": {"price": {"content": "$1"}}}, "product_sub__form_item_lower_price": {"message": "എപ്പോൾ വില <"}, "product_sub__form_submit": {"message": "സമർപ്പിക്കുക"}, "product_sub__form_success": {"message": "ഓർമ്മപ്പെടുത്തൽ ചേർക്കുന്നതിൽ വിജയിച്ചു!"}, "product_sub__high_price_notify": {"message": "വില വർദ്ധനവിനെക്കുറിച്ച് എന്നെ അറിയിക്കുക"}, "product_sub__low_price_notify": {"message": "വിലക്കുറവിനെക്കുറിച്ച് എന്നെ അറിയിക്കുക"}, "product_sub__modal_title": {"message": "സബ്‌സ്‌ക്രിപ്‌ഷൻ വില മാറ്റത്തിന്റെ ഓർമ്മപ്പെടുത്തൽ"}, "province__an_hui": {"message": "<PERSON><PERSON>"}, "province__ao_men": {"message": "Macau"}, "province__bei_jing": {"message": "Beijing"}, "province__chong_qing": {"message": "Chongqing"}, "province__fu_jian": {"message": "Fujian"}, "province__gan_su": {"message": "Gansu"}, "province__guang_dong": {"message": "Guangdong"}, "province__guang_xi": {"message": "Guangxi"}, "province__gui_zhou": {"message": "Guizhou"}, "province__hai_nan": {"message": "Hainan"}, "province__he_bei": {"message": "Hebei"}, "province__he_nan": {"message": "<PERSON><PERSON>"}, "province__hei_long_jiang": {"message": "Heilongjiang"}, "province__hu_bei": {"message": "Hubei"}, "province__hu_nan": {"message": "Hunan"}, "province__ji_lin": {"message": "<PERSON><PERSON>"}, "province__jiang_su": {"message": "Jiangsu"}, "province__jiang_xi": {"message": "Jiangxi"}, "province__liao_ning": {"message": "Liaoning"}, "province__nei_meng_gu": {"message": "Inner Mongolia"}, "province__ning_xia": {"message": "Ningxia"}, "province__qing_hai": {"message": "Qinghai"}, "province__shan_dong": {"message": "Shandong"}, "province__shan_xi": {"message": "Shaanxi"}, "province__shang_hai": {"message": "Shanghai"}, "province__si_chuan": {"message": "Sichuan"}, "province__tai_wan": {"message": "Taiwan"}, "province__tian_jin": {"message": "Tianjin"}, "province__xi_zhang": {"message": "Tibet"}, "province__xiang_gang": {"message": "Hong Kong"}, "province__xin_jiang": {"message": "Xinjiang"}, "province__yun_nan": {"message": "Yunnan"}, "province__zhe_jiang": {"message": "Zhejiang"}, "purple_rocket": {"message": "로켓직구"}, "qi_ding_liang": {"message": "MOQ"}, "qi_ding_liang_qi_ding_jia": {"message": "MOQ & MOP"}, "qi_ye_mian_ji": {"message": "എന്റർപ്രൈസ് ഏരിയ"}, "qing_zhi_shao_xuan_ze_yi_ge_chan_pin": {"message": "ദയവായി ഒരു ഉൽപ്പന്നമെങ്കിലും തിരഞ്ഞെടുക്കുക"}, "qing_zhi_shao_xuan_ze_yi_ge_zi_duan": {"message": "ദയവായി ഒരു ഫീൽഡെങ്കിലും തിരഞ്ഞെടുക്കുക"}, "qu_deng_lu": {"message": "ലോഗിൻ"}, "quan_guo_yan_xuan": {"message": "ആഗോള ചോയ്‌സ്"}, "recommendation_popup_banner_btn_install": {"message": "ഇത് ഇൻസ്റ്റാൾ ചെയ്യുക"}, "recommendation_popup_banner_desc": {"message": "3/6 മാസത്തിനുള്ളിൽ വില ചരിത്രം പ്രദർശിപ്പിക്കുക, വിലക്കുറവ് അറിയിപ്പ്"}, "region__all": {"message": "എല്ലാ പ്രദേശങ്ങളും"}, "region__hai_wai": {"message": "Overseas"}, "region__hua_bei_qu": {"message": "North China"}, "region__hua_dong_qu": {"message": "East China"}, "region__hua_nan_qu": {"message": "South China"}, "region__hua_zhong_qu": {"message": "Central China"}, "region__jiang_zhe_lu": {"message": "Jiangsu, Zhejiang and Shanghai"}, "remove_web_limits": {"message": "റൈറ്റ് ക്ലിക്ക് പ്രവർത്തനക്ഷമമാക്കുക"}, "ren_zheng_gong_chang": {"message": "അംഗീകൃത ഫാക്ടറി"}, "ren_zheng_gong_ying_shang_nian_zhan": {"message": "സർട്ടിഫൈഡ് വിതരണക്കാരനായി വർഷങ്ങൾ"}, "required_to_aliprice_login": {"message": "AliPrice-ലേക്ക് ലോഗിൻ ചെയ്യേണ്ടതുണ്ട്"}, "revenue_last30_days": {"message": "കഴിഞ്ഞ 30 ദിവസങ്ങളിലെ വിൽപ്പന തുക"}, "review_counts": {"message": "ശേഖരിക്കുന്നവരുടെ എണ്ണം"}, "rocket": {"message": "로켓"}, "ru_zhu_nian_xian": {"message": "പ്രവേശന കാലയളവ്"}, "sales_amount_last30_days": {"message": "കഴിഞ്ഞ 30 ദിവസങ്ങളിലെ മൊത്തം വിൽപ്പന"}, "sales_last30_days": {"message": "കഴിഞ്ഞ 30 ദിവസങ്ങളിലെ വിൽപ്പന"}, "sbi_alibaba_cate__accessories": {"message": "ആക്‌സസറികൾ"}, "sbi_alibaba_cate__aqfk": {"message": "സുരക്ഷ"}, "sbi_alibaba_cate__bags_cases": {"message": "ബാഗുകളും കേസുകളും"}, "sbi_alibaba_cate__beauty": {"message": "സൗന്ദര്യം"}, "sbi_alibaba_cate__beverage": {"message": "പാനീയം"}, "sbi_alibaba_cate__bgwh": {"message": "ഓഫീസ് സംസ്കാരം"}, "sbi_alibaba_cate__bz": {"message": "പാക്കേജ്"}, "sbi_alibaba_cate__ccyj": {"message": "അടുക്കള പാത്രങ്ങൾ"}, "sbi_alibaba_cate__clothes": {"message": "വസ്ത്രങ്ങൾ"}, "sbi_alibaba_cate__cmgd": {"message": "മീഡിയ ബ്രോഡ്കാസ്റ്റിംഗ്"}, "sbi_alibaba_cate__coat_jacket": {"message": "കോട്ടും ജാക്കറ്റും"}, "sbi_alibaba_cate__consumer_electronics": {"message": "ഉപഭോക്തൃ ഇലക്ട്രോണിക്സ്"}, "sbi_alibaba_cate__cryp": {"message": "മുതിർന്നവർക്കുള്ള ഉൽപ്പന്നങ്ങൾ"}, "sbi_alibaba_cate__csyp": {"message": "ബെഡ് ലൈനിംഗ്സ്"}, "sbi_alibaba_cate__cwyy": {"message": "വളർത്തുമൃഗങ്ങളുടെ പൂന്തോട്ടപരിപാലനം"}, "sbi_alibaba_cate__cysx": {"message": "കാറ്ററിംഗ് ഫ്രഷ്"}, "sbi_alibaba_cate__dgdq": {"message": "ഇലക്ട്രീഷ്യൻ"}, "sbi_alibaba_cate__dl": {"message": "അഭിനയം"}, "sbi_alibaba_cate__dress_suits": {"message": "വസ്ത്രവും സ്യൂട്ടുകളും"}, "sbi_alibaba_cate__dszm": {"message": "ലൈറ്റിംഗ്"}, "sbi_alibaba_cate__dzqj": {"message": "ഇലക്ട്രോണിക് ഉപകരണം"}, "sbi_alibaba_cate__essb": {"message": "ഉപയോഗിച്ച ഉപകരണങ്ങൾ"}, "sbi_alibaba_cate__food": {"message": "ഭക്ഷണം"}, "sbi_alibaba_cate__fspj": {"message": "വസ്ത്രങ്ങളും അനുബന്ധ ഉപകരണങ്ങളും"}, "sbi_alibaba_cate__furniture": {"message": "ഫർണിച്ചർ"}, "sbi_alibaba_cate__fzpg": {"message": "ടെക്സ്റ്റൈൽ ലെതർ"}, "sbi_alibaba_cate__ghjq": {"message": "സ്വകാര്യ പരിരക്ഷ"}, "sbi_alibaba_cate__gt": {"message": "ഉരുക്ക്"}, "sbi_alibaba_cate__gyp": {"message": "കരകൗശലവസ്തുക്കൾ"}, "sbi_alibaba_cate__hb": {"message": "പരിസ്ഥിതി സൗഹൃദം"}, "sbi_alibaba_cate__hfcz": {"message": "ചർമ്മ സംരക്ഷണ മേക്കപ്പ്"}, "sbi_alibaba_cate__hg": {"message": "രാസ വ്യവസായം"}, "sbi_alibaba_cate__jg": {"message": "പ്രോസസ്സിംഗ്"}, "sbi_alibaba_cate__jianccai": {"message": "കെട്ടിട നിർമാണ സാമഗ്രികൾ"}, "sbi_alibaba_cate__jichuang": {"message": "മെഷീൻ ഉപകരണം"}, "sbi_alibaba_cate__jjry": {"message": "ഗാർഹിക ദൈനംദിന ഉപയോഗം"}, "sbi_alibaba_cate__jtys": {"message": "ഗതാഗതം"}, "sbi_alibaba_cate__jxsb": {"message": "ഉപകരണങ്ങൾ"}, "sbi_alibaba_cate__jxwj": {"message": "മെക്കാനിക്കൽ ഹാർഡ്‌വെയർ"}, "sbi_alibaba_cate__jydq": {"message": "ഗാർഹിക വീട്ടുപകരണങ്ങൾ"}, "sbi_alibaba_cate__jzjc": {"message": "വീട് മെച്ചപ്പെടുത്തുന്നതിനുള്ള നിർമ്മാണ സാമഗ്രികൾ"}, "sbi_alibaba_cate__jzjf": {"message": "ഹോം ടെക്സ്റ്റൈൽസ്"}, "sbi_alibaba_cate__mj": {"message": "ടവൽ"}, "sbi_alibaba_cate__myyp": {"message": "ബേബി ഉൽപ്പന്നങ്ങൾ"}, "sbi_alibaba_cate__nanz": {"message": "പുരുഷന്മാരുടെ"}, "sbi_alibaba_cate__nvz": {"message": "സ്ത്രീകളുടെ വസ്ത്രങ്ങൾ"}, "sbi_alibaba_cate__ny": {"message": "ഊർജ്ജം"}, "sbi_alibaba_cate__others": {"message": "മറ്റുള്ളവർ"}, "sbi_alibaba_cate__qcyp": {"message": "ഓട്ടോ ആക്സസറികൾ"}, "sbi_alibaba_cate__qmpj": {"message": "വാഹനങ്ങളുടെ ഭാഗങ്ങൾ"}, "sbi_alibaba_cate__shoes": {"message": "ഷൂസ്"}, "sbi_alibaba_cate__smdn": {"message": "ഡിജിറ്റൽ കമ്പ്യൂട്ടർ"}, "sbi_alibaba_cate__snqj": {"message": "സംഭരണവും വൃത്തിയാക്കലും"}, "sbi_alibaba_cate__spjs": {"message": "ഭക്ഷണ പാനീയം"}, "sbi_alibaba_cate__swfw": {"message": "വാണിജ്യ സേവനങ്ങൾ"}, "sbi_alibaba_cate__toys_hobbies": {"message": "കളിപ്പാട്ടം"}, "sbi_alibaba_cate__trousers_skirt": {"message": "ട്ര ous സറും പാവാടയും"}, "sbi_alibaba_cate__txcp": {"message": "ആശയവിനിമയ ഉൽപ്പന്നങ്ങൾ"}, "sbi_alibaba_cate__tz": {"message": "കുട്ടികളുടെ വസ്ത്രങ്ങൾ"}, "sbi_alibaba_cate__underwear": {"message": "അടിവസ്ത്രം"}, "sbi_alibaba_cate__wjgj": {"message": "ഹാർഡ്‌വെയർ ഉപകരണങ്ങൾ"}, "sbi_alibaba_cate__xgpi": {"message": "തുകൽ ബാഗുകൾ"}, "sbi_alibaba_cate__xmhz": {"message": "പദ്ധതി സഹകരണം"}, "sbi_alibaba_cate__xs": {"message": "റബ്ബർ"}, "sbi_alibaba_cate__ydfs": {"message": "കായിക വസ്ത്രങ്ങൾ"}, "sbi_alibaba_cate__ydhw": {"message": "ഔട്ട്ഡോർ സ്പോർട്സ്"}, "sbi_alibaba_cate__yjkc": {"message": "മെറ്റലർജിക്കൽ ധാതുക്കൾ"}, "sbi_alibaba_cate__yqyb": {"message": "ഇൻസ്ട്രുമെന്റേഷൻ"}, "sbi_alibaba_cate__ys": {"message": "അച്ചടിക്കുക"}, "sbi_alibaba_cate__yyby": {"message": "വൈദ്യസഹായം"}, "sbi_alibaba_cn_kj_90mjs": {"message": "കഴിഞ്ഞ 90 ദിവസങ്ങളിൽ വാങ്ങുന്നവരുടെ എണ്ണം"}, "sbi_alibaba_cn_kj_90xsl": {"message": "കഴിഞ്ഞ 90 ദിവസങ്ങളിലെ വിൽപ്പന അളവ്"}, "sbi_alibaba_cn_kj_gjsj": {"message": "കണക്കാക്കിയ വില"}, "sbi_alibaba_cn_kj_gjwlyf": {"message": "അന്താരാഷ്ട്ര ഷിപ്പിംഗ് ഫീസ്"}, "sbi_alibaba_cn_kj_gjyf": {"message": "അയയ്ക്കാനുള്ള ചെലവ്"}, "sbi_alibaba_cn_kj_gssj": {"message": "കണക്കാക്കിയ വില"}, "sbi_alibaba_cn_kj_lr": {"message": "ലാഭം"}, "sbi_alibaba_cn_kj_lrgs": {"message": "ലാഭം = കണക്കാക്കിയ വില x ലാഭം"}, "sbi_alibaba_cn_kj_pjfhsd": {"message": "ശരാശരി ഡെലിവറി വേഗത"}, "sbi_alibaba_cn_kj_qtfy": {"message": "മറ്റ് ഫീസ്"}, "sbi_alibaba_cn_kj_qtfygs": {"message": "മറ്റ് ചെലവ് = കണക്കാക്കിയ വില x മറ്റ് ചെലവ് അനുപാതം"}, "sbi_alibaba_cn_kj_spjg": {"message": "വില"}, "sbi_alibaba_cn_kj_spzl": {"message": "ഭാരം"}, "sbi_alibaba_cn_kj_szd": {"message": "സ്ഥാനം"}, "sbi_alibaba_cn_kj_unit_ge": {"message": "കഷണങ്ങൾ"}, "sbi_alibaba_cn_kj_unit_jian": {"message": "കഷണങ്ങൾ"}, "sbi_alibaba_cn_kj_unit_ke": {"message": "ഗ്രാം"}, "sbi_alibaba_cn_kj_unit_ren": {"message": "വാങ്ങുന്നവർ"}, "sbi_alibaba_cn_kj_unit_tai": {"message": "കഷണങ്ങൾ"}, "sbi_alibaba_cn_kj_unit_tao": {"message": "സജ്ജമാക്കുന്നു"}, "sbi_alibaba_cn_kj_unit_tian": {"message": "ദിവസങ്ങളിൽ"}, "sbi_alibaba_cn_kj_zwbj": {"message": "വിലയില്ല"}, "sbi_aliprice_alibaba_cn__jiage": {"message": "വില"}, "sbi_aliprice_alibaba_cn__keshou": {"message": "വില്പനയ്ക്ക് ലഭ്യമാണ്"}, "sbi_aliprice_alibaba_cn__moren": {"message": "സ്ഥിരസ്ഥിതി"}, "sbi_aliprice_alibaba_cn__queding": {"message": "തീർച്ചയായും"}, "sbi_aliprice_alibaba_cn__xiaoliang": {"message": "വിൽപ്പന"}, "sbi_aliprice_alibaba_cn_cate__jiaju": {"message": "ഫർണിച്ചർ"}, "sbi_aliprice_alibaba_cn_cate__linghsi": {"message": "ലഘുഭക്ഷണം"}, "sbi_aliprice_alibaba_cn_cate__meizhuang": {"message": "മേക്കപ്പുകൾ"}, "sbi_aliprice_alibaba_cn_cate__neiyi": {"message": "അടിവസ്ത്രം"}, "sbi_aliprice_alibaba_cn_cate__peishi": {"message": "ആക്സസറികൾ"}, "sbi_aliprice_alibaba_cn_cate__pinchui": {"message": "കുപ്പിവെള്ളം"}, "sbi_aliprice_alibaba_cn_cate__qita": {"message": "മറ്റുള്ളവർ"}, "sbi_aliprice_alibaba_cn_cate__qunzhuang": {"message": "പാവാട"}, "sbi_aliprice_alibaba_cn_cate__shangyi": {"message": "ജാക്കറ്റ്"}, "sbi_aliprice_alibaba_cn_cate__shuma": {"message": "ഇലക്ട്രോണിക്സ്"}, "sbi_aliprice_alibaba_cn_cate__wanju": {"message": "കളിപ്പാട്ടം"}, "sbi_aliprice_alibaba_cn_cate__xiangbao": {"message": "ലഗേജ്"}, "sbi_aliprice_alibaba_cn_cate__xiazhuang": {"message": "അടിവശം"}, "sbi_aliprice_alibaba_cn_cate__xiezi": {"message": "ഷൂ"}, "sbi_aliprice_cate__apparel": {"message": "വസ്ത്രങ്ങൾ"}, "sbi_aliprice_cate__automobiles_motorcycles": {"message": "ഓട്ടോമൊബൈലുകളും മോട്ടോർസൈക്കിളുകളും"}, "sbi_aliprice_cate__beauty_health": {"message": "സൗന്ദര്യവും ആരോഗ്യവും"}, "sbi_aliprice_cate__cellphones_telecommunications": {"message": "സെൽഫോണുകളും ടെലികമ്മ്യൂണിക്കേഷനും"}, "sbi_aliprice_cate__computer_office": {"message": "കമ്പ്യൂട്ടറും ഓഫീസും"}, "sbi_aliprice_cate__consumer_electronics": {"message": "ഉപഭോക്തൃ ഇലക്ട്രോണിക്സ്"}, "sbi_aliprice_cate__education_office_supplies": {"message": "വിദ്യാഭ്യാസവും ഓഫീസ് വിതരണവും"}, "sbi_aliprice_cate__electronic_components_supplies": {"message": "ഇലക്ട്രോണിക് ഘടകങ്ങളും വിതരണങ്ങളും"}, "sbi_aliprice_cate__furniture": {"message": "ഫർണിച്ചർ"}, "sbi_aliprice_cate__hair_extensions_wigs": {"message": "ഹെയർ എക്സ്റ്റൻഷനുകളും വിഗ്ഗുകളും"}, "sbi_aliprice_cate__home_garden": {"message": "വീടും തോട്ടവും"}, "sbi_aliprice_cate__home_improvement": {"message": "വീട് മെച്ചപ്പെടുത്തൽ"}, "sbi_aliprice_cate__jewelry_accessories": {"message": "ആഭരണങ്ങളും അനുബന്ധ ഉപകരണങ്ങളും"}, "sbi_aliprice_cate__luggage_bags": {"message": "ലഗേജുകളും ബാഗുകളും"}, "sbi_aliprice_cate__mother_kids": {"message": "അമ്മയും കുട്ടികളും"}, "sbi_aliprice_cate__novelty_special_use": {"message": "പുതുമയും പ്രത്യേക ഉപയോഗവും"}, "sbi_aliprice_cate__security_protection": {"message": "സുരക്ഷയും പരിരക്ഷണവും"}, "sbi_aliprice_cate__shoes": {"message": "ഷൂസ്"}, "sbi_aliprice_cate__sports_entertainment": {"message": "കായികവും വിനോദവും"}, "sbi_aliprice_cate__toys_hobbies": {"message": "കളിപ്പാട്ടങ്ങളും ഹോബികളും"}, "sbi_aliprice_cate__watches": {"message": "വാച്ചുകൾ"}, "sbi_aliprice_cate__weddings_events": {"message": "വിവാഹങ്ങളും ഇവന്റുകളും"}, "sbi_btn_capture_txt": {"message": "ക്യാപ്‌ചർ"}, "sbi_btn_source_now_txt": {"message": "ഉറവിടം ഇപ്പോൾ"}, "sbi_button__chat_with_me": {"message": "എന്നോട് സംസാരിക്കൂ"}, "sbi_button__contact_supplier": {"message": "ബന്ധപ്പെടുക"}, "sbi_button__hide_on_this_site": {"message": "ഈ സൈറ്റിൽ കാണിക്കരുത്"}, "sbi_button__open_settings": {"message": "ഇമേജ് പ്രകാരം തിരയൽ ക്രമീകരിക്കുക"}, "sbi_capture_shortcut_tip": {"message": "അല്ലെങ്കിൽ കീബോർഡിലെ \"Enter\" കീ അമർത്തുക"}, "sbi_capturing_tip": {"message": "ക്യാപ്‌ചർ ചെയ്യുന്നു"}, "sbi_composed_rating_45": {"message": "4.5 - 5.0 നക്ഷത്രങ്ങൾ"}, "sbi_crop_and_search": {"message": "തിരയുക"}, "sbi_crop_start": {"message": "സ്ക്രീൻഷോട്ട് ഉപയോഗിക്കുക"}, "sbi_err_captcha_action": {"message": "സ്ഥിരീകരിക്കുക"}, "sbi_err_captcha_for_alibaba_cn": {"message": "സ്ഥിരീകരണം ആവശ്യമാണ്, സ്ഥിരീകരിക്കാൻ ഒരു ചിത്രം അപ്‌ലോഡ് ചെയ്യുക. ($video_tutorial$ കാണുക അല്ലെങ്കിൽ കുക്കികൾ മായ്‌ക്കാൻ ശ്രമിക്കുക)", "placeholders": {"feedback": {"content": "$1"}, "video_tutorial": {"content": "$1"}}}, "sbi_err_captcha_for_aliprice": {"message": "അസാധാരണ ട്രാഫിക്, ദയവായി പരിശോധിക്കുക"}, "sbi_err_captcha_for_taobao": {"message": "സ്ഥിരീകരിക്കാൻ ടൊബാവോ നിങ്ങളോട് അഭ്യർത്ഥിക്കുന്നു, ദയവായി ഒരു ചിത്രം സ്വമേധയാ അപ്‌ലോഡുചെയ്‌ത് അത് പരിശോധിക്കാൻ തിരയുക. \"താവോബാവോ ഇമേജ് പ്രകാരമുള്ള തിരയൽ\" പുതിയ സ്ഥിരീകരണ നയം മൂലമാണ് ഈ പിശക്, ടൊബാവോ $feedback$ ൽ പരാതി സ്ഥിരമായി പരിശോധിക്കാൻ ഞങ്ങൾ നിങ്ങളോട് നിർദ്ദേശിക്കുന്നു.", "placeholders": {"feedback": {"content": "$1"}}}, "sbi_err_captcha_for_taobao_feedback": {"message": "ഫീഡ്‌ബാക്ക്"}, "sbi_err_captcha_msg": {"message": "$platform$ തിരയുന്നതിനായി ഒരു ഇമേജ് അപ്‌ലോഡ് ചെയ്യാനോ തിരയൽ നിയന്ത്രണങ്ങൾ നീക്കം ചെയ്യുന്നതിനായി സുരക്ഷാ പരിശോധന പൂർത്തിയാക്കാനോ ആവശ്യപ്പെടുന്നു", "placeholders": {"platform": {"content": "$1"}}}, "sbi_err_checking_if_low_version": {"message": "ഇത് ഏറ്റവും പുതിയ പതിപ്പാണോയെന്ന് പരിശോധിക്കുക"}, "sbi_err_cookie_btn_clear": {"message": "കുക്കികൾ മായ്ക്കുക"}, "sbi_err_cookie_for_alibaba_cn": {"message": "1688 കുക്കികൾ മായ്ക്കണോ?(വീണ്ടും ലോഗിൻ ചെയ്യേണ്ടതുണ്ട്)"}, "sbi_err_desperate_feature_pdd": {"message": "ഇമേജ് സെർച്ച് ഫംഗ്‌ഷൻ ഇമേജ് എക്സ്റ്റൻഷൻ വഴി Pinduoduo തിരയലിലേക്ക് നീക്കി."}, "sbi_err_hidden_skill_of_improve_search_rate": {"message": "ഇമേജ് തിരയലിന്റെ വിജയ നിരക്ക് എങ്ങനെ മെച്ചപ്പെടുത്താം?"}, "sbi_err_img_undersize": {"message": "ചിത്രം > $imgMinimumSize$", "placeholders": {"imgMinimumSize": {"content": "$1"}}}, "sbi_err_login_required": {"message": "ലോഗിൻ ചെയ്യുക $loginSite$", "placeholders": {"loginSite": {"content": "$1"}}}, "sbi_err_login_required_action": {"message": "ലോഗിൻ"}, "sbi_err_low_version": {"message": "ഏറ്റവും പുതിയ പതിപ്പ് ഇൻസ്റ്റാൾ ചെയ്യുക ($latestVersion$)", "placeholders": {"latestVersion": {"content": "$1"}}}, "sbi_err_low_version_action": {"message": "ഡൗൺലോഡ്"}, "sbi_err_need_help": {"message": "സഹായം ആവശ്യമാണ്"}, "sbi_err_network": {"message": "നെറ്റ്‌വർക്ക് പിശക്, നിങ്ങൾക്ക് വെബ്‌സൈറ്റ് സന്ദർശിക്കാനാകുമെന്ന് ഉറപ്പാക്കുക"}, "sbi_err_not_low_version": {"message": "ഏറ്റവും പുതിയ പതിപ്പ് ഇൻസ്റ്റാൾ ചെയ്തു ($latestVersion$)", "placeholders": {"latestVersion": {"content": "$1"}}}, "sbi_err_try_again": {"message": "വീണ്ടും ശ്രമിക്ക്"}, "sbi_err_try_again_action": {"message": "വീണ്ടും ശ്രമിക്ക്"}, "sbi_err_visit_and_try": {"message": "വീണ്ടും ശ്രമിക്കുക, അല്ലെങ്കിൽ വീണ്ടും ശ്രമിക്കാൻ $website$ സന്ദർശിക്കുക", "placeholders": {"website": {"content": "$1"}}}, "sbi_err_visit_site": {"message": "$siteName$ ഹോം പേജ് സന്ദർശിക്കുക", "placeholders": {"siteName": {"content": "$1"}}}, "sbi_fail_tip": {"message": "ലോഡുചെയ്യുന്നത് പരാജയപ്പെട്ടു, ദയവായി പേജ് പുതുക്കി വീണ്ടും ശ്രമിക്കുക."}, "sbi_kuajing_filter_area": {"message": "പ്രദേശം"}, "sbi_kuajing_filter_au": {"message": "ഓസ്ട്രേലിയ"}, "sbi_kuajing_filter_btn_confirm": {"message": "സ്ഥിരീകരിക്കുക"}, "sbi_kuajing_filter_de": {"message": "ജർമ്മനി"}, "sbi_kuajing_filter_destination_country": {"message": "ലക്ഷ്യ രാജ്യം"}, "sbi_kuajing_filter_es": {"message": "സ്പെയിൻ"}, "sbi_kuajing_filter_estimate": {"message": "എസ്റ്റിമേറ്റ്"}, "sbi_kuajing_filter_estimate_price": {"message": "കണക്കാക്കിയ വില"}, "sbi_kuajing_filter_estimate_price_formula": {"message": "കണക്കാക്കിയ വില ഫോർമുല = (ചരക്ക് വില + അന്താരാഷ്ട്ര ലോജിസ്റ്റിക് ചരക്ക്)/(1 - ലാഭ മാർജിൻ - മറ്റ് ചിലവ് അനുപാതം)"}, "sbi_kuajing_filter_fr": {"message": "ഫ്രാൻസ്"}, "sbi_kuajing_filter_kw_placeholder": {"message": "ശീർഷകവുമായി പൊരുത്തപ്പെടുന്നതിന് കീവേഡുകൾ നൽകുക"}, "sbi_kuajing_filter_logistics": {"message": "ലോജിസ്റ്റിക്സ് ടെംപ്ലേറ്റ്"}, "sbi_kuajing_filter_logistics_china_post": {"message": "ചൈന പോസ്റ്റ് എയർ മെയിൽ"}, "sbi_kuajing_filter_logistics_discount": {"message": "ലോജിസ്റ്റിക്സ് കിഴിവ്"}, "sbi_kuajing_filter_logistics_epacket": {"message": "എപ്പാക്കറ്റ്"}, "sbi_kuajing_filter_logistics_price_formula": {"message": "അന്താരാഷ്ട്ര ലോജിസ്റ്റിക് ചരക്ക് = (ഭാരം x ഷിപ്പിംഗ് വില + രജിസ്ട്രേഷൻ ഫീസ്) x (1 - കിഴിവ്)"}, "sbi_kuajing_filter_others_fee": {"message": "മറ്റ് ഫീസ്"}, "sbi_kuajing_filter_profit_percent": {"message": "ലാഭ തോത്"}, "sbi_kuajing_filter_prop": {"message": "ഗുണവിശേഷങ്ങൾ"}, "sbi_kuajing_filter_ru": {"message": "റഷ്യ"}, "sbi_kuajing_filter_total": {"message": "$count$ സമാനമായ ഇനങ്ങൾ പൊരുത്തപ്പെടുത്തുക", "placeholders": {"count": {"content": "$1"}}}, "sbi_kuajing_filter_uk": {"message": "യു.കെ."}, "sbi_kuajing_filter_usa": {"message": "അമേരിക്ക"}, "sbi_login_punish_title__pdd_pifa": {"message": "പിണ്ടുഒദുഒ മൊത്തക്കച്ചവടം"}, "sbi_msg_no_result": {"message": "ഫലമൊന്നും കണ്ടെത്തിയില്ല,ദയവായി $loginSite$ ലേക്ക് ലോഗിൻ ചെയ്യുക അല്ലെങ്കിൽ മറ്റൊരു ചിത്രം പരീക്ഷിക്കുക", "placeholders": {"loginSite": {"content": "$1"}}}, "sbi_msg_no_result_check_support_page_l1": {"message": "സഫാരിക്ക് താൽക്കാലികമായി ലഭ്യമല്ല, ദയവായി $supportPage$ ഉപയോഗിക്കുക.", "placeholders": {"supportPage": {"content": "$1"}}}, "sbi_msg_no_result_check_support_page_l2": {"message": "Chrome ബ്രൗസറും അതിന്റെ വിപുലീകരണങ്ങളും"}, "sbi_msg_no_result_reinstall_l1": {"message": "ഫലങ്ങളൊന്നും കണ്ടെത്തിയില്ല, ദയവായി $loginSite$ ലേക്ക് ലോഗിൻ ചെയ്യുക അല്ലെങ്കിൽ മറ്റൊരു ചിത്രം പരീക്ഷിക്കുക, അല്ലെങ്കിൽ ഏറ്റവും പുതിയ പതിപ്പ് $latestExtUrl$ വീണ്ടും ഇൻസ്റ്റാൾ ചെയ്യുക", "placeholders": {"loginSite": {"content": "$1"}, "latestExtUrl": {"content": "$2"}}}, "sbi_msg_no_result_reinstall_l2": {"message": "പുതിയ പതിപ്പ്", "placeholders": {}}, "sbi_search_by_selected_area": {"message": "തിരഞ്ഞെടുത്ത ഏരിയ"}, "sbi_shipping_": {"message": "അതേ ദിവസം ഷിപ്പിംഗ്"}, "sbi_specify_category": {"message": "വിഭാഗം വ്യക്തമാക്കുക:"}, "sbi_start_crop": {"message": "പ്രദേശം തിരഞ്ഞെടുക്കുക"}, "sbi_store_name_alibabaCNKuaJing": {"message": "1688 വിദേശത്ത്"}, "sbi_tutorial_btn_more": {"message": "കൂടുതൽ വഴികൾ"}, "sbi_tutorial_find_coupons_on_taobao": {"message": "Taobao കൂപ്പണുകൾ കണ്ടെത്തുക"}, "sbi_txt__empty_retry": {"message": "ക്ഷമിക്കണം, ഫലങ്ങളൊന്നും കണ്ടെത്തിയില്ല, വീണ്ടും ശ്രമിക്കുക."}, "sbi_txt__min_order": {"message": "മി. ഓർഡർ"}, "sbi_visiting": {"message": "ബ്രൗസിംഗ്"}, "sbi_yiwugo__jiagexiangtan": {"message": "വിലയ്ക്ക് വിൽപ്പനക്കാരനെ ബന്ധപ്പെടുക"}, "sbi_yiwugo__qigou": {"message": "$num$ കഷണങ്ങൾ (MOQ)", "placeholders": {"num": {"content": "$1"}}}, "sbi_yiwugo__xing": {"message": "നക്ഷത്രങ്ങൾ"}, "searchByImage_screenshot": {"message": "ഒറ്റ ക്ലിക്ക് സ്ക്രീൻഷോട്ട്"}, "searchByImage_search": {"message": "ഒരേ ഇനങ്ങൾക്കായി ഒറ്റ ക്ലിക്ക് തിരയുക"}, "searchByImage_size_type": {"message": "ഫയലിൻ്റെ വലുപ്പം $num$ MB-നേക്കാൾ വലുതായിരിക്കരുത്, $type$ മാത്രം", "placeholders": {"num": {"content": "$1"}, "type": {"content": "$2"}}}, "search_by_image_progress_analysing": {"message": "ചിത്രം വിശകലനം ചെയ്യുന്നു"}, "search_by_image_progress_searching": {"message": "ഉൽപ്പന്നങ്ങൾക്കായി തിരയുക"}, "search_by_image_progress_sending": {"message": "ചിത്രം അയയ്‌ക്കുന്നു"}, "search_by_image_response_rate": {"message": "പ്രതികരണ നിരക്ക്: ഈ വിതരണക്കാരനുമായി ബന്ധപ്പെട്ട ഉപഭോക്താക്കളുടെ  ന് $responseInHour$ മണിക്കൂറിനുള്ളിൽ ഒരു പ്രതികരണം ലഭിച്ചു.", "placeholders": {"responseInHour": {"content": "$1"}}}, "search_by_keyword": {"message": "കീവേഡ് ഉപയോഗിച്ച് തിരയുക:"}, "select_country_language_modal_title_country": {"message": "രാജ്യം"}, "select_country_language_modal_title_language": {"message": "ഭാഷ"}, "select_country_region_modal_title": {"message": "ഒരു രാജ്യം / പ്രദേശം തിരഞ്ഞെടുക്കുക"}, "select_language_modal_title": {"message": "ഒരു ഭാഷ തിരഞ്ഞെടുക്കുക:"}, "select_shop": {"message": "സ്റ്റോർ തിരഞ്ഞെടുക്കുക"}, "sellers_count": {"message": "നിലവിലെ പേജിലെ വിൽപ്പനക്കാരുടെ എണ്ണം"}, "sellers_count_per_page": {"message": "നിലവിലെ പേജിലെ വിൽപ്പനക്കാരുടെ എണ്ണം"}, "service_score": {"message": "സമഗ്രമായ സേവന റേറ്റിംഗ്"}, "set_shortcut_keys": {"message": "കുറുക്കുവഴി കീകൾ സജ്ജമാക്കുക"}, "setting_logo_title": {"message": "ഷോപ്പിംഗ് അസിസ്റ്റന്റ്"}, "setting_modal_options_position_title": {"message": "പ്ലഗ്-ഇൻ സ്ഥാനം"}, "setting_modal_options_position_value_left": {"message": "ഇടത് കോണിൽ"}, "setting_modal_options_position_value_right": {"message": "വലത് കോണിൽ"}, "setting_modal_options_theme_title": {"message": "തീം നിറം"}, "setting_modal_options_theme_value_dark": {"message": "ഇരുണ്ടത്"}, "setting_modal_options_theme_value_light": {"message": "പ്രകാശം"}, "setting_modal_title": {"message": "ക്രമീകരണങ്ങൾ"}, "setting_options_country_title": {"message": "രാജ്യം / പ്രദേശം"}, "setting_options_hover_zoom_desc": {"message": "സൂം ഇൻ ചെയ്യാൻ മൗസ് ഓവർ"}, "setting_options_hover_zoom_title": {"message": "ഹോവർ സൂം"}, "setting_options_jd_coupon_desc": {"message": "JD.com ൽ കൂപ്പൺ കണ്ടെത്തി"}, "setting_options_jd_coupon_title": {"message": "JD.com കൂപ്പൺ"}, "setting_options_language_title": {"message": "ഭാഷ"}, "setting_options_price_drop_alert_desc": {"message": "എന്റെ പ്രിയപ്പെട്ട ഉൽപ്പന്നങ്ങളുടെ വില കുറയുമ്പോൾ, നിങ്ങൾക്ക് പുഷ് അറിയിപ്പ് ലഭിക്കും."}, "setting_options_price_drop_alert_title": {"message": "വില ഡ്രോപ്പ് അലേർട്ട്"}, "setting_options_price_history_on_list_page_desc": {"message": "ഉൽപ്പന്ന തിരയൽ പേജിൽ വില ചരിത്രം പ്രദർശിപ്പിക്കുക"}, "setting_options_price_history_on_list_page_title": {"message": "വില ചരിത്രം (ലിസ്റ്റ് പേജ്)"}, "setting_options_price_history_on_produt_page_desc": {"message": "ഉൽപ്പന്ന വിശദാംശ പേജിൽ ഉൽപ്പന്ന ചരിത്രം പ്രദർശിപ്പിക്കുക"}, "setting_options_price_history_on_produt_page_title": {"message": "വില ചരിത്രം (വിശദാംശങ്ങൾ പേജ്)"}, "setting_options_sales_analysis_desc": {"message": "$platforms$ ഉൽപ്പന്ന ലിസ്റ്റ് പേജിലെ വില, വിൽപ്പന അളവ്, വിൽപ്പനക്കാരുടെ എണ്ണം, സ്റ്റോർ വിൽപ്പന അനുപാതം എന്നിവയുടെ പിന്തുണാ സ്ഥിതിവിവരക്കണക്കുകൾ", "placeholders": {"platforms": {"content": "$1"}}}, "setting_options_sales_analysis_title": {"message": "വിൽപ്പന വിശകലനം"}, "setting_options_save_success_msg": {"message": "വിജയം"}, "setting_options_tacking_price_title": {"message": "വില മാറ്റ മുന്നറിയിപ്പ്"}, "setting_options_value_off": {"message": "ഓഫാണ്"}, "setting_options_value_on": {"message": "ഓണാണ്"}, "setting_pkg_quick_view_desc": {"message": "പിന്തുണ: 1688 & Taobao"}, "setting_saved_message": {"message": "മാറ്റങ്ങൾ വിജയകരമായി സംരക്ഷിച്ചു"}, "setting_section_enable_platform_title": {"message": "ഓൺ-ഓഫ്"}, "setting_section_setting_title": {"message": "ക്രമീകരണങ്ങൾ"}, "setting_section_shortcuts_title": {"message": "കുറുക്കുവഴികൾ"}, "settings_aliprice_agent__desc": {"message": "$platforms$ ഉൽപ്പന്ന വിശദാംശ പേജിൽ പ്രദർശിപ്പിച്ചിരിക്കുന്നു", "placeholders": {"platforms": {"content": "$1"}}}, "settings_aliprice_agent__title": {"message": "എനിക്ക് വേണ്ടി വാങ്ങുക"}, "settings_copy_link__desc": {"message": "ഉൽപ്പന്ന വിശദാംശ പേജിൽ പ്രദർശിപ്പിക്കുക"}, "settings_copy_link__title": {"message": "പകർത്തുക ബട്ടണും തിരയൽ ശീർഷകവും"}, "settings_currency_desc__for_detail": {"message": "1688 ഉൽപ്പന്ന വിശദാംശ പേജ് പിന്തുണയ്ക്കുക"}, "settings_currency_desc__for_list": {"message": "ഇമേജ് പ്രകാരം തിരയുക (1688/1688 വിദേശ / ടൊബാവോ ഉൾപ്പെടുത്തുക)"}, "settings_currency_desc__for_sbi": {"message": "വില തിരഞ്ഞെടുക്കുക"}, "settings_currency_desc_display_for_list": {"message": "ഇമേജ് തിരയലിൽ കാണിച്ചിരിക്കുന്നു (1688/1688 വിദേശത്ത്/താവോബാവോ ഉൾപ്പെടെ)"}, "settings_currency_rate_desc": {"message": "\"$currencyRateFrom$\" ൽ നിന്ന് വിനിമയ നിരക്ക് അപ്‌ഡേറ്റ് ചെയ്യുന്നു", "placeholders": {"currencyRateFrom": {"content": "$1"}}}, "settings_currency_rate_from_boc": {"message": "ബാങ്ക് ഓഫ് ചൈന"}, "settings_download_images__desc": {"message": "$platforms$-ൽ നിന്ന് ചിത്രങ്ങൾ ഡൗൺലോഡ് ചെയ്യുന്നതിനുള്ള പിന്തുണ", "placeholders": {"platforms": {"content": "$1"}}}, "settings_download_images__title": {"message": "ഇമേജ് ഡൗൺലോഡ് ബട്ടൺ"}, "settings_download_reviews__desc": {"message": "$platforms$ ഉൽപ്പന്ന വിശദാംശ പേജിൽ പ്രദർശിപ്പിച്ചിരിക്കുന്നു", "placeholders": {"platforms": {"content": "$1"}}}, "settings_download_reviews__title": {"message": "അവലോകന ചിത്രങ്ങൾ ഡൗൺലോഡ് ചെയ്യുക"}, "settings_google_translate_desc": {"message": "Google വിവർത്തന ബാർ ലഭിക്കാൻ വലത് ക്ലിക്കുചെയ്യുക"}, "settings_google_translate_title": {"message": "വെബ് പേജ് വിവർത്തനം"}, "settings_historical_trend_desc": {"message": "ഉൽപ്പന്ന ലിസ്റ്റ് പേജിൽ ചിത്രത്തിന്റെ താഴെ വലത് കോണിൽ പ്രദർശിപ്പിക്കുക"}, "settings_modal_btn_more": {"message": "കൂടുതൽ ക്രമീകരണങ്ങൾ"}, "settings_productInfo_desc": {"message": "ഉൽപ്പന്ന ലിസ്റ്റ് പേജിൽ കൂടുതൽ വിശദമായ ഉൽപ്പന്ന വിവരങ്ങൾ പ്രദർശിപ്പിക്കുക. ഇത് പ്രവർത്തനക്ഷമമാക്കുന്നത് കമ്പ്യൂട്ടറിന്റെ ലോഡ് വർദ്ധിപ്പിക്കുകയും പേജ് കാലതാമസത്തിന് കാരണമാവുകയും ചെയ്യും. ഇത് പ്രകടനത്തെ ബാധിക്കുന്നുണ്ടെങ്കിൽ, അത് പ്രവർത്തനരഹിതമാക്കാൻ ശുപാർശ ചെയ്യുന്നു."}, "settings_product_recommend__desc": {"message": "$platforms$ ഉൽപ്പന്ന വിശദാംശ പേജിലെ പ്രധാന ചിത്രത്തിന് താഴെ പ്രദർശിപ്പിച്ചിരിക്കുന്നു", "placeholders": {"platforms": {"content": "$1"}}}, "settings_product_recommend__name": {"message": "ഉൽപ്പന്നങ്ങൾ ശുപാർശ ചെയ്യുന്നു"}, "settings_research_desc": {"message": "ഉൽപ്പന്ന ലിസ്റ്റ് പേജിൽ കൂടുതൽ വിശദമായ വിവരങ്ങൾ അന്വേഷിക്കുക"}, "settings_sbi_add_to_list": {"message": "$listType$ ൽ ചേർക്കുക", "placeholders": {"listType": {"content": "$1"}}}, "settings_sbi_detail_page_icon_title_desc": {"message": "ഇമേജ് തിരയൽ ഫല ലഘുചിത്രം"}, "settings_sbi_remove_from_list": {"message": "$listType$ ൽ നിന്ന് നീക്കംചെയ്യുക", "placeholders": {"listType": {"content": "$1"}}}, "settings_search_by_image_add_to_blacklist": {"message": "ബ്ലോക്ക്ലിസ്റ്റിലേക്ക് ചേർക്കുക"}, "settings_search_by_image_adjust_entrance_entrance": {"message": "പ്രവേശന സ്ഥാനം ക്രമീകരിക്കുക"}, "settings_search_by_image_blacklist_desc": {"message": "കരിമ്പട്ടികയിലെ വെബ്‌സൈറ്റുകളിൽ ഐക്കൺ കാണിക്കരുത്."}, "settings_search_by_image_blacklist_title": {"message": "ബ്ലോക്ക്ലിസ്റ്റ്"}, "settings_search_by_image_bottom_left": {"message": "താഴെ ഇടത്"}, "settings_search_by_image_bottom_right": {"message": "താഴെ വലത്"}, "settings_search_by_image_clear_blacklist": {"message": "ബ്ലോക്ക്‌ലിസ്റ്റ് മായ്‌ക്കുക"}, "settings_search_by_image_detail_page_icon_title": {"message": "ലഘുചിത്രം"}, "settings_search_by_image_detail_page_icon_val_large": {"message": "വലിയ"}, "settings_search_by_image_detail_page_icon_val_small": {"message": "ചെറുത്"}, "settings_search_by_image_display_button_desc": {"message": "ഇമേജ് പ്രകാരം തിരയാൻ ഐക്കണിൽ ഒരു ക്ലിക്കുചെയ്യുക"}, "settings_search_by_image_display_button_title": {"message": "ചിത്രങ്ങളിലെ ഐക്കൺ"}, "settings_search_by_image_sourece_websites_desc": {"message": "ഈ വെബ്‌സൈറ്റുകളിൽ ഉറവിട ഉൽപ്പന്നം കണ്ടെത്തുക"}, "settings_search_by_image_sourece_websites_title": {"message": "ഇമേജ് ഫലം പ്രകാരം തിരയുക"}, "settings_search_by_image_top_left": {"message": "മുകളിൽ ഇടത്"}, "settings_search_by_image_top_right": {"message": "മുകളിൽ വലത്"}, "settings_search_keyword_on_x__desc": {"message": "$platform$-ൽ വാക്കുകൾ തിരയുക", "placeholders": {"platform": {"content": "$1"}}}, "settings_search_keyword_on_x__title": {"message": "വാക്കുകൾ തിരഞ്ഞെടുക്കുമ്പോൾ $platform$ ഐക്കൺ കാണിക്കുക", "placeholders": {"platform": {"content": "$1"}}}, "settings_similar_products_desc": {"message": "ആ വെബ്‌സൈറ്റുകളിൽ സമാന ഉൽപ്പന്നം കണ്ടെത്താൻ ശ്രമിക്കുക (പരമാവധി 5 മുതൽ)"}, "settings_similar_products_title": {"message": "സമാന ഉൽപ്പന്നം കണ്ടെത്തുക"}, "settings_toolbar_expand_title": {"message": "പ്ലഗ്-ഇൻ ചെറുതാക്കുക"}, "settings_top_toolbar_desc": {"message": "പേജിന്റെ മുകളിലുള്ള തിരയൽ ബാർ"}, "settings_top_toolbar_title": {"message": "തിരയൽ ബാർ"}, "settings_translate_search_desc": {"message": "ചൈനീസ് ഭാഷയിലേക്ക് വിവർത്തനം ചെയ്ത് തിരയുക"}, "settings_translate_search_title": {"message": "ബഹുഭാഷാ തിരയൽ"}, "settings_translator_contextmenu_title": {"message": "വിവർത്തനം ചെയ്യാൻ ക്യാപ്ചർ ചെയ്യുക"}, "settings_translator_title": {"message": "വിവർത്തനം ചെയ്യുക"}, "shai_xuan_dao_chu": {"message": "കയറ്റുമതി ചെയ്യാൻ ഫിൽട്ടർ ചെയ്യുക"}, "shai_xuan_zi_duan": {"message": "ഫീൽഡുകൾ ഫിൽട്ടർ ചെയ്യുക"}, "shang_jia_shi_jian": {"message": "ഷെൽഫ് സമയത്ത്"}, "shang_pin_biao_ti": {"message": "ഉൽപ്പന്ന ശീർഷകം"}, "shang_pin_dui_bi": {"message": "ഉൽപ്പന്ന താരതമ്യം"}, "shang_pin_lian_jie": {"message": "ഉൽപ്പന്ന ലിങ്ക്"}, "shang_pin_xin_xi": {"message": "ഉൽപ്പന്ന വിവരം"}, "share_modal__content": {"message": "നിങ്ങളുടെ ചങ്ങാതിമാരുമായി പങ്കിടുക"}, "share_modal__disable_for_while": {"message": "എനിക്ക് ഒന്നും പങ്കിടാൻ ആഗ്രഹമില്ല"}, "share_modal__title": {"message": "നിങ്ങൾക്ക് $extensionName$ ഇഷ്ടമാണോ?", "placeholders": {"extensionName": {"content": "$1"}}}, "sheng_yu_ku_cun": {"message": "ശേഷിക്കുന്നത്"}, "shi_fou_ke_ding_zhi": {"message": "ഇത് ഇഷ്ടാനുസൃതമാക്കാനാകുമോ?"}, "shi_fou_ren_zheng_gong_ying_sha": {"message": "സാക്ഷ്യപ്പെടുത്തിയ വിതരണക്കാരൻ"}, "shi_fou_ren_zheng_gong_ying_shang_zong_shu": {"message": "സാക്ഷ്യപ്പെടുത്തിയ വിതരണക്കാർ"}, "shi_fou_you_mao_yi_dan_bao": {"message": "ട്രേഡ് അഷ്വറൻസ്"}, "shi_fou_you_mao_yi_dan_bao_zong_shu": {"message": "വ്യാപാര ഗ്യാരൻ്റി"}, "shipping_fee": {"message": "അയയ്ക്കാനുള്ള ചെലവ്"}, "shop_followers": {"message": "അനുയായികളെ ഷോപ്പുചെയ്യുക"}, "shou_qi": {"message": "കുറവ്"}, "similar_products_warn_max_platforms": {"message": "പരമാവധി 5 മുതൽ"}, "sku_calc_price": {"message": "കണക്കാക്കിയ വില"}, "sku_calc_price_settings": {"message": "കണക്കാക്കിയ വില ക്രമീകരണങ്ങൾ"}, "sku_formula": {"message": "ഫോർമുല"}, "sku_formula_desc": {"message": "ഫോർമുല വിവരണം"}, "sku_formula_desc_text": {"message": "സങ്കീർണ്ണമായ ഗണിത സൂത്രവാക്യങ്ങളെ പിന്തുണയ്ക്കുന്നു, യഥാർത്ഥ വിലയെ A യും ചരക്ക് സംഖ്യയെ B യും പ്രതിനിധീകരിക്കുന്നു\n\n<br/>\n\nബ്രാക്കറ്റുകൾ (), പ്ലസ് +, മൈനസ് -, ഗുണനം *, ഹരിക്കൽ / എന്നിവ പിന്തുണയ്ക്കുന്നു\n\n<br/>\n\nഉദാഹരണം:\n\n<br/>\n\n1. യഥാർത്ഥ വിലയുടെ 1.2 മടങ്ങ് നേടാനും തുടർന്ന് ചരക്ക് സംഖ്യ കൂട്ടാനും, ഫോർമുല ഇതാണ്: A*1.2+B\n\n<br/>\n\n2. യഥാർത്ഥ വിലയെ 1 യുവാൻ കൂടി നേടാനും, തുടർന്ന് 1.2 മടങ്ങ് ഗുണിക്കാനും, ഫോർമുല ഇതാണ്: (A+1)*1.2\n\n<br/>\n\n3. യഥാർത്ഥ വിലയെ 10 യുവാൻ കൂടി നേടാനും, തുടർന്ന് 1.2 മടങ്ങ് ഗുണിക്കാനും, തുടർന്ന് 3 യുവാൻ കുറയ്ക്കാനും, ഫോർമുല ഇതാണ്: (A+10)*1.2-3"}, "sku_in_stock": {"message": "സ്റ്റോക്കിൽ ഉണ്ട്"}, "sku_invalid_formula_format": {"message": "അസാധുവായ ഫോർമുല ഫോർമാറ്റ്"}, "sku_inventory": {"message": "ഇൻവെന്ററി"}, "sku_link_copy_fail": {"message": "വിജയകരമായി പകർത്തി, sku സ്പെസിഫിക്കേഷനുകളും ആട്രിബ്യൂട്ടുകളും തിരഞ്ഞെടുത്തിട്ടില്ല"}, "sku_link_copy_success": {"message": "വിജയകരമായി പകർത്തി, സ്‌കു സ്‌പെസിഫിക്കേഷനുകളും ആട്രിബ്യൂട്ടുകളും തിരഞ്ഞെടുത്തു"}, "sku_list": {"message": "SKU ലിസ്റ്റ്"}, "sku_min_qrder_qty": {"message": "കുറഞ്ഞ ഓർഡർ അളവ്"}, "sku_name": {"message": "SKU പേര്"}, "sku_no": {"message": "ഇല്ല."}, "sku_original_price": {"message": "യഥാർത്ഥ വില"}, "sku_price": {"message": "SKU വില"}, "stop_track_time_label": {"message": "ട്രാക്കിംഗ് സമയപരിധി:"}, "suo_zai_di_qu": {"message": "സ്ഥാനം"}, "tab_pkg_quick_view": {"message": "ലോജിസ്റ്റിക്സ് മോണിറ്റർ"}, "tab_product_details_price_history": {"message": "വില ചരിത്രം"}, "tab_product_details_reviews": {"message": "ഫോട്ടോ അവലോകനങ്ങൾ"}, "tab_product_details_seller_analysis": {"message": "വിൽപ്പനക്കാരന്റെ വിശകലനം"}, "tab_product_details_similar_products": {"message": "ഒരേ ഉൽപ്പന്നങ്ങൾ"}, "total_days_listed_per_product": {"message": "ഷെൽഫ് ദിവസങ്ങളുടെ ആകെത്തുക ÷ ഉൽപ്പന്നങ്ങളുടെ എണ്ണം"}, "total_items": {"message": "ഉൽപ്പന്നങ്ങളുടെ ആകെ എണ്ണം"}, "total_price_per_product": {"message": "വിലകളുടെ തുക ÷ ഉൽപ്പന്നങ്ങളുടെ എണ്ണം"}, "total_rating_per_product": {"message": "റേറ്റിംഗുകളുടെ ആകെത്തുക ÷ ഉൽപ്പന്നങ്ങളുടെ എണ്ണം"}, "total_revenue": {"message": "മൊത്തം വരുമാനം"}, "total_revenue40_items": {"message": "നിലവിലെ പേജിലെ $amount$ ഉൽപ്പന്നങ്ങളുടെ ആകെ വരുമാനം", "placeholders": {"amount": {"content": "$1"}}}, "total_sales": {"message": "മൊത്തം വിൽപ്പന"}, "total_sales40_items": {"message": "നിലവിലെ പേജിലെ $amount$ ഉൽപ്പന്നങ്ങളുടെ ആകെ വിൽപ്പന", "placeholders": {"amount": {"content": "$1"}}}, "track_for_12_months": {"message": "ഇതിനായുള്ള ട്രാക്ക്: 1 വർഷം"}, "track_for_3_months": {"message": "ഇതിനായുള്ള ട്രാക്ക്: 3 മാസം"}, "track_for_6_months": {"message": "ഇതിനായുള്ള ട്രാക്ക്: 6 മാസം"}, "tracking_price_email_add_btn": {"message": "ഇമെയിൽ ചേർക്കുക"}, "tracking_price_email_edit_btn": {"message": "ഇമെയിൽ എഡിറ്റുചെയ്യുക"}, "tracking_price_email_intro": {"message": "ഞങ്ങൾ നിങ്ങളെ ഇമെയിൽ വഴി അറിയിക്കും."}, "tracking_price_email_invalid": {"message": "സാധുവായ ഒരു ഇമെയിൽ നൽകുക"}, "tracking_price_email_verified_desc": {"message": "നിങ്ങൾക്ക് ഇപ്പോൾ ഞങ്ങളുടെ പ്രൈസ് ഡ്രോപ്പ് അലേർട്ട് ലഭിക്കും."}, "tracking_price_email_verified_title": {"message": "വിജയകരമായി പരിശോധിച്ചു"}, "tracking_price_email_verify_desc_line1": {"message": "നിങ്ങളുടെ ഇമെയിൽ വിലാസത്തിലേക്ക് ഞങ്ങൾ ഒരു സ്ഥിരീകരണ ലിങ്ക് അയച്ചു,"}, "tracking_price_email_verify_desc_line2": {"message": "നിങ്ങളുടെ ഇമെയിൽ ഇൻ‌ബോക്സ് പരിശോധിക്കുക."}, "tracking_price_email_verify_title": {"message": "ഇമെയില് ശരിയാണെന്ന് ഉറപ്പുവരുത്തക"}, "tracking_price_web_push_notification_intro": {"message": "ഡെസ്ക്ടോപ്പിൽ: അലിപ്രൈസിന് നിങ്ങൾക്കായി ഏത് ഉൽപ്പന്നവും നിരീക്ഷിക്കാനും വില മാറിയാൽ നിങ്ങൾക്ക് ഒരു വെബ് പുഷ് അറിയിപ്പ് അയയ്ക്കാനും കഴിയും."}, "tracking_price_web_push_notification_title": {"message": "വെബ് പുഷ് അറിയിപ്പുകൾ"}, "translate_im__login_required": {"message": "AliPrice വിവർത്തനം ചെയ്തത്, ദയവായി $loginUrl$-ലേക്ക് ലോഗിൻ ചെയ്യുക", "placeholders": {"loginUrl": {"content": "$1"}}}, "translate_im__paste_fail": {"message": "ക്ലിപ്പ്ബോർഡിലേക്ക് വിവർത്തനം ചെയ്യുകയും പകർത്തുകയും ചെയ്‌തു, പക്ഷേ അലിവാങ്‌വാങ്ങിൻ്റെ പരിമിതി കാരണം, നിങ്ങൾ ഇത് സ്വമേധയാ ഒട്ടിക്കേണ്ടതുണ്ട്!"}, "translate_im__send": {"message": "വിവർത്തനം ചെയ്‌ത് അയയ്‌ക്കുക"}, "translate_search": {"message": "വിവർത്തനം ചെയ്ത് തിരയുക"}, "translation_originals_translated": {"message": "യഥാർത്ഥവും ചൈനീസ്"}, "translation_translated": {"message": "ചൈനീസ്"}, "translator_btn_capture_txt": {"message": "വിവർത്തനം ചെയ്യുക"}, "translator_language_auto_detect": {"message": "യാന്ത്രിക കണ്ടെത്തൽ"}, "translator_language_detected": {"message": "കണ്ടെത്തി"}, "translator_language_search_placeholder": {"message": "തിരയൽ ഭാഷ"}, "try_again": {"message": "വീണ്ടും ശ്രമിക്ക്"}, "tu_pian_chi_cun": {"message": "ചിത്രത്തിന്റെ വലുപ്പം:"}, "tu_pian_lian_jie": {"message": "ചിത്ര ലിങ്ക്"}, "tui_huan_ti_yan": {"message": "തിരിച്ചുവരവ് അനുഭവം"}, "tui_huan_ti_yan__desc": {"message": "വിൽപ്പനക്കാരുടെ വിൽപ്പനാനന്തര സൂചകങ്ങൾ വിലയിരുത്തുക"}, "tutorial__show_all": {"message": "എല്ലാ സവിശേഷതകളും"}, "tutorial_ae_popup_title": {"message": "വിപുലീകരണം പിൻ ചെയ്യുക, Aliexpress തുറക്കുക"}, "tutorial_aliexpress_reviews_analysis": {"message": "AliExpress അവലോകന വിശകലനം"}, "tutorial_aliprice_agent_with1688_desc_l1": {"message": "പിന്തുണ USD"}, "tutorial_aliprice_agent_with1688_desc_l2": {"message": "കൊറിയ/ജപ്പാൻ/മെയിൻലാൻഡ് ചൈനയിലേക്കുള്ള ഷിപ്പിംഗ്"}, "tutorial_aliprice_agent_with1688_title": {"message": "1688 വിദേശ പർച്ചേസിനെ പിന്തുണയ്ക്കുന്നു"}, "tutorial_auto_apply_coupon_title": {"message": "സ്വയമേവ പ്രയോഗിക്കുന്ന കൂപ്പൺ"}, "tutorial_btn_end": {"message": "അവസാനിക്കുന്നു"}, "tutorial_btn_example": {"message": "ഉദാഹരണം"}, "tutorial_btn_see_more": {"message": "കൂടുതൽ"}, "tutorial_compare_products": {"message": "ഉൽപ്പന്നങ്ങൾ താരതമ്യം ചെയ്യുക"}, "tutorial_currency_convert_title": {"message": "നാണയ പരിവര്ത്തനം"}, "tutorial_export_shopping_cart": {"message": "CSV ആയി കയറ്റുമതി ചെയ്യുക, താവോബാവോയെ പിന്തുണയ്ക്കുക, 1688"}, "tutorial_export_shopping_cart_title": {"message": "കയറ്റുമതി വണ്ടി"}, "tutorial_price_history_pro": {"message": "ഉൽപ്പന്ന വിശദാംശ പേജിൽ പ്രദർശിപ്പിച്ചിരിക്കുന്നു.\nഷോപ്പി, ലസാഡ, ആമസോൺ, ഇബേ എന്നിവയെ പിന്തുണയ്ക്കുക"}, "tutorial_price_history_pro_title": {"message": "വർഷം മുഴുവനും വില ചരിത്രവും ഓർഡർ ചരിത്രവും"}, "tutorial_sbi_context_menu_screenshot_title": {"message": "ചിത്രം ഉപയോഗിച്ച് തിരയാൻ ക്യാപ്ചർ ചെയ്യുക"}, "tutorial_translate_search": {"message": "തിരയാൻ വിവർത്തനം ചെയ്യുക"}, "tutorial_translate_search_and_package_tracking": {"message": "വിവർത്തന തിരയലും പാക്കേജ് ട്രാക്കിംഗും"}, "unit_bao": {"message": "pcs"}, "unit_ben": {"message": "pcs"}, "unit_bi": {"message": "ഉത്തരവുകൾ"}, "unit_chuang": {"message": "pcs"}, "unit_dai": {"message": "pcs"}, "unit_dui": {"message": "prs"}, "unit_fen": {"message": "pcs"}, "unit_ge": {"message": "pcs"}, "unit_he": {"message": "pcs"}, "unit_jian": {"message": "pcs"}, "unit_li_fang_mi": {"message": "m³"}, "unit_ping": {"message": "pcs"}, "unit_ping_fang_mi": {"message": "㎡"}, "unit_shuang": {"message": "prs"}, "unit_tai": {"message": "pcs"}, "unit_ti": {"message": "pcs"}, "unit_tiao": {"message": "pcs"}, "unit_xiang": {"message": "pcs"}, "unit_zhang": {"message": "pcs"}, "unit_zhi": {"message": "pcs"}, "verify_contact_support": {"message": "പിന്തുണയുമായി ബന്ധപ്പെടുക"}, "verify_human_verification": {"message": "മനുഷ്യ പരിശോധന"}, "verify_unusual_access": {"message": "അസാധാരണമായ പ്രവേശനം കണ്ടെത്തി"}, "view_history_clean_all": {"message": "എല്ലാം വൃത്തിയാക്കുക"}, "view_history_clean_all_warring": {"message": "കണ്ട എല്ലാ റെക്കോർഡുകളും വൃത്തിയാക്കണോ?"}, "view_history_clean_all_warring_title": {"message": "മുന്നറിയിപ്പ്"}, "view_history_viewd": {"message": "കണ്ടു"}, "website": {"message": "വെബ്സൈറ്റ്"}, "weight": {"message": "ഭാരം"}, "wu_fa_huo_qu_dao_gai_shu_ju": {"message": "ഡാറ്റ നേടാനായില്ല"}, "wu_liu_shi_xiao": {"message": "കൃത്യസമയത്ത് ഷിപ്പിംഗ്"}, "wu_liu_shi_xiao__desc": {"message": "വിൽപ്പനക്കാരൻ്റെ സ്റ്റോറിൻ്റെ 48 മണിക്കൂർ കളക്ഷൻ നിരക്കും പൂർത്തീകരണ നിരക്കും"}, "xia_dan_jia": {"message": "അന്തിമ വില"}, "xian_xuan_ze_product_attributes": {"message": "ഉൽപ്പന്ന ആട്രിബ്യൂട്ടുകൾ തിരഞ്ഞെടുക്കുക"}, "xiao_liang": {"message": "വിൽപ്പന അളവ്"}, "xiao_liang_zhan_bi": {"message": "വിൽപ്പന അളവിൻ്റെ ശതമാനം"}, "xiao_shi": {"message": "$num$ മണിക്കൂർ", "placeholders": {"num": {"content": "$1"}}}, "xiao_shou_e": {"message": "വരുമാനം"}, "xiao_shou_e_zhan_bi": {"message": "വരുമാനത്തിൻ്റെ ശതമാനം"}, "xuan_zhong_x_tiao_ji_lu": {"message": "$amount$ റെക്കോർഡുകൾ തിരഞ്ഞെടുക്കുക", "placeholders": {"amoun": {"content": "$1"}, "amount": {"content": "$1"}}}, "yan_xuan": {"message": "ചോയ്‌സ്"}, "yi_ding_zai_zuo_ce": {"message": "പിൻ ചെയ്തു"}, "yi_jia_zai_wan_suo_you_shang_pin": {"message": "എല്ലാ ഉൽപ്പന്നങ്ങളും ലോഡ് ചെയ്തു"}, "yi_nian_xiao_liang": {"message": "വാർഷിക വിൽപ്പന"}, "yi_nian_xiao_liang_zhan_bi": {"message": "വാർഷിക വിൽപ്പന ഷെയർ"}, "yi_nian_xiao_shou_e": {"message": "വാർഷിക വിറ്റുവരവ്"}, "yi_nian_xiao_shou_e_zhan_bi": {"message": "വാർഷിക വിറ്റുവരവ് വിഹിതം"}, "yi_shua_xin": {"message": "പുതുക്കി"}, "yin_cang_xiang_tong_dian": {"message": "സമാനതകൾ മറയ്ക്കുക"}, "you_xiao_liang": {"message": "വിൽപ്പന വോളിയം ഉപയോഗിച്ച്"}, "yu_ji_dao_da_shi_jian": {"message": "എത്തിച്ചേരാനുള്ള ഏകദേശ സമയം"}, "yuan_gong_ren_shu": {"message": "ജീവനക്കാരുടെ എണ്ണം"}, "yue_cheng_jiao": {"message": "പ്രതിമാസ വോളിയം"}, "yue_dai_xiao": {"message": "ഡ്രോപ്പ്ഷിപ്പിംഗ്"}, "yue_dai_xiao__desc": {"message": "കഴിഞ്ഞ 30 ദിവസങ്ങളിലെ ഡ്രോപ്പ്ഷിപ്പിംഗ് വിൽപ്പന"}, "yue_dai_xiao_pai_xu__desc": {"message": "കഴിഞ്ഞ 30 ദിവസങ്ങളിലെ ഡ്രോപ്പ്ഷിപ്പിംഗ് വിൽപ്പന, ഉയർന്നതിൽ നിന്ന് താഴ്ന്നതിലേക്ക് അടുക്കി"}, "yue_xiao_liang__desc": {"message": "കഴിഞ്ഞ 30 ദിവസങ്ങളിലെ വിൽപ്പന അളവ്"}, "zhan_kai": {"message": "കൂടുതൽ"}, "zhe_kou": {"message": "കിഴിവ്"}, "zhi_chi_yi_jian_dai_fa": {"message": "ഡ്രോപ്പ്ഷിപ്പിംഗ്"}, "zhi_chi_yi_jian_dai_fa_bao_you": {"message": "ഫ്രീ ഷിപ്പിംഗ്"}, "zhi_fu_ding_dan_shu": {"message": "പണമടച്ചുള്ള ഓർഡറുകൾ"}, "zhi_fu_ding_dan_shu__desc": {"message": "ഈ ഉൽപ്പന്നത്തിനായുള്ള ഓർഡറുകളുടെ എണ്ണം (30 ദിവസം)"}, "zhu_ce_xing_zhi": {"message": "രജിസ്ട്രേഷൻ സ്വഭാവം"}, "zi_ding_yi_tiao_jian": {"message": "ഇഷ്ടാനുസൃത വ്യവസ്ഥകൾ"}, "zi_duan": {"message": "<PERSON><PERSON>"}, "zi_ti_xiao_liang": {"message": "വേരിയേഷൻ വിറ്റു"}, "zong_he_fu_wu_fen": {"message": "മൊത്തത്തിലുള്ള റേറ്റിംഗ്"}, "zong_he_fu_wu_fen__desc": {"message": "വിൽപ്പനക്കാരുടെ സേവനത്തിൻ്റെ മൊത്തത്തിലുള്ള റേറ്റിംഗ്"}, "zong_he_fu_wu_fen__short": {"message": "റേറ്റിംഗ്"}, "zong_he_ti_yan_fen": {"message": "റേറ്റിംഗ്"}, "zong_he_ti_yan_fen_3": {"message": "4 നക്ഷത്രങ്ങൾക്ക് താഴെ"}, "zong_he_ti_yan_fen_4": {"message": "4 - 4.5 നക്ഷത്രങ്ങൾ"}, "zong_he_ti_yan_fen_4_5": {"message": "4.5 - 5.0 നക്ഷത്രങ്ങൾ"}, "zong_he_ti_yan_fen_5": {"message": "5 നക്ഷത്രങ്ങൾ"}, "zong_ku_cun": {"message": "മൊത്തം ഇൻവെന്ററി"}, "zong_xiao_liang": {"message": "മൊത്തം വിൽപ്പന"}, "zui_jin_30D_3Min_xiang_ying_lv": {"message": "കഴിഞ്ഞ 30 ദിവസങ്ങളിലെ 3 മിനിറ്റ് പ്രതികരണ നിരക്ക്"}, "zui_jin_30D_48H_lan_shou_lv": {"message": "കഴിഞ്ഞ 30 ദിവസങ്ങളിലെ 48H വീണ്ടെടുക്കൽ നിരക്ക്"}, "zui_jin_30D_48H_lv_yue_lv": {"message": "കഴിഞ്ഞ 30 ദിവസങ്ങളിലെ 48H പ്രകടന നിരക്ക്"}, "zui_jin_30D_jiao_yi_ji_lu": {"message": "വ്യാപാര റെക്കോർഡ് (30 ദിവസം)"}, "zui_jin_30D_jiao_yi_ji_lu__desc": {"message": "വ്യാപാര റെക്കോർഡ് (30 ദിവസം)"}, "zui_jin_30D_jiu_fen_lv": {"message": "കഴിഞ്ഞ 30 ദിവസങ്ങളിലെ തർക്ക നിരക്ക്"}, "zui_jin_30D_pin_zhi_tui_kuan_lv": {"message": "കഴിഞ്ഞ 30 ദിവസങ്ങളിലെ ഗുണമേന്മയുള്ള റീഫണ്ട് നിരക്ക്"}, "zui_jin_30D_zhi_fu_ding_dan_shu": {"message": "കഴിഞ്ഞ 30 ദിവസങ്ങളിലെ പേയ്‌മെന്റ് ഓർഡറുകളുടെ എണ്ണം"}}