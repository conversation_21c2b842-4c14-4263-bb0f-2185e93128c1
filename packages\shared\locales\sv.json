{"EXTENSION_NAME": {"message": "TODO: EXTENSION_NAME"}, "EXTENSION_DESCRIPTION": {"message": "TODO: EXTENSION_DESCRIPTION"}, "1688_cross_border_hot_selling": {"message": "1688 Gränsöverskridande heta försäljningsställe"}, "1688_shi_li_ren_zheng": {"message": "1688 styrka certifiering"}, "1_jian_qi_pi": {"message": "MOQ: 1"}, "1year_yi_shang": {"message": "Mer än 1 år"}, "24H": {"message": "24H"}, "24H_fa_huo": {"message": "Leveran<PERSON> inom 24 timmar"}, "24H_lan_shou_lv": {"message": "24-t<PERSON><PERSON><PERSON> f<PERSON>"}, "30D_shang_xin": {"message": "Månatliga nya ankomster"}, "30d_sales": {"message": "$amount$ s<PERSON><PERSON><PERSON> på 30 dagar", "placeholders": {"amount": {"content": "$1"}}}, "3Min_xiang_ying_lv": {"message": "<PERSON><PERSON> inom 3 min."}, "3Min_xiang_ying_lv__desc": {"message": "Andelen av Wangwangs effektiva svar på meddelanden från köpare inom 3 minuter under de senast<PERSON> 30 dagarna"}, "48H": {"message": "48H"}, "48H_fa_huo": {"message": "Lev<PERSON><PERSON> inom 48 timmar"}, "48H_lan_shou_lv": {"message": "48-<PERSON><PERSON><PERSON><PERSON>"}, "48H_lan_shou_lv__desc": {"message": "Förhållandet mellan det hämtade beställningsnumret inom 48 timmar och det totala antalet beställningar"}, "48H_lv_yue_lv": {"message": "48-t<PERSON><PERSON><PERSON> prestationstakt"}, "48H_lv_yue_lv__desc": {"message": "Förhållandet mellan hämtat eller levererat ordernummer inom 48 timmar och det totala antalet beställningar"}, "72H": {"message": "72H"}, "7D_shang_xin": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON> varje vecka"}, "7D_wu_li_you": {"message": "7 dagar vårdfri"}, "ABS_title_text": {"message": "Denna lista innehåller en varumärkeshistoria"}, "AC_title_text": {"message": "<PERSON><PERSON> annons har Amazon's Choice-märket"}, "A_title_text": {"message": "Denna annons har en innehållssida A+"}, "BS_title_text": {"message": "Denna annons är rankad som $num$ bästsäljare i kategorin $type$", "placeholders": {"type": {"content": "$1"}, "num": {"content": "$2"}}}, "LTD_title_text": {"message": "LTD (Limited Time Deal) betyder att denna lista är en del av ett \"7-dagars kampanj\"-evenemang"}, "NR_title_text": {"message": "Den här annonsen rankas som $num$ New Release i kategorin $type$", "placeholders": {"type": {"content": "$1"}, "num": {"content": "$2"}}}, "SBV_title_text": {"message": "Denna lista har en videoannons, en typ av PPC-annons som vanligtvis visas mitt i sökresultaten"}, "SB_title_text": {"message": "Denna annons har en varumärkesannons, en typ av PPC-annons som vanligtvis visas högst upp eller längst ned i sökresultaten"}, "SP_title_text": {"message": "<PERSON>na annons har en sponsrad produktannons"}, "V_title_text": {"message": "<PERSON>na lista har en videointroduktion"}, "advanced_research": {"message": "Avancerad forskning"}, "agent_ds1688___my_order": {"message": "<PERSON>"}, "agent_ds1688__add_to_cart": {"message": "Inköp utomlands"}, "agent_ds1688__cart": {"message": "Kundvagn"}, "agent_ds1688__desc": {"message": "Tillhandahålls av 1688. Det stöder direktköp från utlandet, betalning i USD och leverans till ditt transitlager i Kina."}, "agent_ds1688__freight": {"message": "Kalk<PERSON><PERSON> för f<PERSON>"}, "agent_ds1688__help": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "agent_ds1688__packages": {"message": "Fraktsedel"}, "agent_ds1688__profile": {"message": "Personligt center"}, "agent_ds1688__warehouse": {"message": "Mitt lager"}, "ai_comment_analysis_advantage": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "ai_comment_analysis_ai": {"message": "AI granskningsanalys"}, "ai_comment_analysis_available": {"message": "Tillgängliga"}, "ai_comment_analysis_balance": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> med mynt, fyll på"}, "ai_comment_analysis_behavior": {"message": "Beteende"}, "ai_comment_analysis_characteristic": {"message": "Publikegenskaper"}, "ai_comment_analysis_comment": {"message": "Produkten har inte tillräckligt många recensioner för att dra korrekta slutsatser, välj en produkt med fler recensioner."}, "ai_comment_analysis_consume": {"message": "Beräknad förbrukning"}, "ai_comment_analysis_default": {"message": "Standard recensioner"}, "ai_comment_analysis_desire": {"message": "Kundernas förväntningar"}, "ai_comment_analysis_disadvantage": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "ai_comment_analysis_free": {"message": "<PERSON><PERSON><PERSON>"}, "ai_comment_analysis_freeNum": {"message": "1 gratis kredit kommer att användas"}, "ai_comment_analysis_go_recharge": {"message": "Gå till fyllning"}, "ai_comment_analysis_intelligence": {"message": "Intelligent granskningsanalys"}, "ai_comment_analysis_location": {"message": "Plats"}, "ai_comment_analysis_motive": {"message": "Köpmotivation"}, "ai_comment_analysis_network_error": {"message": "Nätverksfel, försök igen"}, "ai_comment_analysis_normal": {"message": "Fotorecensioner"}, "ai_comment_analysis_number_reviews": {"message": "Antal recensioner: $num$, uppskattad förbrukning: $consume$", "placeholders": {"num": {"content": "$1"}, "consume": {"content": "$2"}}}, "ai_comment_analysis_ordinary": {"message": "Allmänna kommentarer"}, "ai_comment_analysis_percentage": {"message": "Procentsats"}, "ai_comment_analysis_problem": {"message": "Problem med betalning"}, "ai_comment_analysis_reanalysis": {"message": "Analy<PERSON>a igen"}, "ai_comment_analysis_reason": {"message": "Anledning"}, "ai_comment_analysis_recharge": {"message": "<PERSON><PERSON><PERSON>"}, "ai_comment_analysis_recharged": {"message": "Jag har fyllt på"}, "ai_comment_analysis_retry": {"message": "Försök igen"}, "ai_comment_analysis_scene": {"message": "Användningsscenario"}, "ai_comment_analysis_start": {"message": "B<PERSON><PERSON>ja analysera"}, "ai_comment_analysis_subject": {"message": "Ämnen"}, "ai_comment_analysis_time": {"message": "Användningstid"}, "ai_comment_analysis_tool": {"message": "AI-verktyg"}, "ai_comment_analysis_user_portrait": {"message": "Användarprofil"}, "ai_comment_analysis_welcome": {"message": "Välkommen till AI-granskningsanalys"}, "ai_comment_analysis_year": {"message": "Kommentarer från det gångna året"}, "ai_listing_Exclude_keywords": {"message": "U<PERSON>lut sökord"}, "ai_listing_Login_the_feature": {"message": "Inloggning krävs för funktionen"}, "ai_listing_aI_generation": {"message": "AI generation"}, "ai_listing_add_automatic": {"message": "Automatisk"}, "ai_listing_add_dictionary_new": {"message": "Skapa ett nytt bibliotek"}, "ai_listing_add_enter_keywords": {"message": "<PERSON><PERSON>"}, "ai_listing_add_inputkey_selling": {"message": "Ange ett försäljningsargument och tryck på $key$ för att slutföra tillägget", "placeholders": {"key": {"content": "$1"}}}, "ai_listing_add_inputkey_warning": {"message": "Gränsen har överskridits, upp till $amount$ försäljningspoäng", "placeholders": {"amount": {"content": "$1"}}}, "ai_listing_add_keywords": {"message": "Lägg till nyckelord"}, "ai_listing_add_manually": {"message": "Lägg till manuellt"}, "ai_listing_add_selling": {"message": "Lägg till försäljningsargument"}, "ai_listing_added_keywords": {"message": "Lade till sökord"}, "ai_listing_added_successfully": {"message": "Tillagd framgångsrikt"}, "ai_listing_addexcluded_keywords": {"message": "<PERSON><PERSON> <PERSON>, tryck på enter för att avsluta till<PERSON>."}, "ai_listing_adding_selling": {"message": "Lade till försäljningsargument"}, "ai_listing_addkeyword_enter": {"message": "Skriv in nyckelattributorden och tryck på enter för att slutföra tillägget"}, "ai_listing_ai_description": {"message": "AI-beskrivningsordbibliotek"}, "ai_listing_ai_dictionary": {"message": "AI-titelordbibliotek"}, "ai_listing_ai_title": {"message": "AI titel"}, "ai_listing_aidescription_repeated": {"message": "AI-beskrivningsordbibliotekets namn kan inte upprepas"}, "ai_listing_aititle_repeated": {"message": "AI-titelordbibliotekets namn kan inte upprepas"}, "ai_listing_data_comes_from": {"message": "Uppgifterna kommer från:"}, "ai_listing_deleted_successfully": {"message": "Raderades framgångsrikt"}, "ai_listing_dictionary_name": {"message": "Bibliotekets namn"}, "ai_listing_edit_dictionary": {"message": "Ändra bibliotek..."}, "ai_listing_edit_word_library": {"message": "Redigera ordbiblioteket"}, "ai_listing_enter_keywords": {"message": "<PERSON><PERSON> nyckelord och tryck på $key$ för att slutföra tillägget", "placeholders": {"key": {"content": "$1"}}}, "ai_listing_exceeded_maximum": {"message": "Gränsen har överskridits, maximalt $amount$ sökord", "placeholders": {"amount": {"content": "$1"}}}, "ai_listing_excluded_word_library": {"message": "Undantaget ordbibliotek"}, "ai_listing_generate_characters": {"message": "Skapa tecken"}, "ai_listing_generation_platform": {"message": "Generationsplattform"}, "ai_listing_help_optimize": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON> mig att optimera produkttiteln, originaltiteln är"}, "ai_listing_include_selling": {"message": "<PERSON><PERSON>jningsargument inkluderar:"}, "ai_listing_included_keyword": {"message": "Inkluderade sökord"}, "ai_listing_included_keywords": {"message": "Inkluderade sökord"}, "ai_listing_input_selling": {"message": "Ange ett försäljningsargument"}, "ai_listing_input_selling_fit": {"message": "Ange försäljningsargument för att matcha titeln"}, "ai_listing_input_selling_please": {"message": "Vänligen ange försäljningsargument"}, "ai_listing_intelligently_title": {"message": "<PERSON><PERSON> det nödvändiga innehållet ovan för att generera titeln på ett intelligent sätt"}, "ai_listing_keyword_product_title": {"message": "Nyckelord produkttitel"}, "ai_listing_keywords_repeated": {"message": "Nyckelord kan inte upprepas"}, "ai_listing_listed_selling_points": {"message": "Inkluderat försäljningsargument"}, "ai_listing_long_title_1": {"message": "Innehåller grundläggande information som varumärke, produkttyp, produktegenskaper, etc."}, "ai_listing_long_title_2": {"message": "På grundval av standardprodukttiteln läggs sökord som främjar SEO."}, "ai_listing_long_title_3": {"message": "Föru<PERSON> att innehålla varumärke, produkttyp, produktegenskaper och n<PERSON>, ing<PERSON><PERSON> även long-tail-sökord för att uppnå högre ranking i specifika, segmenterade sökfrågor."}, "ai_listing_longtail_keyword_product_title": {"message": "Longtail sökord produkttitel"}, "ai_listing_manually_enter": {"message": "<PERSON><PERSON> manuellt..."}, "ai_listing_network_not_working": {"message": "Internet är inte till<PERSON><PERSON>, VPN krävs för att komma åt ChatGPT"}, "ai_listing_new_dictionary": {"message": "Skapa ett nytt ordbibliotek ..."}, "ai_listing_new_generate": {"message": "<PERSON><PERSON>"}, "ai_listing_optional_words": {"message": "Valfria ord"}, "ai_listing_original_title": {"message": "Originaltitel"}, "ai_listing_other_keywords_included": {"message": "<PERSON><PERSON> n<PERSON> inkluderade:"}, "ai_listing_please_again": {"message": "Var god fö<PERSON><PERSON><PERSON> igen"}, "ai_listing_please_select": {"message": "Följande titlar har skapats åt dig, välj:"}, "ai_listing_product_category": {"message": "Produktkategori"}, "ai_listing_product_category_is": {"message": "Produktkategorin är"}, "ai_listing_product_category_to": {"message": "Vilken kategori till<PERSON><PERSON>r produkten?"}, "ai_listing_random_keywords": {"message": "Slumpmässiga sökord för $amount$", "placeholders": {"amount": {"content": "$1"}}}, "ai_listing_random_selling": {"message": "Slumpmässiga försäljningspoäng för $amount$", "placeholders": {"amount": {"content": "$1"}}}, "ai_listing_randomize_keywords": {"message": "Randomisera från ordbiblioteket"}, "ai_listing_search_selling": {"message": "Sök efter försäljningsargument"}, "ai_listing_select_product_categories": {"message": "Välj automatiskt produktkategorier."}, "ai_listing_select_product_selling_points": {"message": "Välj automatiskt produktförsäljningspunkter"}, "ai_listing_select_word_library": {"message": "Välj ordbibliotek"}, "ai_listing_selling": {"message": "Försäljningsargument"}, "ai_listing_selling_ask": {"message": "Vilka andra krav på försäljningsargument finns det för titeln?"}, "ai_listing_selling_optional": {"message": "Valfria försäljningsargument"}, "ai_listing_selling_repeat": {"message": "Poäng kan inte dupliceras"}, "ai_listing_set_excluded": {"message": "<PERSON><PERSON><PERSON> in som exkluderat ordbibliotek"}, "ai_listing_set_include_selling_points": {"message": "Inkludera försäljningsargument"}, "ai_listing_set_included": {"message": "<PERSON><PERSON><PERSON> in som inkluderat ordbibliotek"}, "ai_listing_set_selling_dictionary": {"message": "St<PERSON>ll in som försäljningsargumentbibliotek"}, "ai_listing_standard_product_title": {"message": "Standard produkttitel"}, "ai_listing_translated_title": {"message": "Översatt titel"}, "ai_listing_visit_chatGPT": {"message": "Besök ChatGPT"}, "ai_listing_what_other_keywords": {"message": "<PERSON>ilka andra sökord krävs för titeln?"}, "aliprice_coupons_apply_again": {"message": "Ansök igen"}, "aliprice_coupons_apply_coupons": {"message": "<PERSON><PERSON><PERSON><PERSON> k<PERSON>ger"}, "aliprice_coupons_apply_success": {"message": "Hittad kupong: Spara $amount$", "placeholders": {"amount": {"content": "$1"}}}, "aliprice_coupons_applying": {"message": "Testar koder för de bästa erbjudandena..."}, "aliprice_coupons_applying_desc": {"message": "Utcheckning: $code$", "placeholders": {"code": {"content": "$1"}}}, "aliprice_coupons_back_to_checkout": {"message": "Fortsätt till kassan"}, "aliprice_coupons_found_coupons": {"message": "Vi hittade kuponger för $amount$", "placeholders": {"amount": {"content": "$1"}}}, "aliprice_coupons_found_coupons_desc": {"message": "Redo att betala? <PERSON><PERSON>t oss se till att du får det bästa priset!"}, "aliprice_coupons_no_coupon_aviable": {"message": "De koderna fungerade inte. No biggie – du får redan det bästa priset."}, "aliprice_coupons_toolbar_btn": {"message": "<PERSON><PERSON> kuponger"}, "aliww_translate": {"message": "Aliwangwang chattöversättare"}, "aliww_translate_supports": {"message": "Support: 1688 & Taobao"}, "amazon_extended_keywords_Keywords": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "amazon_extended_keywords_copy_all": {"message": "<PERSON><PERSON><PERSON> alla"}, "amazon_extended_keywords_more": {"message": "<PERSON><PERSON>"}, "an_lei_ji_xiao_liang_pai_xu": {"message": "Sortera efter kumulativ försäljning"}, "an_lei_xing_cha_kan": {"message": "Visa efter"}, "an_yue_dai_xiao_pai_xu": {"message": "Rangordning efter dropshipping-förs<PERSON><PERSON>jning"}, "apra_btn__cat_name": {"message": "Recensioner analys"}, "apra_chart__name": {"message": "Procentandel av produktförsäljning per land"}, "apra_chart__update_at": {"message": "Uppdateringstid $time$", "placeholders": {"time": {"content": "$1"}}}, "apra_name": {"message": "Försäljningsstatistik för länder"}, "auto_opening": {"message": "Öppnas automatiskt om $num$ sekunder", "placeholders": {"num": {"content": "$1"}}}, "auto_page_next_x_pages": {"message": "Nästa $autoPaging$ -sidor", "placeholders": {"autoPaging": {"content": "$1"}}}, "average_days_listed": {"message": "Genomsnittligt på hylldagar"}, "average_hui_fu_lv": {"message": "Genomsnittlig svarsfrekvens"}, "average_ping_gong_ying_shang_deng_ji": {"message": "Genomsnittlig leverantörsnivå"}, "average_price": {"message": "Genomsnittspris"}, "average_qi_ding_liang": {"message": "Genomsnittlig MOQ"}, "average_rating": {"message": "Genomsnittligt betyg"}, "average_ren_zheng_gong_ying_nian_chang": {"message": "Genomsnittliga år av certifiering"}, "average_revenue": {"message": "Genomsnittlig intäkt"}, "average_revenue_per_product": {"message": "Totala intäkter ÷ Antal produkter"}, "average_sales": {"message": "Genomsnittlig försäljning"}, "average_sales_per_product": {"message": "Total försäljning ÷ Antal produkter"}, "bao_han": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "bao_zheng_jin": {"message": "<PERSON><PERSON><PERSON>"}, "bian_ti_shu": {"message": "Variationer"}, "biao_ti": {"message": "Titel"}, "blacklist_add_blacklist": {"message": "Blockera den här butiken"}, "blacklist_address_incorrect": {"message": "Adressen är felaktig. Var vänlig kontrollera."}, "blacklist_blacked_out": {"message": "Butiken har blockerats"}, "blacklist_blacklist": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "blacklist_no_records_yet": {"message": "Ingen rekord ännu!"}, "blue_rocket": {"message": "로켓배송"}, "brand_name": {"message": "Stämpla"}, "btn_aliprice_agent__daigou": {"message": "Köpförmedlare"}, "btn_aliprice_agent__dropshipping": {"message": "Dropshipping"}, "btn_have_a_try": {"message": "Försök"}, "btn_refresh": {"message": "Uppdatera"}, "btn_try_it_now": {"message": "Prova det nu"}, "btn_txt_view_on_aliprice": {"message": "Visa på AliPrice"}, "bu_bao_han": {"message": "Innehåller inte"}, "bulk_copy_links": {"message": "Masskopiera länkar"}, "bulk_copy_products": {"message": "Masskopieringsprodukter"}, "cai_gou_zi_xun": {"message": "Kundservice"}, "cai_gou_zi_xun__desc": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON> svarsfrekvens på tre minuter"}, "can_ping_lei_xing": {"message": "<PERSON><PERSON>"}, "cao_zuo": {"message": "Drift"}, "chan_pin_ID": {"message": "Serienummer"}, "chan_pin_e_wai_xin_xi": {"message": "Produkt extra info"}, "chan_pin_lian_jie": {"message": "Produktlänk"}, "cheng_li_shi_jian": {"message": "Etableringstid"}, "city__aba": {"message": "Aba"}, "city__aksu": {"message": "<PERSON><PERSON><PERSON>"}, "city__alaer": {"message": "<PERSON><PERSON><PERSON>"}, "city__altai": {"message": "Altai"}, "city__alxaleague": {"message": "Alxa League"}, "city__ankang": {"message": "Ankan<PERSON>"}, "city__anqing": {"message": "Anqing"}, "city__anshan": {"message": "<PERSON><PERSON>"}, "city__anshun": {"message": "<PERSON><PERSON><PERSON>"}, "city__anyang": {"message": "Anyang"}, "city__baicheng": {"message": "Baicheng"}, "city__baise": {"message": "Baise"}, "city__baisha": {"message": "Baisha"}, "city__baishan": {"message": "Baishan"}, "city__baiyin": {"message": "<PERSON><PERSON><PERSON>"}, "city__baoding": {"message": "<PERSON>od<PERSON>"}, "city__baoji": {"message": "<PERSON><PERSON><PERSON>"}, "city__baoshan": {"message": "<PERSON><PERSON><PERSON>"}, "city__baoting": {"message": "Baoting"}, "city__baotou": {"message": "<PERSON><PERSON><PERSON>"}, "city__bayannur": {"message": "Bayannur"}, "city__bayinguoleng": {"message": "Bayinguoleng"}, "city__bazhong": {"message": "Bazhong"}, "city__beihai": {"message": "Beihai"}, "city__bengbu": {"message": "<PERSON><PERSON><PERSON>"}, "city__benxi": {"message": "Benxi"}, "city__bijie": {"message": "Bijie"}, "city__binzhou": {"message": "Binzhou"}, "city__bortala": {"message": "<PERSON><PERSON><PERSON>"}, "city__bozhou": {"message": "Bozhou"}, "city__cangzhou": {"message": "Cangzhou"}, "city__chamdo": {"message": "<PERSON><PERSON><PERSON>"}, "city__changchun": {"message": "<PERSON><PERSON><PERSON>"}, "city__changde": {"message": "<PERSON><PERSON>"}, "city__changhua": {"message": "Changhua"}, "city__changji": {"message": "Changji"}, "city__changjiang": {"message": "Changjiang"}, "city__changsha": {"message": "Changsha"}, "city__changzhi": {"message": "Changzhi"}, "city__changzhou": {"message": "Changzhou"}, "city__chaohu": {"message": "<PERSON><PERSON>"}, "city__chaoyang": {"message": "Chaoyang"}, "city__chaozhou": {"message": "Chaozhou"}, "city__chengde": {"message": "Chengde"}, "city__chengdu": {"message": "Chengdu"}, "city__chengmai": {"message": "<PERSON><PERSON><PERSON>"}, "city__chenzhou": {"message": "Chenzhou"}, "city__chiayi": {"message": "<PERSON><PERSON><PERSON>"}, "city__chifeng": {"message": "<PERSON><PERSON>"}, "city__chizhou": {"message": "Chizhou"}, "city__chongzuo": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__chuxiong": {"message": "Chuxiong"}, "city__chuzhou": {"message": "Chuzhou"}, "city__dali": {"message": "<PERSON><PERSON>"}, "city__dalian": {"message": "<PERSON><PERSON>"}, "city__dandong": {"message": "Dandong"}, "city__danzhou": {"message": "Danzhou"}, "city__daqing": {"message": "Daqing"}, "city__datong": {"message": "<PERSON><PERSON><PERSON>"}, "city__daxinganling": {"message": "<PERSON><PERSON>'<PERSON>ling"}, "city__dazhou": {"message": "Dazhou"}, "city__dehong": {"message": "<PERSON><PERSON>"}, "city__deyang": {"message": "Deyang"}, "city__dezhou": {"message": "Dezhou"}, "city__dingan": {"message": "Ding'an"}, "city__dingxi": {"message": "Dingxi"}, "city__diqing": {"message": "Diqing"}, "city__dongfang": {"message": "<PERSON><PERSON>g"}, "city__dongguan": {"message": "Dongguan"}, "city__dongying": {"message": "<PERSON><PERSON>"}, "city__enshi": {"message": "<PERSON><PERSON>"}, "city__ezhou": {"message": "Ezhou"}, "city__fangchenggang": {"message": "Fangchenggang"}, "city__foshan": {"message": "<PERSON><PERSON><PERSON>"}, "city__fushun": {"message": "<PERSON><PERSON><PERSON>"}, "city__fuxin": {"message": "Fuxin"}, "city__fuyang": {"message": "Fuyang"}, "city__fuzhou": {"message": "Fuzhou"}, "city__gannan": {"message": "<PERSON><PERSON><PERSON>"}, "city__ganzhou": {"message": "Ganzhou"}, "city__ganzi": {"message": "Ganzi"}, "city__guangan": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__guangyuan": {"message": "Guangyuan"}, "city__guangzhou": {"message": "Guangzhou"}, "city__guigang": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__guilin": {"message": "<PERSON><PERSON><PERSON>"}, "city__guiyang": {"message": "Guiyang"}, "city__guolok": {"message": "Guolok"}, "city__guyuan": {"message": "<PERSON><PERSON>"}, "city__haibei": {"message": "Haibei"}, "city__haidong": {"message": "Haidong"}, "city__haikou": {"message": "Hai<PERSON><PERSON>"}, "city__hainan": {"message": "Hainan"}, "city__haixi": {"message": "Haixi"}, "city__hami": {"message": "<PERSON><PERSON>"}, "city__handan": {"message": "<PERSON><PERSON>"}, "city__hangzhou": {"message": "Hangzhou"}, "city__hanzhong": {"message": "Hanzhong"}, "city__harbin": {"message": "<PERSON><PERSON><PERSON>"}, "city__hebi": {"message": "<PERSON><PERSON>"}, "city__hechi": {"message": "<PERSON><PERSON>"}, "city__hefei": {"message": "<PERSON><PERSON><PERSON>"}, "city__hegang": {"message": "<PERSON><PERSON><PERSON>"}, "city__heihe": {"message": "<PERSON><PERSON><PERSON>"}, "city__hengshui": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__hengyang": {"message": "Hengyang"}, "city__heyuan": {"message": "<PERSON><PERSON>"}, "city__heze": {"message": "<PERSON><PERSON>"}, "city__hezhou": {"message": "Hezhou"}, "city__hohhot": {"message": "Hohhot"}, "city__honghe": {"message": "<PERSON><PERSON>"}, "city__hongkongisland": {"message": "Hong Kong Island"}, "city__hotan": {"message": "<PERSON><PERSON>"}, "city__hsinchu": {"message": "<PERSON><PERSON><PERSON>"}, "city__huaian": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__huaibei": {"message": "<PERSON><PERSON><PERSON>"}, "city__huaihua": {"message": "Huaihua"}, "city__huaisouth": {"message": "Huai South"}, "city__hualien": {"message": "<PERSON><PERSON><PERSON>"}, "city__huanggang": {"message": "<PERSON><PERSON><PERSON>"}, "city__huangnan": {"message": "Huangnan"}, "city__huangshan": {"message": "<PERSON><PERSON>"}, "city__huangshi": {"message": "<PERSON><PERSON>"}, "city__huizhou": {"message": "Huizhou"}, "city__huludao": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__hulunbuir": {"message": "Hulunbuir"}, "city__huzhou": {"message": "Huzhou"}, "city__jiamusi": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__jian": {"message": "<PERSON><PERSON><PERSON>"}, "city__jiangmen": {"message": "Jiangmen"}, "city__jiaozuo": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__jiaxing": {"message": "Jiaxing"}, "city__jiayuguan": {"message": "Jiayuguan"}, "city__jieyang": {"message": "<PERSON><PERSON><PERSON>"}, "city__jilin": {"message": "<PERSON><PERSON>"}, "city__jinan": {"message": "<PERSON><PERSON>"}, "city__jinchang": {"message": "Jinchang"}, "city__jincheng": {"message": "Jincheng"}, "city__jingdezhen": {"message": "Jingdez<PERSON>"}, "city__jingmen": {"message": "Jingmen"}, "city__jingzhou": {"message": "Jingzhou"}, "city__jinhua": {"message": "Jinhua"}, "city__jining": {"message": "Jining"}, "city__jinzhong": {"message": "Jinzhong"}, "city__jinzhou": {"message": "Jinzhou"}, "city__jiujiang": {"message": "Jiujiang"}, "city__jiuquan": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__jixi": {"message": "Ji<PERSON>"}, "city__kaifeng": {"message": "Kaifeng"}, "city__kaohsiung": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__karamay": {"message": "<PERSON><PERSON><PERSON>"}, "city__kashgar": {"message": "Ka<PERSON><PERSON>"}, "city__keelung": {"message": "Keelung"}, "city__kinmen": {"message": "Kinmen"}, "city__kizilsu": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__kowloon": {"message": "Kowloon"}, "city__kunming": {"message": "Ku<PERSON><PERSON>"}, "city__laibin": {"message": "<PERSON><PERSON>"}, "city__laiwu": {"message": "<PERSON><PERSON>"}, "city__langfang": {"message": "<PERSON><PERSON><PERSON>"}, "city__lanzhou": {"message": "Lanzhou"}, "city__ledong": {"message": "<PERSON><PERSON>"}, "city__leshan": {"message": "<PERSON><PERSON>"}, "city__lhasa": {"message": "Lhasa"}, "city__liangshan": {"message": "Liangshan"}, "city__lianyungang": {"message": "Lianyungang"}, "city__liaocheng": {"message": "<PERSON><PERSON><PERSON>"}, "city__liaoyang": {"message": "<PERSON><PERSON><PERSON>"}, "city__liaoyuan": {"message": "<PERSON><PERSON><PERSON>"}, "city__lienchiang": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__lijiang": {"message": "Lijiang"}, "city__lincang": {"message": "Lincang"}, "city__linfen": {"message": "Linfen"}, "city__lingao": {"message": "Lingao"}, "city__lingshui": {"message": "<PERSON><PERSON><PERSON>"}, "city__linxia": {"message": "Lin<PERSON>"}, "city__linyi": {"message": "<PERSON><PERSON>"}, "city__lishui": {"message": "Li<PERSON><PERSON>"}, "city__liupanshui": {"message": "Liupanshu<PERSON>"}, "city__liuzhou": {"message": "Liuzhou"}, "city__longnan": {"message": "<PERSON><PERSON>"}, "city__longyan": {"message": "<PERSON><PERSON>"}, "city__loudi": {"message": "<PERSON><PERSON>"}, "city__luan": {"message": "<PERSON><PERSON><PERSON>"}, "city__luohe": {"message": "<PERSON><PERSON><PERSON>"}, "city__luoyang": {"message": "<PERSON><PERSON><PERSON>"}, "city__luzhou": {"message": "Luzhou"}, "city__lüliang": {"message": "<PERSON>眉liang"}, "city__maanshan": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__macauoutlyingislands": {"message": "Macau Outlying Islands"}, "city__macaupeninsula": {"message": "Macau Peninsula"}, "city__maoming": {"message": "<PERSON><PERSON>"}, "city__meishan": {"message": "<PERSON><PERSON>"}, "city__meizhou": {"message": "Meizhou"}, "city__mianyang": {"message": "Mianyang"}, "city__miaoli": {"message": "<PERSON><PERSON>"}, "city__mudanjiang": {"message": "Mudanjiang"}, "city__nagqu": {"message": "<PERSON>g<PERSON>u"}, "city__nanchang": {"message": "Nanchang"}, "city__nanchong": {"message": "<PERSON><PERSON><PERSON>"}, "city__nanjing": {"message": "Nanjing"}, "city__nanning": {"message": "Nanning"}, "city__nanping": {"message": "<PERSON><PERSON>"}, "city__nantong": {"message": "Nantong"}, "city__nantou": {"message": "<PERSON><PERSON><PERSON>"}, "city__nanyang": {"message": "Nanyang"}, "city__neijiang": {"message": "Neijiang"}, "city__newterritories": {"message": "New Territories"}, "city__ngari": {"message": "<PERSON><PERSON>"}, "city__ningbo": {"message": "Ningbo"}, "city__ningde": {"message": "<PERSON><PERSON><PERSON>"}, "city__nujiang": {"message": "Nujiang"}, "city__nyingchi": {"message": "Nyingchi"}, "city__ordos": {"message": "Ordos"}, "city__panjin": {"message": "Panjin"}, "city__panzhihua": {"message": "Pan Zhihua"}, "city__penghu": {"message": "<PERSON><PERSON><PERSON>"}, "city__pingdingshan": {"message": "Pingdingshan"}, "city__pingliang": {"message": "<PERSON><PERSON><PERSON>"}, "city__pingtung": {"message": "<PERSON><PERSON><PERSON>"}, "city__pingxiang": {"message": "<PERSON><PERSON><PERSON>"}, "city__puer": {"message": "<PERSON>u'er"}, "city__putian": {"message": "<PERSON><PERSON>"}, "city__puyang": {"message": "P<PERSON><PERSON>"}, "city__qiandongnan": {"message": "Qiandongnan"}, "city__qianjiang": {"message": "Qianjiang"}, "city__qiannan": {"message": "<PERSON><PERSON><PERSON>"}, "city__qianxinan": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__qingdao": {"message": "Qingdao"}, "city__qingyang": {"message": "Qingyang"}, "city__qingyuan": {"message": "Qingyuan"}, "city__qinhuangdao": {"message": "Qinhuangdao"}, "city__qinzhou": {"message": "Qinzhou"}, "city__qionghai": {"message": "Qionghai"}, "city__qiongzhong": {"message": "Qiongzhong"}, "city__qiqihar": {"message": "Qiqihar"}, "city__qitaihe": {"message": "<PERSON><PERSON><PERSON>"}, "city__quanzhou": {"message": "Quanzhou"}, "city__qujing": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__quzhou": {"message": "Quzhou"}, "city__rizhao": {"message": "<PERSON><PERSON><PERSON>"}, "city__sanmenxia": {"message": "Sanmenxia"}, "city__sanming": {"message": "Sanming"}, "city__sanya": {"message": "Sanya"}, "city__shangluo": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__shangqiu": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__shangrao": {"message": "<PERSON><PERSON><PERSON>"}, "city__shannan": {"message": "Shannan"}, "city__shantou": {"message": "<PERSON><PERSON><PERSON>"}, "city__shanwei": {"message": "Shanwei"}, "city__shaoguan": {"message": "Shaoguan"}, "city__shaoxing": {"message": "Shaoxing"}, "city__shaoyang": {"message": "Shaoyang"}, "city__shennongjiaforestarea": {"message": "Shennongjia Forest Area"}, "city__shenyang": {"message": "Shenyang"}, "city__shenzhen": {"message": "Shenzhen"}, "city__shigatse": {"message": "Shigat<PERSON>"}, "city__shihezi": {"message": "<PERSON><PERSON><PERSON>"}, "city__shijiazhuang": {"message": "Shijiazhuang"}, "city__shiyan": {"message": "<PERSON><PERSON>"}, "city__shizuishan": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__shuangyashan": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__shuozhou": {"message": "Shuozhou"}, "city__siping": {"message": "<PERSON><PERSON>"}, "city__songyuan": {"message": "Songyuan"}, "city__suihua": {"message": "Su<PERSON>ua"}, "city__suining": {"message": "Suining"}, "city__suizhou": {"message": "<PERSON><PERSON><PERSON>"}, "city__suqian": {"message": "<PERSON><PERSON><PERSON>"}, "city__suzhou": {"message": "Suzhou"}, "city__tacheng": {"message": "Tacheng"}, "city__taian": {"message": "Tai'an"}, "city__taichung": {"message": "Taichung"}, "city__tainan": {"message": "Tainan"}, "city__taipei": {"message": "Taipei"}, "city__taitung": {"message": "<PERSON><PERSON><PERSON>"}, "city__taiyuan": {"message": "Taiyuan"}, "city__taizhou": {"message": "Taizhou"}, "city__tangshan": {"message": "Tangshan"}, "city__taoyuan": {"message": "Taoyuan"}, "city__tianmen": {"message": "Tianmen"}, "city__tianshui": {"message": "<PERSON><PERSON><PERSON>"}, "city__tieling": {"message": "Tieling"}, "city__tongchuan": {"message": "<PERSON><PERSON><PERSON>"}, "city__tonghua": {"message": "Tonghua"}, "city__tongliao": {"message": "<PERSON><PERSON><PERSON>"}, "city__tongling": {"message": "Tongling"}, "city__tongren": {"message": "<PERSON><PERSON>"}, "city__tumushuke": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__tunchang": {"message": "Tunchang"}, "city__turpan": {"message": "<PERSON><PERSON><PERSON>"}, "city__ulanqab": {"message": "Ulanqab"}, "city__urumqi": {"message": "Urumqi"}, "city__wanning": {"message": "Wanning"}, "city__weifang": {"message": "<PERSON><PERSON><PERSON>"}, "city__weihai": {"message": "Weihai"}, "city__weinan": {"message": "Weinan"}, "city__wenchang": {"message": "Wenchang"}, "city__wenshan": {"message": "<PERSON><PERSON>"}, "city__wenzhou": {"message": "Wenzhou"}, "city__wuhai": {"message": "Wu<PERSON>"}, "city__wuhan": {"message": "<PERSON><PERSON>"}, "city__wuhu": {"message": "<PERSON><PERSON>"}, "city__wujiaqu": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__wuwei": {"message": "<PERSON><PERSON>"}, "city__wuxi": {"message": "Wuxi"}, "city__wuzhishan": {"message": "Wuzhishan"}, "city__wuzhong": {"message": "Wuzhong"}, "city__wuzhou": {"message": "Wuzhou"}, "city__xiamen": {"message": "Xiamen"}, "city__xian": {"message": "Xi'<PERSON>"}, "city__xiangfan": {"message": "<PERSON><PERSON><PERSON>"}, "city__xiangtan": {"message": "Xiangtan"}, "city__xiangxi": {"message": "Xiangxi"}, "city__xianning": {"message": "Xianning"}, "city__xiantao": {"message": "Xiantao"}, "city__xianyang": {"message": "<PERSON><PERSON><PERSON>"}, "city__xiaogan": {"message": "<PERSON><PERSON>"}, "city__xilingol": {"message": "Xilingol"}, "city__xinganleague": {"message": "Xing'an League"}, "city__xingtai": {"message": "Xingtai"}, "city__xining": {"message": "Xining"}, "city__xinxiang": {"message": "Xinxiang"}, "city__xinyang": {"message": "Xinyang"}, "city__xinyu": {"message": "Xinyu"}, "city__xinzhou": {"message": "Xinzhou"}, "city__xishuangbanna": {"message": "Xishuangbanna"}, "city__xuancheng": {"message": "Xuancheng"}, "city__xuchang": {"message": "Xuchang"}, "city__xuzhou": {"message": "Xuzhou"}, "city__yaan": {"message": "Ya'an"}, "city__yanan": {"message": "Yan'<PERSON>"}, "city__yanbian": {"message": "Yanbian"}, "city__yancheng": {"message": "Yancheng"}, "city__yangjiang": {"message": "Yangjiang"}, "city__yangquan": {"message": "<PERSON><PERSON><PERSON>"}, "city__yangzhou": {"message": "Yangzhou"}, "city__yantai": {"message": "Yantai"}, "city__yibin": {"message": "<PERSON><PERSON>"}, "city__yichang": {"message": "Yichang"}, "city__yichun": {"message": "<PERSON><PERSON><PERSON>"}, "city__yilan": {"message": "Yilan"}, "city__yili": {"message": "<PERSON><PERSON>"}, "city__yinchuan": {"message": "<PERSON><PERSON><PERSON>"}, "city__yingkou": {"message": "<PERSON><PERSON><PERSON>"}, "city__yingtan": {"message": "Yingtan"}, "city__yiwu": {"message": "<PERSON><PERSON>"}, "city__yiyang": {"message": "Yiyang"}, "city__yongzhou": {"message": "Yongzhou"}, "city__yueyang": {"message": "<PERSON><PERSON><PERSON>"}, "city__yulin": {"message": "Yulin"}, "city__yuncheng": {"message": "Yuncheng"}, "city__yunfu": {"message": "<PERSON><PERSON>"}, "city__yunlin": {"message": "<PERSON><PERSON>"}, "city__yushu": {"message": "Yushu"}, "city__yuxi": {"message": "Yuxi"}, "city__zaozhuang": {"message": "Zaozhuang"}, "city__zhangjiajie": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__zhangjiakou": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__zhangye": {"message": "<PERSON><PERSON>"}, "city__zhangzhou": {"message": "Zhangzhou"}, "city__zhanjiang": {"message": "Zhanjiang"}, "city__zhaoqing": {"message": "Zhaoqing"}, "city__zhaotong": {"message": "Zhaotong"}, "city__zhengzhou": {"message": "Zhengzhou"}, "city__zhenjiang": {"message": "Zhenjiang"}, "city__zhongshan": {"message": "Zhongshan"}, "city__zhongwei": {"message": "Zhongwei"}, "city__zhoukou": {"message": "<PERSON><PERSON><PERSON>"}, "city__zhoushan": {"message": "Zhoushan"}, "city__zhuhai": {"message": "Zhu<PERSON>"}, "city__zhumadian": {"message": "Zhumadian"}, "city__zhuzhou": {"message": "Zhuzhou"}, "city__zibo": {"message": "Zibo"}, "city__zigong": {"message": "Zigong"}, "city__ziyang": {"message": "<PERSON><PERSON><PERSON>"}, "city__zunyi": {"message": "<PERSON><PERSON><PERSON>"}, "click_copy_product_info": {"message": "Klicka på Kopiera produktinformation"}, "commmon_txt_expired": {"message": "Utgånget"}, "common__date_range_12m": {"message": "1 år"}, "common__date_range_1m": {"message": "1 månad"}, "common__date_range_1w": {"message": "1 vecka"}, "common__date_range_2w": {"message": "2 veckor"}, "common__date_range_3m": {"message": "3 månader"}, "common__date_range_3w": {"message": "3 veckor"}, "common__date_range_6m": {"message": "6 månader"}, "common_btn_cancel": {"message": "<PERSON><PERSON><PERSON>"}, "common_btn_close": {"message": "Stänga"}, "common_btn_save": {"message": "Spara"}, "common_btn_setting": {"message": "Uppstart"}, "common_email": {"message": "E-post"}, "common_error_msg_no_data": {"message": "Inga data"}, "common_error_msg_no_result": {"message": "<PERSON><PERSON><PERSON><PERSON>, inget resultat hittades."}, "common_favorites": {"message": "Favoriter"}, "common_feedback": {"message": "Respons"}, "common_help": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "common_loading": {"message": "<PERSON><PERSON><PERSON> in"}, "common_login": {"message": "Logga in"}, "common_logout": {"message": "Logga ut"}, "common_no": {"message": "<PERSON><PERSON>"}, "common_powered_by_aliprice": {"message": "Drivs av AliPrice.com"}, "common_setting": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "common_sign_up": {"message": "<PERSON><PERSON>"}, "common_system_upgrading_title": {"message": "Systemuppgradering"}, "common_system_upgrading_txt": {"message": "F<PERSON>rsök det senare"}, "common_txt__currency": {"message": "valuta"}, "common_txt__video_tutorial": {"message": "Video handledning"}, "common_txt_ago_time": {"message": "$time$ dagar sedan", "placeholders": {"time": {"content": "$1"}}}, "common_txt_all_product": {"message": "alla"}, "common_txt_analysis": {"message": "<PERSON><PERSON><PERSON>"}, "common_txt_basically_used": {"message": "Nästan aldrig använd"}, "common_txt_biaoti_link": {"message": "Titel+länk"}, "common_txt_biaoti_link_dian_pu": {"message": "Titel+länk+butiksnamn"}, "common_txt_blacklist": {"message": "Blockeringslista"}, "common_txt_cancel": {"message": "Avboka"}, "common_txt_category": {"message": "<PERSON><PERSON><PERSON>"}, "common_txt_chakan": {"message": "<PERSON><PERSON> upp"}, "common_txt_colors": {"message": "<PERSON><PERSON><PERSON>"}, "common_txt_confirm": {"message": "Bekräfta"}, "common_txt_copied": {"message": "<PERSON><PERSON><PERSON>"}, "common_txt_copy": {"message": "<PERSON><PERSON><PERSON>"}, "common_txt_copy_link": {"message": "<PERSON><PERSON><PERSON> länk"}, "common_txt_copy_title": {"message": "<PERSON><PERSON><PERSON> titel"}, "common_txt_copy_title__link": {"message": "<PERSON><PERSON>ra rubrik och länk"}, "common_txt_day": {"message": "himmel"}, "common_txt_day__short": {"message": "D"}, "common_txt_delete": {"message": "<PERSON><PERSON><PERSON>"}, "common_txt_dian_pu_link": {"message": "Kopiera butiksnamn + länk"}, "common_txt_download": {"message": "ladda ner"}, "common_txt_downloaded": {"message": "Ladda ner"}, "common_txt_export_as_csv": {"message": "Exportera Excel"}, "common_txt_export_as_txt": {"message": "Exportera text"}, "common_txt_fail": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "common_txt_format": {"message": "Formatera"}, "common_txt_get": {"message": "få"}, "common_txt_incert_selection": {"message": "Invertera urval"}, "common_txt_install": {"message": "Installera"}, "common_txt_load_failed": {"message": "Missly<PERSON><PERSON> att ladda"}, "common_txt_month": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "common_txt_more": {"message": "<PERSON><PERSON>"}, "common_txt_new_unused": {"message": "Helt ny, oanvänd"}, "common_txt_next": {"message": "<PERSON><PERSON><PERSON>"}, "common_txt_no_limit": {"message": "Obegränsat"}, "common_txt_no_noticeable": {"message": "Inga synliga repor eller smuts"}, "common_txt_on_sale": {"message": "Till<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "common_txt_opt_in_out": {"message": "På av"}, "common_txt_order": {"message": "Ordning"}, "common_txt_others": {"message": "<PERSON><PERSON>"}, "common_txt_overall_poor_condition": {"message": "Överlag dåligt skick"}, "common_txt_patterns": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "common_txt_platform": {"message": "Plattformar"}, "common_txt_please_select": {"message": "Vänligen välj"}, "common_txt_prev": {"message": "Föregående"}, "common_txt_price": {"message": "<PERSON><PERSON>"}, "common_txt_privacy_policy": {"message": "Integritetspolicy"}, "common_txt_product_condition": {"message": "Produktstatus"}, "common_txt_rating": {"message": "Betyg"}, "common_txt_ratings": {"message": "Betyg"}, "common_txt_reload": {"message": "Ladda om"}, "common_txt_reset": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "common_txt_review": {"message": "Recension"}, "common_txt_sale": {"message": "Till<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "common_txt_same": {"message": "<PERSON><PERSON>"}, "common_txt_scratches_and_dirt": {"message": "Med repor och smuts"}, "common_txt_search_title": {"message": "Sökningstitel"}, "common_txt_select_all": {"message": "<PERSON><PERSON><PERSON><PERSON> alla"}, "common_txt_selected": {"message": "vald"}, "common_txt_share": {"message": "Dela med sig"}, "common_txt_sold": {"message": "<PERSON><PERSON><PERSON>"}, "common_txt_sold_out": {"message": "sluts<PERSON><PERSON>"}, "common_txt_some_scratches": {"message": "Några repor och smuts"}, "common_txt_sort_by": {"message": "Sortera efter"}, "common_txt_state": {"message": "ange"}, "common_txt_success": {"message": "Framgång"}, "common_txt_sys_err": {"message": "systemfel"}, "common_txt_today": {"message": "I dag"}, "common_txt_total": {"message": "Allt"}, "common_txt_unselect_all": {"message": "Invertera urval"}, "common_txt_upload_image": {"message": "Ladda upp bild"}, "common_txt_visit": {"message": "Besök"}, "common_txt_whitelist": {"message": "Vitlista"}, "common_txt_wildberries": {"message": "Wildberries"}, "common_txt_year": {"message": "<PERSON><PERSON>"}, "common_yes": {"message": "<PERSON>a"}, "compare_tool_btn_clear_all": {"message": "<PERSON><PERSON> alla"}, "compare_tool_btn_compare": {"message": "Jämföra"}, "compare_tool_btn_contact": {"message": "Kontakt"}, "configure_notifiactions": {"message": "Kon<PERSON>gurera aviseringar"}, "contact_us": {"message": "Kontakta oss"}, "context_menu_screenshot_search": {"message": "Skärmdumpsökning för samma stil"}, "context_menus_aliprice_search_by_image": {"message": "Sök bild på AliPrice"}, "context_menus_goote_trans": {"message": "Översätt sida/Visa original"}, "context_menus_search_by_image": {"message": "Sök efter bild på $storeName$", "placeholders": {"storeName": {"content": "$1"}}}, "context_menus_search_by_image_capture": {"message": "Fånga till $storeName$", "placeholders": {"storeName": {"content": "$1"}}}, "context_menus_translator": {"message": "Fånga för att översätta"}, "converter_modal_amount_placeholder": {"message": "<PERSON><PERSON> be<PERSON> här"}, "converter_modal_btn_convert": {"message": "konvertera"}, "converter_modal_exchange_rate_source": {"message": "Uppgifterna kommer från $boc$ valutakurs Uppdateringstid: $updatedAt$", "placeholders": {"boc": {"content": "$1"}, "updatedAt": {"content": "$2"}}}, "converter_modal_name": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "converter_modal_search_placeholder": {"message": "sökvaluta"}, "copy_all_contact_us_notice": {"message": "<PERSON>na sida stöds inte för n<PERSON>, kontak<PERSON> oss"}, "copy_product_info": {"message": "Ko<PERSON>ra produktinformation"}, "copy_suggest_search_kw": {"message": "<PERSON><PERSON><PERSON>"}, "country__han_gou": {"message": "Sydkorea"}, "country__ri_ben": {"message": "Japan"}, "country__yue_nan": {"message": "Vietnam"}, "currency_convert__custom": {"message": "Anpassad växelkurs"}, "currency_convert__sync_server": {"message": "Synkroniseringsserver"}, "dang_ri_fa_huo": {"message": "<PERSON><PERSON><PERSON> samma dag"}, "dao_chu_quan_dian_shang_pin": {"message": "Exportera alla butiksprodukter"}, "dao_chu_wei_CSV": {"message": "Exportera"}, "dao_chu_zi_duan": {"message": "Exportera fält"}, "delivery_address": {"message": "Leveransadress"}, "delivery_company": {"message": "Leveransföretag"}, "di_zhi": {"message": "adress"}, "dian_ji_cha_xun": {"message": "<PERSON><PERSON><PERSON> för att fråga"}, "dian_pu_ID": {"message": "Butiks-ID"}, "dian_pu_di_zhi": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "dian_pu_lian_jie": {"message": "Butikslänk"}, "dian_pu_ming": {"message": "Affärsnamn"}, "dian_pu_ming_cheng": {"message": "Affärsnamn"}, "dian_pu_shang_pin_zong_hsu": {"message": "Totalt antal produkter i butiken: $num$", "placeholders": {"num": {"content": "$1"}}}, "dian_pu_xin_xi": {"message": "Butiksinformation"}, "ding_zai_zuo_ce": {"message": "spikad till vänster"}, "disable_old_version_tips_disable_btn_title": {"message": "Inaktivera gammal version"}, "download_image__SKU_variant_images": {"message": "SKU-variantbilder"}, "download_image__assume": {"message": "Till exempel har vi 2 bilder, produkt1.jpg och produkt2.gif.\nimg_{$no$} kommer att döpas om till img_01.jpg, img_02.gif;\n{$group$}_{$no$} kommer att döpas om till main_image_01.jpg, main_image_02.gif;", "placeholders": {"no": {"content": "$3"}, "group": {"content": "$2"}}}, "download_image__batch_download": {"message": "Batchnedladdning"}, "download_image__combined_image": {"message": "Kombinerad produktdetaljbild"}, "download_image__continue_downloading": {"message": "Fortsätt nedladdningen"}, "download_image__description_images": {"message": "Beskrivningsbilder"}, "download_image__download_combined_image": {"message": "Ladda ner kombinerad produktdetaljbild"}, "download_image__download_zip": {"message": "Ladda ner zip"}, "download_image__enlarge_check": {"message": "St<PERSON>der endast JPEG, JPG, GIF och PNG-bilder, maximal storlek för en enskild bild: 1600 * 1600"}, "download_image__enlarge_image": {"message": "Förstora bilden"}, "download_image__export": {"message": "Exportera länkar"}, "download_image__height": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "download_image__ignore_videos": {"message": "Videon har ignorerats eftersom den inte kan exporteras"}, "download_image__img_translate": {"message": "Översätt bild"}, "download_image__main_image": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "download_image__multi_folder": {"message": "F<PERSON><PERSON><PERSON>"}, "download_image__name": {"message": "ladda ner bild"}, "download_image__notice_content": {"message": "Vänligen kontrollera inte \"Fråga var du ska spara varje fil före nedladdning\" i din webbläsares nedladdningsinställningar!!! Annars kommer det att finnas många dialogrutor."}, "download_image__notice_ignore": {"message": "Frå<PERSON> inte efter det här meddelandet igen"}, "download_image__order_number": {"message": "{$no$} serienummer; {$group$} gruppnamn; {$date$} tidsstämpel", "placeholders": {"no": {"content": "$1"}, "group": {"content": "$2"}, "date": {"content": "$3"}}}, "download_image__overview": {"message": "Översikt"}, "download_image__prompt_download_zip": {"message": "Det finns för må<PERSON> bilder, du borde ladda ner dem som en zip-mapp."}, "download_image__rename": {"message": "Döpa om"}, "download_image__rule": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "download_image__single_folder": {"message": "Enskild mapp"}, "download_image__sku_image": {"message": "SKU-bilder"}, "download_image__video": {"message": "video-"}, "download_image__width": {"message": "Bredd"}, "download_reviews__download_images": {"message": "Ladda ner recensionsbild"}, "download_reviews__dropdown_title": {"message": "Ladda ner recensionsbild"}, "download_reviews__export_csv": {"message": "exportera CSV"}, "download_reviews__no_images": {"message": "0 bilder tillgängliga för nedladdning"}, "download_reviews__no_reviews": {"message": "Ingen recension att ladda ner!"}, "download_reviews__notice": {"message": "Dricks:"}, "download_reviews__notice__chrome_settings": {"message": "<PERSON><PERSON><PERSON> in webbläsaren Chrome för att fråga var varje fil ska sparas före nedladdning, st<PERSON><PERSON> in på \"Av\""}, "download_reviews__notice__wait": {"message": "Be<PERSON>ende på antalet recensioner kan väntetiden bli längre"}, "download_reviews__pages_list__all": {"message": "Allt"}, "download_reviews__pages_list__page": {"message": "Tidigare $page$-sidor", "placeholders": {"page": {"content": "$1"}}}, "download_reviews__pages_list_title": {"message": "Urvalsområde"}, "export_shopping_cart__csv_filed__details_url": {"message": "Produktlänk"}, "export_shopping_cart__csv_filed__details_url_with_sku": {"message": "Echo SKU-länk"}, "export_shopping_cart__csv_filed__images": {"message": "Bildlänk"}, "export_shopping_cart__csv_filed__quantity": {"message": "Kvantitet"}, "export_shopping_cart__csv_filed__sale_price": {"message": "<PERSON><PERSON>"}, "export_shopping_cart__csv_filed__specs": {"message": "Specifika<PERSON><PERSON>"}, "export_shopping_cart__csv_filed__store_name": {"message": "Affärsnamn"}, "export_shopping_cart__csv_filed__store_url": {"message": "Butikslänk"}, "export_shopping_cart__csv_filed__title": {"message": "Produktnamn"}, "export_shopping_cart__export_btn": {"message": "Exportera"}, "export_shopping_cart__export_empty": {"message": "Välj en produkt!"}, "fa_huo_shi_jian": {"message": "<PERSON><PERSON><PERSON>"}, "favorite_add_email": {"message": "<PERSON><PERSON><PERSON> till e-postadress"}, "favorite_add_favorites": {"message": "Lägg till i Favoriter"}, "favorite_added": {"message": "Tillagd"}, "favorite_btn_add": {"message": "<PERSON><PERSON><PERSON>."}, "favorite_btn_notify": {"message": "<PERSON><PERSON><PERSON><PERSON> pris"}, "favorite_cate_name_all": {"message": "<PERSON>a produkter"}, "favorite_current_price": {"message": "Aktuellt pris"}, "favorite_due_date": {"message": "Förfallodatum"}, "favorite_enable_notification": {"message": "Vänligen aktivera e-postaviseringar"}, "favorite_expired": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "favorite_go_to_enable": {"message": "Gå till aktivera"}, "favorite_msg_add_success": {"message": "Tillagd till favoriter"}, "favorite_msg_del_success": {"message": "Borttagen från favoriter"}, "favorite_msg_failure": {"message": "Misslyckas! Uppdatera sidan och försök igen."}, "favorite_please_add_email": {"message": "Vänligen lägg till en e-postadress"}, "favorite_price_drop": {"message": "<PERSON><PERSON>"}, "favorite_price_rise": {"message": "Upp"}, "favorite_price_untracked": {"message": "<PERSON><PERSON> ej s<PERSON>å<PERSON>"}, "favorite_saved_price": {"message": "<PERSON>rat pris"}, "favorite_stop_tracking": {"message": "<PERSON><PERSON><PERSON>"}, "favorite_sub_email_address": {"message": "E-postadress för prenumeration"}, "favorite_tracking_period": {"message": "Spårningsperiod"}, "favorite_tracking_prices": {"message": "<PERSON><PERSON><PERSON><PERSON> priser"}, "favorite_verify_email": {"message": "Verifiera e-postadress"}, "favorites_list_remove_prompt_msg": {"message": "Är du säker på att du raderar den?"}, "favorites_update_button": {"message": "Uppdatera priser nu"}, "fen_lei": {"message": "<PERSON><PERSON><PERSON>"}, "fen_xia_yan_xuan": {"message": "Distributörens val"}, "find_similar": {"message": "<PERSON><PERSON> l<PERSON>"}, "first_ali_price_date": {"message": "Datumet då det först fångades av AliPrice-sökroboten"}, "fooview_coupons_modal_no_data": {"message": "<PERSON><PERSON> kuponger"}, "fooview_coupons_modal_title": {"message": "<PERSON><PERSON><PERSON>"}, "fooview_favorites__product_sub__tooltip_l1": {"message": "Pris < $lowerPrice$", "placeholders": {"lowerPrice": {"content": "$1"}}}, "fooview_favorites__product_sub__tooltip_l2": {"message": "eller pris > $higherPrice$", "placeholders": {"higherPrice": {"content": "$1"}}}, "fooview_favorites__product_sub__tooltip_l3": {"message": "Deadline"}, "fooview_favorites_error_msg_no_favorites": {"message": "Lägg till favoritprodukter här för att få prisfallsvarning."}, "fooview_favorites_filter_latest": {"message": "Senast"}, "fooview_favorites_filter_price_drop": {"message": "p<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "fooview_favorites_filter_price_up": {"message": "pris<PERSON><PERSON><PERSON>"}, "fooview_favorites_modal_title": {"message": "Mina favoriter"}, "fooview_favorites_modal_title_title": {"message": "Gå till AliPrice <PERSON>"}, "fooview_favorites_track_price": {"message": "<PERSON><PERSON><PERSON> att spåra priset"}, "fooview_price_history_app_price": {"message": "APP-pris:"}, "fooview_price_history_title": {"message": "Prishistorik"}, "fooview_product_list_feedback": {"message": "Respons"}, "fooview_product_list_orders": {"message": "Order"}, "fooview_product_list_price": {"message": "<PERSON><PERSON>"}, "fooview_reviews_error_msg_no_review": {"message": "Vi hittade inga recensioner för den här produkten."}, "fooview_reviews_filter_buyer_reviews": {"message": "<PERSON><PERSON><PERSON><PERSON> foton"}, "fooview_reviews_modal_title": {"message": "Recensioner"}, "fooview_same_product_choose_category": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "fooview_same_product_filter_feedback": {"message": "Respons"}, "fooview_same_product_filter_orders": {"message": "Order"}, "fooview_same_product_filter_price": {"message": "<PERSON><PERSON>"}, "fooview_same_product_filter_rating": {"message": "Betyg"}, "fooview_same_product_modal_title": {"message": "<PERSON>ta samma produkt"}, "fooview_same_product_search_by_image": {"message": "<PERSON><PERSON><PERSON> efter bild"}, "fooview_seller_analysis_modal_title": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "for_12_months": {"message": "I ett år"}, "for_12_months_list_pro": {"message": "12 månader"}, "for_12_months_nei": {"message": "Inom 12 månader"}, "for_1_months": {"message": "1 månad"}, "for_1_months_nei": {"message": "Inom 1 månad"}, "for_3_months": {"message": "I tre månader"}, "for_3_months_nei": {"message": "Inom 3 månader"}, "for_6_months": {"message": "I 6 månader"}, "for_6_months_nei": {"message": "Inom 6 månader"}, "for_9_months": {"message": "9 månader"}, "for_9_months_nei": {"message": "Inom 9 månader"}, "fu_gou_lv": {"message": "Återköpskurs"}, "gao_liang_bu_tong_dian": {"message": "lyfta fram skillnader"}, "gao_liang_guang_gao_chan_pin": {"message": "<PERSON><PERSON>"}, "geng_duo_xin_xi": {"message": "Mer information"}, "geng_xin_shi_jian": {"message": "Uppdatera tid"}, "get_store_products_fail_tip": {"message": "Klicka på OK för att gå till verifiering för att säkerställa normal åtkomst"}, "gong_x_kuan_shang_pin": {"message": "Totalt $amount$ produkter", "placeholders": {"amount": {"content": "$1"}}}, "gong_ying_shang": {"message": "Leverantör"}, "gong_ying_shang_ID": {"message": "Leverantörs-ID"}, "gong_ying_shang_deng_ji": {"message": "Leverantörsbetyg"}, "gong_ying_shang_nian_zhan": {"message": "Leverantören är äldre"}, "gong_ying_shang_xin_xi": {"message": "leverantörsinformation"}, "gong_ying_shang_zhu_ye_lian_jie": {"message": "Länk till leverantörens hemsida"}, "green_rocket": {"message": "로켓프레시"}, "grey_rocket": {"message": "로켓설치"}, "gu_ji_shou_jia": {"message": "Beräknat försäljningspris"}, "guan_jian_zi": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "guang_gao_chan_pin": {"message": "<PERSON><PERSON> produkter"}, "guang_gao_zhan_bi": {"message": "<PERSON><PERSON>"}, "guo_ji_wu_liu_yun_fei": {"message": "Internationell fraktavgift"}, "guo_lv_tiao_jian": {"message": "Filter"}, "hao_ping_lv": {"message": "Positivt betyg"}, "highest_price": {"message": "<PERSON><PERSON><PERSON>"}, "historical_trend": {"message": "Historisk trend"}, "how_to_screenshot": {"message": "<PERSON><PERSON><PERSON> ned vänster musknapp för att välja området, tryck på höger musknapp eller Esc-tangenten för att avsluta skärmdumpen"}, "howt_it_works": {"message": "<PERSON><PERSON> det fungerar"}, "hui_fu_lv": {"message": "<PERSON><PERSON><PERSON>f<PERSON><PERSON><PERSON>"}, "hui_tou_lv": {"message": "avkastningsgrad"}, "inquire_freightFee": {"message": "Begäran om transport"}, "inquire_freightFee_Yuan": {"message": "Frakt/Yuan"}, "inquire_freightFee_province": {"message": "<PERSON><PERSON><PERSON>"}, "inquire_freightFee_the": {"message": "Fraktkostnaden är $num$, vilket innebär att fri frakt är tillgänglig i regionen.", "placeholders": {"num": {"content": "$1"}}}, "is_ad": {"message": "Ad."}, "jia_ge": {"message": "<PERSON><PERSON>"}, "jia_ge_dan_wei": {"message": "<PERSON><PERSON><PERSON>"}, "jia_ge_qu_shi": {"message": "Trend"}, "jia_zai_n_ge_shang_pin": {"message": "Ladda $num$ produkter", "placeholders": {"num": {"content": "$1"}}}, "jin_30_tian_xiao_liang_zhan_bi": {"message": "Procent av försäljningsvolymen under de senaste 30 dagarna"}, "jin_30_tian_xiao_shou_e_zhan_bi": {"message": "Procentandel av intäkterna under de senaste 30 dagarna"}, "jin_30d_xiao_liang": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "jin_30d_xiao_liang__desc": {"message": "Total försäljningsvolym för de senaste 30 dagarna"}, "jin_30d_xiao_shou_e": {"message": "Omsättning"}, "jin_30d_xiao_shou_e__desc": {"message": "Total omsättning de senaste 30 dagarna"}, "jin_90_tian_mai_jia_shu": {"message": "Köpare under de senaste 90 dagarna"}, "jin_90_tian_xiao_shou_liang": {"message": "F<PERSON><PERSON><PERSON><PERSON>jning under de senaste 90 dagarna"}, "jing_xuan_huo_yuan": {"message": "Valda k<PERSON>llor"}, "jing_ying_mo_shi": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "jing_ying_mo_shi__gong_chang": {"message": "Tillverkare"}, "jiu_fen_jie_jue": {"message": "Tvistlösning"}, "jiu_fen_jie_jue__desc": {"message": "Redovisning av säljarens butiksrättighetstvister"}, "jiu_fen_lv": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "jiu_fen_lv__desc": {"message": "<PERSON><PERSON>llningar med reklamationer som slutförts under de senaste 30 dagarna och som bedöms vara säljarens eller båda parters ansvar"}, "kai_dian_ri_qi": {"message": "Öppningsdatum"}, "keywords": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "kua_jin_Select_pan_huo": {"message": "Gränsöverskridande Välj"}, "last15_days": {"message": "Senaste 15 dagarna"}, "last180_days": {"message": "Senaste 180 dagarna"}, "last30_days": {"message": "Under de senaste 30 dagarna"}, "last360_days": {"message": "Senaste 360 ​​dagarna"}, "last45_days": {"message": "Senaste 45 dagarna"}, "last60_days": {"message": "Senaste 60 dagarna"}, "last7_days": {"message": "Senaste 7 dagarna"}, "last90_days": {"message": "Senaste 90 dagarna"}, "last_30d_sales": {"message": "Senaste 30 dagars försäljning"}, "lei_ji": {"message": "Kumulativ"}, "lei_ji_xiao_liang": {"message": "Total"}, "lei_ji_xiao_liang__desc": {"message": "All försäljning efter produkt på hylla"}, "lei_ji_xiao_liang_pai_xu__desc": {"message": "Kumulativ försäljningsvolym under de senaste 30 dagarna, sorterad från hög till låg"}, "lian_xi_fang_shi": {"message": "Kontaktinformation"}, "list_time": {"message": "Förvaringsdatum"}, "load_more": {"message": "Ladda mer"}, "login_to_aliprice": {"message": "<PERSON>gga in på AliPrice"}, "long_link": {"message": "<PERSON><PERSON><PERSON> länk"}, "lowest_price": {"message": "<PERSON><PERSON><PERSON>"}, "mai_jia_shu": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "mao_li_lv": {"message": "<PERSON><PERSON><PERSON>"}, "mobile_view__dkxbqy": {"message": "Öppna en ny flik"}, "mobile_view__sjdxq": {"message": "Detaljer i App"}, "mobile_view__sjdxqy": {"message": "Detaljsida i appen"}, "mobile_view__smck": {"message": "<PERSON><PERSON><PERSON> för att visa"}, "mobile_view__smckms": {"message": "<PERSON><PERSON><PERSON><PERSON> kameran eller appen för att skanna och visa"}, "modified_failed": {"message": "<PERSON><PERSON><PERSON> miss<PERSON><PERSON>"}, "modified_successfully": {"message": "<PERSON><PERSON><PERSON>"}, "nav_btn_favorites": {"message": "<PERSON> sam<PERSON>"}, "nav_btn_package": {"message": "<PERSON><PERSON>"}, "nav_btn_product_info": {"message": "Om produkten"}, "nav_btn_viewed": {"message": "Tittade"}, "no_suggest_search_kw": {"message": "Loading..."}, "none": {"message": "Ingen"}, "normal_link": {"message": "Normal länk"}, "notice": {"message": "<PERSON><PERSON><PERSON>"}, "number_reviews": {"message": "Recensioner"}, "only_show_num": {"message": "Totalt antal produkter: $allnum$, Dolda: $hidenum2$", "placeholders": {"allnum": {"content": "$1"}, "hidenum2": {"content": "$2"}}}, "only_show_selected": {"message": "Ta bort omarkerad"}, "open": {"message": "Öppna"}, "open_links": {"message": "Öppna länkar"}, "options_page_tab_check_links": {"message": "Kontrollera länkar"}, "options_page_tab_gernal": {"message": "Allmän"}, "options_page_tab_notifications": {"message": "Meddelanden"}, "options_page_tab_others": {"message": "<PERSON><PERSON>"}, "options_page_tab_sbi": {"message": "<PERSON><PERSON><PERSON> efter bild"}, "options_page_tab_shortcuts": {"message": "Genvä<PERSON>"}, "options_page_tab_shortcuts_title": {"message": "Teckenstorlek för genv<PERSON>gar"}, "options_page_tab_similar_products": {"message": "<PERSON><PERSON> produkter"}, "orange_rocket": {"message": "판매자로켓"}, "order_list_open_links_tip": {"message": "Flera produktlänkar är på väg att öppnas"}, "order_list_sku_show_title": {"message": "Visa valda varianter i de delade länkarna"}, "orders_last30_days": {"message": "<PERSON><PERSON> under de senaste 30 dagarna"}, "pTutorial_favorites_block1_desc1": {"message": "Produkterna som du spårade listas här"}, "pTutorial_favorites_block1_title": {"message": "Favoriter"}, "pTutorial_popup_block1_desc1": {"message": "En grön etikett innebär att det finns prisfallande produkter"}, "pTutorial_popup_block1_title": {"message": "<PERSON><PERSON><PERSON><PERSON> och favoriter"}, "pTutorial_price_history_block1_desc1": {"message": "<PERSON><PERSON><PERSON> p<PERSON> \"Sp<PERSON>ra pris\", lägg till produkter i Favoriter. När priserna sjunker får du aviseringar"}, "pTutorial_price_history_block1_title": {"message": "<PERSON><PERSON><PERSON><PERSON> pris"}, "pTutorial_reviews_block1_desc1": {"message": "Kundernas recensioner fr<PERSON><PERSON> och riktiga foton från feedback från AliExpress"}, "pTutorial_reviews_block1_title": {"message": "Recensioner"}, "pTutorial_reviews_block2_desc1": {"message": "Det är alltid bra att kontrollera recensioner fr<PERSON><PERSON> and<PERSON> k<PERSON>e"}, "pTutorial_same_products_block1_desc1": {"message": "Du kan jämföra dem för att göra det bästa valet"}, "pTutorial_same_products_block1_desc2": {"message": "<PERSON><PERSON><PERSON> <PERSON><PERSON> \"<PERSON><PERSON>\" för att \"<PERSON>ök efter bild\""}, "pTutorial_same_products_block1_title": {"message": "<PERSON><PERSON> produkter"}, "pTutorial_same_products_by_image_search_block1_desc1": {"message": "Släpp produktbilden där och välj en kategori"}, "pTutorial_same_products_by_image_search_block1_title": {"message": "<PERSON><PERSON><PERSON> efter bild"}, "pTutorial_seller_analysis_block1_desc1": {"message": "Säljarens positiva återkopplingsfrekvens, feedbackpoäng och hur länge säljaren har varit på marknaden"}, "pTutorial_seller_analysis_block1_title": {"message": "Säljarbetyg"}, "pTutorial_seller_analysis_block2_desc2": {"message": "Säljarbetyg baseras på 3 index: artikel som beskrivs, kommunikation Leveranshastighet"}, "pTutorial_seller_analysis_block3_desc3": {"message": "Vi använder tre färger och ikoner för att indikera säljarens förtroende"}, "page_count": {"message": "<PERSON>tal sidor"}, "pai_chu": {"message": "Utesluten"}, "pai_chu_HK_bu_yi_xia_xiaoshou": {"message": "<PERSON><PERSON><PERSON> Hong Kong-Restricted"}, "pai_chu_JP_bu_yi_xia_xiaoshou": {"message": "Uteslut Japan-begränsad"}, "pai_chu_KR_bu_yi_xia_xiaoshou": {"message": "Uteslut Korea-Restricted"}, "pai_chu_KZ_bu_yi_xia_xiaoshou": {"message": "<PERSON><PERSON><PERSON>-Restricted"}, "pai_chu_MO_bu_yi_xia_xiaoshou": {"message": "Uteslut Macau-begränsad"}, "pai_chu_RU_bu_yi_xia_xiaoshou": {"message": "Uteslut Östeuropa-begränsad"}, "pai_chu_SA_bu_yi_xia_xiaoshou": {"message": "<PERSON><PERSON><PERSON>-Restricted"}, "pai_chu_TW_bu_yi_xia_xiaoshou": {"message": "U<PERSON>lut Taiwan-Restricted"}, "pai_chu_USA_bu_yi_xia_xiaoshou": {"message": "Uteslut USA-begränsad"}, "pai_chu_VN_bu_yi_xia_xiaoshou": {"message": "Uteslut Vietnam-begränsat"}, "pai_chu_bu_yi_xia_xiaoshou": {"message": "Exkludera begränsade artiklar"}, "payable_price_formula": {"message": "Pris + Frakt + <PERSON><PERSON><PERSON>"}, "pdd_check_retail_btn_txt": {"message": "<PERSON><PERSON>"}, "pdd_pifa_to_retail_btn_txt": {"message": "Köp i detaljhandeln"}, "pdp_copy_fail": {"message": "<PERSON><PERSON><PERSON>en misslyckades!"}, "pdp_copy_success": {"message": "Kopieringen lyckades!"}, "pdp_share_modal_subtitle": {"message": "<PERSON><PERSON> sk<PERSON>, han/hon kommer att se ditt val."}, "pdp_share_modal_title": {"message": "Dela ditt val"}, "pdp_share_screenshot": {"message": "Dela skärmdump"}, "pei_song": {"message": "<PERSON><PERSON><PERSON>"}, "pin_lei": {"message": "<PERSON><PERSON><PERSON>"}, "pin_zhi_ti_yan": {"message": "Produktkvalité"}, "pin_zhi_ti_yan__desc": {"message": "Kvalitetsåterbetalningssats för säljarens butik"}, "pin_zhi_tui_kuan_lv": {"message": "Återbetalningssats"}, "pin_zhi_tui_kuan_lv__desc": {"message": "<PERSON><PERSON> beställningar som endast har återbetalats och returnerats under de senaste 30 dagarna"}, "ping_fen": {"message": "Betyg"}, "ping_jun_fa_huo_su_du": {"message": "Genomsnittlig frakthastighet"}, "pkgInfo_hide": {"message": "Logistikinfo: på/av"}, "pkgInfo_no_trace": {"message": "Ingen logistikinformation"}, "platform_name__1688": {"message": "1688"}, "platform_name__17zwd": {"message": "17zwd.com"}, "platform_name__51taoyang": {"message": "51taoyang.com"}, "platform_name__5ts": {"message": "5ts.com"}, "platform_name__91jf": {"message": "91jf.com"}, "platform_name__alibaba": {"message": "Alibaba.com"}, "platform_name__aliexpress": {"message": "AliExpress"}, "platform_name__amazon": {"message": "Amazon"}, "platform_name__bao66": {"message": "bao66.cn"}, "platform_name__chinagoods": {"message": "chinagoods.com"}, "platform_name__dhgate": {"message": "DHgate"}, "platform_name__e3hui": {"message": "e3hui.com"}, "platform_name__ebay": {"message": "Ebay"}, "platform_name__hznzcn": {"message": "hznzcn.com"}, "platform_name__jd": {"message": "JD"}, "platform_name__juyi5": {"message": "juyi5.cn"}, "platform_name__naver_shopping": {"message": "Naver Shopping"}, "platform_name__pinduoduo": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "platform_name__pinduoduo_pifa": {"message": "<PERSON>nd<PERSON><PERSON>o grossist"}, "platform_name__shopee": {"message": "<PERSON>ee"}, "platform_name__sjxzw": {"message": "571xz.com"}, "platform_name__sooxie": {"message": "sooxie.com"}, "platform_name__taobao": {"message": "Taobao"}, "platform_name__taobao_lite": {"message": "Taobao Lite"}, "platform_name__vvic": {"message": "vvic.com"}, "platform_name__walmart": {"message": "Walmart"}, "platform_name__wsy": {"message": "wsy.com"}, "platform_name__xingfujie": {"message": "xingfujie.cn"}, "platform_name__yiwugo": {"message": "Yiwugo.com"}, "platform_name__yunchepin": {"message": "yunchepin.cn"}, "platform_name__zhaojiafang": {"message": "zhaojiafang.com"}, "popup_go_to_home": {"message": "<PERSON><PERSON>"}, "popup_go_to_platform": {"message": "Gå till $name$", "placeholders": {"name": {"content": "$1"}}}, "popup_search_placeholder": {"message": "Jag handlar efter ..."}, "popup_track_package_btn_track": {"message": "SPÅR"}, "popup_track_package_desc": {"message": "ALL-IN-ONE PACKAGE TRACKING"}, "popup_track_package_search_placeholder": {"message": "Spårningsnummer"}, "popup_translate_search_placeholder": {"message": "Översätt och sök på $searchOn$", "placeholders": {"searchOn": {"content": "$1"}}}, "price_history": {"message": "Prishistorik"}, "price_history_chart_tip_ae": {"message": "Tips: <PERSON><PERSON>t beställningar är det ackumulerade antalet beställningar sedan lanseringen"}, "price_history_chart_tip_coupang": {"message": "Tips: Coupang tar bort orderantalet för <PERSON><PERSON><PERSON><PERSON><PERSON> beställningar"}, "price_history_inm_1688_l1": {"message": "Vänligen installera"}, "price_history_inm_1688_l2": {"message": "AliPrice Shopping Assistant fö<PERSON> 1688"}, "price_history_panel_lowest_price": {"message": "Lägsta pris:"}, "price_history_panel_tab_price_tracking": {"message": "Prishistorik"}, "price_history_panel_tab_seller_analysis": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "price_history_pro_modal_title": {"message": "Prishistorik & Orderhistorik"}, "privacy_consent__btn_agree": {"message": "Återvänd samtycke till datainsamling"}, "privacy_consent__btn_disable_all": {"message": "Inte acceptera"}, "privacy_consent__btn_enable_all": {"message": "Aktivera alla"}, "privacy_consent__btn_uninstall": {"message": "<PERSON> bort"}, "privacy_consent__desc_privacy": {"message": "Observera att utan data eller cookies kommer vissa funktioner att vara avstängda eftersom dessa funktioner behöver förklaras av data eller cookies, men du kan fortfarande använda de andra funktionerna."}, "privacy_consent__desc_privacy_L1": {"message": "<PERSON><PERSON><PERSON><PERSON>, utan data eller kakor fungerar det inte eftersom vi behöver förklara data eller kakor."}, "privacy_consent__desc_privacy_L2": {"message": "Om du inte till<PERSON>ter oss att samla in denna information, ta bort den."}, "privacy_consent__item_cookies_desc": {"message": "<PERSON><PERSON>, vi får bara din valutadata i cookies när vi handlar online för att visa prishistoriken."}, "privacy_consent__item_cookies_title": {"message": "Obligatoriska kakor"}, "privacy_consent__item_functional_desc_L1": {"message": "1. <PERSON><PERSON><PERSON> till cookies i webbläsaren för att anonymt identifiera din dator eller enhet."}, "privacy_consent__item_functional_desc_L2": {"message": "2. <PERSON>ägg till funktionell data i tillägget för att arbeta med funktionen."}, "privacy_consent__item_functional_title": {"message": "Funktionella och Analytics-kakor"}, "privacy_consent__more_desc": {"message": "Observera att vi inte delar dina personuppgifter med andra företag och inga annonsföretag samlar in data via vår tjänst."}, "privacy_consent__options__btn__desc": {"message": "<PERSON><PERSON><PERSON> att kunna använda alla funktioner måste du aktivera den."}, "privacy_consent__options__btn__label": {"message": "Sätt på den"}, "privacy_consent__options__desc_L1": {"message": "<PERSON><PERSON> samlar in följande uppgifter som personligen identifierar dig:"}, "privacy_consent__options__desc_L2": {"message": "- cookies, vi får bara din valutadata i cookies när du handlar online för att visa prishistoriken."}, "privacy_consent__options__desc_L3": {"message": "- och lägg till cookies i webbläsaren för att anonymt identifiera din dator eller enhet."}, "privacy_consent__options__desc_L4": {"message": "- andra anonyma uppgifter gör denna förlängning bekvämare."}, "privacy_consent__options__desc_L5": {"message": "Observera att vi inte delar dina personuppgifter med andra företag och inga annonsföretag samlar in data via vår tjänst."}, "privacy_consent__privacy_preferences": {"message": "Sekretesspreferenser"}, "privacy_consent__read_more": {"message": "<PERSON><PERSON><PERSON> >>"}, "privacy_consent__title_privacy": {"message": "Integritet"}, "product_info": {"message": "Produktinfo"}, "product_recommend__name": {"message": "<PERSON><PERSON> produkter"}, "product_research": {"message": "Produktforskning"}, "product_sub__email_desc": {"message": "Prisavisering via e-post"}, "product_sub__email_edit": {"message": "redigera"}, "product_sub__email_not_verified": {"message": "Vänligen verifiera e-post"}, "product_sub__email_required": {"message": "Vänligen ange e-post"}, "product_sub__form_countdown": {"message": "Automatisk stängning efter $seconds$ sekunder", "placeholders": {"seconds": {"content": "$1"}}}, "product_sub__form_fail": {"message": "<PERSON><PERSON>gg till påminnelse misslyckades!"}, "product_sub__form_input_price": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "product_sub__form_item_country": {"message": "nation"}, "product_sub__form_item_current_price": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON> p<PERSON>"}, "product_sub__form_item_duration": {"message": "<PERSON><PERSON><PERSON><PERSON>ö<PERSON>"}, "product_sub__form_item_higher_price": {"message": "eller pris >"}, "product_sub__form_item_invalid_higher_price": {"message": "Priset måste vara högre än $price$", "placeholders": {"price": {"content": "$1"}}}, "product_sub__form_item_invalid_lower_price": {"message": "Priset måste vara lägre än $price$", "placeholders": {"price": {"content": "$1"}}}, "product_sub__form_item_lower_price": {"message": "<PERSON><PERSON><PERSON> priset <"}, "product_sub__form_submit": {"message": "<PERSON><PERSON><PERSON> in"}, "product_sub__form_success": {"message": "<PERSON><PERSON>gg till påminnelse framgång!"}, "product_sub__high_price_notify": {"message": "<PERSON><PERSON><PERSON> mig om p<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "product_sub__low_price_notify": {"message": "<PERSON><PERSON><PERSON> mig om prissänkningar"}, "product_sub__modal_title": {"message": "Påminnelse om prisändring av abonnemang"}, "province__an_hui": {"message": "<PERSON><PERSON>"}, "province__ao_men": {"message": "Macau"}, "province__bei_jing": {"message": "Beijing"}, "province__chong_qing": {"message": "Chongqing"}, "province__fu_jian": {"message": "Fujian"}, "province__gan_su": {"message": "Gansu"}, "province__guang_dong": {"message": "Guangdong"}, "province__guang_xi": {"message": "Guangxi"}, "province__gui_zhou": {"message": "Guizhou"}, "province__hai_nan": {"message": "Hainan"}, "province__he_bei": {"message": "Hebei"}, "province__he_nan": {"message": "<PERSON><PERSON>"}, "province__hei_long_jiang": {"message": "Heilongjiang"}, "province__hu_bei": {"message": "Hubei"}, "province__hu_nan": {"message": "Hunan"}, "province__ji_lin": {"message": "<PERSON><PERSON>"}, "province__jiang_su": {"message": "Jiangsu"}, "province__jiang_xi": {"message": "Jiangxi"}, "province__liao_ning": {"message": "Liaoning"}, "province__nei_meng_gu": {"message": "Inner Mongolia"}, "province__ning_xia": {"message": "Ningxia"}, "province__qing_hai": {"message": "Qinghai"}, "province__shan_dong": {"message": "Shandong"}, "province__shan_xi": {"message": "Shaanxi"}, "province__shang_hai": {"message": "Shanghai"}, "province__si_chuan": {"message": "Sichuan"}, "province__tai_wan": {"message": "Taiwan"}, "province__tian_jin": {"message": "Tianjin"}, "province__xi_zhang": {"message": "Tibet"}, "province__xiang_gang": {"message": "Hong Kong"}, "province__xin_jiang": {"message": "Xinjiang"}, "province__yun_nan": {"message": "Yunnan"}, "province__zhe_jiang": {"message": "Zhejiang"}, "purple_rocket": {"message": "로켓직구"}, "qi_ding_liang": {"message": "MOQ"}, "qi_ding_liang_qi_ding_jia": {"message": "MOQ & MOP"}, "qi_ye_mian_ji": {"message": "Företagsområde"}, "qing_zhi_shao_xuan_ze_yi_ge_chan_pin": {"message": "<PERSON><PERSON><PERSON><PERSON> minst en produkt"}, "qing_zhi_shao_xuan_ze_yi_ge_zi_duan": {"message": "<PERSON><PERSON><PERSON><PERSON> minst ett fält"}, "qu_deng_lu": {"message": "Logga in"}, "quan_guo_yan_xuan": {"message": "Globalt val"}, "recommendation_popup_banner_btn_install": {"message": "Installera den"}, "recommendation_popup_banner_desc": {"message": "Visa prishistorik inom 3/6 månader och meddelande om prisfall"}, "region__all": {"message": "Alla regioner"}, "region__hai_wai": {"message": "Overseas"}, "region__hua_bei_qu": {"message": "North China"}, "region__hua_dong_qu": {"message": "East China"}, "region__hua_nan_qu": {"message": "South China"}, "region__hua_zhong_qu": {"message": "Central China"}, "region__jiang_zhe_lu": {"message": "Jiangsu, Zhejiang and Shanghai"}, "remove_web_limits": {"message": "Aktivera högerklick"}, "ren_zheng_gong_chang": {"message": "Certifierad fabrik"}, "ren_zheng_gong_ying_shang_nian_zhan": {"message": "<PERSON>r som certifierad leverantör"}, "required_to_aliprice_login": {"message": "Behöver logga in på AliPrice"}, "revenue_last30_days": {"message": "Försäljningsbelopp under de senaste 30 dagarna"}, "review_counts": {"message": "<PERSON><PERSON> sa<PERSON>e"}, "rocket": {"message": "로켓"}, "ru_zhu_nian_xian": {"message": "Anmälningsperiod"}, "sales_amount_last30_days": {"message": "Total försäljning under de senaste 30 dagarna"}, "sales_last30_days": {"message": "F<PERSON><PERSON><PERSON><PERSON>jning under de senaste 30 dagarna"}, "sbi_alibaba_cate__accessories": {"message": "Tillbehör"}, "sbi_alibaba_cate__aqfk": {"message": "säkerhet"}, "sbi_alibaba_cate__bags_cases": {"message": "Väskor och fodral"}, "sbi_alibaba_cate__beauty": {"message": "Skönhet"}, "sbi_alibaba_cate__beverage": {"message": "<PERSON><PERSON>"}, "sbi_alibaba_cate__bgwh": {"message": "Kontorskultur"}, "sbi_alibaba_cate__bz": {"message": "<PERSON><PERSON>"}, "sbi_alibaba_cate__ccyj": {"message": "Köksutrustning"}, "sbi_alibaba_cate__clothes": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "sbi_alibaba_cate__cmgd": {"message": "Media Broadcasting"}, "sbi_alibaba_cate__coat_jacket": {"message": "Kappa"}, "sbi_alibaba_cate__consumer_electronics": {"message": "Hemelektronik"}, "sbi_alibaba_cate__cryp": {"message": "<PERSON><PERSON><PERSON> produkter"}, "sbi_alibaba_cate__csyp": {"message": "Sängfoder"}, "sbi_alibaba_cate__cwyy": {"message": "Trädgårdsskötsel för husd<PERSON>r"}, "sbi_alibaba_cate__cysx": {"message": "<PERSON><PERSON> fräsch"}, "sbi_alibaba_cate__dgdq": {"message": "Elektriker"}, "sbi_alibaba_cate__dl": {"message": "<PERSON>erkan<PERSON>"}, "sbi_alibaba_cate__dress_suits": {"message": "Klänning & kostymer"}, "sbi_alibaba_cate__dszm": {"message": "Belysning"}, "sbi_alibaba_cate__dzqj": {"message": "Elektronisk anordning"}, "sbi_alibaba_cate__essb": {"message": "Begagnad utrustning"}, "sbi_alibaba_cate__food": {"message": "Mat"}, "sbi_alibaba_cate__fspj": {"message": "Kläder och Accessoarer"}, "sbi_alibaba_cate__furniture": {"message": "<PERSON><PERSON><PERSON>"}, "sbi_alibaba_cate__fzpg": {"message": "Textilläder"}, "sbi_alibaba_cate__ghjq": {"message": "Personlig vård"}, "sbi_alibaba_cate__gt": {"message": "<PERSON><PERSON><PERSON>"}, "sbi_alibaba_cate__gyp": {"message": "Hantverk"}, "sbi_alibaba_cate__hb": {"message": "Miljövä<PERSON>lig"}, "sbi_alibaba_cate__hfcz": {"message": "Hudvårdssmink"}, "sbi_alibaba_cate__hg": {"message": "Kemisk industri"}, "sbi_alibaba_cate__jg": {"message": "Bearbetning"}, "sbi_alibaba_cate__jianccai": {"message": "Byggmaterial"}, "sbi_alibaba_cate__jichuang": {"message": "Maskinverktyg"}, "sbi_alibaba_cate__jjry": {"message": "Hushållens dagliga användning"}, "sbi_alibaba_cate__jtys": {"message": "Transport"}, "sbi_alibaba_cate__jxsb": {"message": "Utrustning"}, "sbi_alibaba_cate__jxwj": {"message": "Mekanisk h<PERSON>rdvara"}, "sbi_alibaba_cate__jydq": {"message": "Hushållsprodukter"}, "sbi_alibaba_cate__jzjc": {"message": "Byggmaterial för hemförb<PERSON>"}, "sbi_alibaba_cate__jzjf": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "sbi_alibaba_cate__mj": {"message": "Handduk"}, "sbi_alibaba_cate__myyp": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "sbi_alibaba_cate__nanz": {"message": "<PERSON>"}, "sbi_alibaba_cate__nvz": {"message": "Damkläder"}, "sbi_alibaba_cate__ny": {"message": "Energi"}, "sbi_alibaba_cate__others": {"message": "<PERSON><PERSON>"}, "sbi_alibaba_cate__qcyp": {"message": "Biltillbehör"}, "sbi_alibaba_cate__qmpj": {"message": "Bildelar"}, "sbi_alibaba_cate__shoes": {"message": "Skor"}, "sbi_alibaba_cate__smdn": {"message": "Digital dator"}, "sbi_alibaba_cate__snqj": {"message": "Förvaring och städning"}, "sbi_alibaba_cate__spjs": {"message": "<PERSON>"}, "sbi_alibaba_cate__swfw": {"message": "Företagstjänster"}, "sbi_alibaba_cate__toys_hobbies": {"message": "<PERSON><PERSON><PERSON>"}, "sbi_alibaba_cate__trousers_skirt": {"message": "Byxor & kjol"}, "sbi_alibaba_cate__txcp": {"message": "Kommunikationsprodukter"}, "sbi_alibaba_cate__tz": {"message": "Barnkläder"}, "sbi_alibaba_cate__underwear": {"message": "Underkläder"}, "sbi_alibaba_cate__wjgj": {"message": "Hårdvaruverktyg"}, "sbi_alibaba_cate__xgpi": {"message": "Läder väskor"}, "sbi_alibaba_cate__xmhz": {"message": "projektsamarbete"}, "sbi_alibaba_cate__xs": {"message": "<PERSON><PERSON>"}, "sbi_alibaba_cate__ydfs": {"message": "Sportkläder"}, "sbi_alibaba_cate__ydhw": {"message": "Utomhussport"}, "sbi_alibaba_cate__yjkc": {"message": "Metallurgiska mineraler"}, "sbi_alibaba_cate__yqyb": {"message": "Instrumentation"}, "sbi_alibaba_cate__ys": {"message": "Skriva ut"}, "sbi_alibaba_cate__yyby": {"message": "Sjuk<PERSON><PERSON>rd"}, "sbi_alibaba_cn_kj_90mjs": {"message": "<PERSON><PERSON> k<PERSON> de senaste 90 dagarna"}, "sbi_alibaba_cn_kj_90xsl": {"message": "Försäljningsvolym under de senaste 90 dagarna"}, "sbi_alibaba_cn_kj_gjsj": {"message": "Beräknat pris"}, "sbi_alibaba_cn_kj_gjwlyf": {"message": "Internationell fraktavgift"}, "sbi_alibaba_cn_kj_gjyf": {"message": "Fraktavgift"}, "sbi_alibaba_cn_kj_gssj": {"message": "Beräknat pris"}, "sbi_alibaba_cn_kj_lr": {"message": "<PERSON><PERSON>"}, "sbi_alibaba_cn_kj_lrgs": {"message": "Vinst = uppskattat pris x vinstmarginal"}, "sbi_alibaba_cn_kj_pjfhsd": {"message": "Genomsnittlig leveranshastighet"}, "sbi_alibaba_cn_kj_qtfy": {"message": "annan avgift"}, "sbi_alibaba_cn_kj_qtfygs": {"message": "<PERSON><PERSON>rig kostnad = uppskattat pris x övriga kostnadsförhållande"}, "sbi_alibaba_cn_kj_spjg": {"message": "<PERSON><PERSON>"}, "sbi_alibaba_cn_kj_spzl": {"message": "<PERSON><PERSON><PERSON>"}, "sbi_alibaba_cn_kj_szd": {"message": "Plats"}, "sbi_alibaba_cn_kj_unit_ge": {"message": "Bitar"}, "sbi_alibaba_cn_kj_unit_jian": {"message": "Bitar"}, "sbi_alibaba_cn_kj_unit_ke": {"message": "Gram"}, "sbi_alibaba_cn_kj_unit_ren": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "sbi_alibaba_cn_kj_unit_tai": {"message": "Bitar"}, "sbi_alibaba_cn_kj_unit_tao": {"message": "Uppsättningar"}, "sbi_alibaba_cn_kj_unit_tian": {"message": "Dagar"}, "sbi_alibaba_cn_kj_zwbj": {"message": "Inget pris"}, "sbi_aliprice_alibaba_cn__jiage": {"message": "pris"}, "sbi_aliprice_alibaba_cn__keshou": {"message": "Till salu"}, "sbi_aliprice_alibaba_cn__moren": {"message": "standard"}, "sbi_aliprice_alibaba_cn__queding": {"message": "<PERSON><PERSON><PERSON>"}, "sbi_aliprice_alibaba_cn__xiaoliang": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "sbi_aliprice_alibaba_cn_cate__jiaju": {"message": "<PERSON><PERSON><PERSON>"}, "sbi_aliprice_alibaba_cn_cate__linghsi": {"message": "me<PERSON><PERSON><PERSON><PERSON>"}, "sbi_aliprice_alibaba_cn_cate__meizhuang": {"message": "smink"}, "sbi_aliprice_alibaba_cn_cate__neiyi": {"message": "Underkläder"}, "sbi_aliprice_alibaba_cn_cate__peishi": {"message": "Tillbehör"}, "sbi_aliprice_alibaba_cn_cate__pinchui": {"message": "Flaska dryck"}, "sbi_aliprice_alibaba_cn_cate__qita": {"message": "<PERSON><PERSON>"}, "sbi_aliprice_alibaba_cn_cate__qunzhuang": {"message": "<PERSON><PERSON><PERSON>"}, "sbi_aliprice_alibaba_cn_cate__shangyi": {"message": "<PERSON><PERSON>"}, "sbi_aliprice_alibaba_cn_cate__shuma": {"message": "Elektronik"}, "sbi_aliprice_alibaba_cn_cate__wanju": {"message": "<PERSON><PERSON><PERSON>"}, "sbi_aliprice_alibaba_cn_cate__xiangbao": {"message": "Bagage"}, "sbi_aliprice_alibaba_cn_cate__xiazhuang": {"message": "<PERSON><PERSON><PERSON>"}, "sbi_aliprice_alibaba_cn_cate__xiezi": {"message": "sko"}, "sbi_aliprice_cate__apparel": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "sbi_aliprice_cate__automobiles_motorcycles": {"message": "Bilar och motorcyklar"}, "sbi_aliprice_cate__beauty_health": {"message": "Skönhet"}, "sbi_aliprice_cate__cellphones_telecommunications": {"message": "Mobiltelefoner och telekommunikation"}, "sbi_aliprice_cate__computer_office": {"message": "Dator & kontor"}, "sbi_aliprice_cate__consumer_electronics": {"message": "Hemelektronik"}, "sbi_aliprice_cate__education_office_supplies": {"message": "Utbildning och kontorsmateriel"}, "sbi_aliprice_cate__electronic_components_supplies": {"message": "Elektroniska komponenter och tillbehör"}, "sbi_aliprice_cate__furniture": {"message": "<PERSON><PERSON><PERSON>"}, "sbi_aliprice_cate__hair_extensions_wigs": {"message": "Hårförlängningar och peruker"}, "sbi_aliprice_cate__home_garden": {"message": "<PERSON><PERSON>"}, "sbi_aliprice_cate__home_improvement": {"message": "Renovering"}, "sbi_aliprice_cate__jewelry_accessories": {"message": "Smycken & Accessoarer"}, "sbi_aliprice_cate__luggage_bags": {"message": "Bagage & väskor"}, "sbi_aliprice_cate__mother_kids": {"message": "Mor & barn"}, "sbi_aliprice_cate__novelty_special_use": {"message": "Nyhet och speciell användning"}, "sbi_aliprice_cate__security_protection": {"message": "Säker<PERSON><PERSON> och skydd"}, "sbi_aliprice_cate__shoes": {"message": "Skor"}, "sbi_aliprice_cate__sports_entertainment": {"message": "Sport & underhållning"}, "sbi_aliprice_cate__toys_hobbies": {"message": "Leks<PERSON> och hobbyer"}, "sbi_aliprice_cate__watches": {"message": "<PERSON><PERSON><PERSON>"}, "sbi_aliprice_cate__weddings_events": {"message": "Br<PERSON><PERSON><PERSON> och evenemang"}, "sbi_btn_capture_txt": {"message": "Fånga"}, "sbi_btn_source_now_txt": {"message": "<PERSON><PERSON>lla nu"}, "sbi_button__chat_with_me": {"message": "Chatta med mig"}, "sbi_button__contact_supplier": {"message": "Kontakt"}, "sbi_button__hide_on_this_site": {"message": "Visa inte på den här webbplatsen"}, "sbi_button__open_settings": {"message": "Konfigurera sökning efter bild"}, "sbi_capture_shortcut_tip": {"message": "eller tryck på \"<PERSON><PERSON>\" på tangentbordet"}, "sbi_capturing_tip": {"message": "Fångande"}, "sbi_composed_rating_45": {"message": "4,5 - 5,0 stjärnor"}, "sbi_crop_and_search": {"message": "<PERSON>ö<PERSON>"}, "sbi_crop_start": {"message": "Använd skärmdump"}, "sbi_err_captcha_action": {"message": "Kontrollera"}, "sbi_err_captcha_for_alibaba_cn": {"message": "Behöver verifiering, ladda upp en bild för att verifiera. (Visa $video_tutorial$ eller försök att rensa cookies)", "placeholders": {"feedback": {"content": "$1"}, "video_tutorial": {"content": "$1"}}}, "sbi_err_captcha_for_aliprice": {"message": "O<PERSON><PERSON>g tra<PERSON>, verifiera"}, "sbi_err_captcha_for_taobao": {"message": "Taobao ber dig verifiera, ladda upp en bild manuellt och sök för att verifiera den. Det här felet beror på den nya verifieringspolicyn \"TaoBao search by image\", vi föreslår att klagomålet verifieras för ofta på Taobao $feedback$.", "placeholders": {"feedback": {"content": "$1"}}}, "sbi_err_captcha_for_taobao_feedback": {"message": "respons"}, "sbi_err_captcha_msg": {"message": "$platform$ kräver att du laddar upp en bild för att söka eller slutföra säkerhetsverifieringen för att ta bort sökbegränsningar", "placeholders": {"platform": {"content": "$1"}}}, "sbi_err_checking_if_low_version": {"message": "Kontrollera om det är den senaste versionen"}, "sbi_err_cookie_btn_clear": {"message": "<PERSON><PERSON> kakor"}, "sbi_err_cookie_for_alibaba_cn": {"message": "Rensa 1688 cookies? (Behöver du logga in igen)"}, "sbi_err_desperate_feature_pdd": {"message": "Bildsökningsfunktionen har flyttats till Pinduoduo Search by Image extension."}, "sbi_err_hidden_skill_of_improve_search_rate": {"message": "Hur kan man förbättra framgångsfrekvensen för bildsökning?"}, "sbi_err_img_undersize": {"message": "Bild > $imgMinimumSize$", "placeholders": {"imgMinimumSize": {"content": "$1"}}}, "sbi_err_login_required": {"message": "Logga in $loginSite$", "placeholders": {"loginSite": {"content": "$1"}}}, "sbi_err_login_required_action": {"message": "Logga in"}, "sbi_err_low_version": {"message": "Installera den senaste versionen ($latestVersion$)", "placeholders": {"latestVersion": {"content": "$1"}}}, "sbi_err_low_version_action": {"message": "Ladda ner"}, "sbi_err_need_help": {"message": "Behöver du hjälp"}, "sbi_err_network": {"message": "Nätverksfel, se till att du kan besöka webbplatsen"}, "sbi_err_not_low_version": {"message": "Den senaste versionen har installerats ($latestVersion$)", "placeholders": {"latestVersion": {"content": "$1"}}}, "sbi_err_try_again": {"message": "Försök igen"}, "sbi_err_try_again_action": {"message": "Försök igen"}, "sbi_err_visit_and_try": {"message": "Försök igen eller besök $website$ för att försöka igen", "placeholders": {"website": {"content": "$1"}}}, "sbi_err_visit_site": {"message": "Besök startsidan för $siteName$", "placeholders": {"siteName": {"content": "$1"}}}, "sbi_fail_tip": {"message": "Laddningen misslyckades. Uppdatera sidan och försök igen."}, "sbi_kuajing_filter_area": {"message": "<PERSON><PERSON>r<PERSON><PERSON>"}, "sbi_kuajing_filter_au": {"message": "Australien"}, "sbi_kuajing_filter_btn_confirm": {"message": "Bekräfta"}, "sbi_kuajing_filter_de": {"message": "Tyskland"}, "sbi_kuajing_filter_destination_country": {"message": "Destinationsland"}, "sbi_kuajing_filter_es": {"message": "Spanien"}, "sbi_kuajing_filter_estimate": {"message": "Uppskatta"}, "sbi_kuajing_filter_estimate_price": {"message": "Beräknat pris"}, "sbi_kuajing_filter_estimate_price_formula": {"message": "Uppskattad prisformel = (råvarup<PERSON> + internationell logistikfrakt)/(1 - vinstmarginal - annan kostnad)"}, "sbi_kuajing_filter_fr": {"message": "<PERSON><PERSON><PERSON>"}, "sbi_kuajing_filter_kw_placeholder": {"message": "<PERSON><PERSON> nyckelord för att matcha titeln"}, "sbi_kuajing_filter_logistics": {"message": "Logistikmall"}, "sbi_kuajing_filter_logistics_china_post": {"message": "kinesisk flygpost"}, "sbi_kuajing_filter_logistics_discount": {"message": "Logistikrabatt"}, "sbi_kuajing_filter_logistics_epacket": {"message": "epacket"}, "sbi_kuajing_filter_logistics_price_formula": {"message": "Internationell logistikfrakt = (vikt x fraktpris + registreringsavgift) x (1 - rabatt)"}, "sbi_kuajing_filter_others_fee": {"message": "<PERSON>n avgift"}, "sbi_kuajing_filter_profit_percent": {"message": "Vinstmarginal"}, "sbi_kuajing_filter_prop": {"message": "Attribut"}, "sbi_kuajing_filter_ru": {"message": "Ryssland"}, "sbi_kuajing_filter_total": {"message": "Matcha $count$ liknande föremål", "placeholders": {"count": {"content": "$1"}}}, "sbi_kuajing_filter_uk": {"message": "STORBRITANNIEN"}, "sbi_kuajing_filter_usa": {"message": "Amerika"}, "sbi_login_punish_title__pdd_pifa": {"message": "<PERSON>nd<PERSON><PERSON>o grossist"}, "sbi_msg_no_result": {"message": "Inget resultat hittades,logga in på $loginSite$ eller prova en annan bild", "placeholders": {"loginSite": {"content": "$1"}}}, "sbi_msg_no_result_check_support_page_l1": {"message": "Till<PERSON><PERSON><PERSON>gt inte tillgängligt för <PERSON>, använd $supportPage$.", "placeholders": {"supportPage": {"content": "$1"}}}, "sbi_msg_no_result_check_support_page_l2": {"message": "Chrome-webbl<PERSON><PERSON><PERSON> och dess tillägg"}, "sbi_msg_no_result_reinstall_l1": {"message": "Inga resultat hittades, logga in på $loginSite$ eller prova en annan bild eller installera om den senaste versionen $latestExtUrl$", "placeholders": {"loginSite": {"content": "$1"}, "latestExtUrl": {"content": "$2"}}}, "sbi_msg_no_result_reinstall_l2": {"message": "Senaste versionen", "placeholders": {}}, "sbi_search_by_selected_area": {"message": "Vald o<PERSON>rå<PERSON>"}, "sbi_shipping_": {"message": "<PERSON><PERSON><PERSON> samma dag"}, "sbi_specify_category": {"message": "<PERSON><PERSON> kate<PERSON>:"}, "sbi_start_crop": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "sbi_store_name_alibabaCNKuaJing": {"message": "1688 utomlands"}, "sbi_tutorial_btn_more": {"message": "<PERSON><PERSON> s<PERSON>t"}, "sbi_tutorial_find_coupons_on_taobao": {"message": "<PERSON><PERSON>-k<PERSON><PERSON>"}, "sbi_txt__empty_retry": {"message": "<PERSON><PERSON><PERSON><PERSON>, inga resultat hittades. Försök igen."}, "sbi_txt__min_order": {"message": "Min. ordning"}, "sbi_visiting": {"message": "Bläddring"}, "sbi_yiwugo__jiagexiangtan": {"message": "Kontakta s<PERSON>jaren"}, "sbi_yiwugo__qigou": {"message": "$num$ bitar (MOQ)", "placeholders": {"num": {"content": "$1"}}}, "sbi_yiwugo__xing": {"message": "Stjärnor"}, "searchByImage_screenshot": {"message": "Skärmdump med ett klick"}, "searchByImage_search": {"message": "<PERSON><PERSON>k med ett klick efter samma objekt"}, "searchByImage_size_type": {"message": "Filstorleken får inte vara större än $num$ MB, endast $type$", "placeholders": {"num": {"content": "$1"}, "type": {"content": "$2"}}}, "search_by_image_progress_analysing": {"message": "<PERSON><PERSON><PERSON><PERSON> bilden"}, "search_by_image_progress_searching": {"message": "<PERSON><PERSON><PERSON> efter produkter"}, "search_by_image_progress_sending": {"message": "<PERSON><PERSON><PERSON> bild"}, "search_by_image_response_rate": {"message": "Svarsfrekvens: $responseRate$ av köpare som kontaktade den här leverantören fick svar inom $responseInHour$ timmar.", "placeholders": {"responseRate": {"content": "$1"}, "responseInHour": {"content": "$2"}}}, "search_by_keyword": {"message": "<PERSON><PERSON><PERSON> efter n<PERSON>:"}, "select_country_language_modal_title_country": {"message": "Land"}, "select_country_language_modal_title_language": {"message": "Språk"}, "select_country_region_modal_title": {"message": "Välj ett land / en region"}, "select_language_modal_title": {"message": "<PERSON><PERSON><PERSON><PERSON>:"}, "select_shop": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "sellers_count": {"message": "<PERSON><PERSON> s<PERSON> på den aktuella sidan"}, "sellers_count_per_page": {"message": "<PERSON><PERSON> s<PERSON> på den aktuella sidan"}, "service_score": {"message": "Omfattande servicebetyg"}, "set_shortcut_keys": {"message": "Ställ in kortkommandon"}, "setting_logo_title": {"message": "Shoppingassistent"}, "setting_modal_options_position_title": {"message": "Plug-in-läge"}, "setting_modal_options_position_value_left": {"message": "Vänstra hörnet"}, "setting_modal_options_position_value_right": {"message": "<PERSON><PERSON><PERSON>"}, "setting_modal_options_theme_title": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "setting_modal_options_theme_value_dark": {"message": "M<PERSON><PERSON>"}, "setting_modal_options_theme_value_light": {"message": "<PERSON><PERSON><PERSON>"}, "setting_modal_title": {"message": "inställningar"}, "setting_options_country_title": {"message": "Land / region"}, "setting_options_hover_zoom_desc": {"message": "<PERSON><PERSON><PERSON> mus<PERSON>en över för att zooma in"}, "setting_options_hover_zoom_title": {"message": "Hover Zoom"}, "setting_options_jd_coupon_desc": {"message": "Hittade kupongen på JD.com"}, "setting_options_jd_coupon_title": {"message": "JD.com kupong"}, "setting_options_language_title": {"message": "Språk"}, "setting_options_price_drop_alert_desc": {"message": "<PERSON><PERSON>r priset på produkter i Min favorit sjunker får du push-meddelande."}, "setting_options_price_drop_alert_title": {"message": "Varning om prisfall"}, "setting_options_price_history_on_list_page_desc": {"message": "Visa prishistorik på produktsökningssidan"}, "setting_options_price_history_on_list_page_title": {"message": "Prishistorik (listsida)"}, "setting_options_price_history_on_produt_page_desc": {"message": "Visa produkthistorik på produktdetaljsidan"}, "setting_options_price_history_on_produt_page_title": {"message": "Prishistorik (detaljsida)"}, "setting_options_sales_analysis_desc": {"message": "Stöd statistik över pris, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>lym, antal s<PERSON><PERSON><PERSON><PERSON> och butiksförsäljningskvot på produktlistan $platforms$", "placeholders": {"platforms": {"content": "$1"}}}, "setting_options_sales_analysis_title": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>analy<PERSON>"}, "setting_options_save_success_msg": {"message": "Framgång"}, "setting_options_tacking_price_title": {"message": "<PERSON>arning för <PERSON>"}, "setting_options_value_off": {"message": "Av"}, "setting_options_value_on": {"message": "På"}, "setting_pkg_quick_view_desc": {"message": "Support: 1688 & Taobao"}, "setting_saved_message": {"message": "Ändringarna sparades"}, "setting_section_enable_platform_title": {"message": "På av"}, "setting_section_setting_title": {"message": "inställningar"}, "setting_section_shortcuts_title": {"message": "Genvä<PERSON>"}, "settings_aliprice_agent__desc": {"message": "Visas på $platforms$ produktdetaljsida", "placeholders": {"platforms": {"content": "$1"}}}, "settings_aliprice_agent__title": {"message": "<PERSON><PERSON><PERSON>t mig"}, "settings_copy_link__desc": {"message": "Visas på produktdetaljsidan"}, "settings_copy_link__title": {"message": "Kopiera-knapp och söktitel"}, "settings_currency_desc__for_detail": {"message": "Support 1688 produktdetaljsida"}, "settings_currency_desc__for_list": {"message": "<PERSON><PERSON><PERSON> efter bild (inkludera 1688/1688 utomlands / Taobao)"}, "settings_currency_desc__for_sbi": {"message": "<PERSON><PERSON><PERSON><PERSON> pris"}, "settings_currency_desc_display_for_list": {"message": "Visas i bildsökning (inklusive 1688/1688 utomlands/Taobao)"}, "settings_currency_rate_desc": {"message": "Uppdaterar växelkursen från \"$currencyRateFrom$\"", "placeholders": {"currencyRateFrom": {"content": "$1"}}}, "settings_currency_rate_from_boc": {"message": "Kinesiska banken"}, "settings_download_images__desc": {"message": "Stöd för nedladdning av bilder från $platforms$", "placeholders": {"platforms": {"content": "$1"}}}, "settings_download_images__title": {"message": "ladda ner bild-knapp"}, "settings_download_reviews__desc": {"message": "Visas på $platforms$ produktdetaljsida", "placeholders": {"platforms": {"content": "$1"}}}, "settings_download_reviews__title": {"message": "<PERSON>dda ner recensionsbilder"}, "settings_google_translate_desc": {"message": "Högerklicka för att få Google översättningsfält"}, "settings_google_translate_title": {"message": "webbsida översättning"}, "settings_historical_trend_desc": {"message": "Visas i det nedre högra hörnet av bilden på produktlistans sida"}, "settings_modal_btn_more": {"message": "Fler inställningar"}, "settings_productInfo_desc": {"message": "Visa mer detaljerad produktinformation på produktlistans sida. Att aktivera detta kan öka datorns belastning och orsaka sidfördröjning. Om det påverkar prestandan rekommenderas det att inaktivera det."}, "settings_product_recommend__desc": {"message": "Visas under huvudbilden på $platforms$ produktinformationssida", "placeholders": {"platforms": {"content": "$1"}}}, "settings_product_recommend__name": {"message": "Produkter som rekommenderas"}, "settings_research_desc": {"message": "Fråga mer detaljerad information på produktlistans sida"}, "settings_sbi_add_to_list": {"message": "Lägg till i $listType$", "placeholders": {"listType": {"content": "$1"}}}, "settings_sbi_detail_page_icon_title_desc": {"message": "Miniatyrbild av sökresultat efter foto"}, "settings_sbi_remove_from_list": {"message": "Ta bort från $listType$", "placeholders": {"listType": {"content": "$1"}}}, "settings_search_by_image_add_to_blacklist": {"message": "Lägg till i blocklista"}, "settings_search_by_image_adjust_entrance_entrance": {"message": "<PERSON><PERSON>"}, "settings_search_by_image_blacklist_desc": {"message": "Visa inte ikonen på webbplatser i den svarta listan."}, "settings_search_by_image_blacklist_title": {"message": "Blockeringslista"}, "settings_search_by_image_bottom_left": {"message": "<PERSON>ä<PERSON>st ner till vänster"}, "settings_search_by_image_bottom_right": {"message": "Nederst till höger"}, "settings_search_by_image_clear_blacklist": {"message": "Rensa blocklista"}, "settings_search_by_image_detail_page_icon_title": {"message": "Miniatyr"}, "settings_search_by_image_detail_page_icon_val_large": {"message": "<PERSON><PERSON><PERSON>"}, "settings_search_by_image_detail_page_icon_val_small": {"message": "Mindre"}, "settings_search_by_image_display_button_desc": {"message": "Ett klick på ikonen för att söka efter bild"}, "settings_search_by_image_display_button_title": {"message": "<PERSON><PERSON> på bilder"}, "settings_search_by_image_sourece_websites_desc": {"message": "<PERSON>ta källprodukten på dessa webbplatser"}, "settings_search_by_image_sourece_websites_title": {"message": "Sök efter bildresultat"}, "settings_search_by_image_top_left": {"message": "Överst till vänster"}, "settings_search_by_image_top_right": {"message": "Överst till höger"}, "settings_search_keyword_on_x__desc": {"message": "S<PERSON>k efter ord på $platform$", "placeholders": {"platform": {"content": "$1"}}}, "settings_search_keyword_on_x__title": {"message": "Visa ikonen $platform$ när du har valt ord", "placeholders": {"platform": {"content": "$1"}}}, "settings_similar_products_desc": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON> hitta samma produkt på dessa webbplatser (max till 5)"}, "settings_similar_products_title": {"message": "<PERSON>ta samma produkt"}, "settings_toolbar_expand_title": {"message": "Plug-in minimera"}, "settings_top_toolbar_desc": {"message": "Sökfält högst upp på sidan"}, "settings_top_toolbar_title": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "settings_translate_search_desc": {"message": "Översätt till kinesiska och sök"}, "settings_translate_search_title": {"message": "Flerspråkig sökning"}, "settings_translator_contextmenu_title": {"message": "Fånga för att översätta"}, "settings_translator_title": {"message": "Översätt"}, "shai_xuan_dao_chu": {"message": "Filtrera för att exportera"}, "shai_xuan_zi_duan": {"message": "Filtrera fält"}, "shang_jia_shi_jian": {"message": "På hylltid"}, "shang_pin_biao_ti": {"message": "produkttitel"}, "shang_pin_dui_bi": {"message": "Produktjämförelse"}, "shang_pin_lian_jie": {"message": "produktlänk"}, "shang_pin_xin_xi": {"message": "Produktinformation"}, "share_modal__content": {"message": "Dela med dina vänner"}, "share_modal__disable_for_while": {"message": "Jag vill inte dela någonting"}, "share_modal__title": {"message": "Gillar du $extensionName$?", "placeholders": {"extensionName": {"content": "$1"}}}, "sheng_yu_ku_cun": {"message": "Å<PERSON>tående"}, "shi_fou_ke_ding_zhi": {"message": "Är det an<PERSON>nings<PERSON>t?"}, "shi_fou_ren_zheng_gong_ying_sha": {"message": "Certifierad leverantör"}, "shi_fou_ren_zheng_gong_ying_shang_zong_shu": {"message": "Certifierade leverantörer"}, "shi_fou_you_mao_yi_dan_bao": {"message": "Handelsförsäkring"}, "shi_fou_you_mao_yi_dan_bao_zong_shu": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "shipping_fee": {"message": "Fraktavgift"}, "shop_followers": {"message": "<PERSON><PERSON> f<PERSON>"}, "shou_qi": {"message": "Mindre"}, "similar_products_warn_max_platforms": {"message": "Max till 5"}, "sku_calc_price": {"message": "Beräknat pris"}, "sku_calc_price_settings": {"message": "Beräknade prisinställningar"}, "sku_formula": {"message": "Formel"}, "sku_formula_desc": {"message": "Formelbeskrivning"}, "sku_formula_desc_text": {"message": "<PERSON><PERSON>der komplexa matematiska formler, där det ursprungliga priset representeras av A och frakten representeras av B\n\n<br/>\n\n<PERSON><PERSON><PERSON> parenteser (), plus +, minus -, multiplikation * och division /\n\n<br/>\n\nExempel:\n\n<br/>\n\n1. <PERSON><PERSON><PERSON> att få 1,2 gånger det ursprungliga priset och sedan lägga till frakten, är formeln: A*1,2+B\n\n<br/>\n\n2. <PERSON><PERSON><PERSON> att få det ursprungliga priset plus 1 yuan, multiplicera sedan med 1,2 gånger, formeln är: (A+1)*1,2\n\n<br/>\n\n3. <PERSON><PERSON><PERSON> att få det ursprungliga priset plus 10 yuan, multiplicera sedan med 1,2 gånger och subtrahera sedan 3 yuan, formeln är: (A+10)*1,2-3"}, "sku_in_stock": {"message": "I lager"}, "sku_invalid_formula_format": {"message": "Ogiltigt formelformat"}, "sku_inventory": {"message": "Lager"}, "sku_link_copy_fail": {"message": "Ko<PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON>, varuspecifikationer och attribut har inte valts"}, "sku_link_copy_success": {"message": "<PERSON><PERSON><PERSON> f<PERSON><PERSON><PERSON>, varuspecifikationer och attribut har valts"}, "sku_list": {"message": "Artikelnummerlista"}, "sku_min_qrder_qty": {"message": "Minsta orderkvantitet"}, "sku_name": {"message": "Artikelnummernamn"}, "sku_no": {"message": "Nr."}, "sku_original_price": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> pris"}, "sku_price": {"message": "Art<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "stop_track_time_label": {"message": "Spårningsfrist:"}, "suo_zai_di_qu": {"message": "plats"}, "tab_pkg_quick_view": {"message": "Logistikövervakare"}, "tab_product_details_price_history": {"message": "Prishistorik"}, "tab_product_details_reviews": {"message": "Fotorecensioner"}, "tab_product_details_seller_analysis": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "tab_product_details_similar_products": {"message": "<PERSON><PERSON> produkter"}, "total_days_listed_per_product": {"message": "Summa av hylldagar ÷ Antal produkter"}, "total_items": {"message": "Totalt antal produkter"}, "total_price_per_product": {"message": "Summa av priser ÷ Antal produkter"}, "total_rating_per_product": {"message": "Summa av betyg ÷ Antal produkter"}, "total_revenue": {"message": "Totala intäkter"}, "total_revenue40_items": {"message": "Totala intäkter för de $amount$ produkterna på den aktuella sidan", "placeholders": {"amount": {"content": "$1"}}}, "total_sales": {"message": "Total rea"}, "total_sales40_items": {"message": "Total försäljning av de $amount$ produkterna på den aktuella sidan", "placeholders": {"amount": {"content": "$1"}}}, "track_for_12_months": {"message": "Spår för: 1 år"}, "track_for_3_months": {"message": "Spår i: 3 månader"}, "track_for_6_months": {"message": "Spår i: 6 månader"}, "tracking_price_email_add_btn": {"message": "Lägg till e-post"}, "tracking_price_email_edit_btn": {"message": "Redigera e-post"}, "tracking_price_email_intro": {"message": "<PERSON><PERSON> medd<PERSON><PERSON> dig via e-post."}, "tracking_price_email_invalid": {"message": "Var god ange en giltig e-postadress"}, "tracking_price_email_verified_desc": {"message": "Du kan nu få vår prisavisering."}, "tracking_price_email_verified_title": {"message": "Verifierat"}, "tracking_price_email_verify_desc_line1": {"message": "Vi har skickat en verifieringslänk till din e-postadress,"}, "tracking_price_email_verify_desc_line2": {"message": "kontrollera din e-post inkorg."}, "tracking_price_email_verify_title": {"message": "Verifiera Email"}, "tracking_price_web_push_notification_intro": {"message": "På skrivbordet: AliPrice kan övervaka vilken produkt som helst åt dig och skicka ett meddelande via webben när priset ändras."}, "tracking_price_web_push_notification_title": {"message": "<PERSON> Push-medd<PERSON><PERSON>"}, "translate_im__login_required": {"message": "Översatt av AliPrice, vänligen logga in på $loginUrl$", "placeholders": {"loginUrl": {"content": "$1"}}}, "translate_im__paste_fail": {"message": "Översatt och kopierat till <PERSON><PERSON><PERSON><PERSON>, men på grund av begränsningen av Aliwangwang måste du klistra in det manuellt!"}, "translate_im__send": {"message": "Översätt och skicka"}, "translate_search": {"message": "Översätt och sökning"}, "translation_originals_translated": {"message": "Original och kinesiskt"}, "translation_translated": {"message": "kinesiska"}, "translator_btn_capture_txt": {"message": "Översätt"}, "translator_language_auto_detect": {"message": "Automatisk detektering"}, "translator_language_detected": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "translator_language_search_placeholder": {"message": "<PERSON><PERSON><PERSON> sp<PERSON>k"}, "try_again": {"message": "Försök igen"}, "tu_pian_chi_cun": {"message": "Bildstorlek:"}, "tu_pian_lian_jie": {"message": "Bildlänk"}, "tui_huan_ti_yan": {"message": "Returupplevelse"}, "tui_huan_ti_yan__desc": {"message": "Bedöm säljarens eftermarknadsindikatorer"}, "tutorial__show_all": {"message": "<PERSON><PERSON> funk<PERSON><PERSON>"}, "tutorial_ae_popup_title": {"message": "<PERSON>äst förlängningen, öppna Aliexpress"}, "tutorial_aliexpress_reviews_analysis": {"message": "AliExpress recensionsanalys"}, "tutorial_aliprice_agent_with1688_desc_l1": {"message": "Stöd USD"}, "tutorial_aliprice_agent_with1688_desc_l2": {"message": "Leverans till Korea/Japan/Kina"}, "tutorial_aliprice_agent_with1688_title": {"message": "1688 stöder köp utomlands"}, "tutorial_auto_apply_coupon_title": {"message": "Applicera kupong automatiskt"}, "tutorial_btn_end": {"message": "Slutet"}, "tutorial_btn_example": {"message": "Exempel"}, "tutorial_btn_see_more": {"message": "<PERSON><PERSON>"}, "tutorial_compare_products": {"message": "Jämför med samma stil"}, "tutorial_currency_convert_title": {"message": "växelkursomvandling"}, "tutorial_export_shopping_cart": {"message": "Exportera CSV, stöd Taobao och 1688"}, "tutorial_export_shopping_cart_title": {"message": "exportvagn"}, "tutorial_price_history_pro": {"message": "Visas på produktdetaljsidan.\n<PERSON><PERSON>d <PERSON>, La<PERSON>a, Amazon och Ebay"}, "tutorial_price_history_pro_title": {"message": "Helårs prishistorik och orderhistorik"}, "tutorial_sbi_context_menu_screenshot_title": {"message": "Skärmdumpsökning för samma stil"}, "tutorial_translate_search": {"message": "Översätt till sökning"}, "tutorial_translate_search_and_package_tracking": {"message": "Översättningssökning och paketspårning"}, "unit_bao": {"message": "st"}, "unit_ben": {"message": "st"}, "unit_bi": {"message": "order"}, "unit_chuang": {"message": "st"}, "unit_dai": {"message": "st"}, "unit_dui": {"message": "par"}, "unit_fen": {"message": "st"}, "unit_ge": {"message": "st"}, "unit_he": {"message": "st"}, "unit_jian": {"message": "st"}, "unit_li_fang_mi": {"message": "m³"}, "unit_ping": {"message": "st"}, "unit_ping_fang_mi": {"message": "㎡"}, "unit_shuang": {"message": "par"}, "unit_tai": {"message": "st"}, "unit_ti": {"message": "st"}, "unit_tiao": {"message": "st"}, "unit_xiang": {"message": "st"}, "unit_zhang": {"message": "st"}, "unit_zhi": {"message": "st"}, "verify_contact_support": {"message": "Kontakta support"}, "verify_human_verification": {"message": "Mänsklig verifiering"}, "verify_unusual_access": {"message": "Ovanlig åtkomst upptäckt"}, "view_history_clean_all": {"message": "Städa allt"}, "view_history_clean_all_warring": {"message": "Rensa alla visade poster?"}, "view_history_clean_all_warring_title": {"message": "Varning"}, "view_history_viewd": {"message": "Tittade"}, "website": {"message": "webbplats"}, "weight": {"message": "<PERSON><PERSON><PERSON>"}, "wu_fa_huo_qu_dao_gai_shu_ju": {"message": "Det går inte att hämta data"}, "wu_liu_shi_xiao": {"message": "<PERSON><PERSON><PERSON> i tid"}, "wu_liu_shi_xiao__desc": {"message": "48-t<PERSON><PERSON><PERSON> och uppfyllnadsgrad för s<PERSON><PERSON> butik"}, "xia_dan_jia": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "xian_xuan_ze_product_attributes": {"message": "Välj produktattribut"}, "xiao_liang": {"message": "Försäljningsvolymen"}, "xiao_liang_zhan_bi": {"message": "Andel av försäljningsvolymen"}, "xiao_shi": {"message": "$num$ timmar", "placeholders": {"num": {"content": "$1"}}}, "xiao_shou_e": {"message": "Inkomst"}, "xiao_shou_e_zhan_bi": {"message": "Procent av intäkterna"}, "xuan_zhong_x_tiao_ji_lu": {"message": "Välj $amount$ poster", "placeholders": {"amoun": {"content": "$1"}, "amount": {"content": "$1"}}}, "yan_xuan": {"message": "Val"}, "yi_ding_zai_zuo_ce": {"message": "<PERSON><PERSON><PERSON>"}, "yi_jia_zai_wan_suo_you_shang_pin": {"message": "Alla produkter laddade"}, "yi_nian_xiao_liang": {"message": "Årlig försäljning"}, "yi_nian_xiao_liang_zhan_bi": {"message": "Årlig försäljningsandel"}, "yi_nian_xiao_shou_e": {"message": "Årlig omsättning"}, "yi_nian_xiao_shou_e_zhan_bi": {"message": "Årlig omsättningsandel"}, "yi_shua_xin": {"message": "Uppfriskad"}, "yin_cang_xiang_tong_dian": {"message": "<PERSON><PERSON><PERSON><PERSON> lik<PERSON>"}, "you_xiao_liang": {"message": "Med försäljningsvolym"}, "yu_ji_dao_da_shi_jian": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "yuan_gong_ren_shu": {"message": "<PERSON><PERSON>"}, "yue_cheng_jiao": {"message": "Månadsvolym"}, "yue_dai_xiao": {"message": "Dropshipping"}, "yue_dai_xiao__desc": {"message": "Dropshipping f<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> under de senaste 30 dagarna"}, "yue_dai_xiao_pai_xu__desc": {"message": "Dropshipping-f<PERSON><PERSON><PERSON><PERSON><PERSON>ing under de senaste 30 dagarna, sorterad från hög till låg"}, "yue_xiao_liang__desc": {"message": "Försäljningsvolym under de senaste 30 dagarna"}, "zhan_kai": {"message": "<PERSON><PERSON>"}, "zhe_kou": {"message": "<PERSON><PERSON><PERSON>"}, "zhi_chi_yi_jian_dai_fa": {"message": "Dropshipping"}, "zhi_chi_yi_jian_dai_fa_bao_you": {"message": "<PERSON><PERSON><PERSON> frakt"}, "zhi_fu_ding_dan_shu": {"message": "<PERSON><PERSON>ll<PERSON>"}, "zhi_fu_ding_dan_shu__desc": {"message": "<PERSON><PERSON> för denna produkt (30 dagar)"}, "zhu_ce_xing_zhi": {"message": "Registreringskaraktär"}, "zi_ding_yi_tiao_jian": {"message": "Anpassade villkor"}, "zi_duan": {"message": "<PERSON><PERSON><PERSON>"}, "zi_ti_xiao_liang": {"message": "Variation såld"}, "zong_he_fu_wu_fen": {"message": "Helhetsbetyg"}, "zong_he_fu_wu_fen__desc": {"message": "Totalt betyg av säljarens tjänst"}, "zong_he_fu_wu_fen__short": {"message": "Betyg"}, "zong_he_ti_yan_fen": {"message": "Betyg"}, "zong_he_ti_yan_fen_3": {"message": "Under 4 stjärnor"}, "zong_he_ti_yan_fen_4": {"message": "4 - 4,5 stjärnor"}, "zong_he_ti_yan_fen_4_5": {"message": "4,5 - 5,0 stjärnor"}, "zong_he_ti_yan_fen_5": {"message": "5 stjärnor"}, "zong_ku_cun": {"message": "Totalt lager"}, "zong_xiao_liang": {"message": "Total rea"}, "zui_jin_30D_3Min_xiang_ying_lv": {"message": "3 minuters s<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> under de senast<PERSON> 30 dagarna"}, "zui_jin_30D_48H_lan_shou_lv": {"message": "48H återhämtningshastighet under de senaste 30 dagarna"}, "zui_jin_30D_48H_lv_yue_lv": {"message": "48H prestanda under de senaste 30 da<PERSON>na"}, "zui_jin_30D_jiao_yi_ji_lu": {"message": "Handelsrekord (30 dagar)"}, "zui_jin_30D_jiao_yi_ji_lu__desc": {"message": "Handelsrekord (30 dagar)"}, "zui_jin_30D_jiu_fen_lv": {"message": "Tvistfrekvens under de senaste 30 dagarna"}, "zui_jin_30D_pin_zhi_tui_kuan_lv": {"message": "Kvalitetsåterbetalningssats under de senaste 30 dagarna"}, "zui_jin_30D_zhi_fu_ding_dan_shu": {"message": "Antalet betalningsorder under de senaste 30 dagarna"}}