/**
 * @fileoverview 扩展配置管理器工具函数
 * @description 提供扩展配置管理器内部使用的通用辅助函数，包括路径管理、权限处理、文件系统操作等
 */

import fs from 'fs-extra';
import { glob } from 'glob';
import path from 'path';
import { projectPaths, simpleMatch, createLogger } from '../helpers/index.js';
import { SUPPORT_MVS, SUPPORT_VARIANTS, SUPPORT_WEBSTORES } from './constants.js';
import type {
  ManifestVersionType,
  ProcessedPaths,
  ProcessedVariantConfig,
  VariantInfo,
  VariantType,
  WebstoreType,
} from './types.js';
import { pathToFileURL } from 'url';

const logger = createLogger('ExtensionConfigUtils');

// 重新导出 projectPaths 供其他模块使用
export { projectPaths };

// #region --- 路径管理 ---

/**
 * 生成特定插件和变体的所有相关路径。
 * @param extensionName - 插件名称。
 * @param variantTarget - 变体目标字符串。
 * @param version - 插件版本。
 * @returns 包含所有路径的对象。
 */
export function generatePaths(
  extensionName: string,
  variantTarget: string,
  version: string,
): ProcessedPaths {
  const extensionRoot = path.join(projectPaths.extensions, extensionName);
  const paths: ProcessedPaths = {
    ...projectPaths,

    extensionRoot,
    extensionVariantsRoot: path.join(extensionRoot, '.variants'),
    extensionManifestsRoot: path.join(extensionRoot, '.manifests'),
    extensionRawConfig: path.join(extensionRoot, 'extension.config.ts'),
    extensionRawLocales: path.join(extensionRoot, 'locales'),
    extensionVariantTargetRoot: '',
    extensionVariantTargetJSON: '',
    extensionVariantTargetI18n: '',
    extensionVariantTargetLocales: '',
    extensionVariantTargetManifest: '',
    extensionVariantTargetOutput: '',
  };

  // 动态生成变体相关路径
  paths.extensionVariantTargetRoot = path.join(paths.extensionVariantsRoot, variantTarget);
  paths.extensionVariantTargetJSON = path.join(
    paths.extensionVariantTargetRoot,
    'extension.config.json',
  );
  paths.extensionVariantTargetI18n = path.join(paths.extensionVariantTargetRoot, 'i18n.json');
  paths.extensionVariantTargetLocales = path.join(
    paths.extensionVariantTargetRoot,
    'public',
    '_locales',
  );
  paths.extensionVariantTargetManifest = path.join(
    paths.extensionManifestsRoot,
    `manifest.${variantTarget}.json`,
  );
  // paths.extensionVariantTargetOutput = path.join(
  //   paths.workspace,
  //   'dist',
  //   extensionName,
  //   version,
  //   variantTarget,
  // );

  return paths;
}

// #endregion

// #region --- 权限处理 ---

export function isHostPermission(permission: string): boolean {
  return /^(https?|ftp|file|\*):\/\//.test(permission);
}

export function separatePermissions(permissions: string[] = []): {
  regularPermissions: string[];
  hostPermissions: string[];
} {
  const regularPermissions: string[] = [];
  const hostPermissions: string[] = [];
  permissions.forEach((permission) => {
    if (isHostPermission(permission)) {
      hostPermissions.push(permission);
    } else {
      regularPermissions.push(permission);
    }
  });
  return { regularPermissions, hostPermissions };
}

// #endregion

// #region --- 文件系统与匹配 ---

/**
 * @description 查找指定目录下的所有语言包文件
 * @param p 目录路径
 * @returns 排序后的 JSON 文件路径数组
 *
 * 功能说明：
 * 1. 检查目录是否存在，不存在则返回空数组
 * 2. 使用 glob 模式匹配所有 .json 文件
 * 3. 按文件名字母顺序排序
 * 4. 异常情况下记录错误日志并返回空数组
 */
export function findLocaleMessageFiles(p: string): string[] {
  try {
    if (!fs.pathExistsSync(p)) return [];
    return glob
      .sync(`${p}/*.json`)
      .sort((a, b) => path.basename(a).localeCompare(path.basename(b)));
  } catch (e) {
    logger.error(`查找语言包文件失败 ${path.relative(projectPaths.workspace, p)}:`, e);
    return [];
  }
}

export function getLocaleFromPath(filePath: string): string {
  return path.basename(filePath, '.json');
}

/**
 * @description 扫描并列出插件目录名称
 * @param extensionNames 可选的插件名称列表，如果提供则验证这些插件是否存在；如果为空则返回所有插件
 * @returns 包含插件名称数组和总数的对象
 *
 * 功能说明：
 * 1. 如果未指定插件名称，则扫描所有插件目录
 * 2. 如果指定了插件名称，则验证这些插件是否存在
 * 3. 过滤掉隐藏目录（以 . 开头的目录）
 * 4. 返回排序后的插件名称列表和总数
 */
export function listExtensions(extensionNames?: string[]): {
  extensionNames: string[];
  count: number;
} {
  try {
    if (!fs.existsSync(projectPaths.extensions)) {
      logger.warn(`插件目录不存在: ${projectPaths.extensions}`);
      return { extensionNames: [], count: 0 };
    }

    // 如果没有指定插件名称，则扫描所有插件目录
    if (!extensionNames || extensionNames.length === 0) {
      const entries = fs.readdirSync(projectPaths.extensions, { withFileTypes: true });
      const allExtensionNames = entries
        .filter((entry) => entry.isDirectory() && !entry.name.startsWith('.'))
        .map((entry) => entry.name)
        .sort();

      return {
        extensionNames: allExtensionNames,
        count: allExtensionNames.length,
      };
    }

    // 如果指定了插件名称，则验证这些插件是否存在
    const validExtensionNames: string[] = [];
    const invalidExtensionNames: string[] = [];

    for (const extensionName of extensionNames) {
      const extensionPath = path.join(projectPaths.extensions, extensionName);
      if (fs.existsSync(extensionPath) && fs.statSync(extensionPath).isDirectory()) {
        validExtensionNames.push(extensionName);
      } else {
        invalidExtensionNames.push(extensionName);
      }
    }

    if (invalidExtensionNames.length > 0) {
      throw new Error(`以下插件不存在: ${invalidExtensionNames.join(', ')}`);
    }

    return {
      extensionNames: validExtensionNames.sort(),
      count: validExtensionNames.length,
    };
  } catch (error) {
    const message = error instanceof Error ? error.message : String(error);
    logger.error(`扫描插件目录失败: ${message}`);
    throw error;
  }
}

/**
 * @description 加载并处理插件的配置，返回有效的变体信息
 * @param extensionNames 可选的插件名称列表，如果为空则处理所有插件
 * @returns 包含插件变体信息和统计数据的对象
 *
 * 功能说明：
 * 1. 获取目标插件列表（如果未指定则获取所有插件）
 * 2. 遍历每个插件，动态导入其配置文件
 * 3. 验证配置文件的有效性和结构
 * 4. 提取变体目标列表并统计数量
 * 5. 返回插件变体映射和统计信息
 */
export async function listExtensionVariants(extensionNames?: string[]): Promise<{
  extensions: Record<string, string[]>;
  stats: {
    extensionCount: number;
    variantTargetCount: number;
  };
}> {
  const { extensionNames: targetExtensions } = listExtensions(extensionNames);
  const extensions: Record<string, string[]> = {};
  let totalVariantCount = 0;

  for (const extensionName of targetExtensions) {
    try {
      const configPath = path.join(projectPaths.extensions, extensionName, 'extension.config.ts');
      if (!fs.existsSync(configPath)) {
        continue; // Skip directories without a config file
      }

      const configModule = await import(pathToFileURL(configPath).href);
      const allProcessedVariants = configModule.default as Record<string, ProcessedVariantConfig>;

      if (!allProcessedVariants || typeof allProcessedVariants !== 'object') {
        logger.warn(`[${extensionName}] 配置文件没有默认导出或不是对象类型`);
        continue;
      }

      const variantTargets = Object.keys(allProcessedVariants);

      if (variantTargets.length > 0) {
        extensions[extensionName] = variantTargets;
        totalVariantCount += variantTargets.length;
      }
    } catch (error) {
      const message = error instanceof Error ? error.message : String(error);
      logger.warn(`[${extensionName}] 配置处理失败: ${message}`);
    }
  }

  return {
    extensions,
    stats: {
      extensionCount: Object.keys(extensions).length,
      variantTargetCount: totalVariantCount,
    },
  };
}

/**
 * @description 清理插件的 .variants 缓存目录
 * @param extensionNames 可选的插件名称列表，如果为空则清理所有插件
 *
 * 功能说明：
 * 1. 获取目标插件列表（如果未指定则获取所有插件）
 * 2. 遍历每个插件，检查其 .variants 目录是否存在
 * 3. 如果存在则删除整个 .variants 目录
 * 4. 记录清理操作的日志信息
 */
export async function cleanExtensionConfigs(extensionNames?: string[]): Promise<void> {
  const { extensionNames: targetExtensions } = listExtensions(extensionNames);
  for (const extensionName of targetExtensions) {
    const variantsDir = path.join(projectPaths.extensions, extensionName, '.variants');
    if (await fs.pathExists(variantsDir)) {
      await fs.remove(variantsDir);
      logger.info(`已清理: ${extensionName}/.variants`);
    }
  }
}

export function matchKey(key: string, include?: string[], exclude?: string[]): boolean {
  if (exclude?.some((pattern) => simpleMatch(key, pattern))) {
    return false;
  }
  return !include || include.length === 0 || include.some((pattern) => simpleMatch(key, pattern));
}

// #endregion

// #region --- Variant Target 解析 ---

export function genVariantTarget(
  webstore: WebstoreType,
  variant: VariantType,
  manifestVersion: ManifestVersionType,
): string {
  const mv = `mv${manifestVersion}`;
  if (
    !SUPPORT_WEBSTORES.includes(webstore) ||
    !SUPPORT_VARIANTS.includes(variant) ||
    !SUPPORT_MVS.includes(mv)
  ) {
    throw new Error(`Invalid variant components: ${webstore}, ${variant}, mv${manifestVersion}`);
  }
  return `${webstore}-${mv}-${variant}`;
}

export function parseVariantTarget(variantTarget: string): VariantInfo {
  const parts = variantTarget.split('-');
  if (parts.length !== 3) throw new Error(`Invalid variantTarget format: ${variantTarget}`);

  const [webstore, mv, variant] = parts;
  const parsedMV = Number(mv.slice(2)) as ManifestVersionType;

  const regeneratedTarget = genVariantTarget(
    webstore as WebstoreType,
    variant as VariantType,
    parsedMV,
  );
  if (regeneratedTarget !== variantTarget) {
    throw new Error(`Mismatch in parsed variantTarget: ${variantTarget}`);
  }

  return {
    webstore: webstore as WebstoreType,
    mv,
    variant: variant as VariantType,
    target: variantTarget,
    'webstore-variant': `${webstore}-${variant}`,
  };
}

// #endregion
