/**
 * @fileoverview 核心配置处理器
 * @description 负责扩展配置的合并、验证和处理，是配置系统的核心组件
 */

import { isEmpty } from 'lodash-es';
import {
  DEFAULT_CHROME_ONLY_KEYS,
  DEFAULT_CHROME_ONLY_LOCALES,
  SUPPORT_MANIFEST_VERSIONS,
  SUPPORT_VARIANTS,
  SUPPORT_WEBSTORES,
  WEBSTORE_CN,
  WEBSTORES_UPDATE_URL,
} from './constants.js';
import { getAvailableLocales } from './i18n-processor.js';
import type {
  BaseManifestConfig,
  ExtensionConfig,
  I18nConfig,
  ManifestConfig,
  ManifestVersionType,
  ProcessedI18nConfig,
  ProcessedVariantConfig,
  ValidationResult,
  VariantConfig,
  WebstoreCNType,
} from './types.js';
import { generatePaths, genVariantTarget, separatePermissions } from './utils.js';
import { compileUrlTemplate, deepMerge } from '../helpers/utils.js';

// #region --- 配置解析与合并 ---

/**
 * @description 解析 Manifest 版本号
 * @param ext 扩展配置对象
 * @param variant 变体配置对象
 * @returns Manifest 版本号
 *
 * 优先级顺序（从高到低）：
 * 1. variant.manifest.manifestVersion
 * 2. variant.manifestVersion
 * 3. ext.manifest.manifestVersion
 * 4. ext.manifestVersion
 * 5. 默认值 3
 */
function resolveManifestVersion(ext: ExtensionConfig, variant: VariantConfig): ManifestVersionType {
  return (
    variant.manifest?.manifestVersion ??
    variant.manifestVersion ??
    ext.manifest?.manifestVersion ??
    ext.manifestVersion ??
    3
  );
}

/**
 * @description 解析默认语言
 * @param ext 扩展配置对象
 * @param variant 变体配置对象
 * @returns 默认语言代码
 *
 * 优先级顺序（从高到低）：
 * 1. variant.manifest.defaultLocale
 * 2. variant.defaultLocale
 * 3. ext.manifest.defaultLocale
 * 4. ext.defaultLocale
 * 5. 默认值 'en'
 */
function resolveDefaultLocale(ext: ExtensionConfig, variant: VariantConfig): string {
  return (
    variant.manifest?.defaultLocale ??
    variant.defaultLocale ??
    ext.manifest?.defaultLocale ??
    ext.defaultLocale ??
    'en'
  );
}

function resolveMeasurementId(ext: ExtensionConfig, variant: VariantConfig): string | undefined {
  return variant.measurementId ?? ext.measurementId;
}

function mergeManifests(
  base: ManifestConfig = {},
  override: ManifestConfig = {},
  processed: Partial<ProcessedVariantConfig>,
): BaseManifestConfig {
  const merged = deepMerge(base, override);

  // --- 自动填充字段 ---
  merged.version = processed.version;
  merged.manifest_version = processed.manifestVersion;
  merged.default_locale = processed.defaultLocale;

  merged.homepage_url = compileUrlTemplate(
    'https://www.aliprice.com?ext_id={variantID}&platform={variantName}&channel={channel}&browser={webstore}&version={version}&mv={manifestVersion}',
    {
      variantID: processed.variantID,
      variantName: processed.variantName,
      channel: processed.variantChannel,
      webstore: processed.webstore,
      version: processed.version,
      manifestVersion: processed.manifestVersion,
    },
  );

  if (processed.webstore) {
    merged.update_url = compileUrlTemplate(WEBSTORES_UPDATE_URL[processed.webstore], {
      extensionName: processed.variantName,
    });
  }

  // --- 权限分离 ---
  const { regularPermissions, hostPermissions } = separatePermissions(merged.permissions || []);
  merged.permissions = regularPermissions;
  merged.host_permissions = [...(merged.host_permissions || []), ...hostPermissions];

  const { regularPermissions: optionalRegular, hostPermissions: optionalHost } =
    separatePermissions(merged.optional_permissions || []);
  merged.optional_permissions = optionalRegular;
  merged.optional_host_permissions = [...(merged.optional_host_permissions || []), ...optionalHost];

  // --- 清理空值 ---
  Object.keys(merged).forEach((key) => {
    const k = key as keyof typeof merged;
    if (isEmpty(merged[k])) {
      delete merged[k];
    }
  });

  return merged as BaseManifestConfig;
}

function mergeI18n(
  base: I18nConfig = {},
  override: I18nConfig = {},
  extensionName: string,
): Omit<ProcessedI18nConfig, 'vueMessages' | 'chromeMessages'> {
  const merged = deepMerge(
    base as Record<string, unknown>,
    override as Record<string, unknown>,
  ) as I18nConfig;

  if (merged.locales && Array.isArray(merged.locales) && merged.locales.length > 0) {
    console.warn(
      `[${extensionName}] Manual i18n.locales config is ignored. Locales are now automatically scanned from the 'locales/' directory.`,
    );
  }

  return {
    locales: getAvailableLocales(extensionName),
    includeKeys: merged.includeKeys ?? [],
    excludeKeys: merged.excludeKeys ?? [],
    chromeOnlyLocales: merged.chromeOnlyLocales ?? [...DEFAULT_CHROME_ONLY_LOCALES],
    chromeOnlyKeys: [...DEFAULT_CHROME_ONLY_KEYS, ...(merged.chromeOnlyKeys ?? [])],
  };
}

// #endregion

// #region --- 验证 ---

function validate(config: ProcessedVariantConfig): ValidationResult {
  const errors: string[] = [];
  const warnings: string[] = [];

  // --- 基本字段 ---
  if (!config.name?.trim()) errors.push('Name is required.');
  if (!config.version?.trim()) errors.push('Version is required.');
  if (!config.variantID?.trim()) errors.push('Variant ID is required.');
  if (!config.variantName?.trim()) errors.push('Variant name is required.');
  if (!config.variantType?.trim()) errors.push('Variant type is required.');
  if (!config.variantTarget?.trim()) errors.push('Variant target is required.');
  if (!config.webstore?.trim()) errors.push('Webstore is required.');

  // --- 特殊字段 ---
  if (config.webstore && !SUPPORT_WEBSTORES.includes(config.webstore)) {
    errors.push(`Unsupported webstore: ${config.webstore}`);
  }
  if (config.variantType && !SUPPORT_VARIANTS.includes(config.variantType)) {
    errors.push(`Unsupported variant type: ${config.variantType}`);
  }
  if (config.manifestVersion && !SUPPORT_MANIFEST_VERSIONS.includes(config.manifestVersion)) {
    errors.push(`Unsupported manifest version: ${config.manifestVersion}`);
  }
  if (
    config.variantTarget &&
    !genVariantTarget(config.webstore, config.variantType, config.manifestVersion)
  ) {
    errors.push(`Invalid variant target: ${config.variantTarget}`);
  }

  return { isValid: errors.length === 0, errors, warnings };
}

// #endregion

// #region --- 主处理器 ---

export function processConfig(rawConfig: ExtensionConfig): Record<string, ProcessedVariantConfig> {
  const processedVariants: Record<string, ProcessedVariantConfig> = {};
  const allVariantTargets = new Set<string>();

  for (const variantConfig of rawConfig.variants) {
    const manifestVersion = resolveManifestVersion(rawConfig, variantConfig);
    const variantTarget = genVariantTarget(
      variantConfig.webstore,
      variantConfig.variantType,
      manifestVersion,
    );

    if (allVariantTargets.has(variantTarget)) {
      console.warn(
        `[${rawConfig.name}] Duplicate variantTarget "${variantTarget}" detected. This may lead to unexpected behavior.`,
      );
    }
    allVariantTargets.add(variantTarget);

    const i18nMeta = mergeI18n(rawConfig.i18n, variantConfig.i18n, rawConfig.name);

    const partial: Partial<ProcessedVariantConfig> = {
      name: rawConfig.name,
      version: rawConfig.version,
      manifestVersion,
      defaultLocale: resolveDefaultLocale(rawConfig, variantConfig),
      measurementId: resolveMeasurementId(rawConfig, variantConfig),
      variantID: variantConfig.variantId,
      variantName: variantConfig.variantName,
      variantType: variantConfig.variantType,
      variantChannel:
        variantConfig.variantType === 'offline'
          ? `${variantConfig.webstore}_offline`
          : variantConfig.webstore,
      variantTarget,
      webstore: variantConfig.webstore,
      webstoreId: variantConfig.webstoreId,
      webstoreUrl: variantConfig.webstoreUrl,
      webstoreCN: (WEBSTORE_CN[variantConfig.webstore] || variantConfig.webstore) as WebstoreCNType,
    };

    const manifest = mergeManifests(rawConfig.manifest, variantConfig.manifest, partial);

    const finalConfig: ProcessedVariantConfig = {
      name: partial.name!,
      version: partial.version!,
      manifestVersion: partial.manifestVersion!,
      defaultLocale: partial.defaultLocale!,
      variantID: partial.variantID!,
      variantName: partial.variantName!,
      variantType: partial.variantType!,
      variantChannel: partial.variantChannel!,
      webstore: partial.webstore!,
      webstoreCN: partial.webstoreCN!,
      variantTarget: partial.variantTarget!,
      measurementId: partial.measurementId,
      webstoreId: partial.webstoreId,
      webstoreUrl: partial.webstoreUrl,
      manifest,
      i18n: {
        ...i18nMeta,
        vueMessages: {}, // Will be populated by the generator
        chromeMessages: {}, // Will be populated by the generator
      },
      paths: generatePaths(rawConfig.name, variantTarget, rawConfig.version),
    };

    // 验证配置
    const validation = validate(finalConfig);
    if (!validation.isValid) {
      console.error(
        `[${rawConfig.name}] Validation failed for variant "${variantTarget}":`,
        validation.errors.join('\n'),
      );
      continue; // Skip this variant if validation fails
    }

    processedVariants[variantTarget] = finalConfig;
  }

  return processedVariants;
}

// #endregion
