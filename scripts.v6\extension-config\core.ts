/**
 * @fileoverview 插件配置管理模块的核心处理逻辑
 * @description 实现 defineExtensionConfig 函数、配置验证器和配置扁平化合并
 */

import { merge } from 'lodash-es';
import { createLogger } from '../shared/logger.js';
import type {
  WebstoreType,
  VariantType,
  ManifestVersionType,
  VariantChannel,
  WebstoreCNType,
} from './types.js';
import {
  SUPPORT_WEBSTORES,
  SUPPORT_VARIANTS,
  SUPPORT_MANIFEST_VERSIONS,
  WEBSTORE_CN,
} from './types.js';

const logger = createLogger('ExtensionConfig');

export function defineExtensionConfig() {}
