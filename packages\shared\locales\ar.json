{"EXTENSION_NAME": {"message": "TODO: EXTENSION_NAME"}, "EXTENSION_DESCRIPTION": {"message": "TODO: EXTENSION_DESCRIPTION"}, "1688_cross_border_hot_selling": {"message": "1688 نقطة بيع ساخنة عبر الحدود"}, "1688_shi_li_ren_zheng": {"message": "1688 شهادة القوة"}, "1_jian_qi_pi": {"message": "1:<PERSON><PERSON><PERSON>"}, "1year_yi_shang": {"message": "أكثر من 1 سنة"}, "24H": {"message": "24H"}, "24H_fa_huo": {"message": "التسليم خلال 24 ساعة"}, "24H_lan_shou_lv": {"message": "معدل التعبئة والتغليف على مدار 24 ساعة"}, "30D_shang_xin": {"message": "المنتجات الجديدة الشهرية"}, "30d_sales": {"message": "تم بيع $amount$ خلال 30 يومًا", "placeholders": {"amount": {"content": "$1"}}}, "3Min_xiang_ying_lv": {"message": "الرد خلال 3 دقائق."}, "3Min_xiang_ying_lv__desc": {"message": "نسبة ردود <PERSON> الفعالة على رسائل استفسار المشتري خلال 3 دقائق في آخر 30 يومًا"}, "48H": {"message": "48 ح"}, "48H_fa_huo": {"message": "التسليم خلال 48 ساعة"}, "48H_lan_shou_lv": {"message": "معدل التعبئة والتغليف لمدة 48 ساعة"}, "48H_lan_shou_lv__desc": {"message": "نسبة رقم الطلب الذي تم استلامه خلال 48 ساعة إلى إجمالي عدد الطلبات"}, "48H_lv_yue_lv": {"message": "معدل أداء 48 ساعة"}, "48H_lv_yue_lv__desc": {"message": "نسبة رقم الطلب الذي تم استلامه أو تسليمه خلال 48 ساعة إلى إجمالي عدد الطلبات"}, "72H": {"message": "72 ح"}, "7D_shang_xin": {"message": "المنتجات الجديدة الأسبوعية"}, "7D_wu_li_you": {"message": "7 أيام رعاية مجانية"}, "ABS_title_text": {"message": "يتضمن هذا الإعلان قصة علامة تجارية"}, "AC_title_text": {"message": "يحتوي هذا الإعلان على شارة اختيار أمازون"}, "A_title_text": {"message": "يحتوي هذا الإعلان على صفحة محتوى A+"}, "BS_title_text": {"message": "يُصنف هذا الإعلان باعتباره $num$ الأكثر مبيعًا في فئة $type$", "placeholders": {"type": {"content": "$1"}, "num": {"content": "$2"}}}, "LTD_title_text": {"message": "يعني مصطلح \"LTD (صفقة محدودة الوقت)\" أن هذا الإعلان جزء من حدث \"ترويج لمدة 7 أيام\""}, "NR_title_text": {"message": "يُصنف هذا الإعلان باعتباره $num$ الإصدار الجديد في فئة $type$", "placeholders": {"type": {"content": "$1"}, "num": {"content": "$2"}}}, "SBV_title_text": {"message": "يحتوي هذا الإعلان على إعلان فيديو، وهو نوع من إعلانات الدفع لكل نقرة يظهر عادةً في منتصف نتائج البحث"}, "SB_title_text": {"message": "يحتوي هذا الإعلان على إعلان علامة تجارية، وهو نوع من إعلانات الدفع لكل نقرة يظهر عادةً في أعلى أو أسفل نتائج البحث"}, "SP_title_text": {"message": "يحتوي هذا الإعلان على إعلان منتج برعاية"}, "V_title_text": {"message": "يحتوي هذا الإعلان على مقدمة فيديو"}, "advanced_research": {"message": "البحث المتقدم"}, "agent_ds1688___my_order": {"message": "طلباتي"}, "agent_ds1688__add_to_cart": {"message": "الشراء من الخارج"}, "agent_ds1688__cart": {"message": "عربة التسوق"}, "agent_ds1688__desc": {"message": "تم تقديمه بواسطة 1688. وهو يدعم الشراء المباشر من الخارج، والدفع بالدولار الأمريكي والتسليم إلى مستودع النقل الخاص بك في الصين."}, "agent_ds1688__freight": {"message": "حاسبة تكلفة الشحن"}, "agent_ds1688__help": {"message": "يساعد"}, "agent_ds1688__packages": {"message": "بيان الشحنة"}, "agent_ds1688__profile": {"message": "المركز الشخصي"}, "agent_ds1688__warehouse": {"message": "مستودع بلدي"}, "ai_comment_analysis_advantage": {"message": "الإيجابيات"}, "ai_comment_analysis_ai": {"message": "تحليل المراجعة بالذكاء الاصطناعي"}, "ai_comment_analysis_available": {"message": "متوفرة"}, "ai_comment_analysis_balance": {"message": "عدد غير كافٍ من العملات، يرجى إعادة الشحن"}, "ai_comment_analysis_behavior": {"message": "السلوك"}, "ai_comment_analysis_characteristic": {"message": "خصائص الحشد"}, "ai_comment_analysis_comment": {"message": "لا يحتوي المنتج على تقييمات كافية لاستخلاص استنتاجات دقيقة، يرجى تحديد منتج يحتوي على تقييمات أكثر."}, "ai_comment_analysis_consume": {"message": "الاستهلاك المقدر"}, "ai_comment_analysis_default": {"message": "التقييمات الافتراضية"}, "ai_comment_analysis_desire": {"message": "توقعات العملاء"}, "ai_comment_analysis_disadvantage": {"message": "السلبيات"}, "ai_comment_analysis_free": {"message": "محاولات مجانية"}, "ai_comment_analysis_freeNum": {"message": "سيتم استخدام رصيد مجاني واحد"}, "ai_comment_analysis_go_recharge": {"message": "الانتقال إلى إعادة الشحن"}, "ai_comment_analysis_intelligence": {"message": "تحليل المراجعة الذكي"}, "ai_comment_analysis_location": {"message": "الموقع"}, "ai_comment_analysis_motive": {"message": "دافع الشراء"}, "ai_comment_analysis_network_error": {"message": "خطأ في الشبكة، يرجى المحاولة مرة أخرى"}, "ai_comment_analysis_normal": {"message": "التقييمات المصورة"}, "ai_comment_analysis_number_reviews": {"message": "عدد المراجعات: $num$، الاستهلاك المقدر: $consume$", "placeholders": {"num": {"content": "$1"}, "consume": {"content": "$2"}}}, "ai_comment_analysis_ordinary": {"message": "تعليقات عامة"}, "ai_comment_analysis_percentage": {"message": "النسبة المئوية"}, "ai_comment_analysis_problem": {"message": "مشاكل في الدفع"}, "ai_comment_analysis_reanalysis": {"message": "إعادة التحليل"}, "ai_comment_analysis_reason": {"message": "السبب"}, "ai_comment_analysis_recharge": {"message": "إعادة الشحن"}, "ai_comment_analysis_recharged": {"message": "لق<PERSON> قمت بإعادة الشحن"}, "ai_comment_analysis_retry": {"message": "إعادة المحاولة"}, "ai_comment_analysis_scene": {"message": "سيناريو الاستخدام"}, "ai_comment_analysis_start": {"message": "بدء التحليل"}, "ai_comment_analysis_subject": {"message": "المواضيع"}, "ai_comment_analysis_time": {"message": "وقت الاستخدام"}, "ai_comment_analysis_tool": {"message": "أداة الذكاء الاصطناعي"}, "ai_comment_analysis_user_portrait": {"message": "ملف تعريف المستخدم"}, "ai_comment_analysis_welcome": {"message": "مرحبًا بك في تحليل المراجعة بالذكاء الاصطناعي"}, "ai_comment_analysis_year": {"message": "تعليقات من العام الماضي"}, "ai_listing_Exclude_keywords": {"message": "استبعاد الكلمات الرئيسية"}, "ai_listing_Login_the_feature": {"message": "مطلوب تسجيل الدخول لهذه الميزة"}, "ai_listing_aI_generation": {"message": "جيل الذكاء الاصطناعي"}, "ai_listing_add_automatic": {"message": "تلقائي"}, "ai_listing_add_dictionary_new": {"message": "إنشاء مكتبة جديدة"}, "ai_listing_add_enter_keywords": {"message": "أدخل الكلمات الرئيسية"}, "ai_listing_add_inputkey_selling": {"message": "أدخل نقطة بيع واضغط على $key$ لإنهاء الإضافة", "placeholders": {"key": {"content": "$1"}}}, "ai_listing_add_inputkey_warning": {"message": "تم تجاوز الحد، بما يصل إلى $amount$ من نقاط البيع", "placeholders": {"amount": {"content": "$1"}}}, "ai_listing_add_keywords": {"message": "أضف كلمات رئيسية"}, "ai_listing_add_manually": {"message": "إضافة يدويا"}, "ai_listing_add_selling": {"message": "إضافة نقاط البيع"}, "ai_listing_added_keywords": {"message": "الكلمات الرئيسية المضافة"}, "ai_listing_added_successfully": {"message": "اضي<PERSON> بنجاح"}, "ai_listing_addexcluded_keywords": {"message": "أدخل الكلمات الرئيسية المستبعدة، اضغط على زر الإدخال لإنهاء الإضافة."}, "ai_listing_adding_selling": {"message": "نقاط البيع المضافة"}, "ai_listing_addkeyword_enter": {"message": "اكتب كلمات السمات الرئيسية واضغط على زر الإدخال لإنهاء الإضافة"}, "ai_listing_ai_description": {"message": "مكتبة الكلمات الوصفية بالذكاء الاصطناعي"}, "ai_listing_ai_dictionary": {"message": "مكتبة كلمات العنوان لمنظمة العفو الدولية"}, "ai_listing_ai_title": {"message": "عنوان منظمة العفو الدولية"}, "ai_listing_aidescription_repeated": {"message": "لا يمكن تكرار اسم مكتبة الكلمات الوصفية لمنظمة العفو الدولية"}, "ai_listing_aititle_repeated": {"message": "لا يمكن تكرار اسم مكتبة كلمات عنوان AI"}, "ai_listing_data_comes_from": {"message": "تأتي هذه البيانات من:"}, "ai_listing_deleted_successfully": {"message": "حذ<PERSON> بنجاح"}, "ai_listing_dictionary_name": {"message": "اسم المكتبة"}, "ai_listing_edit_dictionary": {"message": "تعديل المكتبة..."}, "ai_listing_edit_word_library": {"message": "تحرير مكتبة الكلمات"}, "ai_listing_enter_keywords": {"message": "أدخل الكلمات الرئيسية واضغط على $key$ لإنهاء الإضافة", "placeholders": {"key": {"content": "$1"}}}, "ai_listing_exceeded_maximum": {"message": "تم تجاوز الحد الأقصى للكلمات الرئيسية $amount$", "placeholders": {"amount": {"content": "$1"}}}, "ai_listing_excluded_word_library": {"message": "مكتبة الكلمات المستبعدة"}, "ai_listing_generate_characters": {"message": "توليد الشخصيات"}, "ai_listing_generation_platform": {"message": "منصة الجيل"}, "ai_listing_help_optimize": {"message": "ساعدني في تحسين عنوان المنتج، العنوان الأصلي هو"}, "ai_listing_include_selling": {"message": "وشملت نقاط البيع الأخرى:"}, "ai_listing_included_keyword": {"message": "الكلمات الرئيسية المضمنة"}, "ai_listing_included_keywords": {"message": "الكلمات الرئيسية المضمنة"}, "ai_listing_input_selling": {"message": "أدخل نقطة بيع"}, "ai_listing_input_selling_fit": {"message": "أدخل نقاط البيع لتتناسب مع العنوان"}, "ai_listing_input_selling_please": {"message": "الرجاء إدخال نقاط البيع"}, "ai_listing_intelligently_title": {"message": "أد<PERSON><PERSON> المحتوى المطلوب أعلاه لإنشاء العنوان بذكاء"}, "ai_listing_keyword_product_title": {"message": "عنوان المنتج الكلمة الرئيسية"}, "ai_listing_keywords_repeated": {"message": "لا يمكن تكرار الكلمات الرئيسية"}, "ai_listing_listed_selling_points": {"message": "تشمل نقاط البيع"}, "ai_listing_long_title_1": {"message": "يحتوي على معلومات أساسية مثل اسم العلامة التجارية ونوع المنتج وميزات المنتج وما إلى ذلك."}, "ai_listing_long_title_2": {"message": "على أساس عنوان المنتج القياسي، تتم إضافة الكلمات الرئيسية التي تساعد على تحسين محركات البحث."}, "ai_listing_long_title_3": {"message": "بالإضافة إلى احتواء اسم العلامة التجارية ونوع المنتج وميزات المنتج والكلمات الرئيسية، يتم أيضًا تضمين الكلمات الرئيسية الطويلة لتحقيق تصنيفات أعلى في استعلامات بحث محددة ومجزأة."}, "ai_listing_longtail_keyword_product_title": {"message": "عنوان المنتج ذو الكلمة الرئيسية الطويلة"}, "ai_listing_manually_enter": {"message": "ادخل يدويا..."}, "ai_listing_network_not_working": {"message": "الإنترنت غير متوفر، مطلوب VPN للوصول إلى ChatGPT"}, "ai_listing_new_dictionary": {"message": "إنشاء مكتبة كلمات جديدة..."}, "ai_listing_new_generate": {"message": "يولد"}, "ai_listing_optional_words": {"message": "كلمات اختيارية"}, "ai_listing_original_title": {"message": "العنوان الأصلي"}, "ai_listing_other_keywords_included": {"message": "وشملت الكلمات الرئيسية الأخرى:"}, "ai_listing_please_again": {"message": "حاول مرة اخرى"}, "ai_listing_please_select": {"message": "تم إنشاء العناوين التالية لك، يرجى الاختيار:"}, "ai_listing_product_category": {"message": "فئة المنتج"}, "ai_listing_product_category_is": {"message": "فئة المنتج هي"}, "ai_listing_product_category_to": {"message": "ما هي الفئة التي ينتمي إليها المنتج؟"}, "ai_listing_random_keywords": {"message": "كلمات رئيسية عشوائية $amount$", "placeholders": {"amount": {"content": "$1"}}}, "ai_listing_random_selling": {"message": "نقاط بيع عشوائية بقيمة $amount$", "placeholders": {"amount": {"content": "$1"}}}, "ai_listing_randomize_keywords": {"message": "عشوائية من مكتبة الكلمات"}, "ai_listing_search_selling": {"message": "البحث عن طريق نقطة البيع"}, "ai_listing_select_product_categories": {"message": "تحديد فئات المنتجات تلقائيًا."}, "ai_listing_select_product_selling_points": {"message": "تحديد نقاط بيع المنتج تلقائيًا"}, "ai_listing_select_word_library": {"message": "ح<PERSON><PERSON> مكتبة الكلمات"}, "ai_listing_selling": {"message": "نقاط بيع - مرا<PERSON>ز البيع"}, "ai_listing_selling_ask": {"message": "ما هي متطلبات نقطة البيع الأخرى الموجودة للعنوان؟"}, "ai_listing_selling_optional": {"message": "نقاط بيع اختيارية"}, "ai_listing_selling_repeat": {"message": "لا يمكن تكرار النقاط"}, "ai_listing_set_excluded": {"message": "تعيين كمكتبة كلمات مستبعدة"}, "ai_listing_set_include_selling_points": {"message": "تضمين نقاط البيع"}, "ai_listing_set_included": {"message": "تعيين كمكتبة كلمات مضمنة"}, "ai_listing_set_selling_dictionary": {"message": "تعيين كمكتبة نقطة بيع"}, "ai_listing_standard_product_title": {"message": "عنوان المنتج القياسي"}, "ai_listing_translated_title": {"message": "عنوان مترجم"}, "ai_listing_visit_chatGPT": {"message": "قم بزيارة ChatGPT"}, "ai_listing_what_other_keywords": {"message": "ما هي الكلمات الرئيسية الأخرى المطلوبة للعنوان؟"}, "aliprice_coupons_apply_again": {"message": "تقدم مرة أخرى"}, "aliprice_coupons_apply_coupons": {"message": "تطبيق القسائم"}, "aliprice_coupons_apply_success": {"message": "القسيمة التي تم العثور عليها: وفر $amount$", "placeholders": {"amount": {"content": "$1"}}}, "aliprice_coupons_applying": {"message": "اختبار الأكواد لأفضل الصفقات ..."}, "aliprice_coupons_applying_desc": {"message": "تسجيل المغادرة: $code$", "placeholders": {"code": {"content": "$1"}}}, "aliprice_coupons_back_to_checkout": {"message": "الاستمرار في الخروج"}, "aliprice_coupons_found_coupons": {"message": "تم العثور على قسائم $amount$", "placeholders": {"amount": {"content": "$1"}}}, "aliprice_coupons_found_coupons_desc": {"message": "جاهز للسداد؟ دعونا نتأكد\nتحصل على أفضل سعر!"}, "aliprice_coupons_no_coupon_aviable": {"message": "هذه الرموز لا تعمل.\nليس بالأمر الكبير - أنت بالفعل\nالحصول على أفضل سعر."}, "aliprice_coupons_toolbar_btn": {"message": "احصل على كوبونات"}, "aliww_translate": {"message": "مترجم الدردشة Aliwangwang"}, "aliww_translate_supports": {"message": "الدعم: 1688 وتاوباو"}, "amazon_extended_keywords_Keywords": {"message": "الكلمات الرئيسية"}, "amazon_extended_keywords_copy_all": {"message": "نسخ الكل"}, "amazon_extended_keywords_more": {"message": "المزيد من"}, "an_lei_ji_xiao_liang_pai_xu": {"message": "فرز حسب المبيعات التراكمية"}, "an_lei_xing_cha_kan": {"message": "يكتب"}, "an_yue_dai_xiao_pai_xu": {"message": "الترتيب حسب مبيعات دروبشيبينغ"}, "apra_btn__cat_name": {"message": "تحليل المراجعات"}, "apra_chart__name": {"message": "النسبة المئوية لمبيعات المنتج حسب البلد"}, "apra_chart__update_at": {"message": "وقت التحديث $time$", "placeholders": {"time": {"content": "$1"}}}, "apra_name": {"message": "إحصائيات مبيعات الدول"}, "auto_opening": {"message": "يتم الفتح تلقائيًا في غضون $num$ ثانية", "placeholders": {"num": {"content": "$1"}}}, "auto_page_next_x_pages": {}, "average_days_listed": {"message": "متوسط أيام الرف"}, "average_hui_fu_lv": {"message": "متوسط ​​معدل الرد"}, "average_ping_gong_ying_shang_deng_ji": {"message": "متوسط ​​مستوى المورد"}, "average_price": {"message": "متوسط ​​السعر"}, "average_qi_ding_liang": {"message": "متوسط ​​موك"}, "average_rating": {"message": "متوسط ​​تقييم"}, "average_ren_zheng_gong_ying_nian_chang": {"message": "متوسط ​​سنوات الشهادة"}, "average_revenue": {"message": "متوسط ​​العائد"}, "average_revenue_per_product": {"message": "إجمالي الإيرادات ÷ عدد المنتجات"}, "average_sales": {"message": "متوسط المبيعات"}, "average_sales_per_product": {"message": "إجمالي المبيعات ÷ عدد المنتجات"}, "bao_han": {"message": "يتضمن"}, "bao_zheng_jin": {"message": "هامِش"}, "bian_ti_shu": {"message": "الاختلافات"}, "biao_ti": {"message": "عنوان"}, "blacklist_add_blacklist": {"message": "حظر هذا المتجر"}, "blacklist_address_incorrect": {"message": "العنوان غير صحيح. رجاءا تأكد."}, "blacklist_blacked_out": {"message": "تم حظر المتجر"}, "blacklist_blacklist": {"message": "القائمة السوداء"}, "blacklist_no_records_yet": {"message": "لا يوجد سجل حتى الآن!"}, "blue_rocket": {"message": "로켓배송"}, "brand_name": {"message": "العلامة التجارية"}, "btn_aliprice_agent__daigou": {"message": "وسيط شراء"}, "btn_aliprice_agent__dropshipping": {"message": "إسقاط الشحن"}, "btn_have_a_try": {"message": "جربه الآن"}, "btn_refresh": {"message": "تحديث"}, "btn_try_it_now": {"message": "جربه الآن"}, "btn_txt_view_on_aliprice": {"message": "عر<PERSON> ع<PERSON>ى AliPrice"}, "bu_bao_han": {"message": "لا يحتوي"}, "bulk_copy_links": {"message": "روابط النسخ بالجملة"}, "bulk_copy_products": {"message": "نسخ المنتجات بالجملة"}, "cai_gou_zi_xun": {"message": "خدمة الزبائن"}, "cai_gou_zi_xun__desc": {"message": "معدل استجابة البائع لمدة ثلاث دقائق"}, "can_ping_lei_xing": {"message": "النوع"}, "cao_zuo": {"message": "عملية"}, "chan_pin_ID": {"message": "معر<PERSON> المنتج"}, "chan_pin_e_wai_xin_xi": {"message": "منتج معلومات إضافية"}, "chan_pin_lian_jie": {"message": "رابط المنتج"}, "cheng_li_shi_jian": {"message": "وقت التأسيس"}, "city__aba": {"message": "Aba"}, "city__aksu": {"message": "<PERSON><PERSON><PERSON>"}, "city__alaer": {"message": "<PERSON><PERSON><PERSON>"}, "city__altai": {"message": "Altai"}, "city__alxaleague": {"message": "Alxa League"}, "city__ankang": {"message": "Ankan<PERSON>"}, "city__anqing": {"message": "Anqing"}, "city__anshan": {"message": "<PERSON><PERSON>"}, "city__anshun": {"message": "<PERSON><PERSON><PERSON>"}, "city__anyang": {"message": "Anyang"}, "city__baicheng": {"message": "Baicheng"}, "city__baise": {"message": "Baise"}, "city__baisha": {"message": "Baisha"}, "city__baishan": {"message": "Baishan"}, "city__baiyin": {"message": "<PERSON><PERSON><PERSON>"}, "city__baoding": {"message": "<PERSON>od<PERSON>"}, "city__baoji": {"message": "<PERSON><PERSON><PERSON>"}, "city__baoshan": {"message": "<PERSON><PERSON><PERSON>"}, "city__baoting": {"message": "Baoting"}, "city__baotou": {"message": "<PERSON><PERSON><PERSON>"}, "city__bayannur": {"message": "Bayannur"}, "city__bayinguoleng": {"message": "Bayinguoleng"}, "city__bazhong": {"message": "Bazhong"}, "city__beihai": {"message": "Beihai"}, "city__bengbu": {"message": "<PERSON><PERSON><PERSON>"}, "city__benxi": {"message": "Benxi"}, "city__bijie": {"message": "Bijie"}, "city__binzhou": {"message": "Binzhou"}, "city__bortala": {"message": "<PERSON><PERSON><PERSON>"}, "city__bozhou": {"message": "Bozhou"}, "city__cangzhou": {"message": "Cangzhou"}, "city__chamdo": {"message": "<PERSON><PERSON><PERSON>"}, "city__changchun": {"message": "<PERSON><PERSON><PERSON>"}, "city__changde": {"message": "<PERSON><PERSON>"}, "city__changhua": {"message": "Changhua"}, "city__changji": {"message": "Changji"}, "city__changjiang": {"message": "Changjiang"}, "city__changsha": {"message": "Changsha"}, "city__changzhi": {"message": "Changzhi"}, "city__changzhou": {"message": "Changzhou"}, "city__chaohu": {"message": "<PERSON><PERSON>"}, "city__chaoyang": {"message": "Chaoyang"}, "city__chaozhou": {"message": "Chaozhou"}, "city__chengde": {"message": "Chengde"}, "city__chengdu": {"message": "Chengdu"}, "city__chengmai": {"message": "<PERSON><PERSON><PERSON>"}, "city__chenzhou": {"message": "Chenzhou"}, "city__chiayi": {"message": "<PERSON><PERSON><PERSON>"}, "city__chifeng": {"message": "<PERSON><PERSON>"}, "city__chizhou": {"message": "Chizhou"}, "city__chongzuo": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__chuxiong": {"message": "Chuxiong"}, "city__chuzhou": {"message": "Chuzhou"}, "city__dali": {"message": "<PERSON><PERSON>"}, "city__dalian": {"message": "<PERSON><PERSON>"}, "city__dandong": {"message": "Dandong"}, "city__danzhou": {"message": "Danzhou"}, "city__daqing": {"message": "Daqing"}, "city__datong": {"message": "<PERSON><PERSON><PERSON>"}, "city__daxinganling": {"message": "<PERSON><PERSON>'<PERSON>ling"}, "city__dazhou": {"message": "Dazhou"}, "city__dehong": {"message": "<PERSON><PERSON>"}, "city__deyang": {"message": "Deyang"}, "city__dezhou": {"message": "Dezhou"}, "city__dingan": {"message": "Ding'an"}, "city__dingxi": {"message": "Dingxi"}, "city__diqing": {"message": "Diqing"}, "city__dongfang": {"message": "<PERSON><PERSON>g"}, "city__dongguan": {"message": "Dongguan"}, "city__dongying": {"message": "<PERSON><PERSON>"}, "city__enshi": {"message": "<PERSON><PERSON>"}, "city__ezhou": {"message": "Ezhou"}, "city__fangchenggang": {"message": "Fangchenggang"}, "city__foshan": {"message": "<PERSON><PERSON><PERSON>"}, "city__fushun": {"message": "<PERSON><PERSON><PERSON>"}, "city__fuxin": {"message": "Fuxin"}, "city__fuyang": {"message": "Fuyang"}, "city__fuzhou": {"message": "Fuzhou"}, "city__gannan": {"message": "<PERSON><PERSON><PERSON>"}, "city__ganzhou": {"message": "Ganzhou"}, "city__ganzi": {"message": "Ganzi"}, "city__guangan": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__guangyuan": {"message": "Guangyuan"}, "city__guangzhou": {"message": "Guangzhou"}, "city__guigang": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__guilin": {"message": "<PERSON><PERSON><PERSON>"}, "city__guiyang": {"message": "Guiyang"}, "city__guolok": {"message": "Guolok"}, "city__guyuan": {"message": "<PERSON><PERSON>"}, "city__haibei": {"message": "Haibei"}, "city__haidong": {"message": "Haidong"}, "city__haikou": {"message": "Hai<PERSON><PERSON>"}, "city__hainan": {"message": "Hainan"}, "city__haixi": {"message": "Haixi"}, "city__hami": {"message": "<PERSON><PERSON>"}, "city__handan": {"message": "<PERSON><PERSON>"}, "city__hangzhou": {"message": "Hangzhou"}, "city__hanzhong": {"message": "Hanzhong"}, "city__harbin": {"message": "<PERSON><PERSON><PERSON>"}, "city__hebi": {"message": "<PERSON><PERSON>"}, "city__hechi": {"message": "<PERSON><PERSON>"}, "city__hefei": {"message": "<PERSON><PERSON><PERSON>"}, "city__hegang": {"message": "<PERSON><PERSON><PERSON>"}, "city__heihe": {"message": "<PERSON><PERSON><PERSON>"}, "city__hengshui": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__hengyang": {"message": "Hengyang"}, "city__heyuan": {"message": "<PERSON><PERSON>"}, "city__heze": {"message": "<PERSON><PERSON>"}, "city__hezhou": {"message": "Hezhou"}, "city__hohhot": {"message": "Hohhot"}, "city__honghe": {"message": "<PERSON><PERSON>"}, "city__hongkongisland": {"message": "Hong Kong Island"}, "city__hotan": {"message": "<PERSON><PERSON>"}, "city__hsinchu": {"message": "<PERSON><PERSON><PERSON>"}, "city__huaian": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__huaibei": {"message": "<PERSON><PERSON><PERSON>"}, "city__huaihua": {"message": "Huaihua"}, "city__huaisouth": {"message": "Huai South"}, "city__hualien": {"message": "<PERSON><PERSON><PERSON>"}, "city__huanggang": {"message": "<PERSON><PERSON><PERSON>"}, "city__huangnan": {"message": "Huangnan"}, "city__huangshan": {"message": "<PERSON><PERSON>"}, "city__huangshi": {"message": "<PERSON><PERSON>"}, "city__huizhou": {"message": "Huizhou"}, "city__huludao": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__hulunbuir": {"message": "Hulunbuir"}, "city__huzhou": {"message": "Huzhou"}, "city__jiamusi": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__jian": {"message": "<PERSON><PERSON><PERSON>"}, "city__jiangmen": {"message": "Jiangmen"}, "city__jiaozuo": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__jiaxing": {"message": "Jiaxing"}, "city__jiayuguan": {"message": "Jiayuguan"}, "city__jieyang": {"message": "<PERSON><PERSON><PERSON>"}, "city__jilin": {"message": "<PERSON><PERSON>"}, "city__jinan": {"message": "<PERSON><PERSON>"}, "city__jinchang": {"message": "Jinchang"}, "city__jincheng": {"message": "Jincheng"}, "city__jingdezhen": {"message": "Jingdez<PERSON>"}, "city__jingmen": {"message": "Jingmen"}, "city__jingzhou": {"message": "Jingzhou"}, "city__jinhua": {"message": "Jinhua"}, "city__jining": {"message": "Jining"}, "city__jinzhong": {"message": "Jinzhong"}, "city__jinzhou": {"message": "Jinzhou"}, "city__jiujiang": {"message": "Jiujiang"}, "city__jiuquan": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__jixi": {"message": "Ji<PERSON>"}, "city__kaifeng": {"message": "Kaifeng"}, "city__kaohsiung": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__karamay": {"message": "<PERSON><PERSON><PERSON>"}, "city__kashgar": {"message": "Ka<PERSON><PERSON>"}, "city__keelung": {"message": "Keelung"}, "city__kinmen": {"message": "Kinmen"}, "city__kizilsu": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__kowloon": {"message": "Kowloon"}, "city__kunming": {"message": "Ku<PERSON><PERSON>"}, "city__laibin": {"message": "<PERSON><PERSON>"}, "city__laiwu": {"message": "<PERSON><PERSON>"}, "city__langfang": {"message": "<PERSON><PERSON><PERSON>"}, "city__lanzhou": {"message": "Lanzhou"}, "city__ledong": {"message": "<PERSON><PERSON>"}, "city__leshan": {"message": "<PERSON><PERSON>"}, "city__lhasa": {"message": "Lhasa"}, "city__liangshan": {"message": "Liangshan"}, "city__lianyungang": {"message": "Lianyungang"}, "city__liaocheng": {"message": "<PERSON><PERSON><PERSON>"}, "city__liaoyang": {"message": "<PERSON><PERSON><PERSON>"}, "city__liaoyuan": {"message": "<PERSON><PERSON><PERSON>"}, "city__lienchiang": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__lijiang": {"message": "Lijiang"}, "city__lincang": {"message": "Lincang"}, "city__linfen": {"message": "Linfen"}, "city__lingao": {"message": "Lingao"}, "city__lingshui": {"message": "<PERSON><PERSON><PERSON>"}, "city__linxia": {"message": "Lin<PERSON>"}, "city__linyi": {"message": "<PERSON><PERSON>"}, "city__lishui": {"message": "Li<PERSON><PERSON>"}, "city__liupanshui": {"message": "Liupanshu<PERSON>"}, "city__liuzhou": {"message": "Liuzhou"}, "city__longnan": {"message": "<PERSON><PERSON>"}, "city__longyan": {"message": "<PERSON><PERSON>"}, "city__loudi": {"message": "<PERSON><PERSON>"}, "city__luan": {"message": "<PERSON><PERSON><PERSON>"}, "city__luohe": {"message": "<PERSON><PERSON><PERSON>"}, "city__luoyang": {"message": "<PERSON><PERSON><PERSON>"}, "city__luzhou": {"message": "Luzhou"}, "city__lüliang": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__maanshan": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__macauoutlyingislands": {"message": "Macau Outlying Islands"}, "city__macaupeninsula": {"message": "Macau Peninsula"}, "city__maoming": {"message": "<PERSON><PERSON>"}, "city__meishan": {"message": "<PERSON><PERSON>"}, "city__meizhou": {"message": "Meizhou"}, "city__mianyang": {"message": "Mianyang"}, "city__miaoli": {"message": "<PERSON><PERSON>"}, "city__mudanjiang": {"message": "Mudanjiang"}, "city__nagqu": {"message": "<PERSON>g<PERSON>u"}, "city__nanchang": {"message": "Nanchang"}, "city__nanchong": {"message": "<PERSON><PERSON><PERSON>"}, "city__nanjing": {"message": "Nanjing"}, "city__nanning": {"message": "Nanning"}, "city__nanping": {"message": "<PERSON><PERSON>"}, "city__nantong": {"message": "Nantong"}, "city__nantou": {"message": "<PERSON><PERSON><PERSON>"}, "city__nanyang": {"message": "Nanyang"}, "city__neijiang": {"message": "Neijiang"}, "city__newterritories": {"message": "New Territories"}, "city__ngari": {"message": "<PERSON><PERSON>"}, "city__ningbo": {"message": "Ningbo"}, "city__ningde": {"message": "<PERSON><PERSON><PERSON>"}, "city__nujiang": {"message": "Nujiang"}, "city__nyingchi": {"message": "Nyingchi"}, "city__ordos": {"message": "Ordos"}, "city__panjin": {"message": "Panjin"}, "city__panzhihua": {"message": "Pan Zhihua"}, "city__penghu": {"message": "<PERSON><PERSON><PERSON>"}, "city__pingdingshan": {"message": "Pingdingshan"}, "city__pingliang": {"message": "<PERSON><PERSON><PERSON>"}, "city__pingtung": {"message": "<PERSON><PERSON><PERSON>"}, "city__pingxiang": {"message": "<PERSON><PERSON><PERSON>"}, "city__puer": {"message": "<PERSON>u'er"}, "city__putian": {"message": "<PERSON><PERSON>"}, "city__puyang": {"message": "P<PERSON><PERSON>"}, "city__qiandongnan": {"message": "Qiandongnan"}, "city__qianjiang": {"message": "Qianjiang"}, "city__qiannan": {"message": "<PERSON><PERSON><PERSON>"}, "city__qianxinan": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__qingdao": {"message": "Qingdao"}, "city__qingyang": {"message": "Qingyang"}, "city__qingyuan": {"message": "Qingyuan"}, "city__qinhuangdao": {"message": "Qinhuangdao"}, "city__qinzhou": {"message": "Qinzhou"}, "city__qionghai": {"message": "Qionghai"}, "city__qiongzhong": {"message": "Qiongzhong"}, "city__qiqihar": {"message": "Qiqihar"}, "city__qitaihe": {"message": "<PERSON><PERSON><PERSON>"}, "city__quanzhou": {"message": "Quanzhou"}, "city__qujing": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__quzhou": {"message": "Quzhou"}, "city__rizhao": {"message": "<PERSON><PERSON><PERSON>"}, "city__sanmenxia": {"message": "Sanmenxia"}, "city__sanming": {"message": "Sanming"}, "city__sanya": {"message": "Sanya"}, "city__shangluo": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__shangqiu": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__shangrao": {"message": "<PERSON><PERSON><PERSON>"}, "city__shannan": {"message": "Shannan"}, "city__shantou": {"message": "<PERSON><PERSON><PERSON>"}, "city__shanwei": {"message": "Shanwei"}, "city__shaoguan": {"message": "Shaoguan"}, "city__shaoxing": {"message": "Shaoxing"}, "city__shaoyang": {"message": "Shaoyang"}, "city__shennongjiaforestarea": {"message": "Shennongjia Forest Area"}, "city__shenyang": {"message": "Shenyang"}, "city__shenzhen": {"message": "Shenzhen"}, "city__shigatse": {"message": "Shigat<PERSON>"}, "city__shihezi": {"message": "<PERSON><PERSON><PERSON>"}, "city__shijiazhuang": {"message": "Shijiazhuang"}, "city__shiyan": {"message": "<PERSON><PERSON>"}, "city__shizuishan": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__shuangyashan": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__shuozhou": {"message": "Shuozhou"}, "city__siping": {"message": "<PERSON><PERSON>"}, "city__songyuan": {"message": "Songyuan"}, "city__suihua": {"message": "Su<PERSON>ua"}, "city__suining": {"message": "Suining"}, "city__suizhou": {"message": "<PERSON><PERSON><PERSON>"}, "city__suqian": {"message": "<PERSON><PERSON><PERSON>"}, "city__suzhou": {"message": "Suzhou"}, "city__tacheng": {"message": "Tacheng"}, "city__taian": {"message": "Tai'an"}, "city__taichung": {"message": "Taichung"}, "city__tainan": {"message": "Tainan"}, "city__taipei": {"message": "Taipei"}, "city__taitung": {"message": "<PERSON><PERSON><PERSON>"}, "city__taiyuan": {"message": "Taiyuan"}, "city__taizhou": {"message": "Taizhou"}, "city__tangshan": {"message": "Tangshan"}, "city__taoyuan": {"message": "Taoyuan"}, "city__tianmen": {"message": "Tianmen"}, "city__tianshui": {"message": "<PERSON><PERSON><PERSON>"}, "city__tieling": {"message": "Tieling"}, "city__tongchuan": {"message": "<PERSON><PERSON><PERSON>"}, "city__tonghua": {"message": "Tonghua"}, "city__tongliao": {"message": "<PERSON><PERSON><PERSON>"}, "city__tongling": {"message": "Tongling"}, "city__tongren": {"message": "<PERSON><PERSON>"}, "city__tumushuke": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__tunchang": {"message": "Tunchang"}, "city__turpan": {"message": "<PERSON><PERSON><PERSON>"}, "city__ulanqab": {"message": "Ulanqab"}, "city__urumqi": {"message": "Urumqi"}, "city__wanning": {"message": "Wanning"}, "city__weifang": {"message": "<PERSON><PERSON><PERSON>"}, "city__weihai": {"message": "Weihai"}, "city__weinan": {"message": "Weinan"}, "city__wenchang": {"message": "Wenchang"}, "city__wenshan": {"message": "<PERSON><PERSON>"}, "city__wenzhou": {"message": "Wenzhou"}, "city__wuhai": {"message": "Wu<PERSON>"}, "city__wuhan": {"message": "<PERSON><PERSON>"}, "city__wuhu": {"message": "<PERSON><PERSON>"}, "city__wujiaqu": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__wuwei": {"message": "<PERSON><PERSON>"}, "city__wuxi": {"message": "Wuxi"}, "city__wuzhishan": {"message": "Wuzhishan"}, "city__wuzhong": {"message": "Wuzhong"}, "city__wuzhou": {"message": "Wuzhou"}, "city__xiamen": {"message": "Xiamen"}, "city__xian": {"message": "Xi'<PERSON>"}, "city__xiangfan": {"message": "<PERSON><PERSON><PERSON>"}, "city__xiangtan": {"message": "Xiangtan"}, "city__xiangxi": {"message": "Xiangxi"}, "city__xianning": {"message": "Xianning"}, "city__xiantao": {"message": "Xiantao"}, "city__xianyang": {"message": "<PERSON><PERSON><PERSON>"}, "city__xiaogan": {"message": "<PERSON><PERSON>"}, "city__xilingol": {"message": "Xilingol"}, "city__xinganleague": {"message": "Xing'an League"}, "city__xingtai": {"message": "Xingtai"}, "city__xining": {"message": "Xining"}, "city__xinxiang": {"message": "Xinxiang"}, "city__xinyang": {"message": "Xinyang"}, "city__xinyu": {"message": "Xinyu"}, "city__xinzhou": {"message": "Xinzhou"}, "city__xishuangbanna": {"message": "Xishuangbanna"}, "city__xuancheng": {"message": "Xuancheng"}, "city__xuchang": {"message": "Xuchang"}, "city__xuzhou": {"message": "Xuzhou"}, "city__yaan": {"message": "Ya'an"}, "city__yanan": {"message": "Yan'<PERSON>"}, "city__yanbian": {"message": "Yanbian"}, "city__yancheng": {"message": "Yancheng"}, "city__yangjiang": {"message": "Yangjiang"}, "city__yangquan": {"message": "<PERSON><PERSON><PERSON>"}, "city__yangzhou": {"message": "Yangzhou"}, "city__yantai": {"message": "Yantai"}, "city__yibin": {"message": "<PERSON><PERSON>"}, "city__yichang": {"message": "Yichang"}, "city__yichun": {"message": "<PERSON><PERSON><PERSON>"}, "city__yilan": {"message": "Yilan"}, "city__yili": {"message": "<PERSON><PERSON>"}, "city__yinchuan": {"message": "<PERSON><PERSON><PERSON>"}, "city__yingkou": {"message": "<PERSON><PERSON><PERSON>"}, "city__yingtan": {"message": "Yingtan"}, "city__yiwu": {"message": "<PERSON><PERSON>"}, "city__yiyang": {"message": "Yiyang"}, "city__yongzhou": {"message": "Yongzhou"}, "city__yueyang": {"message": "<PERSON><PERSON><PERSON>"}, "city__yulin": {"message": "Yulin"}, "city__yuncheng": {"message": "Yuncheng"}, "city__yunfu": {"message": "<PERSON><PERSON>"}, "city__yunlin": {"message": "<PERSON><PERSON>"}, "city__yushu": {"message": "Yushu"}, "city__yuxi": {"message": "Yuxi"}, "city__zaozhuang": {"message": "Zaozhuang"}, "city__zhangjiajie": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__zhangjiakou": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__zhangye": {"message": "<PERSON><PERSON>"}, "city__zhangzhou": {"message": "Zhangzhou"}, "city__zhanjiang": {"message": "Zhanjiang"}, "city__zhaoqing": {"message": "Zhaoqing"}, "city__zhaotong": {"message": "Zhaotong"}, "city__zhengzhou": {"message": "Zhengzhou"}, "city__zhenjiang": {"message": "Zhenjiang"}, "city__zhongshan": {"message": "Zhongshan"}, "city__zhongwei": {"message": "Zhongwei"}, "city__zhoukou": {"message": "<PERSON><PERSON><PERSON>"}, "city__zhoushan": {"message": "Zhoushan"}, "city__zhuhai": {"message": "Zhu<PERSON>"}, "city__zhumadian": {"message": "Zhumadian"}, "city__zhuzhou": {"message": "Zhuzhou"}, "city__zibo": {"message": "Zibo"}, "city__zigong": {"message": "Zigong"}, "city__ziyang": {"message": "<PERSON><PERSON><PERSON>"}, "city__zunyi": {"message": "<PERSON><PERSON><PERSON>"}, "click_copy_product_info": {"message": "ثم انقر فوق نسخ معلومات المنتج"}, "commmon_txt_expired": {"message": "منتهية الصلاحية"}, "common__date_range_12m": {"message": "سنة واحدة"}, "common__date_range_1m": {"message": "شهر واحد"}, "common__date_range_1w": {"message": "أسبوع 1"}, "common__date_range_2w": {"message": "2 أسابيع"}, "common__date_range_3m": {"message": "3 اشهر"}, "common__date_range_3w": {"message": "3 أسابيع"}, "common__date_range_6m": {"message": "6 اشهر"}, "common_btn_cancel": {"message": " إلغاء"}, "common_btn_close": {"message": "أغلق"}, "common_btn_save": {"message": "<PERSON><PERSON><PERSON>"}, "common_btn_setting": {"message": "الإعداد"}, "common_email": {"message": "الب<PERSON>يد الإلكتروني"}, "common_error_msg_no_data": {"message": " لا توجد بيانات"}, "common_error_msg_no_result": {"message": " عذرا ، لم يتم العثور على نتيجة."}, "common_favorites": {"message": "المفضلة"}, "common_feedback": {"message": "ر<PERSON><PERSON><PERSON> الفعل"}, "common_help": {"message": "مساعدة"}, "common_loading": {"message": "جا<PERSON>ى التحميل"}, "common_login": {"message": "تسجيل الدخول"}, "common_logout": {"message": "  تسجيل خروج"}, "common_no": {"message": "لا"}, "common_powered_by_aliprice": {"message": "مدعوم من AliPrice.com"}, "common_setting": {"message": "ضبط"}, "common_sign_up": {"message": " تسجيل"}, "common_system_upgrading_title": {"message": "ترقية النظام"}, "common_system_upgrading_txt": {"message": "يرجى المحاولة لاحقًا"}, "common_txt__currency": {"message": "عملة"}, "common_txt__video_tutorial": {"message": "فيديو تعليمي"}, "common_txt_ago_time": {"message": "$time$ منذ أيام", "placeholders": {"time": {"content": "$1"}}}, "common_txt_all_product": {"message": "الجميع"}, "common_txt_analysis": {"message": "تحليل"}, "common_txt_basically_used": {"message": "لم يتم استخدامه تقريبًا"}, "common_txt_biaoti_link": {"message": "العنوان + الرابط"}, "common_txt_biaoti_link_dian_pu": {"message": "العنوان + الرابط + اسم المتجر"}, "common_txt_blacklist": {"message": "قائمة الحظر"}, "common_txt_cancel": {"message": "يلغي"}, "common_txt_category": {"message": "فئة"}, "common_txt_chakan": {"message": "ي<PERSON><PERSON><PERSON>"}, "common_txt_colors": {"message": "الألوان"}, "common_txt_confirm": {"message": "يتأكد"}, "common_txt_copied": {"message": "نسخ"}, "common_txt_copy": {"message": "نسخ"}, "common_txt_copy_link": {"message": "ان<PERSON><PERSON> الرابط"}, "common_txt_copy_title": {"message": "نسخة العنوان"}, "common_txt_copy_title__link": {"message": "انسخ العنوان والرابط"}, "common_txt_day": {"message": "سماء"}, "common_txt_day__short": {"message": "D"}, "common_txt_delete": {"message": "<PERSON><PERSON><PERSON>"}, "common_txt_dian_pu_link": {"message": "انسخ اسم المتجر + الرابط"}, "common_txt_download": {"message": "تحميل"}, "common_txt_downloaded": {"message": "تحميل"}, "common_txt_export_as_csv": {"message": "تصدير Excel"}, "common_txt_export_as_txt": {"message": "تصدير النص"}, "common_txt_fail": {"message": "يفشل"}, "common_txt_format": {"message": "شكل"}, "common_txt_get": {"message": "يحصل"}, "common_txt_incert_selection": {"message": "اختيار المقلوب"}, "common_txt_install": {"message": "تثبيت"}, "common_txt_load_failed": {"message": "فشل في التحميل"}, "common_txt_month": {"message": "شهر واحد"}, "common_txt_more": {"message": "أكثر"}, "common_txt_new_unused": {"message": "جديد تمامًا، غير مستعمل"}, "common_txt_next": {"message": "التالى"}, "common_txt_no_limit": {"message": "<PERSON>ير محدود"}, "common_txt_no_noticeable": {"message": "لا يوجد خدوش أو أوساخ مرئية"}, "common_txt_on_sale": {"message": "متاح"}, "common_txt_opt_in_out": {"message": "تشغيل/إيقاف"}, "common_txt_order": {"message": "ترتيب"}, "common_txt_others": {"message": "الآخرين"}, "common_txt_overall_poor_condition": {"message": "حالة سيئة بشكل عام"}, "common_txt_patterns": {"message": "أنماط"}, "common_txt_platform": {"message": "المنصات"}, "common_txt_please_select": {"message": "ير<PERSON><PERSON> التحديد"}, "common_txt_prev": {"message": "السابق"}, "common_txt_price": {"message": "سعر"}, "common_txt_privacy_policy": {"message": "سياسة الخصوصية"}, "common_txt_product_condition": {"message": "حالة المنتج"}, "common_txt_rating": {"message": "تقييم"}, "common_txt_ratings": {"message": "التقييمات"}, "common_txt_reload": {"message": "ዳግም ጫን"}, "common_txt_reset": {"message": "إعادة ضبط"}, "common_txt_retail": {"message": "التجزئه"}, "common_txt_review": {"message": "مراجعة"}, "common_txt_sale": {"message": "متاح"}, "common_txt_same": {"message": "نفسه"}, "common_txt_scratches_and_dirt": {"message": "مع الخدوش والأوساخ"}, "common_txt_search_title": {"message": "عنو<PERSON> البحث"}, "common_txt_select_all": {"message": "اختر الكل"}, "common_txt_selected": {"message": "المحدد"}, "common_txt_share": {"message": "شارك"}, "common_txt_sold": {"message": "تم البيع"}, "common_txt_sold_out": {"message": "نفذ"}, "common_txt_some_scratches": {"message": "بعض الخدوش والأوساخ"}, "common_txt_sort_by": {"message": "صن<PERSON> حسب"}, "common_txt_state": {"message": "حالة"}, "common_txt_success": {"message": "نجاح"}, "common_txt_sys_err": {"message": "خطأ في النظام"}, "common_txt_today": {"message": "اليوم"}, "common_txt_total": {"message": "الكل"}, "common_txt_unselect_all": {"message": "اختيار المقلوب"}, "common_txt_upload_image": {"message": "تحميل الصور"}, "common_txt_visit": {"message": "يزور"}, "common_txt_whitelist": {"message": "القائمة البيضاء"}, "common_txt_wholesale": {"message": "بالجملة"}, "common_txt_wildberries": {"message": "Wildberries"}, "common_txt_year": {"message": "سنة"}, "common_yes": {"message": "نعم"}, "compare_tool_btn_clear_all": {"message": "ا<PERSON><PERSON><PERSON> الكل"}, "compare_tool_btn_compare": {"message": "قارن"}, "compare_tool_btn_contact": {"message": "اتصل"}, "compare_tool_tips_max_compared": {"message": "أضف ما يصل إلى $maxComparedCount$", "placeholders": {"maxComparedCount": {"content": "$1"}}}, "configure_notifiactions": {"message": "تكوين الإخطارات"}, "contact_us": {"message": "اتصل بنا"}, "context_menu_screenshot_search": {"message": "التقاط للبحث عن طريق الصورة"}, "context_menus_aliprice_search_by_image": {"message": " البحث عن صورة على AliPrice"}, "context_menus_goote_trans": {"message": "ترجمة الصفحة/إظهار الأصل"}, "context_menus_search_by_image": {"message": "البحث بالصورة على بابا", "placeholders": {}}, "context_menus_search_by_image_capture": {"message": "القبض على علي بابا", "placeholders": {}}, "context_menus_translator": {"message": "التقاط للترجمة"}, "converter_modal_amount_placeholder": {"message": "أد<PERSON>ل المبلغ هنا"}, "converter_modal_btn_convert": {"message": "يتحول"}, "converter_modal_exchange_rate_source": {"message": "تأتي البيانات من $boc$ سعر الصرف الأجنبي وقت التحديث: $updatedAt$", "placeholders": {"boc": {"content": "$1"}, "updatedAt": {"content": "$2"}}}, "converter_modal_name": {"message": "تحويل العملات"}, "converter_modal_search_placeholder": {"message": "عم<PERSON>ة البحث"}, "copy_all_contact_us_notice": {"message": "هذا الموقع غير مدعوم في الوقت الحالي، يرجى الاتصال بنا"}, "copy_product_info": {"message": "نسخ معلومات المنتج"}, "copy_suggest_search_kw": {"message": "نسخ القوائم المنسدلة"}, "country__han_gou": {"message": "كوريا الجنوبية"}, "country__ri_ben": {"message": "اليابان"}, "country__yue_nan": {"message": "فيتنام"}, "currency_convert__custom": {"message": "سعر الصرف المخصص"}, "currency_convert__sync_server": {"message": "مزامنة الخادم"}, "dang_ri_fa_huo": {"message": "نفس الشحن يوم"}, "dao_chu_quan_dian_shang_pin": {"message": "تصدير جميع منتجات المتجر"}, "dao_chu_wei_CSV": {"message": "يصدّر"}, "dao_chu_zi_duan": {"message": "حقول التصدير"}, "delivery_address": {"message": "عنوان الشحن"}, "delivery_company": {"message": "شركة توصيل"}, "di_zhi": {"message": "عنوان"}, "dian_ji_cha_xun": {"message": "انقر للاستعلام"}, "dian_pu_ID": {"message": "معر<PERSON> المت<PERSON>ر"}, "dian_pu_di_zhi": {"message": "عنوان المتجر"}, "dian_pu_lian_jie": {"message": "رابط المتجر"}, "dian_pu_ming": {"message": "اسم المتجر"}, "dian_pu_ming_cheng": {"message": "اسم المتجر"}, "dian_pu_shang_pin_zong_hsu": {"message": "إجمالي عدد المنتجات في المتجر: $num$", "placeholders": {"num": {"content": "$1"}}}, "dian_pu_xin_xi": {"message": "معلومات المتجر"}, "ding_zai_zuo_ce": {"message": "مس<PERSON>ر إلى اليسار"}, "download_image__SKU_variant_images": {"message": "صور متغيرات SKU"}, "download_image__assume": {"message": "على سبيل المثال، لدينا صورتان، product1.jpg وproduct2.gif.\nسيتم إعادة تسمية img_{$no$} إلى img_01.jpg، img_02.gif؛\nسيتم إعادة تسمية {$group$}_{$no$} إلى main_image_01.jpg، main_image_02.gif؛", "placeholders": {"no": {"content": "$3"}, "group": {"content": "$2"}}}, "download_image__batch_download": {"message": "تنزيل الدفعة"}, "download_image__combined_image": {"message": "صورة المنتج التفصيلية المدمجة"}, "download_image__continue_downloading": {"message": "متابعة التنزيل"}, "download_image__description_images": {"message": "صور الوصف"}, "download_image__download_combined_image": {"message": "تنزيل صورة المنتج التفصيلية المدمجة"}, "download_image__download_zip": {"message": "تنزيل بصيغة zip"}, "download_image__enlarge_check": {"message": "يدعم فقط صور JPEG وJPG وGIF وPNG، الحد الأقصى لحجم الصورة الواحدة: 1600 * 1600"}, "download_image__enlarge_image": {"message": "تكبير الصورة"}, "download_image__export": {"message": "يصدّر"}, "download_image__height": {"message": "الارتفاع"}, "download_image__ignore_videos": {"message": "تم تجاهل الفيديو حيث لا يمكن تصديره"}, "download_image__img_translate": {"message": "ترجمة الصور"}, "download_image__main_image": {"message": "الصورة الرئيسية"}, "download_image__multi_folder": {"message": "مجلدات متعددة"}, "download_image__name": {"message": "تحميل الصورة"}, "download_image__notice_content": {"message": "من فضلك لا تحدد \"السؤال عن مكان حفظ كل ملف قبل التنزيل\" في إعدادات التنزيل بالمتصفح الخاص بك!!! وإلا سيكون هناك الكثير من مربعات الحوار."}, "download_image__notice_ignore": {"message": "لا تطالب بهذه الرسالة مرة أخرى"}, "download_image__order_number": {"message": "الرقم التسلسلي {$no$}؛ اسم المجموعة {$group$}؛ الطابع الزمني {$date$}", "placeholders": {"no": {"content": "$1"}, "group": {"content": "$2"}, "date": {"content": "$3"}}}, "download_image__overview": {"message": "ملخص"}, "download_image__prompt_download_zip": {"message": "هناك عدد كبير جدًا من الصور، من الأفضل تنزيلها كمجلد مضغوط."}, "download_image__rename": {"message": "إعادة التسمية"}, "download_image__rule": {"message": "قواعد التسمية"}, "download_image__single_folder": {"message": "مج<PERSON><PERSON> واحد"}, "download_image__sku_image": {"message": "صور SKU"}, "download_image__video": {"message": "فيديو"}, "download_image__width": {"message": "العرض"}, "download_reviews__download_images": {"message": "تنزيل صورة المراجعة"}, "download_reviews__dropdown_title": {"message": "تنزيل صورة المراجعة"}, "download_reviews__export_csv": {"message": "تصدير CSV"}, "download_reviews__no_images": {"message": "نسخة عينة: 0 صور للتحميل"}, "download_reviews__no_reviews": {"message": "لا توجد تعليقات متاحة للتنزيل!"}, "download_reviews__notice": {"message": "نصيحة:"}, "download_reviews__notice__chrome_settings": {"message": "اضبط متصفح Chrome ليسأل عن مكان حفظ كل ملف قبل التنزيل ، واضبطه على \"إيقاف\""}, "download_reviews__notice__wait": {"message": "اعتمادًا على عدد المراجعات ، قد يكون وقت الانتظار أطول"}, "download_reviews__pages_list__all": {"message": "الجميع"}, "download_reviews__pages_list__page": {"message": "صفحات $page$ السابقة", "placeholders": {"page": {"content": "$1"}}}, "download_reviews__pages_list_title": {"message": "نطاق الاختيار"}, "export_shopping_cart__csv_filed__details_url": {"message": "روابط المنتج"}, "export_shopping_cart__csv_filed__details_url_with_sku": {"message": "رابط صدى SKU"}, "export_shopping_cart__csv_filed__images": {"message": "رابط الصورة"}, "export_shopping_cart__csv_filed__quantity": {"message": "كمية"}, "export_shopping_cart__csv_filed__sale_price": {"message": "سعر"}, "export_shopping_cart__csv_filed__specs": {"message": "تحديد"}, "export_shopping_cart__csv_filed__store_name": {"message": "اسم المتجر"}, "export_shopping_cart__csv_filed__store_url": {"message": "رابط المتجر"}, "export_shopping_cart__csv_filed__title": {"message": "اسم المنتج"}, "export_shopping_cart__export_btn": {"message": "يصدّر"}, "export_shopping_cart__export_empty": {"message": "الرجاء اختيار منتج!"}, "fa_huo_shi_jian": {"message": "شحن"}, "favorite_add_email": {"message": "أ<PERSON><PERSON> ال<PERSON><PERSON><PERSON><PERSON> الإلكتروني"}, "favorite_add_favorites": {"message": "إضافة إلى المفضلة"}, "favorite_added": {"message": "وأضاف"}, "favorite_btn_add": {"message": " تنبيه بانخفاض الأسعار."}, "favorite_btn_notify": {"message": "تتبع السعر"}, "favorite_cate_name_all": {"message": "جميع المنتجات"}, "favorite_current_price": {"message": "السعر الحالي"}, "favorite_due_date": {"message": "تاريخ الاستحقاق"}, "favorite_enable_notification": {"message": "الرجاء تفعيل إشعارات البريد الإلكتروني"}, "favorite_expired": {"message": "منتهية الصلاحية"}, "favorite_go_to_enable": {"message": "اذهب لفتح"}, "favorite_msg_add_success": {"message": " أضيفت إلى المفضلة"}, "favorite_msg_del_success": {"message": " محذوف من المفضلة"}, "favorite_msg_failure": {"message": " فشل! قم بتحديث الصفحة وحاول مرة أخرى."}, "favorite_please_add_email": {"message": "الرجاء إضافة بريد إلكتروني"}, "favorite_price_drop": {"message": "أسفل"}, "favorite_price_rise": {"message": "أعلى"}, "favorite_price_untracked": {"message": "السعر غير متتبع"}, "favorite_saved_price": {"message": "السعر المحفوظ"}, "favorite_stop_tracking": {"message": "إلغاء التتبع"}, "favorite_sub_email_address": {"message": "عنوان البريد الإلكتروني للاشتراك"}, "favorite_tracking_period": {"message": "فترة التتبع"}, "favorite_tracking_prices": {"message": "تتبع الأسعار"}, "favorite_verify_email": {"message": "التحقق من البريد الإلكتروني"}, "favorites_list_remove_prompt_msg": {"message": "هل أنت متأكد من حذف ذلك؟"}, "favorites_update_button": {"message": "تحديث الأسعار الآن"}, "fen_lei": {"message": "فئة"}, "fen_xia_yan_xuan": {"message": "اختيار الموزع"}, "find_similar": {"message": "البحث عن مماثلة"}, "first_ali_price_date": {"message": "التاريخ الذي تم فيه التقاطه لأول مرة بواسطة زاحف AliPrice"}, "fooview_coupons_modal_no_data": {"message": "لا كوبونات"}, "fooview_coupons_modal_title": {"message": "كوبونات"}, "fooview_favorites__product_sub__tooltip_l1": {"message": "السعر <$lowerPrice$", "placeholders": {"lowerPrice": {"content": "$1"}}}, "fooview_favorites__product_sub__tooltip_l2": {"message": "أو السعر> $higherPrice$ دولار", "placeholders": {"higherPrice": {"content": "$1"}}}, "fooview_favorites__product_sub__tooltip_l3": {"message": "الموعد النهائي"}, "fooview_favorites_error_msg_no_favorites": {"message": " إضف المنتجات المفضلة هنا لتلقي تنبيه عند انخفاض أسعارها."}, "fooview_favorites_filter_latest": {"message": "الأحدث"}, "fooview_favorites_filter_price_drop": {"message": "<PERSON><PERSON><PERSON> السعر"}, "fooview_favorites_filter_price_up": {"message": "زيادة الأسعار"}, "fooview_favorites_modal_title": {"message": "مفضلاتى"}, "fooview_favorites_modal_title_title": {"message": "انتقل إلى AliPrice"}, "fooview_favorites_track_price": {"message": " لتتبع السعر"}, "fooview_price_history_app_price": {"message": "APP Price :"}, "fooview_price_history_title": {"message": "تاريخ السعر"}, "fooview_product_list_feedback": {"message": "ر<PERSON><PERSON><PERSON> الفعل"}, "fooview_product_list_orders": {"message": "الطلبات"}, "fooview_product_list_price": {"message": "السعر"}, "fooview_reviews_error_msg_no_review": {"message": " لم نعثر على أي تقييمات لهذا المنتج."}, "fooview_reviews_filter_buyer_reviews": {"message": "صور المشترين"}, "fooview_reviews_modal_title": {"message": "التقييمات"}, "fooview_same_product_choose_category": {"message": "اختر فئة"}, "fooview_same_product_filter_feedback": {"message": "ر<PERSON><PERSON><PERSON> الفعل"}, "fooview_same_product_filter_orders": {"message": "الطلبات"}, "fooview_same_product_filter_price": {"message": "السعر"}, "fooview_same_product_filter_rating": {"message": "تقييم"}, "fooview_same_product_modal_title": {"message": " ابحث عن المنتج المماثل"}, "fooview_same_product_search_by_image": {"message": " البحث بالصور"}, "fooview_seller_analysis_modal_title": {"message": " تحليل البائع"}, "for_12_months": {"message": "لمدة عام"}, "for_12_months_list_pro": {"message": "12 شهر"}, "for_12_months_nei": {"message": "خلال 12 شهرًا"}, "for_1_months": {"message": "شهر واحد"}, "for_1_months_nei": {"message": "خلال شهر واحد"}, "for_3_months": {"message": "لمدة 3 أشهر"}, "for_3_months_nei": {"message": "خلال 3 أشهر"}, "for_6_months": {"message": "لمدة 6 أشهر"}, "for_6_months_nei": {"message": "خلال 6 أشهر"}, "for_9_months": {"message": "9 أشهر"}, "for_9_months_nei": {"message": "خلال 9 أشهر"}, "fu_gou_lv": {"message": "معدل إعادة الشراء"}, "gao_liang_bu_tong_dian": {"message": "تسليط الضوء على الاختلافات"}, "gao_liang_guang_gao_chan_pin": {"message": "إبراز منتجات الإعلانات"}, "geng_duo_xin_xi": {"message": "مزيد من المعلومات"}, "geng_xin_shi_jian": {"message": "وقت التحديث"}, "get_store_products_fail_tip": {"message": "انقر فوق موافق للانتقال إلى التحقق للتأكد من الوصول العادي"}, "gong_x_kuan_shang_pin": {"message": "إجمالي المنتجات$amount$", "placeholders": {"amount": {"content": "$1"}}}, "gong_ying_shang": {"message": "المورد"}, "gong_ying_shang_ID": {"message": "واسم المورد"}, "gong_ying_shang_deng_ji": {"message": "تقييم الموردين"}, "gong_ying_shang_nian_zhan": {"message": "المورد أقدم"}, "gong_ying_shang_xin_xi": {"message": "معلومات المورد"}, "gong_ying_shang_zhu_ye_lian_jie": {"message": "رابط الصفحة الرئيسية للمورد"}, "green_rocket": {"message": "로켓프레시"}, "grey_rocket": {"message": "로켓설치"}, "gu_ji_shou_jia": {"message": "سعر البيع المقدر"}, "guan_jian_zi": {"message": "الكلمة الرئيسية"}, "guang_gao_chan_pin": {"message": "منتجات إعلانية"}, "guang_gao_zhan_bi": {"message": "نسبة الإعلان"}, "guo_ji_wu_liu_yun_fei": {"message": "رسوم الشحن الدولي"}, "guo_lv_tiao_jian": {"message": "المرشحات"}, "hao_ping_lv": {"message": "تقييم إيجابي"}, "highest_price": {"message": "اغ<PERSON>ى سعر"}, "historical_trend": {"message": "الاتجاه التاريخي"}, "how_to_screenshot": {"message": "اضغط مع الاستمرار على زر الماوس الأيسر لتحديد المنطقة، ثم اضغط على زر الماوس الأيمن أو مفتاح Esc للخروج من لقطة الشاشة"}, "howt_it_works": {"message": "<PERSON>ي<PERSON> تعمل"}, "hui_fu_lv": {"message": "معدل الاستجابة"}, "hui_tou_lv": {"message": "معدل العائد"}, "inquire_freightFee": {"message": "استفسار الشحن"}, "inquire_freightFee_Yuan": {"message": "الشحن/يوان"}, "inquire_freightFee_province": {"message": "مقاطعة"}, "inquire_freightFee_the": {"message": "الشحن هو $num$ ، مما يعني أن المنطقة لديها شحن مجاني.", "placeholders": {"num": {"content": "$1"}}}, "is_ad": {"message": "إعلان."}, "jia_ge": {"message": "سعر"}, "jia_ge_dan_wei": {"message": "الوحدة"}, "jia_ge_qu_shi": {"message": "اتجاهات"}, "jia_zai_n_ge_shang_pin": {"message": "تحميل $num$ منتج", "placeholders": {"num": {"content": "$1"}}}, "jin_30_tian_xiao_liang_zhan_bi": {"message": "نسبة حجم المبيعات في آخر 30 يومًا"}, "jin_30_tian_xiao_shou_e_zhan_bi": {"message": "نسبة الإيرادات في آخر 30 يومًا"}, "jin_30d_xiao_liang": {"message": "مبيعات"}, "jin_30d_xiao_liang__desc": {"message": "إجمالي المبيعات في آخر 30 يومًا"}, "jin_30d_xiao_shou_e": {"message": "دوران"}, "jin_30d_xiao_shou_e__desc": {"message": "إجمالي حجم التداول في آخر 30 يومًا"}, "jin_90_tian_mai_jia_shu": {"message": "المشترين في آخر 90 يومًا"}, "jin_90_tian_xiao_shou_liang": {"message": "المبيعات في آخر 90 يومًا"}, "jing_xuan_huo_yuan": {"message": "مصادر مختارة"}, "jing_ying_mo_shi": {"message": "نموذج العمل"}, "jing_ying_mo_shi__gong_chang": {"message": "الصانع"}, "jiu_fen_jie_jue": {"message": "حل النزاعات"}, "jiu_fen_jie_jue__desc": {"message": "محاسبة منازعات حقوق المتجر للبائعين"}, "jiu_fen_lv": {"message": "معدل النزاع"}, "jiu_fen_lv__desc": {"message": "نسبة الطلبات ذات الشكاوى المكتملة خلال الثلاثين يومًا الماضية والتي تم الحكم عليها بأنها مسؤولية البائع أو كلا الطرفين"}, "kai_dian_ri_qi": {"message": "تاريخ الافتتاح"}, "keywords": {"message": "الكلمات الدالة"}, "kua_jin_Select_pan_huo": {"message": "اختيار عبر الحدود"}, "last15_days": {"message": "آ<PERSON>ر 15 يومًا"}, "last180_days": {"message": "آخر 180 يومًا"}, "last30_days": {"message": "في آخر 30 يومًا"}, "last360_days": {"message": "آخر 360 يومًا"}, "last45_days": {"message": "<PERSON><PERSON>ر 45 يومًا"}, "last60_days": {"message": "<PERSON><PERSON><PERSON> 60 يومًا"}, "last7_days": {"message": "آخر 7 أيام"}, "last90_days": {"message": "<PERSON><PERSON><PERSON> 90 يومًا"}, "last_30d_sales": {"message": "مبيعات آخر 30 يومًا"}, "lei_ji": {"message": "تراكمي"}, "lei_ji_xiao_liang": {"message": "المجموع"}, "lei_ji_xiao_liang__desc": {"message": "جميع المبيعات بعد المنتج على الرف"}, "lei_ji_xiao_liang_pai_xu__desc": {"message": "حجم المبيعات التراكمي في آخر 30 يومًا، مرتبة من الأعلى إلى الأقل"}, "lian_xi_fang_shi": {"message": "معلومات الاتصال"}, "list_time": {"message": "في تاريخ الرف"}, "load_more": {"message": "تحميل المزيد"}, "login_to_aliprice": {"message": "قم بتسجيل الدخول إلى AliPrice"}, "long_link": {"message": "رابط طويل"}, "lowest_price": {"message": "أقل سعر"}, "mai_jia_shu": {"message": "الباعة"}, "mao_li_lv": {"message": "الهامش الإجمالي"}, "mobile_view__dkxbqy": {"message": "فتح علامة تبويب جديدة"}, "mobile_view__sjdxq": {"message": "التفاصيل في التطبيق"}, "mobile_view__sjdxqy": {"message": "صفحة التفاصيل في التطبيق"}, "mobile_view__smck": {"message": "المسح الضوئي للعرض"}, "mobile_view__smckms": {"message": "يرجى استخدام الكاميرا أو التطبيق للمسح الضوئي والعرض"}, "modified_failed": {"message": "فشل التعديل"}, "modified_successfully": {"message": "تم التعديل بنجاح"}, "nav_btn_favorites": {"message": "مجموعاتي"}, "nav_btn_package": {"message": "صفقة"}, "nav_btn_product_info": {"message": "عن المنتج"}, "nav_btn_viewed": {"message": "تمت المشاهدة"}, "no_suggest_search_kw": {"message": "Loading..."}, "none": {"message": "لا شيء"}, "normal_link": {"message": "<PERSON><PERSON><PERSON><PERSON> عادي"}, "notice": {"message": "ملحوظة"}, "number_reviews": {"message": "التعليقات"}, "only_show_num": {"message": "إجمالي المنتجات: $allnum$، مخفي: $hidenum2$", "placeholders": {"allnum": {"content": "$1"}, "hidenum2": {"content": "$2"}}}, "only_show_selected": {"message": "إزالة غير محدد"}, "open": {"message": "فتح"}, "open_links": {"message": "فتح الروابط"}, "options_page_tab_check_links": {"message": "تحقق من الروابط"}, "options_page_tab_gernal": {"message": "جنرال لواء"}, "options_page_tab_notifications": {"message": "إشعارات"}, "options_page_tab_others": {"message": "الآخرين"}, "options_page_tab_sbi": {"message": " البحث بالصور"}, "options_page_tab_shortcuts": {"message": "اختصارات"}, "options_page_tab_shortcuts_title": {"message": "حجم الخط للاختصارات"}, "options_page_tab_similar_products": {"message": "نفسه"}, "orange_rocket": {"message": "판매자로켓"}, "order_list_open_links_tip": {"message": "ستفتح روابط منتجات متعددة قريبًا"}, "order_list_sku_show_title": {"message": "إظهار المتغيرات المحددة في الروابط المشتركة"}, "orders_last30_days": {"message": "عد<PERSON> الطلبات في آخر 30 يومًا"}, "pTutorial_favorites_block1_desc1": {"message": "المنتجات التي تتبعها مدرجة هنا"}, "pTutorial_favorites_block1_title": {"message": "المفضلة"}, "pTutorial_popup_block1_desc1": {"message": "التسمية الخضراء تعني أن هناك منتجات انخفضت الأسعار"}, "pTutorial_popup_block1_title": {"message": "اختصارات والمفضلة"}, "pTutorial_price_history_block1_desc1": {"message": "انقر فوق \"تتبع السعر\" ، إضافة منتجات إلى المفضلة. بمجرد انخفاض أسعارها ، سوف تتلقى الإخطارات"}, "pTutorial_price_history_block1_title": {"message": "سعر المسار"}, "pTutorial_reviews_block1_desc1": {"message": "ملاحظات المشترين من Itao والصور الحقيقية من ردود الفعل AliExpress"}, "pTutorial_reviews_block1_title": {"message": "التعليقات"}, "pTutorial_reviews_block2_desc1": {"message": "من المفيد دائمًا التحقق من المراجعات من المشترين الآخرين"}, "pTutorial_same_products_block1_desc1": {"message": "يمكنك مقارنتها لجعل الخيار الأفضل"}, "pTutorial_same_products_block1_desc2": {"message": "انقر فوق \"المزي<PERSON>\" إلى \"البحث بالصور\""}, "pTutorial_same_products_block1_title": {"message": "نفس المنتجات"}, "pTutorial_same_products_by_image_search_block1_desc1": {"message": "إسقاط صورة المنتج هناك واختيار فئة"}, "pTutorial_same_products_by_image_search_block1_title": {"message": "البحث عن طريق الصورة"}, "pTutorial_seller_analysis_block1_desc1": {"message": "معدل التقييم الإيجابي للبائع ، وعشرات الملاحظات ومدة بقاء البائع في السوق"}, "pTutorial_seller_analysis_block1_title": {"message": "تقييم البائع"}, "pTutorial_seller_analysis_block2_desc2": {"message": "يعتمد تصنيف البائع على 3 فهارس: العنصر كما هو موصوف ، سرعة شحن الاتصالات"}, "pTutorial_seller_analysis_block3_desc3": {"message": "نستخدم 3 ألوان وأيقونات للإشارة إلى مستويات ثقة البائعين"}, "page_count": {"message": "<PERSON><PERSON><PERSON> الصفحات"}, "pai_chu": {"message": "مستبعد"}, "pai_chu_HK_bu_yi_xia_xiaoshou": {"message": "استبعاد المنتجات المحظورة في هونج كونج"}, "pai_chu_JP_bu_yi_xia_xiaoshou": {"message": "استبعاد المنتجات المحظورة في اليابان"}, "pai_chu_KR_bu_yi_xia_xiaoshou": {"message": "استبعاد المنتجات المحظورة في كوريا"}, "pai_chu_KZ_bu_yi_xia_xiaoshou": {"message": "استبعاد المنتجات المحظورة في كازاخستان"}, "pai_chu_MO_bu_yi_xia_xiaoshou": {"message": "استبعاد المنتجات المحظورة في ماكاو"}, "pai_chu_RU_bu_yi_xia_xiaoshou": {"message": "استبعاد المنتجات المحظورة في أوروبا الشرقية"}, "pai_chu_SA_bu_yi_xia_xiaoshou": {"message": "استبعاد المنتجات المحظورة في المملكة العربية السعودية"}, "pai_chu_TW_bu_yi_xia_xiaoshou": {"message": "استبعاد المنتجات المحظورة في تايوان"}, "pai_chu_USA_bu_yi_xia_xiaoshou": {"message": "استبعاد المنتجات المحظورة في الولايات المتحدة"}, "pai_chu_VN_bu_yi_xia_xiaoshou": {"message": "استبعاد المنتجات المحظورة في فيتنام"}, "pai_chu_bu_yi_xia_xiaoshou": {"message": "استبعاد المنتجات المحظورة"}, "payable_price_formula": {"message": "السعر + الشحن + الخصم"}, "pdd_check_retail_btn_txt": {"message": "تحقق من البيع بالتجزئة"}, "pdd_pifa_to_retail_btn_txt": {"message": "شراء في التجزئة"}, "pdp_copy_fail": {"message": "فشل النسخ!"}, "pdp_copy_success": {"message": "تم النسخ بنجاح!"}, "pdp_share_modal_subtitle": {"message": "مشاركة لقطة الشاشة ، سيرى / سترى chioce الخاص بك."}, "pdp_share_modal_title": {"message": "شارك اختيارك"}, "pdp_share_screenshot": {"message": "مشاركة لقطة الشاشة"}, "pei_song": {"message": "طريقة الشحن"}, "pin_lei": {"message": "الفئة"}, "pin_zhi_ti_yan": {"message": "جودة المنتج"}, "pin_zhi_ti_yan__desc": {"message": "معدل استرداد الجودة لمتجر البائع"}, "pin_zhi_tui_kuan_lv": {"message": "معدل الاسترداد"}, "pin_zhi_tui_kuan_lv__desc": {"message": "نسبة الطلبات التي تم استرداد أموالها وإعادتها خلال آخر 30 يومًا فقط"}, "ping_fen": {"message": "تقييم"}, "ping_jun_fa_huo_su_du": {"message": "متوسط سرعة الشحن"}, "pkgInfo_hide": {"message": "المعلومات اللوجستية: تشغيل/إيقاف"}, "pkgInfo_no_trace": {"message": "لا توجد معلومات لوجستية"}, "platform_name__1688": {"message": "1688"}, "platform_name__17zwd": {"message": "17zwd.com"}, "platform_name__51taoyang": {"message": "51taoyang.com"}, "platform_name__5ts": {"message": "5ts.com"}, "platform_name__91jf": {"message": "91jf.com"}, "platform_name__alibaba": {"message": "Alibaba.com"}, "platform_name__aliexpress": {"message": "AliExpress"}, "platform_name__amazon": {"message": "Amazon"}, "platform_name__bao66": {"message": "bao66.cn"}, "platform_name__chinagoods": {"message": "chinagoods.com"}, "platform_name__dhgate": {"message": "DHgate"}, "platform_name__e3hui": {"message": "e3hui.com"}, "platform_name__ebay": {"message": "موقع ئي باي"}, "platform_name__hznzcn": {"message": "hznzcn.com"}, "platform_name__jd": {"message": "JD"}, "platform_name__juyi5": {"message": "juyi5.cn"}, "platform_name__naver_shopping": {"message": "Naver Shopping"}, "platform_name__pinduoduo": {"message": "بيندوودو"}, "platform_name__pinduoduo_pifa": {"message": "بالجملة Pinduoduo"}, "platform_name__shopee": {"message": "<PERSON>ee"}, "platform_name__sjxzw": {"message": "571xz.com"}, "platform_name__sooxie": {"message": "sooxie.com"}, "platform_name__taobao": {"message": "تاوباو"}, "platform_name__taobao_lite": {"message": "Taobao Lite"}, "platform_name__vvic": {"message": "vvic.com"}, "platform_name__walmart": {"message": "وول مارت"}, "platform_name__wsy": {"message": "wsy.com"}, "platform_name__xingfujie": {"message": "xingfujie.cn"}, "platform_name__yiwugo": {"message": "Yiwugo.com"}, "platform_name__yunchepin": {"message": "yunchepin.cn"}, "platform_name__zhaojiafang": {"message": "zhaojiafang.com"}, "popup_go_to_home": {"message": "الصفحة الرئيسية"}, "popup_go_to_platform": {"message": "اذهب إلى $name$", "placeholders": {"name": {"content": "$1"}}}, "popup_search_placeholder": {"message": " أنا أتسوق لـ..."}, "popup_track_package_btn_track": {"message": "تتبع"}, "popup_track_package_desc": {"message": " الكل في واحد حزمة التتبع"}, "popup_track_package_search_placeholder": {"message": " رقم التتبع"}, "popup_translate_search_placeholder": {"message": "ترجم وابحث على $searchOn$", "placeholders": {"searchOn": {"content": "$1"}}}, "price_history": {"message": "تاريخ السعر"}, "price_history_chart_tip_ae": {"message": "نصيحة: ع<PERSON><PERSON> الطلبات هو العدد التراكمي للطلبات منذ الإطلاق"}, "price_history_chart_tip_coupang": {"message": "نصيحة: سيقو<PERSON>ang بحذف عدد الطلبات الخاصة بالطلبات الاحتيالية"}, "price_history_inm_1688_l1": {"message": "رجاءا حمل"}, "price_history_inm_1688_l2": {"message": "مساعد التسوق AliPrice لـ 1688"}, "price_history_panel_lowest_price": {"message": "أقل سعر: "}, "price_history_panel_tab_price_tracking": {"message": "تاريخ السعر"}, "price_history_panel_tab_seller_analysis": {"message": " تحليل البائع"}, "price_history_pro_modal_title": {"message": "تاريخ الأسعار وتاريخ الطلب"}, "privacy_consent__btn_agree": {"message": "إعادة النظر في الموافقة على جمع البيانات"}, "privacy_consent__btn_disable_all": {"message": "<PERSON>ير مقبول"}, "privacy_consent__btn_enable_all": {"message": "تمكين الكل"}, "privacy_consent__btn_uninstall": {"message": "إزالة"}, "privacy_consent__desc_privacy": {"message": "لاحظ أنه بدون البيانات أو ملفات تعريف الارتباط ، سيتم إيقاف تشغيل بعض الوظائف لأن هذه الوظائف تحتاج إلى شرح البيانات أو ملفات تعريف الارتباط ، ولكن لا يزال بإمكانك استخدام الوظائف الأخرى."}, "privacy_consent__desc_privacy_L1": {"message": "لسوء الحظ ، بدون البيانات أو ملفات تعريف الارتباط لن يعمل لأننا بحاجة إلى تفسير البيانات أو ملفات تعريف الارتباط."}, "privacy_consent__desc_privacy_L2": {"message": "إذا لم تسمح لنا بجمع هذه المعلومات ، يرجى إزالتها."}, "privacy_consent__item_cookies_desc": {"message": "ملف تعريف الارتباط ، نحصل فقط على بيانات العملة الخاصة بك في ملفات تعريف الارتباط عند التسوق عبر الإنترنت لإظهار سجل الأسعار."}, "privacy_consent__item_cookies_title": {"message": "ملفات تعريف الارتباط المطلوبة"}, "privacy_consent__item_functional_desc_L1": {"message": "1. أضف ملفات تعريف الارتباط في المتصفح لتعريف جهاز الكمبيوتر أو الجهاز بشكل مجهول."}, "privacy_consent__item_functional_desc_L2": {"message": "2. إضافة بيانات وظيفية في الوظيفة الإضافية للعمل مع الوظيفة."}, "privacy_consent__item_functional_title": {"message": "ملفات تعريف الارتباط الوظيفية والتحليلية"}, "privacy_consent__more_desc": {"message": "يرجى العلم أننا لا نشارك بياناتك الشخصية مع شركات أخرى ولا تقوم أي شركات إعلانية بجمع البيانات من خلال خدمتنا."}, "privacy_consent__options__btn__desc": {"message": "لاستخدام جميع الميزات ، تحتاج إلى تشغيلها."}, "privacy_consent__options__btn__label": {"message": "قم بتشغيله"}, "privacy_consent__options__desc_L1": {"message": "سنجمع البيانات التالية التي تحدد هويتك الشخصية:"}, "privacy_consent__options__desc_L2": {"message": "- ملفات تعريف الارتباط ، نحصل فقط على بيانات عملتك في ملفات تعريف الارتباط عندما تتسوق عبر الإنترنت لإظهار سجل الأسعار."}, "privacy_consent__options__desc_L3": {"message": "- وإضافة ملفات تعريف الارتباط في المتصفح للتعرف على جهاز الكمبيوتر الخاص بك أو جهازك بشكل مجهول."}, "privacy_consent__options__desc_L4": {"message": "’- تجعل البيانات المجهولة الأخرى هذا التمديد أكثر ملاءمة."}, "privacy_consent__options__desc_L5": {"message": "يرجى ملاحظة أننا لا نشارك بياناتك الشخصية مع شركات أخرى ولا توجد شركات إعلانية تجمع البيانات من خلال خدمتنا."}, "privacy_consent__privacy_preferences": {"message": "تفضيلات الخصوصية"}, "privacy_consent__read_more": {"message": "اقرأ المزيد >>"}, "privacy_consent__title_privacy": {"message": "خصوصية"}, "product_info": {"message": "معلومات المنتج"}, "product_recommend__name": {"message": "نفسه"}, "product_research": {"message": "أب<PERSON>ا<PERSON> المنتج"}, "product_sub__email_desc": {"message": "البريد الإلكتروني لتنبيه السعر"}, "product_sub__email_edit": {"message": "تعديل"}, "product_sub__email_not_verified": {"message": "يرجى التحقق من البريد الإلكتروني"}, "product_sub__email_required": {"message": "يرجى تقديم بريد إلكتروني"}, "product_sub__form_countdown": {"message": "الإغلاق التلقائي بعد $seconds$ ثانية", "placeholders": {"seconds": {"content": "$1"}}}, "product_sub__form_fail": {"message": "فشل في إضافة تذكير!"}, "product_sub__form_input_price": {"message": "سعر الإدخال"}, "product_sub__form_item_country": {"message": "الأمة"}, "product_sub__form_item_current_price": {"message": "السعر الحالي"}, "product_sub__form_item_duration": {"message": "مسار"}, "product_sub__form_item_higher_price": {"message": "أو السعر>"}, "product_sub__form_item_invalid_higher_price": {"message": "يجب أن يكون السعر أكبر من $price$", "placeholders": {"price": {"content": "$1"}}}, "product_sub__form_item_invalid_lower_price": {"message": "يجب أن يكون السعر أقل من $price$", "placeholders": {"price": {"content": "$1"}}}, "product_sub__form_item_lower_price": {"message": "عندما يكون السعر <"}, "product_sub__form_submit": {"message": "يقدم"}, "product_sub__form_success": {"message": "نجحت في إضافة تذكير!"}, "product_sub__high_price_notify": {"message": "أعلمني بزيادات الأسعار"}, "product_sub__low_price_notify": {"message": "أعلمني بتخفيضات الأسعار"}, "product_sub__modal_title": {"message": "تذكير بتغيير سعر الاشتراك"}, "province__an_hui": {"message": "<PERSON><PERSON>"}, "province__ao_men": {"message": "Macau"}, "province__bei_jing": {"message": "Beijing"}, "province__chong_qing": {"message": "Chongqing"}, "province__fu_jian": {"message": "Fujian"}, "province__gan_su": {"message": "Gansu"}, "province__guang_dong": {"message": "Guangdong"}, "province__guang_xi": {"message": "Guangxi"}, "province__gui_zhou": {"message": "Guizhou"}, "province__hai_nan": {"message": "Hainan"}, "province__he_bei": {"message": "Hebei"}, "province__he_nan": {"message": "<PERSON><PERSON>"}, "province__hei_long_jiang": {"message": "Heilongjiang"}, "province__hu_bei": {"message": "Hubei"}, "province__hu_nan": {"message": "Hunan"}, "province__ji_lin": {"message": "<PERSON><PERSON>"}, "province__jiang_su": {"message": "Jiangsu"}, "province__jiang_xi": {"message": "Jiangxi"}, "province__liao_ning": {"message": "Liaoning"}, "province__nei_meng_gu": {"message": "Inner Mongolia"}, "province__ning_xia": {"message": "Ningxia"}, "province__qing_hai": {"message": "Qinghai"}, "province__shan_dong": {"message": "Shandong"}, "province__shan_xi": {"message": "Shaanxi"}, "province__shang_hai": {"message": "Shanghai"}, "province__si_chuan": {"message": "Sichuan"}, "province__tai_wan": {"message": "Taiwan"}, "province__tian_jin": {"message": "Tianjin"}, "province__xi_zhang": {"message": "Tibet"}, "province__xiang_gang": {"message": "Hong Kong"}, "province__xin_jiang": {"message": "Xinjiang"}, "province__yun_nan": {"message": "Yunnan"}, "province__zhe_jiang": {"message": "Zhejiang"}, "purple_rocket": {"message": "로켓직구"}, "qi_ding_liang": {"message": "موك"}, "qi_ding_liang_qi_ding_jia": {"message": "الح<PERSON> الأدنى لكمية الطلب والكمية المطلوبة"}, "qi_ye_mian_ji": {"message": "منطقة المشروع"}, "qing_zhi_shao_xuan_ze_yi_ge_chan_pin": {"message": "الرجاء تحديد منتج واحد على الأقل"}, "qing_zhi_shao_xuan_ze_yi_ge_zi_duan": {"message": "الرجاء تحديد حقل واحد على الأقل"}, "qu_deng_lu": {"message": "تسجيل الدخول"}, "quan_guo_yan_xuan": {"message": "الاختيار العالمي"}, "recommendation_popup_banner_btn_install": {"message": "قم بتثبيته"}, "recommendation_popup_banner_desc": {"message": "عرض سجل الأسعار في غضون 3/6 أشهر وإخطار انخفاض السعر"}, "region__all": {"message": "جميع المناطق"}, "region__hai_wai": {"message": "Overseas"}, "region__hua_bei_qu": {"message": "North China"}, "region__hua_dong_qu": {"message": "East China"}, "region__hua_nan_qu": {"message": "South China"}, "region__hua_zhong_qu": {"message": "Central China"}, "region__jiang_zhe_lu": {"message": "Jiangsu, Zhejiang and Shanghai"}, "remove_web_limits": {"message": "تمكين النقر بزر الماوس الأيمن"}, "ren_zheng_gong_chang": {"message": "مصنع معتمد"}, "ren_zheng_gong_ying_shang_nian_zhan": {"message": "سنوات كمورد معتمد"}, "required_to_aliprice_login": {"message": "تحتاج إلى تسجيل الدخول إلى AliPrice"}, "revenue_last30_days": {"message": "حجم المبيعات في آخر 30 يومًا"}, "review_counts": {"message": "<PERSON><PERSON><PERSON> جامعي"}, "rocket": {"message": "로켓"}, "ru_zhu_nian_xian": {"message": "فترة الدخول"}, "sales_amount_last30_days": {"message": "إجمالي المبيعات في آخر 30 يومًا"}, "sales_last30_days": {"message": "المبيعات في آخر 30 يومًا"}, "sbi_alibaba_cate__accessories": {"message": "مستلزمات"}, "sbi_alibaba_cate__aqfk": {"message": "حماية"}, "sbi_alibaba_cate__bags_cases": {"message": "حقائب وحافظات"}, "sbi_alibaba_cate__beauty": {"message": "جمال"}, "sbi_alibaba_cate__beverage": {"message": "المشروبات"}, "sbi_alibaba_cate__bgwh": {"message": "ثقافة المكتب"}, "sbi_alibaba_cate__bz": {"message": "طَرد"}, "sbi_alibaba_cate__ccyj": {"message": "أدوات المطبخ"}, "sbi_alibaba_cate__clothes": {"message": "ثياب"}, "sbi_alibaba_cate__cmgd": {"message": "البث الإعلامي"}, "sbi_alibaba_cate__coat_jacket": {"message": "معطف وسترة"}, "sbi_alibaba_cate__consumer_electronics": {"message": "مستهلكى الكترونيات"}, "sbi_alibaba_cate__cryp": {"message": "منتجات الكبار"}, "sbi_alibaba_cate__csyp": {"message": "أغطية السرير"}, "sbi_alibaba_cate__cwyy": {"message": "بستنة الحيوانات الأليفة"}, "sbi_alibaba_cate__cysx": {"message": "تقديم الطعام طازج"}, "sbi_alibaba_cate__dgdq": {"message": "عامل الكهرباء"}, "sbi_alibaba_cate__dl": {"message": "التمثيل"}, "sbi_alibaba_cate__dress_suits": {"message": "فستان و بدلات"}, "sbi_alibaba_cate__dszm": {"message": "إضاءة"}, "sbi_alibaba_cate__dzqj": {"message": "ج<PERSON><PERSON><PERSON> الكتروني"}, "sbi_alibaba_cate__essb": {"message": "المعدات المستعملة"}, "sbi_alibaba_cate__food": {"message": "طعام"}, "sbi_alibaba_cate__fspj": {"message": "الملابس والاكسسوارات"}, "sbi_alibaba_cate__furniture": {"message": "أثاث المنزل"}, "sbi_alibaba_cate__fzpg": {"message": "<PERSON><PERSON><PERSON> منسوج"}, "sbi_alibaba_cate__ghjq": {"message": "رعاية شخصية"}, "sbi_alibaba_cate__gt": {"message": "صلب"}, "sbi_alibaba_cate__gyp": {"message": "الحرف"}, "sbi_alibaba_cate__hb": {"message": "صديق للبيئة"}, "sbi_alibaba_cate__hfcz": {"message": "مكياج العناية بالبشرة"}, "sbi_alibaba_cate__hg": {"message": "الصناعة الكيماوية"}, "sbi_alibaba_cate__jg": {"message": "يعالج"}, "sbi_alibaba_cate__jianccai": {"message": "مواد بناء"}, "sbi_alibaba_cate__jichuang": {"message": "أداة آلة"}, "sbi_alibaba_cate__jjry": {"message": "الاستخدام المنزلي اليومي"}, "sbi_alibaba_cate__jtys": {"message": "وسائل النقل"}, "sbi_alibaba_cate__jxsb": {"message": "معدات"}, "sbi_alibaba_cate__jxwj": {"message": "الأجهزة الميكانيكية"}, "sbi_alibaba_cate__jydq": {"message": "الأجهزة المنزلية"}, "sbi_alibaba_cate__jzjc": {"message": "مواد بناء تحسين المنزل"}, "sbi_alibaba_cate__jzjf": {"message": "المنسوجات المنزلية"}, "sbi_alibaba_cate__mj": {"message": "منشفة"}, "sbi_alibaba_cate__myyp": {"message": "منتجات الأطفال"}, "sbi_alibaba_cate__nanz": {"message": "مِل<PERSON> الرجال"}, "sbi_alibaba_cate__nvz": {"message": "ملابس نسائية"}, "sbi_alibaba_cate__ny": {"message": "طاقة"}, "sbi_alibaba_cate__others": {"message": "الآخرين"}, "sbi_alibaba_cate__qcyp": {"message": "اكسسوارات السيارات"}, "sbi_alibaba_cate__qmpj": {"message": "قطع غيار السيارات"}, "sbi_alibaba_cate__shoes": {"message": "أحذية"}, "sbi_alibaba_cate__smdn": {"message": "حاسوب رقمي"}, "sbi_alibaba_cate__snqj": {"message": "التخزين والتنظيف"}, "sbi_alibaba_cate__spjs": {"message": "طعام شراب"}, "sbi_alibaba_cate__swfw": {"message": "خدمات الأعمال"}, "sbi_alibaba_cate__toys_hobbies": {"message": "عروسه لعبه"}, "sbi_alibaba_cate__trousers_skirt": {"message": "بنطلون وتنورة"}, "sbi_alibaba_cate__txcp": {"message": "منتجات الاتصالات"}, "sbi_alibaba_cate__tz": {"message": "ملابس الأطفال"}, "sbi_alibaba_cate__underwear": {"message": "ثياب داخلية"}, "sbi_alibaba_cate__wjgj": {"message": "أدوات الأجهزة"}, "sbi_alibaba_cate__xgpi": {"message": "حقائ<PERSON> جلدية"}, "sbi_alibaba_cate__xmhz": {"message": "مشروع التعاون"}, "sbi_alibaba_cate__xs": {"message": "ممحاة"}, "sbi_alibaba_cate__ydfs": {"message": "ملابس رياضية"}, "sbi_alibaba_cate__ydhw": {"message": "رياضة في الهواء الطلق"}, "sbi_alibaba_cate__yjkc": {"message": "المعادن المعدنية"}, "sbi_alibaba_cate__yqyb": {"message": "الأجهزة"}, "sbi_alibaba_cate__ys": {"message": "مطبعة"}, "sbi_alibaba_cate__yyby": {"message": "الرعاية الطبية"}, "sbi_alibaba_cn_kj_90mjs": {"message": "عدد المشترين في الـ 90 يومًا الماضية"}, "sbi_alibaba_cn_kj_90xsl": {"message": "حجم المبيعات في آخر 90 يومًا"}, "sbi_alibaba_cn_kj_gjsj": {"message": "السعر المقدر"}, "sbi_alibaba_cn_kj_gjwlyf": {"message": "رسوم الشحن الدولي"}, "sbi_alibaba_cn_kj_gjyf": {"message": "رسوم الشحن"}, "sbi_alibaba_cn_kj_gssj": {"message": "السعر المقدر"}, "sbi_alibaba_cn_kj_lr": {"message": "<PERSON><PERSON><PERSON>"}, "sbi_alibaba_cn_kj_lrgs": {"message": "الربح = السعر المقدر × هامش الربح"}, "sbi_alibaba_cn_kj_pjfhsd": {"message": "متوسط سرعة التسليم"}, "sbi_alibaba_cn_kj_qtfy": {"message": "رسوم أخرى"}, "sbi_alibaba_cn_kj_qtfygs": {"message": "التكلفة الأخرى = السعر المقدر × نسبة التكلفة الأخرى"}, "sbi_alibaba_cn_kj_spjg": {"message": "سعر"}, "sbi_alibaba_cn_kj_spzl": {"message": "وزن"}, "sbi_alibaba_cn_kj_szd": {"message": "موقع"}, "sbi_alibaba_cn_kj_unit_ge": {"message": "قطع"}, "sbi_alibaba_cn_kj_unit_jian": {"message": "قطع"}, "sbi_alibaba_cn_kj_unit_ke": {"message": "غرام"}, "sbi_alibaba_cn_kj_unit_ren": {"message": "المشترون"}, "sbi_alibaba_cn_kj_unit_tai": {"message": "قطع"}, "sbi_alibaba_cn_kj_unit_tao": {"message": "مجموعات"}, "sbi_alibaba_cn_kj_unit_tian": {"message": "أيام"}, "sbi_alibaba_cn_kj_zwbj": {"message": "لا ثمن"}, "sbi_aliprice_alibaba_cn__jiage": {"message": "سعر"}, "sbi_aliprice_alibaba_cn__keshou": {"message": "متاح للبيع"}, "sbi_aliprice_alibaba_cn__moren": {"message": "إفتراضي"}, "sbi_aliprice_alibaba_cn__queding": {"message": "بالتأكيد"}, "sbi_aliprice_alibaba_cn__xiaoliang": {"message": "مبيعات"}, "sbi_aliprice_alibaba_cn_cate__jiaju": {"message": "أثاث المنزل"}, "sbi_aliprice_alibaba_cn_cate__linghsi": {"message": "وجبة خفيفة"}, "sbi_aliprice_alibaba_cn_cate__meizhuang": {"message": "الماكياج"}, "sbi_aliprice_alibaba_cn_cate__neiyi": {"message": "ثياب داخلية"}, "sbi_aliprice_alibaba_cn_cate__peishi": {"message": "مستلزمات"}, "sbi_aliprice_alibaba_cn_cate__pinchui": {"message": "مشروب معبأ"}, "sbi_aliprice_alibaba_cn_cate__qita": {"message": "آحرون"}, "sbi_aliprice_alibaba_cn_cate__qunzhuang": {"message": "جيبة"}, "sbi_aliprice_alibaba_cn_cate__shangyi": {"message": "السترة"}, "sbi_aliprice_alibaba_cn_cate__shuma": {"message": "إلكترونيات"}, "sbi_aliprice_alibaba_cn_cate__wanju": {"message": "عروسه لعبه"}, "sbi_aliprice_alibaba_cn_cate__xiangbao": {"message": "أمتعة السفر"}, "sbi_aliprice_alibaba_cn_cate__xiazhuang": {"message": "قيعان"}, "sbi_aliprice_alibaba_cn_cate__xiezi": {"message": "حذاء"}, "sbi_aliprice_cate__apparel": {"message": "ثياب"}, "sbi_aliprice_cate__automobiles_motorcycles": {"message": "سيارات ودراجات نارية"}, "sbi_aliprice_cate__beauty_health": {"message": "الصحة و الجمال"}, "sbi_aliprice_cate__cellphones_telecommunications": {"message": "الهواتف المحمولة والاتصالات"}, "sbi_aliprice_cate__computer_office": {"message": "كمبيوتر ومكتب"}, "sbi_aliprice_cate__consumer_electronics": {"message": "مستهلكى الكترونيات"}, "sbi_aliprice_cate__education_office_supplies": {"message": "اللوازم التعليمية والمكتبية"}, "sbi_aliprice_cate__electronic_components_supplies": {"message": "المكونات واللوازم الإلكترونية"}, "sbi_aliprice_cate__furniture": {"message": "أثاث المنزل"}, "sbi_aliprice_cate__hair_extensions_wigs": {"message": "وصلات الشعر والشعر المستعار"}, "sbi_aliprice_cate__home_garden": {"message": "المنزل والحديقة"}, "sbi_aliprice_cate__home_improvement": {"message": "تحسين المنزل"}, "sbi_aliprice_cate__jewelry_accessories": {"message": "المجوهرات والاكسسوارات"}, "sbi_aliprice_cate__luggage_bags": {"message": "أمتعة و حقائب"}, "sbi_aliprice_cate__mother_kids": {"message": "الأم والاطفال"}, "sbi_aliprice_cate__novelty_special_use": {"message": "الجدة والاستخدام الخاص"}, "sbi_aliprice_cate__security_protection": {"message": "الأمن والحماية"}, "sbi_aliprice_cate__shoes": {"message": "أحذية"}, "sbi_aliprice_cate__sports_entertainment": {"message": "الرياضة والترفيه"}, "sbi_aliprice_cate__toys_hobbies": {"message": "الألعاب والهوايات"}, "sbi_aliprice_cate__watches": {"message": "ساعات"}, "sbi_aliprice_cate__weddings_events": {"message": "حفلا<PERSON> الزفاف والمناسبات"}, "sbi_btn_capture_txt": {"message": "إلتقاط"}, "sbi_btn_source_now_txt": {"message": "المصدر الآن"}, "sbi_button__chat_with_me": {"message": "الدردشة معي"}, "sbi_button__contact_supplier": {"message": "اتصل"}, "sbi_button__hide_on_this_site": {"message": "لا تظهر في هذا الموقع"}, "sbi_button__open_settings": {"message": "تكوين البحث بالصورة"}, "sbi_capture_shortcut_tip": {"message": "أو اضغط على مفتاح \"Enter\" بلوحة المفاتيح"}, "sbi_capturing_tip": {"message": "اسر"}, "sbi_composed_rating_45": {"message": "4.5 - 5.0 نجوم"}, "sbi_crop_and_search": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "sbi_crop_start": {"message": "استخدم لقطة الشاشة"}, "sbi_err_captcha_action": {"message": "يؤكد"}, "sbi_err_captcha_for_alibaba_cn": {"message": "بحاجة إلى التحقق، يرجى تحميل صورة للتحقق. (عرض $video_tutorial$ أو حاول مسح ملفات تعريف الارتباط)", "placeholders": {"feedback": {"content": "$1"}, "video_tutorial": {"content": "$1"}}}, "sbi_err_captcha_for_aliprice": {"message": "حركة غير عادية ، يرجى التحقق"}, "sbi_err_captcha_for_taobao": {"message": "يطلب منك Taobao التحقق ، يرجى تحميل الصورة يدويًا والبحث للتحقق منها. يرجع هذا الخطأ إلى سياسة التحقق الجديدة \"بحث TaoBao بالصور\" ، نقترح عليك التحقق من هذه الشكوى بشكل متكرر جدًا على Taobao $feedback$.", "placeholders": {"feedback": {"content": "$1"}}}, "sbi_err_captcha_for_taobao_feedback": {"message": "تعليق"}, "sbi_err_captcha_msg": {"message": "يتطلب منك $platform$ تحميل صورة للبحث أو إكمال التحقق الأمني ​​لإزالة قيود البحث", "placeholders": {"platform": {"content": "$1"}}}, "sbi_err_checking_if_low_version": {"message": "تحقق مما إذا كان هذا هو أحدث إصدار"}, "sbi_err_cookie_btn_clear": {"message": "مسح ملفات تعريف الارتباط"}, "sbi_err_cookie_for_alibaba_cn": {"message": "حاول مسح 1688 ملفات تعريف الارتباط؟ (تحتاج إلى تسجيل الدخول مرة أخرى)"}, "sbi_err_desperate_feature_pdd": {"message": "تم نقل وظيفة البحث عن الصور إلى Pinduoduo Search by Image Extension"}, "sbi_err_hidden_skill_of_improve_search_rate": {"message": "كيفية تحسين معدل نجاح البحث عن الصور؟"}, "sbi_err_img_undersize": {"message": "صورة > $imgMinimumSize$", "placeholders": {"imgMinimumSize": {"content": "$1"}}}, "sbi_err_login_required": {"message": "تسجيل الدخول $loginSite$", "placeholders": {"loginSite": {"content": "$1"}}}, "sbi_err_login_required_action": {"message": "تسجيل الدخول"}, "sbi_err_low_version": {"message": "قم بتثبيت أحدث إصدار ($latestVersion$)", "placeholders": {"latestVersion": {"content": "$1"}}}, "sbi_err_low_version_action": {"message": "تحميل"}, "sbi_err_need_help": {"message": "بحاجة الى مساعدة"}, "sbi_err_network": {"message": "خطأ في الشبكة، تأكد من أنه يمكنك زيارة الموقع"}, "sbi_err_not_low_version": {"message": "تم تثبيت أحدث إصدار ($latestVersion$)", "placeholders": {"latestVersion": {"content": "$1"}}}, "sbi_err_try_again": {"message": "حاول مجددا"}, "sbi_err_try_again_action": {"message": "حاول مجددا"}, "sbi_err_visit_and_try": {"message": "حاول مرة أخرى، أو قم بزيارة $website$ لإعادة المحاولة", "placeholders": {"website": {"content": "$1"}}}, "sbi_err_visit_site": {"message": "قم بزيارة الصفحة الرئيسية $siteName$", "placeholders": {"siteName": {"content": "$1"}}}, "sbi_fail_tip": {"message": "فشل التحميل ، يرجى تحديث الصفحة والمحاولة مرة أخرى."}, "sbi_kuajing_filter_area": {"message": "منطقة"}, "sbi_kuajing_filter_au": {"message": "أستراليا"}, "sbi_kuajing_filter_btn_confirm": {"message": "يتأكد"}, "sbi_kuajing_filter_de": {"message": "ألمانيا"}, "sbi_kuajing_filter_destination_country": {"message": "<PERSON><PERSON><PERSON> المقصد"}, "sbi_kuajing_filter_es": {"message": "إسبانيا"}, "sbi_kuajing_filter_estimate": {"message": "تقدير"}, "sbi_kuajing_filter_estimate_price": {"message": "السعر المقدر"}, "sbi_kuajing_filter_estimate_price_formula": {"message": "صيغة السعر المقدر = (سعر السلعة + الشحن اللوجستي الدولي) / (1 - هامش الربح - نسبة التكلفة الأخرى)"}, "sbi_kuajing_filter_fr": {"message": "فرنسا"}, "sbi_kuajing_filter_kw_placeholder": {"message": "أدخل الكلمات الرئيسية لمطابقة العنوان"}, "sbi_kuajing_filter_logistics": {"message": "قالب اللوجستيات"}, "sbi_kuajing_filter_logistics_china_post": {"message": "البريد الجوي الصيني"}, "sbi_kuajing_filter_logistics_discount": {"message": "خصم الخدمات اللوجستية"}, "sbi_kuajing_filter_logistics_epacket": {"message": "epacket"}, "sbi_kuajing_filter_logistics_price_formula": {"message": "الشحن اللوجيستي الدولي = (الوزن × سعر الشحن + رسوم التسجيل) × (1 - خصم)"}, "sbi_kuajing_filter_others_fee": {"message": "رسوم أخرى"}, "sbi_kuajing_filter_profit_percent": {"message": "ها<PERSON><PERSON> الربح"}, "sbi_kuajing_filter_prop": {"message": "صفات"}, "sbi_kuajing_filter_ru": {"message": "روسيا"}, "sbi_kuajing_filter_total": {"message": "تطابق $count$ عناصر مماثلة", "placeholders": {"count": {"content": "$1"}}}, "sbi_kuajing_filter_uk": {"message": "المملكة المتحدة"}, "sbi_kuajing_filter_usa": {"message": "أمريكا"}, "sbi_login_punish_title__pdd_pifa": {"message": "البيع بالجملة Pinduoduo"}, "sbi_msg_no_result": {"message": "لم يتم العثور على نتيجة ، يرجى تسجيل الدخول إلى $loginSite$ أو تجربة صورة أخرى", "placeholders": {"loginSite": {"content": "$1"}}}, "sbi_msg_no_result_check_support_page_l1": {"message": "غير متاح مؤقتًا لـ Safari ، يرجى استخدام $supportPage$.", "placeholders": {"supportPage": {"content": "$1"}}}, "sbi_msg_no_result_check_support_page_l2": {"message": "متصفح كروم وملحقاته"}, "sbi_msg_no_result_reinstall_l1": {"message": "لم يتم العثور على نتائج ، الرجاء تسجيل الدخول إلى $loginSite$ أو تجربة صورة أخرى ، أو إعادة تثبيت أحدث إصدار $latestExtUrl$", "placeholders": {"loginSite": {"content": "$1"}, "latestExtUrl": {"content": "$2"}}}, "sbi_msg_no_result_reinstall_l2": {"message": "أحد<PERSON> إصدار من", "placeholders": {}}, "sbi_search_by_selected_area": {"message": "المنطقة المختارة"}, "sbi_shipping_": {"message": "نفس الشحن يوم"}, "sbi_specify_category": {"message": "ح<PERSON><PERSON> الفئة:"}, "sbi_start_crop": {"message": "<PERSON><PERSON><PERSON> المنطقة"}, "sbi_store_name_alibabaCNKuaJing": {"message": "1688 من الخارج"}, "sbi_tutorial_btn_more": {"message": "المزيد من الاستخدام"}, "sbi_tutorial_find_coupons_on_taobao": {"message": "ابحث عن كوبونات Taobao"}, "sbi_txt__empty_retry": {"message": "عذرا ، لم يتم العثور على نتائج ، يرجى المحاولة مرة أخرى."}, "sbi_txt__min_order": {"message": "الح<PERSON> الأدنى. طلب"}, "sbi_visiting": {"message": "التصفح"}, "sbi_yiwugo__jiagexiangtan": {"message": "تواصل مع البائع للسعر"}, "sbi_yiwugo__qigou": {"message": "$num$ قطعة (موك)", "placeholders": {"num": {"content": "$1"}}}, "sbi_yiwugo__xing": {"message": "النجوم"}, "searchByImage_screenshot": {"message": "لقطة شاشة بنقرة واحدة"}, "searchByImage_search": {"message": "بنقرة واحدة البحث عن نفس العناصر"}, "searchByImage_size_type": {"message": "لا يمكن أن يكون حجم الملف أكبر من $num$ ميغابايت، $type$ فقط", "placeholders": {"num": {"content": "$1"}, "type": {"content": "$2"}}}, "search_by_image_progress_analysing": {"message": "تحليل الصورة"}, "search_by_image_progress_searching": {"message": "ابحث عن المنتجات"}, "search_by_image_progress_sending": {"message": "إرسال الصورة"}, "search_by_image_response_rate": {"message": "معدل الاستجابة: $responseRate$ من المشترين الذين اتصلوا بهذا المورد تلقوا ردًا في غضون $responseInHour$ ساعة.", "placeholders": {"responseRate": {"content": "$1"}, "responseInHour": {"content": "$2"}}}, "search_by_keyword": {"message": "البحث بالكلمة المفتاحية:"}, "select_country_language_modal_title_country": {"message": "بلد"}, "select_country_language_modal_title_language": {"message": "لغة"}, "select_country_region_modal_title": {"message": "اختر البلد / المنطقة"}, "select_language_modal_title": {"message": "اختر لغة:"}, "select_shop": {"message": "<PERSON><PERSON><PERSON> المت<PERSON>ر"}, "sellers_count": {"message": "ع<PERSON><PERSON> البائعين في الصفحة الحالية"}, "sellers_count_per_page": {"message": "ع<PERSON><PERSON> البائعين في الصفحة الحالية"}, "service_score": {"message": "تصنيف الخدمة الشاملة"}, "set_shortcut_keys": {"message": "تعيين مفاتيح الاختصار"}, "setting_logo_title": {"message": " مس<PERSON><PERSON><PERSON> التسوق"}, "setting_modal_options_position_title": {"message": "موقع"}, "setting_modal_options_position_value_left": {"message": "على اليسار"}, "setting_modal_options_position_value_right": {"message": "على اليمين"}, "setting_modal_options_theme_title": {"message": " لون الموضوع"}, "setting_modal_options_theme_value_dark": {"message": " دا<PERSON>ن"}, "setting_modal_options_theme_value_light": {"message": "مضيئ"}, "setting_modal_title": {"message": "الإعدادات"}, "setting_options_country_title": {"message": "البلد / المنطقة"}, "setting_options_hover_zoom_desc": {"message": " حرك الماوس فوقه للتكبير"}, "setting_options_hover_zoom_title": {"message": "تحوم والتكبير"}, "setting_options_jd_coupon_desc": {"message": "تم العثور على قسيمة على JD.com"}, "setting_options_jd_coupon_title": {"message": "قسيمة JD.com"}, "setting_options_language_title": {"message": "اللغة"}, "setting_options_price_drop_alert_desc": {"message": " عندما ينخفض سعر المنتجات في قائمة مفضلاتى ، سوف تتلقى إشعار بذلك."}, "setting_options_price_drop_alert_title": {"message": " تنبيه بانخفاض الأسعار"}, "setting_options_price_history_on_list_page_desc": {"message": " سجل أسعار العرض في صفحة البحث عن المنتج "}, "setting_options_price_history_on_list_page_title": {"message": "تاريخ الأسعار (صفحة القائمة)"}, "setting_options_price_history_on_produt_page_desc": {"message": " عرض سجل المنتج على صفحة تفاصيل المنتج"}, "setting_options_price_history_on_produt_page_title": {"message": "تاريخ الأسعار (صفحة التفاصيل)"}, "setting_options_sales_analysis_desc": {"message": "دعم إحصائيات السعر وحجم المبيعات وعدد البائعين ونسبة مبيعات المتجر على صفحة قائمة المنتجات $platforms$", "placeholders": {"platforms": {"content": "$1"}}}, "setting_options_sales_analysis_title": {"message": "تحليل المبيعات"}, "setting_options_save_success_msg": {"message": " نجاح"}, "setting_options_tacking_price_title": {"message": "تنبيه تغيير السعر"}, "setting_options_value_off": {"message": "<PERSON>ير متاح"}, "setting_options_value_on": {"message": "متاح"}, "setting_pkg_quick_view_desc": {"message": "ድጋፍ: 1688 & Taobao"}, "setting_saved_message": {"message": "التغييرات التي تم حفظها بنجاح"}, "setting_section_enable_platform_title": {"message": "على الخروج"}, "setting_section_setting_title": {"message": "إعدادات ل"}, "setting_section_shortcuts_title": {"message": "إختصارات"}, "settings_aliprice_agent__desc": {"message": "معروض على صفحة تفاصيل المنتج $platforms$", "placeholders": {"platforms": {"content": "$1"}}}, "settings_aliprice_agent__title": {"message": "اشتري لي"}, "settings_copy_link__desc": {"message": "عرض على صفحة تفاصيل المنتج"}, "settings_copy_link__title": {"message": "زر النسخ وعنوان البحث"}, "settings_currency_desc__for_detail": {"message": "دعم صفحة تفاصيل المنتج 1688"}, "settings_currency_desc__for_list": {"message": "البحث بالصور (بما في ذلك 1688/1688 في الخارج / Taobao)"}, "settings_currency_desc__for_sbi": {"message": "<PERSON><PERSON><PERSON> السعر"}, "settings_currency_desc_display_for_list": {"message": "تظهر في بحث الصور (بما في ذلك 1688/1688 في الخارج/تاوباو)"}, "settings_currency_rate_desc": {"message": "تحديث سعر الصرف من \"$currencyRateFrom$\"", "placeholders": {"currencyRateFrom": {"content": "$1"}}}, "settings_currency_rate_from_boc": {"message": "بنك الصين"}, "settings_download_images__desc": {"message": "دعم تنزيل الصور من $platforms$", "placeholders": {"platforms": {"content": "$1"}}}, "settings_download_images__title": {"message": "زر تنزيل الصورة"}, "settings_download_reviews__desc": {"message": "معروض على صفحة تفاصيل المنتج $platforms$", "placeholders": {"platforms": {"content": "$1"}}}, "settings_download_reviews__title": {"message": "تنزيل صور المراجعة"}, "settings_google_translate_desc": {"message": "انقر بزر الماوس الأيمن للحصول على شريط ترجمة جوجل"}, "settings_google_translate_title": {"message": "ترجمة صفحة الويب"}, "settings_historical_trend_desc": {"message": "العرض في الزاوية اليمنى السفلية من الصورة في صفحة قائمة المنتجات"}, "settings_modal_btn_more": {"message": "المزيد من الإعدادات"}, "settings_productInfo_desc": {"message": "عرض معلومات أكثر تفصيلاً عن المنتج في صفحة قائمة المنتجات. قد يؤدي تمكين هذا إلى زيادة تحميل الكمبيوتر والتسبب في تأخر الصفحة. إذا كان يؤثر على الأداء، فمن المستحسن تعطيله."}, "settings_product_recommend__desc": {"message": "يتم عرضه أسفل الصورة الرئيسية في صفحة تفاصيل المنتج $platforms$", "placeholders": {"platforms": {"content": "$1"}}}, "settings_product_recommend__name": {"message": "المنتجات الموصى بها"}, "settings_research_desc": {"message": "الاستعلام عن معلومات أكثر تفصيلاً في صفحة قائمة المنتجات"}, "settings_sbi_add_to_list": {"message": "أضف <PERSON> $listType$", "placeholders": {"listType": {"content": "$1"}}}, "settings_sbi_detail_page_icon_title_desc": {"message": "صورة مصغرة لنتائج البحث عن الصور"}, "settings_sbi_remove_from_list": {"message": "إزالة من$listType$", "placeholders": {"listType": {"content": "$1"}}}, "settings_search_by_image_add_to_blacklist": {"message": "أ<PERSON><PERSON> <PERSON><PERSON> القائمة السوداء"}, "settings_search_by_image_adjust_entrance_entrance": {"message": "<PERSON>بط موضع الدخول"}, "settings_search_by_image_blacklist_desc": {"message": "لا تظهر أيقونة على مواقع الويب في القائمة السوداء."}, "settings_search_by_image_blacklist_title": {"message": "القائمة السوداء"}, "settings_search_by_image_bottom_left": {"message": "أسفل اليسار"}, "settings_search_by_image_bottom_right": {"message": "أسفل اليمين"}, "settings_search_by_image_clear_blacklist": {"message": "محو القائمة السوداء"}, "settings_search_by_image_detail_page_icon_title": {"message": "ظف<PERSON>ي"}, "settings_search_by_image_detail_page_icon_val_large": {"message": "أكبر"}, "settings_search_by_image_detail_page_icon_val_small": {"message": "الأصغر"}, "settings_search_by_image_display_button_desc": {"message": "نقرة واحدة على الأيقونة للبحث بالصورة"}, "settings_search_by_image_display_button_title": {"message": "أيقونة على الصور"}, "settings_search_by_image_sourece_websites_desc": {"message": "ابحث عن المنتج المصدر على هذه المواقع"}, "settings_search_by_image_sourece_websites_title": {"message": "البحث عن طريق نتيجة الصورة"}, "settings_search_by_image_top_left": {"message": "<PERSON><PERSON><PERSON><PERSON> اليسار"}, "settings_search_by_image_top_right": {"message": "أع<PERSON>ى اليمين"}, "settings_search_keyword_on_x__desc": {"message": "كلمات البحث على $platform$", "placeholders": {"platform": {"content": "$1"}}}, "settings_search_keyword_on_x__title": {"message": "إظهار رمز $platform$ عند تحديد الكلمات", "placeholders": {"platform": {"content": "$1"}}}, "settings_similar_products_desc": {"message": "حاول العثور على نفس المنتج في تلك المواقع (بحد أقصى 5)"}, "settings_similar_products_title": {"message": "ابحث عن نفس المنتج"}, "settings_toolbar_expand_title": {"message": "المكونات في تصغير"}, "settings_top_toolbar_desc": {"message": "شريط البحث في أعلى الصفحة"}, "settings_top_toolbar_title": {"message": "شريط البحث"}, "settings_translate_search_desc": {"message": "ترجمة إلى الصينية والبحث"}, "settings_translate_search_title": {"message": "بح<PERSON> متعدد اللغات"}, "settings_translator_contextmenu_title": {"message": "التقاط للترجمة"}, "settings_translator_title": {"message": "ترجمة"}, "shai_xuan_dao_chu": {"message": "تصفية للتصدير"}, "shai_xuan_zi_duan": {"message": "تصفية الحقول"}, "shang_jia_shi_jian": {"message": "في وقت الرف"}, "shang_pin_biao_ti": {"message": "عنوان المنتج"}, "shang_pin_dui_bi": {"message": "مقارنة السلعة"}, "shang_pin_lian_jie": {"message": "رابط المنتج"}, "shang_pin_xin_xi": {"message": "معلومات المنتج"}, "share_modal__content": {"message": "شارك الموضوع مع أصدقائك"}, "share_modal__disable_for_while": {"message": "لا أريد مشاركة أي شيء"}, "share_modal__title": {"message": "هل تحب $extensionName$؟", "placeholders": {"extensionName": {"content": "$1"}}}, "sheng_yu_ku_cun": {"message": "المتبقي"}, "shi_fou_ke_ding_zhi": {"message": "هل هي قابلة للتخصيص؟"}, "shi_fou_ren_zheng_gong_ying_sha": {"message": "المورد المعتمد"}, "shi_fou_ren_zheng_gong_ying_shang_zong_shu": {"message": "الموردين المعتمدين"}, "shi_fou_you_mao_yi_dan_bao": {"message": "<PERSON><PERSON><PERSON> التجارة"}, "shi_fou_you_mao_yi_dan_bao_zong_shu": {"message": "ضمانات التجارة"}, "shipping_fee": {"message": "رسوم الشحن"}, "shop_followers": {"message": "متجر المتابعين"}, "shou_qi": {"message": "أقل"}, "similar_products_warn_max_platforms": {"message": "<PERSON><PERSON><PERSON> 5"}, "sku_calc_price": {"message": "السعر المحسوب"}, "sku_calc_price_settings": {"message": "إعدادات السعر المحسوب"}, "sku_formula": {"message": "الصيغة"}, "sku_formula_desc": {"message": "وصف الصيغة"}, "sku_formula_desc_text": {"message": "يدعم الصيغ الرياضية المعقدة، حيث يُمثل السعر الأصلي بـ A، ويُمثل الشحن بـ B.\n\n\n\nيدعم الأقواس ()، وعلامة الجمع (+)، وعلامة الطرح (-)، والضرب (*)، والقسمة (/).\n\n\n\nمثال:\n\n\n\n1. للحصول على 1.2 ضعف السعر الأصلي، ثم إضافة الشحن، تكون الصيغة: A*1.2+B.\n\n\n\n2. للحصول على السعر الأصلي مضافًا إليه يوان واحد، ثم الضرب في 1.2 مرة، تكون الصيغة: (A+1)*1.2.\n\n\n\n3. للحصول على السعر الأصلي مضافًا إليه 10 يوان، ثم الضرب في 1.2 مرة، ثم طرح 3 يوانات، تكون الصيغة: (A+10)*1.2-3."}, "sku_in_stock": {"message": "المتوفر"}, "sku_invalid_formula_format": {"message": "صيغة الصيغة غير صحيحة"}, "sku_inventory": {"message": "المخزون"}, "sku_link_copy_fail": {"message": "تم النسخ بنجاح، ولم يتم تحديد مواصفات وسمات SKU"}, "sku_link_copy_success": {"message": "تم النسخ بنجاح، وتم تحديد مواصفات وسمات SKU"}, "sku_list": {"message": "قائمة رموز المنتج"}, "sku_min_qrder_qty": {"message": "الح<PERSON> الأدنى لكمية الطلب"}, "sku_name": {"message": "اسم رمز المنتج"}, "sku_no": {"message": "الرقم"}, "sku_original_price": {"message": "السعر الأصلي"}, "sku_price": {"message": "سعر رمز المنتج"}, "stop_track_time_label": {"message": "الموعد النهائي للتتبع:"}, "suo_zai_di_qu": {"message": "موقع"}, "tab_pkg_quick_view": {"message": "مراقب اللوجستيات"}, "tab_product_details_price_history": {"message": "التاريخ"}, "tab_product_details_reviews": {"message": "المراجعات"}, "tab_product_details_seller_analysis": {"message": "تحليل"}, "tab_product_details_similar_products": {"message": "نفسه"}, "total_days_listed_per_product": {"message": "مجموع أيام الرفوف ÷ عدد المنتجات"}, "total_items": {"message": "العدد الإجمالي للمنتجات"}, "total_price_per_product": {"message": "مجموع الأسعار ÷ عدد المنتجات"}, "total_rating_per_product": {"message": "مجموع التقييمات ÷ عدد المنتجات"}, "total_revenue": {"message": "إجمالي الإيرادات"}, "total_revenue40_items": {"message": "إجمالي الإيرادات من $amount$ منتجًا في الصفحة الحالية", "placeholders": {"amount": {"content": "$1"}}}, "total_sales": {"message": "إجمالي المبيعات"}, "total_sales40_items": {"message": "إجمالي مبيعات $amount$ منتجًا في الصفحة الحالية", "placeholders": {"amount": {"content": "$1"}}}, "track_for_12_months": {"message": "المسار: سنة واحدة"}, "track_for_3_months": {"message": "المسار: 3 شهور"}, "track_for_6_months": {"message": "المسار: 6 شهور"}, "tracking_price_email_add_btn": {"message": "أضف بريدًا إلكترونيًا"}, "tracking_price_email_edit_btn": {"message": "تحرير البريد الإلكتروني"}, "tracking_price_email_intro": {"message": "سنقوم بإعلامك عبر البريد الإلكتروني."}, "tracking_price_email_invalid": {"message": "يرجى تقديم عنوان بريد إلكتروني صالح"}, "tracking_price_email_verified_desc": {"message": "يمكنك الآن تلقي تنبيه انخفاض الأسعار."}, "tracking_price_email_verified_title": {"message": "تم التحقق بنجاح"}, "tracking_price_email_verify_desc_line1": {"message": "لقد أرسلنا رابط التحقق إلى عنوان بريدك الإلكتروني ،"}, "tracking_price_email_verify_desc_line2": {"message": "يرجى التحقق من صندوق البريد الإلكتروني الخاص بك."}, "tracking_price_email_verify_title": {"message": "التحقق من البريد الإلكتروني"}, "tracking_price_web_push_notification_intro": {"message": "على سطح المكتب: يمكن لـ AliPrice مراقبة أي منتج من أجلك وإرسال إشعار Web Push إليك بمجرد تغير السعر."}, "tracking_price_web_push_notification_title": {"message": "إخطارات دفع الويب"}, "translate_im__login_required": {"message": "تمت الترجمة بواسطة AliPrice، يرجى تسجيل الدخول إلى $loginUrl$", "placeholders": {"loginUrl": {"content": "$1"}}}, "translate_im__paste_fail": {"message": "ترجمتها ونسخها إلى الحافظة، ولكن نظرًا لمحدودية Aliwangwang، تحتاج إلى لصقها يدويًا!"}, "translate_im__send": {"message": "ترجمة وإرسال"}, "translate_search": {"message": "ترجمة والبحث"}, "translation_originals_translated": {"message": "اصلي وصيني"}, "translation_translated": {"message": "صينى"}, "translator_btn_capture_txt": {"message": "ترجمة"}, "translator_language_auto_detect": {"message": "كش<PERSON>ي"}, "translator_language_detected": {"message": "تم العثور"}, "translator_language_search_placeholder": {"message": "لغة البحث"}, "try_again": {"message": "حاول مجددا"}, "tu_pian_chi_cun": {"message": "حجم الصورة:"}, "tu_pian_lian_jie": {"message": "رابط الصورة"}, "tui_huan_ti_yan": {"message": "تجربة العودة"}, "tui_huan_ti_yan__desc": {"message": "تقييم مؤشرات ما بعد البيع للبائعين"}, "tutorial__show_all": {"message": "كل المميزات"}, "tutorial_ae_popup_title": {"message": "قم بتثبيت الامتداد ، افتح Aliexpress"}, "tutorial_aliexpress_reviews_analysis": {"message": "تحليل مراجعة AliExpress"}, "tutorial_aliprice_agent_with1688_desc_l1": {"message": "دعم USD"}, "tutorial_aliprice_agent_with1688_desc_l2": {"message": "الشحن إلى كوريا/اليابان/البر الرئيسي للصين"}, "tutorial_aliprice_agent_with1688_title": {"message": "1688 يدعم الشراء من الخارج"}, "tutorial_auto_apply_coupon_title": {"message": "تطبيق القسيمة تلقائيًا"}, "tutorial_btn_end": {"message": "نهاية"}, "tutorial_btn_example": {"message": "مثال"}, "tutorial_btn_have_a_try": {"message": " حسنا ، لديك محاولة"}, "tutorial_btn_next": {"message": "التالى"}, "tutorial_btn_see_more": {"message": "أكثر"}, "tutorial_compare_products": {"message": "قارن بين المنتجات"}, "tutorial_currency_convert_title": {"message": "تحويل العملات"}, "tutorial_export_shopping_cart": {"message": "تصدير كملف CSV ودعم Taobao و 1688"}, "tutorial_export_shopping_cart_title": {"message": "عربة التصدير"}, "tutorial_price_history_pro": {"message": "معروض في صفحة تفاصيل المنتج.\nدعم Shopee ، Lazada ، Amazon ، Ebay"}, "tutorial_price_history_pro_title": {"message": "تاريخ الأسعار وتاريخ الطلب للعام بأكمله"}, "tutorial_sbi_context_menu_screenshot_title": {"message": "التقاط للبحث عن طريق الصورة"}, "tutorial_translate_search": {"message": "ترجمة للبحث"}, "tutorial_translate_search_and_package_tracking": {"message": "البحث عن الترجمة وتتبع الطرود"}, "unit_bao": {"message": "pcs"}, "unit_ben": {"message": "pcs"}, "unit_bi": {"message": "طلبات"}, "unit_chuang": {"message": "pcs"}, "unit_dai": {"message": "pcs"}, "unit_dui": {"message": "prs"}, "unit_fen": {"message": "pcs"}, "unit_ge": {"message": "pcs"}, "unit_he": {"message": "pcs"}, "unit_jian": {"message": "pcs"}, "unit_li_fang_mi": {"message": "m³"}, "unit_ping": {"message": "pcs"}, "unit_ping_fang_mi": {"message": "㎡"}, "unit_shuang": {"message": "prs"}, "unit_tai": {"message": "pcs"}, "unit_ti": {"message": "pcs"}, "unit_tiao": {"message": "pcs"}, "unit_xiang": {"message": "pcs"}, "unit_zhang": {"message": "ج<PERSON><PERSON>ز كمبيوتر شخصى"}, "unit_zhi": {"message": "pcs"}, "verify_contact_support": {"message": "الاتصال بالدعم"}, "verify_human_verification": {"message": "التحقق البشري"}, "verify_unusual_access": {"message": "تم اكتشاف وصول غير عادي"}, "view_history_clean_all": {"message": "نظف كل شيء"}, "view_history_clean_all_warring": {"message": "تنظيف جميع السجلات المعروضة؟"}, "view_history_clean_all_warring_title": {"message": "تحذير"}, "view_history_viewd": {"message": "تمت المشاهدة"}, "website": {"message": "موقع إلكتروني"}, "weight": {"message": "وزن"}, "wu_fa_huo_qu_dao_gai_shu_ju": {"message": "<PERSON>ير قادر على الحصول على البيانات"}, "wu_liu_shi_xiao": {"message": "الشحن في الوقت المحدد"}, "wu_liu_shi_xiao__desc": {"message": "معدل التحصيل خلال 48 ساعة ومعدل الاستيفاء من متجر البائع"}, "xia_dan_jia": {"message": "السعر النهائي"}, "xian_xuan_ze_product_attributes": {"message": "حدد أولاً سمات المنتج"}, "xiao_liang": {"message": "حجم المبيعات"}, "xiao_liang_zhan_bi": {"message": "نسبة حجم المبيعات"}, "xiao_shi": {"message": "$num$ ساعة", "placeholders": {"num": {"content": "$1"}}}, "xiao_shou_e": {"message": "الإيرادات"}, "xiao_shou_e_zhan_bi": {"message": "نسبة الإيرادات"}, "xuan_zhong_x_tiao_ji_lu": {"message": "حدد $amount$ سجلات", "placeholders": {"amoun": {"content": "$1"}, "amount": {"content": "$1"}}}, "yan_xuan": {"message": "الاختيار"}, "yi_ding_zai_zuo_ce": {"message": "مثبت"}, "yi_jia_zai_wan_suo_you_shang_pin": {"message": "تم تحميل جميع المنتجات"}, "yi_nian_xiao_liang": {"message": "المبيعات السنوية"}, "yi_nian_xiao_liang_zhan_bi": {"message": "حصة المبيعات السنوية"}, "yi_nian_xiao_shou_e": {"message": "المبيعات السنوية"}, "yi_nian_xiao_shou_e_zhan_bi": {"message": "حصة المبيعات السنوية"}, "yi_shua_xin": {"message": "منت<PERSON>ش"}, "yin_cang_xiang_tong_dian": {"message": "إخفاء أوجه التشابه"}, "you_xiao_liang": {"message": "مع حجم المبيعات"}, "yu_ji_dao_da_shi_jian": {"message": "وقت الوصول المقدر"}, "yuan_gong_ren_shu": {"message": "<PERSON><PERSON><PERSON> الموظفين"}, "yue_cheng_jiao": {"message": "الحجم الشهري"}, "yue_dai_xiao": {"message": "إسقاط الشحن"}, "yue_dai_xiao__desc": {"message": "مبيعات دروبشيبينغ في آخر 30 يومًا"}, "yue_dai_xiao_pai_xu__desc": {"message": "مبيعات الدروبشيبينغ خلال آخر 30 يومًا، مرتبة من الأعلى إلى الأقل"}, "yue_xiao_liang__desc": {"message": "حجم المبيعات في آخر 30 يومًا"}, "zhan_kai": {"message": "أكثر"}, "zhe_kou": {"message": "الخصم"}, "zhi_chi_yi_jian_dai_fa": {"message": "إسقاط الشحن"}, "zhi_chi_yi_jian_dai_fa_bao_you": {"message": "ًالشحن مجانا"}, "zhi_fu_ding_dan_shu": {"message": "الطلبات المدفوعة"}, "zhi_fu_ding_dan_shu__desc": {"message": "ع<PERSON><PERSON> الطلبات لهذا المنتج (30 يومًا)"}, "zhu_ce_xing_zhi": {"message": "طبيعة التسجيل"}, "zi_ding_yi_tiao_jian": {"message": "الشروط المخصصة"}, "zi_duan": {"message": "الحقول"}, "zi_ti_xiao_liang": {"message": "تم بيع التنوع"}, "zong_he_fu_wu_fen": {"message": "تقييم عام"}, "zong_he_fu_wu_fen__desc": {"message": "التقييم العام لخدمة البائع"}, "zong_he_fu_wu_fen__short": {"message": "تقييم"}, "zong_he_ti_yan_fen": {"message": "تقييم"}, "zong_he_ti_yan_fen_3": {"message": "أقل من 4 نجوم"}, "zong_he_ti_yan_fen_4": {"message": "4 - 4.5 نجوم"}, "zong_he_ti_yan_fen_4_5": {"message": "4.5 - 5.0 نجوم"}, "zong_he_ti_yan_fen_5": {"message": "5 نجوم"}, "zong_ku_cun": {"message": "إجمالي المخزون"}, "zong_xiao_liang": {"message": "إجمالي المبيعات"}, "zui_jin_30D_3Min_xiang_ying_lv": {"message": "معدل الاستجابة لمدة 3 دقائق في آخر 30 يومًا"}, "zui_jin_30D_48H_lan_shou_lv": {"message": "معدل استرداد 48 ساعة في آخر 30 يومًا"}, "zui_jin_30D_48H_lv_yue_lv": {"message": "معدل أداء 48 ساعة في آخر 30 يومًا"}, "zui_jin_30D_jiao_yi_ji_lu": {"message": "سجل التداول (30 يومًا)"}, "zui_jin_30D_jiao_yi_ji_lu__desc": {"message": "سجل التداول (30 يومًا)"}, "zui_jin_30D_jiu_fen_lv": {"message": "معدل النزاع في آخر 30 يومًا"}, "zui_jin_30D_pin_zhi_tui_kuan_lv": {"message": "معدل استرداد الجودة في آخر 30 يومًا"}, "zui_jin_30D_zhi_fu_ding_dan_shu": {"message": "عدد أوامر الدفع في آخر 30 يومًا"}}