import { createLogger } from '../helpers/logger.js';
import { getCommitsSinceTag, getLatestTagForPackage } from './git.js';
import { parseCommit } from './commit-parser.js';
import { determineNextVersion, formatChangelog } from './generator.js';

const logger = createLogger('预览');

/**
 * 为指定插件生成并显示变更日志预览
 *
 * 该函数为指定插件生成发布预览，包含以下步骤：
 * - 获取插件的最新 git 标签作为起始点
 * - 获取自上次标签以来的所有提交记录
 * - 解析提交记录并过滤出适用于该插件的提交
 * - 根据提交类型确定下一个版本号
 * - 格式化变更日志内容并显示预览
 * - 如果没有新提交，显示维护版本的预览
 *
 * @param {string} extensionName - 要预览的插件名称，必须与插件目录名一致
 * @returns {Promise<void>} 无返回值，直接在控制台输出预览内容
 *
 * @example
 * // 预览 cookies_manager 插件的变更日志
 * await previewChangelog('cookies_manager');
 * // 输出:
 * // [cookies_manager 预览]
 * // 预估版本更新: 1.0.0 -> 1.1.0
 * // --- RELEASE.md 内容 ---
 * // ### ✨ 新功能
 * // - 添加新功能 (Issue-ID: 1#20250101-01)
 *
 * @example
 * // 预览没有新提交的插件
 * await previewChangelog('stable_extension');
 * // 输出维护版本的预览内容
 */
export async function previewChangelog(extensionName: string) {
  logger.info(`正在为插件生成预览: ${extensionName}`);

  const lastTag = await getLatestTagForPackage(extensionName);
  const commitsRaw = await getCommitsSinceTag(lastTag);
  const commits = commitsRaw
    .map(parseCommit)
    .filter((c): c is NonNullable<typeof c> => c !== null)
    .filter((c) => c.appliesTo.includes(extensionName));

  if (commits.length === 0) {
    logger.warn(`自上次发布以来，未找到 ${extensionName} 的新提交。将显示维护版本预览。`);
  }

  const lastVersion = lastTag ? lastTag.split('-v')[1] : '0.0.0';
  const nextVersion = determineNextVersion(lastVersion, commits);
  const changelogContent = formatChangelog(commits);

  logger.info(`[${extensionName} 预览]`);
  logger.info(`预估版本更新: ${lastVersion} -> ${nextVersion}`);
  logger.info('--- RELEASE.md 内容 ---');
  console.log(changelogContent);
}
