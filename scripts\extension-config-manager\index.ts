/**
 * @fileoverview 扩展配置管理器的公共 API 入口
 * @description 提供扩展配置处理的核心 API，包括配置定义、处理、生成等功能
 */

import path from 'path';
import { processConfig } from './config-processor.js';
import { generateExtensionConfigs } from './generator.js';
import type { ExtensionConfig, ProcessedVariantConfig } from './types.js';
import { projectPaths } from '../helpers/utils.js';
import { basicDefaultPermissions } from '../helpers/permissions.js';
import { pathToFileURL } from 'url';

// #region --- 类型导出 ---

export * from './types.js';

// #endregion

// #region --- 核心 API ---

/**
 * @description 在内存中处理 extension.config.ts，返回所有处理完成的变体配置
 * @param config 从 extension.config.ts 文件中导入的原始配置对象
 * @returns 一个以 variantTarget 为键，处理后的变体配置 (ProcessedVariantConfig) 为值的对象
 *
 * 功能说明：
 * 1. 接收原始的扩展配置对象
 * 2. 调用配置处理器进行完整的配置处理
 * 3. 返回所有变体的处理结果，每个变体都是独立完整的配置
 * 4. 这是配置系统的核心入口函数
 */
export function defineExtensionConfig(
  config: ExtensionConfig,
): Record<string, ProcessedVariantConfig> {
  return processConfig(config);
}

/**
 * @description 加载并处理指定插件的配置，可选择性地生成文件
 * @param extensionName 插件名称
 * @param options 处理选项
 * @returns 处理后的变体配置对象
 *
 * 功能说明：
 * 1. 动态导入指定插件的 extension.config.ts 文件
 * 2. 获取已经通过 defineExtensionConfig 处理的配置对象
 * 3. 根据 variantTargets 参数过滤特定的变体配置
 * 4. 如果 generateMessages 为 true，则生成配置文件和语言包
 * 5. 返回过滤后的变体配置对象
 */
export async function getProcessedExtensionConfig(
  extensionName: string,
  options: {
    variantTargets?: string[];
    generateMessages?: boolean;
  } = {},
): Promise<Record<string, ProcessedVariantConfig>> {
  // 动态导入插件配置 - extension.config.ts 已经通过 defineExtensionConfig 处理过了
  const configPath = path.join(projectPaths.extensions, extensionName, 'extension.config.ts');
  const configModule = await import(pathToFileURL(configPath).href);

  // 直接使用已处理的配置对象
  const allProcessedVariants: Record<string, ProcessedVariantConfig> = configModule.default || {};

  // 如果指定了特定的 variantTargets，则过滤出指定的变体配置
  let filteredVariants = allProcessedVariants;
  if (options.variantTargets && options.variantTargets.length > 0) {
    filteredVariants = {};
    for (const target of options.variantTargets) {
      if (allProcessedVariants[target]) {
        filteredVariants[target] = allProcessedVariants[target];
      }
    }
  }

  // 如果需要生成配置文件和语言包到文件系统
  if (options.generateMessages) {
    await generateExtensionConfigs(filteredVariants);
  }

  return filteredVariants;
}

/**
 * 返回一个基础配置模板，用于快速创建新的 extension.config.ts。
 * @returns {ExtensionConfig}
 */
export function getBaseExtensionConfig(): ExtensionConfig {
  return {
    name: 'my-extension',
    version: '2.0.0',
    manifestVersion: 3,
    defaultLocale: 'en',
    i18n: {
      locales: [],
      includeKeys: [],
      excludeKeys: [],
      chromeOnlyLocales: [],
      chromeOnlyKeys: [],
    },
    manifest: {
      name: '__MSG_EXTENSION_NAME__',
      description: '__MSG_EXTENSION_DESCRIPTION__',
      // icons: {
      //   '16': 'icons/16.png',
      //   '48': 'icons/48.png',
      //   '128': 'icons/128.png',
      // },
      permissions: [...basicDefaultPermissions],
    },
    variants: [
      // {
      //   variantId: 'my-variant',
      //   variantName: 'My Variant',
      //   variant: 'master',
      //   webstore: 'chrome',
      // },
    ],
  };
}

// #endregion

// #region --- 文件与目录 API ---

export { generateExtensionConfigs } from './generator.js';
export { listExtensions, listExtensionVariants, cleanExtensionConfigs } from './utils.js';

// #endregion
