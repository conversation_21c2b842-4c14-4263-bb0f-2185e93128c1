/**
 * @fileoverview 构建工具函数集合
 * @description 提供构建流程中所需的核心工具函数，包括构建队列创建、构建计划生成、缓存策略等
 */

import { execa } from 'execa';
import fs from 'fs-extra';
import path from 'path';
import {
  listExtensionVariants,
  getProcessedExtensionConfig,
  cleanExtensionConfigs,
} from '../extension-config-manager/index.js';
import type { BuildOptions, BuildQueueItem, BuildPlanItem } from './types.js';
import { createLogger, projectPaths, GIT_HOOK_CRITICAL_PATHS } from '../helpers/index.js';

const logger = createLogger('BuildUtils');

/**
 * @description 创建并验证构建队列
 * @param buildQueue 待构建的插件列表
 * @param options 构建选项配置
 * @returns 验证通过的构建队列
 *
 * @example
 * // 开发模式 - 必须指定单个插件和单个渠道包
 * const devQueue = await createBuildQueue([
 *   { extensionName: 'cookies_manager', variantTargets: ['chrome-mv3-master'] }
 * ], { mode: 'dev' });
 *
 * // 生产模式 - 可以有多个插件，空数组表示该插件的所有渠道包
 * const releaseQueue = await createBuildQueue([
 *   { extensionName: 'cookies_manager', variantTargets: [] }, // 所有渠道包
 *   { extensionName: 'price_tracker', variantTargets: ['chrome-mv3-master'] } // 指定渠道包
 * ], { mode: 'release' });
 *
 * 验证规则：
 * - 开发模式：只允许一个插件和一个渠道包，必须明确指定
 * - 生产模式：可以多个插件，空渠道包列表表示该插件的所有渠道包
 *
 * 功能说明：
 * 1. 根据构建模式进行预验证（开发模式限制单个插件和渠道包）
 * 2. 获取所有可用的插件和变体信息进行验证
 * 3. 验证插件名称是否存在
 * 4. 处理空的渠道包列表（生产模式下自动填充所有可用渠道包）
 * 5. 验证指定的渠道包是否存在
 */
export async function createBuildQueue(
  buildQueue: BuildQueueItem[],
  options: BuildOptions,
): Promise<BuildQueueItem[]> {
  const { mode } = options;

  if (!buildQueue || buildQueue.length === 0) {
    throw new Error('构建队列不能为空');
  }

  // 1. 模式特定的预验证
  if (mode === 'dev') {
    // 开发模式限制：只能构建单个插件
    if (buildQueue.length !== 1) {
      throw new Error('开发模式（dev）下只允许构建一个插件');
    }

    const queueItem = buildQueue[0];
    // 开发模式下，如果没有指定渠道包或指定了多个渠道包，都是错误的
    if (!queueItem.variantTargets || queueItem.variantTargets.length === 0) {
      throw new Error('开发模式（dev）下必须指定一个渠道包');
    }
    if (queueItem.variantTargets.length !== 1) {
      throw new Error('开发模式（dev）下只允许构建一个渠道包');
    }
  } else if (mode === 'release') {
    // 发布模式验证：确保至少有一个有效的构建目标
    const hasValidTargets = buildQueue.some(
      (item) => item.extensionName && item.extensionName.trim().length > 0,
    );
    if (!hasValidTargets) {
      throw new Error('发布模式（release）下构建列表不能为空');
    }
  }

  // 2. 获取所有可用的插件和变体信息
  const { extensions: availableExtensions } = await listExtensionVariants();

  // 3. 验证插件名和渠道包是否存在，并处理空的 variantTargets
  for (const queueItem of buildQueue) {
    const { extensionName } = queueItem;
    let { variantTargets } = queueItem;

    // 验证插件名是否存在
    if (!availableExtensions[extensionName]) {
      throw new Error(`插件不存在: ${extensionName}`);
    }

    // 处理空的 variantTargets（只在生产模式下允许）
    if (!variantTargets || variantTargets.length === 0) {
      if (mode === 'release') {
        // 生产模式：如果没有指定渠道包，则使用所有可用的渠道包
        variantTargets = availableExtensions[extensionName];
        queueItem.variantTargets = variantTargets; // 更新原始对象
      }
      // 开发模式的情况已经在上面的预验证中处理了
    } else {
      // 验证指定的渠道包是否存在
      const availableVariants = availableExtensions[extensionName];
      for (const variantTarget of variantTargets) {
        if (!availableVariants.includes(variantTarget)) {
          throw new Error(`插件 ${extensionName} 的渠道包不存在: ${variantTarget}`);
        }
      }
    }
  }

  logger.info(`构建队列验证通过: ${buildQueue.length} 个插件`);
  return buildQueue;
}

/**
 * @description 生成构建计划
 * @param buildQueue 验证后的构建队列
 * @param options 构建选项
 * @returns 构建计划列表
 *
 * 功能说明：
 * 1. 遍历构建队列中的每个插件
 * 2. 确定缓存策略（是否使用缓存）
 * 3. 检查缓存文件是否存在，决定是否重新生成配置
 * 4. 调用配置处理器生成或加载配置文件
 * 5. 为每个渠道包创建构建计划项
 */
export async function generateBuildPlan(
  buildQueue: BuildQueueItem[],
  options: BuildOptions,
): Promise<BuildPlanItem[]> {
  const buildPlan: BuildPlanItem[] = [];

  for (const queueItem of buildQueue) {
    const { extensionName, variantTargets } = queueItem;

    logger.verbose(`处理插件: ${extensionName}`);

    // 确定缓存策略
    const shouldUseCache = await determineCacheStrategy(options.mode, options.useCache);
    let usedCache = false;

    let processedConfigs: Record<string, any>;

    if (shouldUseCache) {
      // 检查是否所有渠道包的 extension.config.json 都存在
      const allConfigsExist = await checkAllConfigsExist(extensionName, variantTargets);

      if (allConfigsExist) {
        // 使用缓存：读取已存在的配置文件
        processedConfigs = await loadCachedConfigs(extensionName, variantTargets);
        usedCache = true;
        logger.verbose(`[${extensionName}] 使用缓存配置`);
      } else {
        // 重新生成配置文件
        processedConfigs = await getProcessedExtensionConfig(extensionName, {
          variantTargets,
          generateMessages: true, // 生成配置文件
        });
        usedCache = false;
        logger.verbose(`[${extensionName}] 缓存未命中，重新生成配置`);
      }
    } else {
      // 不使用缓存，直接重新生成配置文件
      processedConfigs = await getProcessedExtensionConfig(extensionName, {
        variantTargets,
        generateMessages: true, // 生成配置文件
      });
      usedCache = false;
      logger.verbose(`[${extensionName}] 不使用缓存，重新生成配置`);
    }

    logger.info(
      `[${extensionName}] ${usedCache ? '使用缓存' : '重新生成'} ${Object.keys(processedConfigs).length} 个渠道包的配置文件。`,
    );

    // 创建构建计划项
    for (const variantTarget in processedConfigs) {
      buildPlan.push({
        extensionName,
        variantTarget,
        config: processedConfigs[variantTarget],
        usedCache,
      });
    }
  }

  logger.info(`构建计划生成完成: ${buildPlan.length} 个渠道包`);
  return buildPlan;
}

/**
 * @description 确定缓存使用策略
 * @param {('dev'|'release')} mode - 构建模式
 * @param {(boolean|'auto')} [useCache] - 缓存选项
 * @returns {Promise<boolean>} 是否使用缓存
 *
 * @example
 * // 强制使用缓存
 * const shouldCache1 = await determineCacheStrategy('dev', true); // true
 *
 * // 强制不使用缓存
 * const shouldCache2 = await determineCacheStrategy('release', false); // false
 *
 * // 自动决策（默认行为）
 * const shouldCache3 = await determineCacheStrategy('dev', 'auto'); // 根据 Git 状态决定
 * const shouldCache4 = await determineCacheStrategy('release'); // false（生产模式默认不缓存）
 *
 * 缓存策略逻辑：
 * 1. 用户明确指定 true/false 时，直接使用用户选择
 * 2. 开发模式下自动检测：检查关键文件是否有修改，有修改则不使用缓存
 * 3. 生产模式下默认不使用缓存，确保构建结果的一致性
 */
export async function determineCacheStrategy(
  mode: 'dev' | 'release',
  useCache?: boolean | 'auto',
): Promise<boolean> {
  if (useCache === true) {
    logger.verbose('强制使用缓存');
    return true;
  } else if (useCache === false) {
    logger.verbose('强制不使用缓存');
    return false;
  } else {
    // useCache 为 'auto' 或 undefined，使用智能缓存策略
    if (mode === 'dev') {
      // 开发模式：检查关键文件是否有修改
      const hasModifications = await checkGitModifiedFiles();
      if (hasModifications) {
        logger.verbose('检测到关键文件修改，忽略缓存');
        return false;
      } else {
        logger.verbose('未检测到关键文件修改，使用缓存');
        return true;
      }
    } else {
      // 生产模式：默认不使用缓存，确保构建结果的一致性和可重现性
      logger.verbose('生产模式，不使用缓存');
      return false;
    }
  }
}

/**
 * @description 检查指定路径是否存在未提交的 Git 修改
 * @param {string[]} pathsToCheck - 要检查的路径列表（文件或目录）
 * @returns {Promise<boolean>} 如果有任何指定路径的文件被修改则返回 true
 *
 * @example
 * // 检查配置文件是否有修改
 * const hasConfigChanges = await checkGitModifiedFiles(['extension.config.ts']);
 *
 * // 检查多个关键路径
 * const hasCriticalChanges = await checkGitModifiedFiles(GIT_HOOK_CRITICAL_PATHS);
 *
 * Git 状态码说明：
 * - M: 已修改的文件
 * - A: 新添加的文件
 * - D: 已删除的文件
 * - ??: 未跟踪的文件
 *
 * 检查逻辑：
 * 1. 使用 `git status --porcelain` 获取工作区状态
 * 2. 解析状态输出，提取修改的文件路径
 * 3. 检查修改的文件是否以指定的任一关键路径开头
 * 4. 如果 Git 命令失败，为安全起见返回 true（不使用缓存）
 */
async function checkGitModifiedFiles(): Promise<boolean> {
  try {
    const { stdout } = await execa('git', ['status', '--porcelain']);
    if (!stdout.trim()) {
      logger.info('Git working directory is clean. Using cache.');
      return false;
    }

    const modifiedFiles = stdout
      .trim()
      .split('\n')
      .map((line) => line.slice(3)); // 移除状态码和空格，获取相对路径

    logger.debug(`Git modified files: \n${modifiedFiles.join('\n')}`);

    for (const checkPath of GIT_HOOK_CRITICAL_PATHS) {
      if (modifiedFiles.some((modifiedFile) => modifiedFile.startsWith(checkPath))) {
        logger.info(`Change detected in critical path: "${checkPath}". Invalidating cache.`);
        return true;
      }
    }

    logger.info('No critical file changes detected. Cache can be used.');
    return false;
  } catch (error) {
    logger.error(
      `Failed to execute Git command to check for modified files: ${error instanceof Error ? error.message : String(error)}`,
    );
    // 在 Git 命令失败时，为安全起见返回 true，强制重新生成配置
    return true;
  }
}

/**
 * @description 检查插件的所有渠道包的 extension.config.json 是否都存在
 * @param extensionName - 插件名称
 * @param variantTargets - 渠道包列表
 * @returns 是否所有配置文件都存在
 */
async function checkAllConfigsExist(
  extensionName: string,
  variantTargets: string[],
): Promise<boolean> {
  for (const variantTarget of variantTargets) {
    const configPath = path.join(
      projectPaths.extensions,
      extensionName,
      '.variants',
      variantTarget,
      'extension.config.json',
    );

    if (!(await fs.pathExists(configPath))) {
      return false; // 只要有一个不存在，就返回 false
    }
  }
  return true; // 所有配置文件都存在
}

/**
 * @description 加载缓存的配置文件
 * @param extensionName - 插件名称
 * @param variantTargets - 渠道包列表
 * @returns 缓存的配置对象
 */
async function loadCachedConfigs(
  extensionName: string,
  variantTargets: string[],
): Promise<Record<string, any>> {
  const configs: Record<string, any> = {};

  for (const variantTarget of variantTargets) {
    const configPath = path.join(
      projectPaths.extensions,
      extensionName,
      '.variants',
      variantTarget,
      'extension.config.json',
    );

    try {
      const configContent = await fs.readJson(configPath);
      configs[variantTarget] = configContent;
    } catch (error) {
      throw new Error(
        `读取缓存配置失败 ${extensionName}:${variantTarget}: ${error instanceof Error ? error.message : String(error)}`,
      );
    }
  }

  return configs;
}
