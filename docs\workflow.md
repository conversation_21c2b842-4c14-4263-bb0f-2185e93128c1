# Project Development and Release Workflow

This document provides a comprehensive guide to the development and release workflows used in this project. It is designed to ensure consistency, quality, and a high degree of automation, from writing a single line of code to publishing the final product.

The core philosophy is to separate concerns:
1.  **Daily Development**: Fast, iterative, and focused on a single task.
2.  **Formal Release**: Deliberate, automated, and safe, with clear quality gates.

---

## 1. Development Workflow (`pnpm dev`)

This workflow is used for day-to-day coding and testing. It is optimized for speed and rapid iteration by focusing on a single extension variant and leveraging caching and hot-reloading.

**Scenario:** A developer needs to add a new feature to the `cookies_manager` extension for the Chrome browser.

### Workflow Steps:

1.  **Run Command:** The developer initiates the development environment with a single command. If a specific variant isn't provided, a pre-configured default is used.
    ```bash
    pnpm dev cookies_manager chrome-mv3-master
    ```
2.  **Check Cache (for speed):** The build script first checks if a valid, pre-compiled configuration for this exact variant exists in `packages/extensions/cookies_manager/.variants/chrome-mv3-master/`. The goal is to do the minimum work necessary to start the server.
3.  **Generate Config (if needed):** If no valid cache is found (or if the developer runs with `--force`), the script generates the necessary configuration from the source `extension.config.ts`. This includes creating the final `extension.config.json` and processing all i18n `_locales` files. This ensures the environment is always based on the latest source of truth.
4.  **Execute WXT:** The script hands off control to the WXT development server (`wxt dev`). It passes the path to the generated configuration via an environment variable, completely decoupling the configuration generation from the WXT build tool.
5.  **Live Development:** WXT opens a development browser with the extension loaded. Any changes the developer makes to the source code (e.g., in a Vue or TypeScript file) will trigger an instantaneous hot-reload in the browser, allowing for a fluid and efficient coding experience.

---

## 2. Production Release Workflow

This is the formal, multi-stage process for creating an official, versioned release ready for public distribution. It is designed to be safe, repeatable, and fully automated after the initial setup.

It is divided into two distinct stages to separate Git history manipulation from the creation of physical build artifacts.

### **Stage 1: Versioning & Changelog Generation**

**Goal:** To analyze all new commits, automatically determine the correct new version number for each extension, generate a changelog, and create an immutable Git tag to mark the release point. This stage modifies the Git history.

**Scenario:** The team has completed several features and fixes for multiple extensions and is ready to declare a new version.

#### Phase 1.1: Developer Preparation (Prerequisites)

1.  **Commit Changes via `pnpm commit`:** All code changes **must** be committed using the interactive `pnpm commit` command. This is a critical quality gate. It enforces a structured commit message format, forcing the developer to specify the `type` of change (e.g., `feat`, `fix`) and, most importantly, the `Applies-To:` field, which links the commit to the specific extension(s) it affects. This link is what allows the release system to work automatically.
2.  **Update Issue Status:** The developer must ensure the `issue_status.json` file is up-to-date. This file acts as a second quality gate, preventing the release of code that is linked to issues marked as "In Progress" or unfinished.

#### Phase 1.2: Execute Release Script (Automated)

This process is handled by the `changelog-release` system.

1.  **Dry Run (Safety Check):** Before making any changes, the developer **must** run a dry run to preview the release plan.
    ```bash
    pnpm release run --dry-run
    ```
    The script analyzes the Git history since the last tag for each extension, showing the proposed version changes (e.g., `1.3.5 -> 1.4.0`), the commits triggering them, and any warnings about unfinished issues. This allows for verification before any permanent action is taken.

2.  **Execute Release:** Once the plan is verified, the developer runs the command to perform the release.
    ```bash
    pnpm release run
    ```
3.  **Confirmation:** The script displays the final release plan again and prompts for a final `y/N` confirmation.

4.  **System Actions (The Automation):** Upon confirmation, the script performs the following for each extension being released:
    *   Updates `changelog.json` with the new version number and formatted release notes derived from the commit messages.
    *   Creates a dedicated Git commit for the changelog update (e.g., `chore(release): publish cookies_manager v1.4.0`).
    *   Creates a version-specific, immutable Git tag (e.g., `cookies_manager-v1.4.0`). This tag serves as a permanent, historical marker for this exact version.

5.  **Push to Remote:** The automated process concludes here. The developer is then instructed to push the new commits and tags to the remote repository to share the release with the team.
    ```bash
    git push --follow-tags
    ```

---

### **Stage 2: Building & Packaging Release Artifacts**

**Goal:** To create the final, installable `.zip` packages for distribution. This stage does **not** modify Git history; it only creates build artifacts.

**Scenario:** Now that `v1.4.0` of `cookies_manager` is tagged in Git, the developer needs to build the installable packages for both the Chrome and Firefox stores.

#### Phase 2.1: Developer Preparation (Manual Task)

1.  **Configure Build Targets:** The developer specifies which extensions and variants to build in the `scripts/build/release-extensions.json` file. This file acts as the build plan for this stage.

#### Phase 2.2: Execute Build Script (Automated)

This process is handled by the `build` system.

1.  **Read Build Plan:** The script reads `release-extensions.json` to determine its workload.

2.  **Loop Through Variants:** The script iterates through each entry. For each variant, it performs a clean, production-grade build:
    *   **Generate Config (Clean Build):** It runs a fresh configuration generation. **No cache is used** to ensure the build is 100% clean and based on the current state of the repository.
    *   **Manifest Permission Guard (Security Check):**
        *   It reads the manifest from the *previous* build, which is kept in a backup location (`.manifest/<variantTarget>.manifest.json`).
        *   It compares the permissions of the old manifest with the newly generated one.
        *   If any permissions have been added or removed, it prints a **bold warning** to the console. This is a critical safety feature to prevent accidental changes that could lead to a rejected store submission or alarm users.
    *   **Execute WXT Build:** It invokes `wxt build` to create an optimized, production-ready package.
    *   **Generate `RELEASE.md`:** It creates a `RELEASE.md` file inside the final package. This file includes the version, build date, and the human-readable changelog, providing clear traceability within the artifact itself.
    *   **Update Manifest Backup:** It saves the newly built manifest to the `.manifest/` directory, so it can be used for the permission check in the *next* release.
    *   **Collect Stats:** It records metadata about the build (name, version, final package size) for the final summary.

3.  **Final Summary:** After all builds are complete, the script prints a summary table to the console, providing a clear overview of what was built.

    ```
    [Build] ✔️ Build process completed successfully.

    === Build Summary ===
    Total Extensions Built: 2
    Total Variants Built:   3

    ┌───────────────────┬──────────────────────┬─────────┬──────────────┐
    │ Extension         │ Variant Target       │ Version │ Package Size │
    ├───────────────────┼──────────────────────┼─────────┼──────────────┤
    │ cookies_manager   │ chrome-mv3-master    │ 1.4.0   │ 1.2 MB       │
    │ cookies_manager   │ firefox-mv2-master   │ 1.4.0   │ 1.1 MB       │
    │ price_tracker     │ chrome-mv3-master    │ 2.1.0   │ 850 KB       │
    └───────────────────┴──────────────────────┴─────────┴──────────────┘

    Build artifacts are located in the /release directory.
    ```

---

## 3. Post-Release Workflow

These are the final manual steps after all automated scripts have successfully run.

1.  **Push Commits and Tags:** After Stage 1 is complete, push the versioning commits and tags to the remote repository. The `--follow-tags` flag is important as it ensures that the new version tags are pushed along with the commits.
    ```bash
    git push --follow-tags
    ```
2.  **Distribute Artifacts:** After Stage 2 is complete, take the generated `.zip` files from the `/release` directory and upload them to their respective destinations (e.g., Chrome Web Store, Firefox Add-on Portal, self-hosted server).

