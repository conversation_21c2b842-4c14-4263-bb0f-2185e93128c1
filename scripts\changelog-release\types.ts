/**
 * 提交类型相关的类型定义和配置
 *
 * 该文件定义了项目中使用的所有提交类型配置，确保：
 * - changelog.ts 和 commitizen-adapter.mjs 使用一致的类型定义
 * - 提交类型的显示顺序和样式统一
 * - 便于维护和扩展新的提交类型
 */

/**
 * 解析后的提交对象接口
 */
export interface ParsedCommit {
  hash: string;
  subject: string;
  body: string;
  type: string;
  issueId: string | null;
  appliesTo: string[];
  isBreaking: boolean;
}

/**
 * 提交类型配置接口
 *
 * 定义了每种提交类型的显示信息：
 * - emoji: 对应的表情符号
 * - title: 中文标题
 * - order: 显示顺序（数字越小越靠前）
 * - description: 英文描述（用于 Commitizen）
 */
export interface CommitTypeConfig {
  emoji: string;
  title: string;
  order: number;
  description: string;
}

/**
 * 提交类型配置映射
 *
 * 统一定义所有支持的提交类型及其显示配置
 * 按照重要性和常用程度排序
 */
export const COMMIT_TYPE_CONFIG: Record<string, CommitTypeConfig> = {
  feat: {
    emoji: '✨',
    title: '新功能',
    order: 1,
    description: '引入新功能或特性',
  },
  fix: {
    emoji: '🐛',
    title: '问题修复',
    order: 2,
    description: '修复问题或缺陷',
  },
  perf: {
    emoji: '⚡️',
    title: '性能优化',
    order: 3,
    description: '提升性能的相关改动',
  },
  refactor: {
    emoji: '♻️',
    title: '重构',
    order: 4,
    description: '代码结构调整但不影响功能',
  },
  style: {
    emoji: '🎨',
    title: '样式',
    order: 5,
    description: '代码格式、样式等非功能性修改',
  },
  docs: {
    emoji: '📝',
    title: '文档',
    order: 6,
    description: '文档内容的新增或修改',
  },
  test: {
    emoji: '✅',
    title: '测试',
    order: 7,
    description: '新增或修正测试用例',
  },
  build: {
    emoji: '🛠️',
    title: '构建',
    order: 8,
    description: '构建流程或依赖相关变更',
  },
  ci: {
    emoji: '⚙️',
    title: 'CI',
    order: 9,
    description: 'CI 配置或脚本相关变更',
  },
  chore: {
    emoji: '🔧',
    title: '维护',
    order: 10,
    description: '其他不影响源代码和测试的维护性变更',
  },
  revert: {
    emoji: '🗑️',
    title: '回滚',
    order: 11,
    description: '撤销或回滚之前的提交',
  },
};

/**
 * 获取所有提交类型的列表（按顺序排序）
 *
 * @returns {string[]} 按 order 字段排序的提交类型数组
 */
export function getOrderedCommitTypes(): string[] {
  return Object.keys(COMMIT_TYPE_CONFIG).sort(
    (a, b) => COMMIT_TYPE_CONFIG[a].order - COMMIT_TYPE_CONFIG[b].order,
  );
}

/**
 * 检查是否为已知的提交类型
 *
 * @param {string} type - 要检查的提交类型
 * @returns {boolean} 如果是已知类型返回 true，否则返回 false
 */
export function isKnownCommitType(type: string): boolean {
  return type in COMMIT_TYPE_CONFIG;
}

/**
 * 获取提交类型的显示名称（包含 emoji 和标题）
 *
 * @param {string} type - 提交类型
 * @returns {string} 格式化的显示名称，如 "✨ 新功能"
 */
export function getCommitTypeDisplayName(type: string): string {
  const config = COMMIT_TYPE_CONFIG[type];
  return config ? `${config.emoji} ${config.title}` : '其他';
}

/**
 * Commitizen 选项接口
 */
export interface CommitizenChoice {
  value: string;
  name: string;
  title: string;
}

/**
 * 生成 Commitizen 的提交类型选项
 *
 * @returns {CommitizenChoice[]} 按顺序排列的 Commitizen 选项数组
 */
export function generateCommitizenChoices(): CommitizenChoice[] {
  return getOrderedCommitTypes().map((type) => {
    const config = COMMIT_TYPE_CONFIG[type];
    return {
      value: type,
      name: `${type}:${' '.repeat(Math.max(1, 10 - type.length))}${config.emoji} ${config.title} (${config.description})`,
      title: config.title,
    };
  });
}

/**
 * 按提交类型分组提交记录
 *
 * @param {ParsedCommit[]} commits - 要分组的提交记录数组
 * @returns {Map<string, ParsedCommit[]>} 按类型分组的提交记录 Map
 */
export function groupCommitsByType(commits: ParsedCommit[]): Map<string, ParsedCommit[]> {
  const commitsByType = new Map<string, ParsedCommit[]>();

  for (const commit of commits) {
    const type = commit.type;
    if (!commitsByType.has(type)) {
      commitsByType.set(type, []);
    }
    commitsByType.get(type)!.push(commit);
  }

  return commitsByType;
}

/**
 * 获取已知类型的提交记录（按优先级排序）
 *
 * @param {Map<string, ParsedCommit[]>} commitsByType - 按类型分组的提交记录
 * @returns {Array<[string, ParsedCommit[]]>} 排序后的 [类型, 提交记录] 数组
 */
export function getKnownTypeCommits(
  commitsByType: Map<string, ParsedCommit[]>,
): Array<[string, ParsedCommit[]]> {
  return Array.from(commitsByType.entries())
    .filter(([type]) => isKnownCommitType(type))
    .sort(([a], [b]) => COMMIT_TYPE_CONFIG[a].order - COMMIT_TYPE_CONFIG[b].order);
}

/**
 * 获取未知类型的提交记录
 *
 * @param {Map<string, ParsedCommit[]>} commitsByType - 按类型分组的提交记录
 * @returns {ParsedCommit[]} 未知类型的提交记录数组
 */
export function getUnknownTypeCommits(commitsByType: Map<string, ParsedCommit[]>): ParsedCommit[] {
  return Array.from(commitsByType.entries())
    .filter(([type]) => !isKnownCommitType(type))
    .flatMap(([, commits]) => commits);
}
