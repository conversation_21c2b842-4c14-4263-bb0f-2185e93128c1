/**
 * @fileoverview 构建工作流主入口
 * @description 提供完整的浏览器插件构建流程，支持开发和生产两种模式
 */

import { execa } from 'execa';
import type { Options as ExecaOptions } from 'execa';
import path from 'path';
import { createLogger, projectPaths } from '../helpers/index.js';
import type { BuildOptions, BuildQueueItem, BuildPlanItem } from './types.js';
import { createBuildQueue, generateBuildPlan } from './utils.js';

const logger = createLogger('BuildWorkflow');

/**
 * @description 构建工作流主入口
 * @param buildQueue 构建队列
 * @param options 构建选项
 * @returns Promise<void>
 *
 * @example
 * // 开发模式构建
 * await runBuildWorkflow([
 *   { extensionName: 'cookies_manager', variantTargets: ['chrome-mv3-master'] }
 * ], { mode: 'dev' });
 *
 * // 生产模式构建
 * await runBuildWorkflow([
 *   { extensionName: 'cookies_manager', variantTargets: [] }
 * ], { mode: 'release' });
 *
 * 构建流程：
 * 1. 创建构建队列 - 验证和解析构建目标
 * 2. 生成构建计划 - 处理配置生成和缓存策略
 * 3. 执行构建计划 - 运行 WXT 构建命令
 * 4. 完成构建 - 统计结果和后处理
 *
 * 功能说明：
 * 1. 验证构建队列的有效性，确保所有目标都存在
 * 2. 根据缓存策略生成或加载配置文件
 * 3. 根据构建模式执行不同的构建策略
 * 4. 统计构建结果并输出详细信息
 */
export async function runBuildWorkflow(
  buildQueue: BuildQueueItem[],
  options: BuildOptions,
): Promise<void> {
  const { mode } = options;
  logger.info(`构建流程开始 [${mode}]`);

  // 1. 创建并验证构建队列
  const validatedQueue = await createBuildQueue(buildQueue, options);
  if (validatedQueue.length === 0) {
    logger.warn('没有需要构建的目标，退出。');
    return;
  }

  // 2. 生成构建计划
  const buildPlan = await generateBuildPlan(validatedQueue, options);
  logger.success(`构建计划创建完成: ${buildPlan.length} 个渠道包`);

  // 3. 执行构建计划
  await executeBuildPlan(buildPlan, mode);

  // 4. 完成构建
  await finalizeBuild(buildPlan);

  logger.success('构建流程完成');
}

/**
 * @description 执行构建计划
 * @param buildPlan 构建计划列表
 * @param mode 构建模式
 * @returns Promise<void>
 *
 * 执行策略：
 * - 开发模式：只构建第一个目标（通常只有一个）
 * - 生产模式：逐个构建所有目标
 *
 * 功能说明：
 * 1. 根据构建模式选择不同的执行策略
 * 2. 开发模式下只构建第一个目标，提高开发效率
 * 3. 生产模式下逐个构建所有目标，确保完整性
 * 4. 每个目标都会调用 WXT 构建命令
 */
async function executeBuildPlan(
  buildPlan: BuildPlanItem[],
  mode: 'dev' | 'release',
): Promise<void> {
  logger.info('开始执行 WXT 构建');

  if (mode === 'dev') {
    // 开发模式：只构建一个目标
    const planItem = buildPlan[0];
    await executeWxtCommand(planItem, mode);
  } else {
    // 生产模式：逐个构建所有目标
    for (const planItem of buildPlan) {
      await executeWxtCommand(planItem, mode);
    }
  }

  logger.success('WXT 构建完成');
}

/**
 * @description 执行单个 WXT 构建命令
 * @param {BuildPlanItem} planItem - 构建计划项
 * @param {('dev'|'release')} mode - 构建模式
 * @returns {Promise<void>}
 *
 * @example
 * await executeWxtCommand({
 *   extensionName: 'cookies_manager',
 *   variantTarget: 'chrome-mv3-master',
 *   config: processedConfig,
 *   usedCache: false
 * }, 'dev');
 *
 * WXT 命令构建：
 * - 开发模式：启动开发服务器 (pnpm wxt)
 * - 生产模式：构建生产包 (pnpm wxt build)
 * - 通过环境变量传递渠道包配置路径
 */
async function executeWxtCommand(planItem: BuildPlanItem, mode: 'dev' | 'release'): Promise<void> {
  const { extensionName, variantTarget, config } = planItem;

  logger.info(`构建 ${extensionName}:${variantTarget}`);

  const variantTargetConfigPath = config.paths.extensionVariantTargetJSON;

  const command = 'pnpm';
  const args = [
    mode === 'dev' ? 'wxt' : 'wxt build',

    // 指定 WXT 配置文件路径
    '-c',
    path.resolve(projectPaths.workspace, 'wxt.config.ts'),

    // 指定 manifest 版本
    `--mv${config.manifestVersion}`,
  ];
  const options: ExecaOptions = {
    // cwd: config.paths.extension,
    stdio: 'inherit',

    // 设置环境变量，传递渠道包配置路径和目标
    env: {
      VARIANT_TARGET: variantTarget,
      VARIANT_TARGET_CONFIG_PATH: variantTargetConfigPath,
    },
  };

  // 开发模式下添加信号处理，避免 PowerShell 卡死问题
  const childProcess = execa(command, args, options);
  // 处理 Ctrl+C 信号，避免 PowerShell 询问终止批处理操作
  const handleExit = () => {
    logger.info('收到中断信号，正在停止开发服务器...');
    childProcess.kill('SIGTERM');
    process.exit(0);
  };
  process.on('SIGINT', handleExit);
  process.on('SIGTERM', handleExit);
  try {
    await childProcess;
  } catch (error: any) {
    // 忽略由于进程被终止导致的错误
    if (error?.signal === 'SIGTERM' || error?.signal === 'SIGINT') {
      logger.info('开发服务器已停止');
      process.exit(0);
    }
    throw error;
  }

  logger.verbose(`完成构建: ${extensionName}:${variantTarget}`);
}

/**
 * @description 完成构建并输出统计信息
 * @param {BuildPlanItem[]} buildPlan - 构建计划列表
 * @returns {Promise<void>}
 *
 * @example
 * await finalizeBuild([
 *   { extensionName: 'cookies_manager', variantTarget: 'chrome-mv3-master', config, usedCache: true },
 *   { extensionName: 'cookies_manager', variantTarget: 'chrome-mv3-tm', config, usedCache: false }
 * ]);
 *
 * 输出格式：
 * [插件更新] 共更新 1 个插件项目，2 个插件渠道包:
 * - cookies_manager v3.4.9
 *   - chrome-mv3-master
 *   - chrome-mv3-tm
 * [缓存使用] 1 个插件使用了缓存: cookies_manager
 *
 * 统计逻辑：
 * 1. 按插件分组统计渠道包和版本信息
 * 2. 插件级别的缓存统计（任一渠道包使用缓存即算使用缓存）
 * 3. 输出详细的构建结果和缓存使用情况
 */
async function finalizeBuild(buildPlan: BuildPlanItem[]): Promise<void> {
  // 按插件分组统计
  const extensionGroups = new Map<
    string,
    {
      variantTargets: string[];
      version: string;
      usedCache: boolean;
    }
  >();

  for (const item of buildPlan) {
    if (!extensionGroups.has(item.extensionName)) {
      extensionGroups.set(item.extensionName, {
        variantTargets: [],
        version: item.config.version,
        usedCache: item.usedCache,
      });
    }
    const group = extensionGroups.get(item.extensionName)!;
    group.variantTargets.push(item.variantTarget);
    // 如果任何一个渠道包使用了缓存，则认为该插件使用了缓存
    if (item.usedCache) {
      group.usedCache = true;
    }
  }

  const totalExtensions = extensionGroups.size;
  const totalVariantTargets = buildPlan.length;

  // 输出详细的构建统计
  logger.info(
    `[插件更新] 共更新 ${totalExtensions} 个插件项目，${totalVariantTargets} 个插件渠道包:`,
  );

  for (const [extensionName, group] of extensionGroups) {
    logger.info(`- ${extensionName} v${group.version}`);
    for (const variantTarget of group.variantTargets) {
      logger.info(`  - ${variantTarget}`);
    }
  }

  // 缓存统计（插件级别）
  const cachedExtensions: string[] = [];
  const regeneratedExtensions: string[] = [];

  for (const [extensionName, group] of extensionGroups) {
    if (group.usedCache) {
      cachedExtensions.push(extensionName);
    } else {
      regeneratedExtensions.push(extensionName);
    }
  }

  if (cachedExtensions.length > 0) {
    logger.info(
      `[缓存使用] ${cachedExtensions.length} 个插件使用了缓存: ${cachedExtensions.join(', ')}`,
    );
  }

  if (regeneratedExtensions.length > 0) {
    logger.info(
      `[重新生成] ${regeneratedExtensions.length} 个插件重新生成配置: ${regeneratedExtensions.join(', ')}`,
    );
  }

  // TODO: 生产模式的后处理工作
  // - 给每个插件都生成 `release/{extensionName}/{version}/RELEASED.md` 文件，内容为插件名，版本号，渠道包列表和更新的内容。
  // - 给每个插件渠道包都生成 `packages/{extensionName}/.manifest/manifest.{variantTarget}.json` 文件，用于留底，权限校验和对比。
  // - 统计构建结果
  // - 上传到 SVN/Git（如果需要）
}
