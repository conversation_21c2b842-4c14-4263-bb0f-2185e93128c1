/**
 * @fileoverview 国际化处理器
 * @description 负责所有 i18n 相关处理，包括语言包扫描、缓存、合并和消息生成
 */

import fs from 'fs-extra';
import { isEmpty } from 'lodash-es';
import {
  findLocaleMessageFiles,
  getLocaleFromPath,
  matchKey,
  parseVariantTarget,
} from './utils.js';
import type { ChromeMessage, LocaleMessages, ProcessedI18nConfig } from './types.js';
import { projectPaths, createLogger } from '../helpers/index.js';

const logger = createLogger('I18nProcessor');

// #region --- 强制性文件 I/O 缓存 ---

/**
 * @description 语言包文件缓存，避免重复读取相同文件
 */
const localeFileCache = new Map<string, Record<string, unknown>>();

/**
 * @description 读取语言包文件并缓存结果
 * @param filePath 语言包文件路径
 * @returns 语言包数据对象
 *
 * 功能说明：
 * 1. 首先检查缓存中是否已存在该文件的数据
 * 2. 如果缓存命中，直接返回缓存的数据
 * 3. 如果缓存未命中，读取文件并解析 JSON
 * 4. 将解析结果存入缓存以供后续使用
 * 5. 如果读取或解析失败，记录错误并返回空对象
 */
function readLocaleFile(filePath: string): Record<string, unknown> {
  if (localeFileCache.has(filePath)) {
    return localeFileCache.get(filePath)!;
  }
  try {
    const data = fs.readJsonSync(filePath);
    localeFileCache.set(filePath, data);
    return data;
  } catch (error) {
    logger.error(`读取或解析语言包文件失败: ${filePath}`, error);
    return {};
  }
}

// #endregion

// #region --- 语言包扫描与合并 ---

function getSharedLocaleFiles(): string[] {
  return findLocaleMessageFiles(projectPaths.sharedLocales);
}

import path from 'path';

// ... (rest of the file)

function getExtensionLocaleFiles(extensionName: string): string[] {
  return findLocaleMessageFiles(path.join(projectPaths.extensions, extensionName, 'locales'));
}

export function getAvailableLocales(extensionName: string): string[] {
  const files = getExtensionLocaleFiles(extensionName);
  const locales = files.map(getLocaleFromPath);
  return [...new Set(locales)].sort();
}

function mergeMessagesForLocale(locale: string, extensionName: string): Record<string, unknown> {
  const sharedFiles = getSharedLocaleFiles();
  const extensionFiles = getExtensionLocaleFiles(extensionName);

  const sharedFile = sharedFiles.find((file) => getLocaleFromPath(file) === locale);
  const extensionFile = extensionFiles.find((file) => getLocaleFromPath(file) === locale);

  const messages = [sharedFile, extensionFile].filter((p): p is string => !!p).map(readLocaleFile);

  return messages.reduce((acc, current) => ({ ...acc, ...current }), {});
}

// #endregion

// #region --- 条件文案与占位符处理 ---

const PLACEHOLDER_REGEX = /\$([a-zA-Z_][a-zA-Z0-9_]*)\$/g;

function isConditionalMessage(
  value: unknown,
): value is Record<string, unknown> & { message: string } {
  if (typeof value !== 'object' || value === null || !('message' in value)) {
    return false;
  }
  const standardFields = new Set(['message', 'description', 'placeholders']);
  return Object.keys(value).some((key) => !standardFields.has(key));
}

function resolveConditionalMessage(
  message: Record<string, unknown>,
  variantTarget: string,
): string {
  if (typeof message === 'string') return message;
  if (!isConditionalMessage(message)) {
    return typeof message.message === 'string' ? message.message : '';
  }

  const variantInfo = parseVariantTarget(variantTarget);
  if (message[variantInfo.target]) return message[variantInfo.target] as string;
  if (message[variantInfo['webstore-variant']])
    return message[variantInfo['webstore-variant']] as string;
  if (message[variantInfo.webstore]) return message[variantInfo.webstore] as string;

  return typeof message.message === 'string' ? message.message : '';
}

function formatForVue(message: string): string {
  return message.replace(PLACEHOLDER_REGEX, '{$1}');
}

function formatForChrome(message: string): ChromeMessage {
  const chromeMessage: ChromeMessage = { message };
  const placeholders: Record<string, { content: string }> = {};
  const matches = Array.from(message.matchAll(PLACEHOLDER_REGEX));

  if (matches.length > 0) {
    const seen = new Set<string>();
    let index = 1;
    for (const match of matches) {
      const placeholderName = match[1];
      if (!seen.has(placeholderName)) {
        placeholders[placeholderName] = { content: `$${index++}` };
        seen.add(placeholderName);
      }
    }
    if (!isEmpty(placeholders)) {
      chromeMessage.placeholders = placeholders;
    }
  }

  return chromeMessage;
}

// #endregion

// #region --- 主处理器 ---

interface GenerateMessagesOptions {
  extensionName: string;
  variantTarget: string;
  includeKeys?: string[];
  excludeKeys?: string[];
  chromeOnlyLocales?: string[];
  chromeOnlyKeys?: string[];
}

export function generateI18n(options: GenerateMessagesOptions): ProcessedI18nConfig {
  const {
    extensionName,
    variantTarget,
    includeKeys,
    excludeKeys,
    chromeOnlyLocales = [],
    chromeOnlyKeys = [],
  } = options;

  const allLocales = getAvailableLocales(extensionName);
  const result: ProcessedI18nConfig = {
    locales: allLocales.filter((locale) => !chromeOnlyLocales.includes(locale)),
    vueMessages: {},
    chromeMessages: {},
    // Keep original filter settings for reference
    includeKeys: includeKeys || [],
    excludeKeys: excludeKeys || [],
    chromeOnlyLocales: chromeOnlyLocales,
    chromeOnlyKeys: chromeOnlyKeys,
  };

  for (const locale of allLocales) {
    const mergedMessages = mergeMessagesForLocale(locale, extensionName);
    const vueLocaleMessages: LocaleMessages = {};
    const chromeLocaleMessages: Record<string, ChromeMessage> = {};

    for (const key in mergedMessages) {
      const messageValue = mergedMessages[key];
      const resolvedMsg = resolveConditionalMessage(
        messageValue as Record<string, unknown>,
        variantTarget,
      );

      // Populate Vue messages
      if (result.locales.includes(locale) && matchKey(key, includeKeys, excludeKeys)) {
        vueLocaleMessages[key] = formatForVue(resolvedMsg);
      }

      // Populate Chrome messages
      if (matchKey(key, chromeOnlyKeys)) {
        chromeLocaleMessages[key] = formatForChrome(resolvedMsg);
      }
    }

    if (!isEmpty(vueLocaleMessages)) {
      result.vueMessages[locale] = vueLocaleMessages;
    }
    if (!isEmpty(chromeLocaleMessages)) {
      result.chromeMessages[locale] = chromeLocaleMessages;
    }
  }

  return result;
}

// #endregion
