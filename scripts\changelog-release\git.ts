import { execaCommand } from 'execa';
import { glob } from 'glob';
import path from 'path';
import { createLogger } from '../helpers/logger.js';
import { projectPaths } from '../helpers/utils.js';

const logger = createLogger('Git工具');

export interface ExtensionPackage {
  name: string;
  path: string;
}

/**
 * 在 `packages/extensions` 目录中查找所有插件包
 *
 * 该函数扫描扩展目录，通过查找子目录来识别所有插件包：
 * - 直接读取 extensions 目录下的所有子目录
 * - 过滤出包含 wxt.config.ts 或 extension.config.ts 的目录作为有效插件
 * - 返回包含名称和路径的插件包信息数组
 * - 记录找到的插件包数量用于调试
 *
 * @returns {Promise<ExtensionPackage[]>} 包含插件包信息的对象数组
 *   - name: 插件包名称（目录名）
 *   - path: 插件包的绝对路径
 *
 * @example
 * // 获取所有插件包
 * const packages = await getExtensionPackages();
 * // 返回: [
 * //   { name: 'cookies_manager', path: '/path/to/packages/extensions/cookies_manager' },
 * //   { name: 'price_tracker', path: '/path/to/packages/extensions/price_tracker' }
 * // ]
 */
export async function getExtensionPackages(): Promise<ExtensionPackage[]> {
  try {
    // 使用 glob 查找包含配置文件的目录
    const configPaths = await glob('*/+(wxt.config.ts|extension.config.ts)', {
      cwd: projectPaths.extensions,
      absolute: true,
    });

    const packages = configPaths.map((configPath) => {
      const pkgDir = path.dirname(configPath);
      const pkgName = path.basename(pkgDir);
      return {
        name: pkgName,
        path: pkgDir,
      };
    });

    // 去重（如果一个目录同时有两个配置文件）
    const uniquePackages = packages.filter(
      (pkg, index, self) => index === self.findIndex((p) => p.name === pkg.name),
    );

    logger.info(`找到 ${uniquePackages.length} 个插件包。`);
    return uniquePackages;
  } catch (error) {
    logger.error('扫描插件包时出错:', error);
    return [];
  }
}

/**
 * 查找特定包的最新 git 标签
 *
 * 该函数使用 git describe 命令查找指定插件包的最新版本标签：
 * - 使用标签模式 "{packageName}-v*" 匹配特定包的版本标签
 * - 使用 --abbrev=0 参数获取完整的标签名（不包含提交哈希）
 * - 如果找不到匹配的标签，git describe 会抛出错误，函数返回 null
 * - 记录调试信息和警告信息用于问题排查
 *
 * @param {string} packageName - 插件包名称，用于匹配标签模式
 * @returns {Promise<string | null>} 最新的标签名（如 "cookies_manager-v1.2.0"），如果找不到则返回 null
 *
 * @example
 * // 查找 cookies_manager 包的最新标签
 * const tag = await getLatestTagForPackage('cookies_manager');
 * // 返回: "cookies_manager-v1.2.0" 或 null
 *
 * @example
 * // 首次发布的包（无标签）
 * const tag = await getLatestTagForPackage('new_extension');
 * // 返回: null
 */
export async function getLatestTagForPackage(packageName: string): Promise<string | null> {
  try {
    const { stdout } = await execaCommand(
      `git describe --tags --match "${packageName}-v*" --abbrev=0`,
    );
    const tag = stdout.trim();
    logger.debug(`包 ${packageName} 的最新标签: ${tag}`);
    return tag;
  } catch (error) {
    // 如果找不到标签，git describe 会报错，这是正常情况
    logger.warn(`未找到包 ${packageName} 的标签。`);
    return null;
  }
}

/**
 * 获取自指定标签以来的所有提交
 *
 * 该函数使用 git log 命令获取提交历史记录：
 * - 如果提供了标签，获取从该标签到 HEAD 的所有提交
 * - 如果标签为 null，获取整个仓库的所有提交历史
 * - 使用自定义格式输出：哈希、主题、正文，用 "---" 分隔每个提交
 * - 过滤掉空的提交记录
 * - 处理 git log 命令执行失败的情况
 *
 * @param {string | null} tag - 起始标签名称。如果为 null，则获取所有提交历史
 * @returns {Promise<string[]>} 包含提交信息的字符串数组，每个字符串包含一个完整的提交记录
 *
 * @example
 * // 获取自特定标签以来的提交
 * const commits = await getCommitsSinceTag('cookies_manager-v1.0.0');
 * // 返回: ['hash1\nsubject1\nbody1', 'hash2\nsubject2\nbody2', ...]
 *
 * @example
 * // 获取所有提交历史
 * const allCommits = await getCommitsSinceTag(null);
 * // 返回: 整个仓库的提交历史数组
 */
export async function getCommitsSinceTag(tag: string | null): Promise<string[]> {
  const range = tag ? `${tag}..HEAD` : 'HEAD';
  try {
    const { stdout } = await execaCommand(`git log ${range} --pretty=format:"%H%n%s%n%b%n---"`);
    if (!stdout) {
      return [];
    }
    // 使用自定义分隔符 '---' 分割，并过滤掉空字符串
    return stdout.split('---').filter((commit) => commit.trim());
  } catch (error) {
    logger.error(`获取自 ${tag} 以来的提交失败:`, error);
    return [];
  }
}
