import { SUPPORTED_LOCALES as WXT_SUPPORTED_LOCALES } from '@wxt-dev/i18n/build';
import { ManifestVersionType, VariantType, WebstoreType } from './types';

export const SUPPORTED_LOCALES = WXT_SUPPORTED_LOCALES;
/**
 * @description 只有 Chrome 风格的语言包
 * 这些语言包只会出现在 `_locales/xx/messages.json` 中，且一般只包含 `EXTENSION_NAME` 和 `EXTENSION_DESCRIPTION` 等少量 key。
 */
export const DEFAULT_CHROME_ONLY_LOCALES = ['en_US', 'en_GB', 'pt_BR', 'es_419'];
/**
 * @description Chrome 风格的语言包中包含的 key
 * `_locales/xx/messages.json` 只会有这些文案。
 */
export const DEFAULT_CHROME_ONLY_KEYS = [
  'EXTENSION_NAME',
  'EXTENSION_DESCRIPTION',
  '^context_menu_.*',
];

export const SUPPORT_WEBSTORE = {
  chrome: 'chrome',
  firefox: 'firefox',
  browser360: 'browser360',
  safari: 'safari',
  adspower: 'adspower',
  opera: 'opera',
  edge: 'edge',
};
export const SUPPORT_WEBSTORES: WebstoreType[] = Object.values(SUPPORT_WEBSTORE) as WebstoreType[];
/**
 * @description 来源插件还是网站的标识
 * 参考文档：https://easydoc.net/doc/91904722/fvS6AHYM/9TYy2p86
 */
export const WEBSTORE_CN: Record<WebstoreType, string> = {
  chrome: 'e-c', // Chrome 应用商店
  firefox: 'e-f', // Firefox 应用商店
  opera: 'e-o', // Opera 应用商店
  browser360: 'e-360', // 360 浏览器
  safari: 'e-s', // Safari 浏览器
  adspower: 'e-ads', // AdsPower 浏览器
  edge: 'e-edge', // Edge 浏览器
};
/**
 * @description 各应用商店的扩展自动更新地址
 * 参考：https://learn.microsoft.com/zh-cn/microsoft-edge/extensions-chromium/publish/auto-update
 */
export const WEBSTORES_UPDATE_URL: Record<WebstoreType | 'aliprice', string> = {
  chrome: 'https://clients2.google.com/service/update2/crx', // Chrome/Opera 更新地址
  opera: 'https://clients2.google.com/service/update2/crx',
  browser360: 'http://upext.chrome.360.cn/intf.php?method=ExtUpdate.query', // 360 浏览器更新地址
  firefox: '', // Firefox 不需要
  safari: '', // Safari 不需要
  adspower: '', // AdsPower 不需要
  edge: '', // Edge 默认不需要，除非托管自己的扩展
  aliprice: 'https://www.aliprice.com/extension_page/{extensionName}/updates.json', // AliPrice 官网更新地址
};
/**
 * @description 需要 AMO（Firefox Add-ons）强制要求的商店
 */
export const AMO_REQUIRED_WEBSTORES: Partial<Record<WebstoreType, WebstoreType>> = {
  firefox: 'firefox', // Firefox 应用商店
  browser360: 'browser360', // 360 浏览器
};
/**
 * @description 各应用商店扩展的基础 URL
 */
export const EXTENSION_BASE_URL: Record<WebstoreType, string> = {
  chrome: 'chrome-extension://__MSG_@@extension_id__', // Chrome
  opera: 'chrome-extension://__MSG_@@extension_id__', // Opera
  browser360: 'chrome-extension://__MSG_@@extension_id__', // 360 浏览器
  firefox: 'moz-extension://__MSG_@@extension_id__', // Firefox
  safari: 'chrome-extension://__MSG_@@extension_id__', // Safari
  adspower: 'chrome-extension://__MSG_@@extension_id__', // AdsPower
  edge: 'chrome-extension://__MSG_@@extension_id__', // Edge
};

export const SUPPORT_MANIFEST_VERSIONS: ManifestVersionType[] = [2, 3];
export const SUPPORT_MV = {
  mv2: 'mv2',
  mv3: 'mv3',
};
export const SUPPORT_MVS = Object.values(SUPPORT_MV) as string[];
export const SUPPORT_VARIANT = {
  /**
   * @description 应用商店的安装包
   */
  master: 'master',
  /**
   * @description 应用商店的安装包，区别在于插件的名字中会包含一些非官方授权的商标的字眼，如：1688，AliExpress，Taobao，Alibaba
   *
   * alias for trademark
   */
  tm: 'tm',
  /**
   * @description 和 `tm` 一样，但是是 beta 版本。
   */
  tmBeta: 'tmBeta',
  /**
   * @description 不能通过应用商店安装，也不会出现在应用商店里，只能通过 AliPrice 网站安装。目前只用于 Firefox。
   *
   * alias for distribute by aliprice
   */
  dba: 'dba',
  /**
   * @description 离线安装包版
   */
  offline: 'offline',
};
export const SUPPORT_VARIANTS: VariantType[] = Object.values(SUPPORT_VARIANT) as VariantType[];
