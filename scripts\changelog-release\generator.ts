import semver from 'semver';
import type { ParsedCommit } from './types.js';
import {
  COMMIT_TYPE_CONFIG,
  groupCommitsByType,
  getKnownTypeCommits,
  getUnknownTypeCommits,
} from './types.js';

/**
 * 根据提交类型确定下一个 SemVer 版本
 *
 * 该函数分析提交历史，根据 Conventional Commits 规范确定版本号的升级类型：
 * - 如果包含破坏性变更（`!` 或 `BREAKING CHANGE`），则升级 major 版本
 * - 如果包含 'feat' 类型提交，则升级 minor 版本
 * - 其他情况升级 patch 版本
 * - 如果没有提交记录，默认升级 patch 版本用于维护发布
 *
 * @param {string | null} lastVersion - 上一个版本号，如果为 null 则表示首次发布
 * @param {ParsedCommit[]} commits - 解析后的提交对象数组，包含提交类型和内容信息
 * @returns {string} 计算出的下一个版本号，遵循 SemVer 规范
 *
 * @example
 * // 包含破坏性变更的发布
 * determineNextVersion('1.0.0', [{isBreaking: true, ...}]) // 返回 '2.0.0'
 */
export function determineNextVersion(lastVersion: string | null, commits: ParsedCommit[]): string {
  if (!lastVersion) {
    return '1.0.0';
  }

  if (commits.length === 0) {
    // 对于没有新提交的维护版本，默认为 patch 更新
    return semver.inc(lastVersion, 'patch') || lastVersion;
  }

  let bump: semver.ReleaseType = 'patch';
  for (const commit of commits) {
    if (commit.isBreaking) {
      bump = 'major';
      break; // major 是最高级别，无需再检查
    }
    if (commit.type === 'feat') {
      bump = 'minor'; // 可能会被后续的 major 覆盖
    }
  }

  return semver.inc(lastVersion, bump) || lastVersion;
}

/**
 * 将提交列表格式化为 changelog 字符串
 *
 * 该函数将解析后的提交记录转换为结构化的 Markdown 格式变更日志：
 * - 按提交类型分组并按预定义顺序排列
 * - 为每个分组添加相应的 emoji 图标和中文标题
 * - 如果没有提交记录，生成维护版本的默认内容
 * - 每个提交条目包含主题和 Issue-ID（如果存在）
 * - 未知类型的提交归类到"其他"分组
 *
 * @param {ParsedCommit[]} commits - 解析后的提交对象数组，包含类型、主题、Issue-ID等信息
 * @returns {string} Markdown 格式的 changelog 字符串，包含分类的变更内容
 *
 * @example
 * // 包含多种类型提交的格式化
 * formatChangelog([
 *   {type: 'feat', subject: '添加新功能', issueId: '1#20250101-01'},
 *   {type: 'fix', subject: '修复bug', issueId: '2#20250101-02'}
 * ])
 * // 返回:
 * // ### ✨ 新功能
 * // - 添加新功能 (Issue-ID: 1#20250101-01)
 * // ### 🐛 问题修复
 * // - 修复bug (Issue-ID: 2#20250101-02)
 *
 * @example
 * // 空提交列表的处理
 * formatChangelog([]) // 返回维护版本的默认内容
 */
export function formatChangelog(commits: ParsedCommit[]): string {
  if (commits.length === 0) {
    return `### 🔧 维护与优化\n\n- 本次发布包含了一些小的更新和维护性改进。\n`;
  }

  // 按提交类型分组
  const commitsByType = groupCommitsByType(commits);
  let content = '';

  // 处理已知类型（按优先级排序）
  const knownTypeCommits = getKnownTypeCommits(commitsByType);
  for (const [type, typeCommits] of knownTypeCommits) {
    const config = COMMIT_TYPE_CONFIG[type];
    content += `### ${config.emoji} ${config.title}\n\n`;
    content +=
      typeCommits.map((commit) => `- ${commit.subject} (Issue-ID: ${commit.issueId})`).join('\n') +
      '\n\n';
  }

  // 处理未知类型（归类到"其他"）
  const unknownCommits = getUnknownTypeCommits(commitsByType);
  if (unknownCommits.length > 0) {
    content += '### 其他\n\n';
    content +=
      unknownCommits
        .map((commit) => `- ${commit.subject} (Issue-ID: ${commit.issueId})`)
        .join('\n') + '\n\n';
  }

  return content;
}
