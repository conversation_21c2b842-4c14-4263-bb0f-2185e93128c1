{"EXTENSION_NAME": {"message": "TODO: EXTENSION_NAME"}, "EXTENSION_DESCRIPTION": {"message": "TODO: EXTENSION_DESCRIPTION"}, "1688_cross_border_hot_selling": {"message": "1688 क्रॉस-बॉर्डर हॉट सेलिंग स्पॉट"}, "1688_shi_li_ren_zheng": {"message": "1688 शक्ति प्रमाणीकरण"}, "1_jian_qi_pi": {"message": "MOQ: 1"}, "1year_yi_shang": {"message": "1 साल से ज़्यादा"}, "24H": {"message": "24H"}, "24H_fa_huo": {"message": "24 घंटे के भीतर डिलीवरी"}, "24H_lan_shou_lv": {"message": "24 घंटे की पैकेजिंग दर"}, "30D_shang_xin": {"message": "मासिक नई आवक"}, "30d_sales": {"message": "$amount$ 30 दिनों में बिक गया", "placeholders": {"amount": {"content": "$1"}}}, "3Min_xiang_ying_lv": {"message": "3 मिनट के भीतर प्रतिक्रिया."}, "3Min_xiang_ying_lv__desc": {"message": "पिछले 30 दिनों में 3 मिनट के भीतर खरीदार पूछताछ संदेशों पर वांगवांग की प्रभावी प्रतिक्रियाओं का अनुपात"}, "48H": {"message": "48एच"}, "48H_fa_huo": {"message": "48 घंटे के अंदर डिलिवरी"}, "48H_lan_shou_lv": {"message": "48 घंटे की पैकेजिंग दर"}, "48H_lan_shou_lv__desc": {"message": "48 घंटों के भीतर उठाए गए ऑर्डर संख्या का ऑर्डर की कुल संख्या से अनुपात"}, "48H_lv_yue_lv": {"message": "48 घंटे की प्रदर्शन दर"}, "48H_lv_yue_lv__desc": {"message": "48 घंटों के भीतर उठाए गए या वितरित किए गए ऑर्डर संख्या का ऑर्डर की कुल संख्या से अनुपात"}, "72H": {"message": "72एच"}, "7D_shang_xin": {"message": "साप्ताहिक नई आवक"}, "7D_wu_li_you": {"message": "7 दिन देखभाल मुक्त"}, "ABS_title_text": {"message": "इस लिस्टिंग में एक ब्रांड स्टोरी शामिल है"}, "AC_title_text": {"message": "इस लिस्टिंग में Amazon's Choice बैज है"}, "A_title_text": {"message": "इस लिस्टिंग में एक A+ कंटेंट पेज है"}, "BS_title_text": {"message": "इस लिस्टिंग को $type$ श्रेणी में $num$ बेस्ट सेलर के रूप में रैंक किया गया है", "placeholders": {"type": {"content": "$1"}, "num": {"content": "$2"}}}, "LTD_title_text": {"message": "LTD (सीमित समय डील) का मतलब है कि यह लिस्टिंग \"7-दिवसीय प्रचार\" इवेंट का हिस्सा है"}, "NR_title_text": {"message": "इस लिस्टिंग को $type$ श्रेणी में $num$ नई रिलीज़ के रूप में रैंक किया गया है", "placeholders": {"type": {"content": "$1"}, "num": {"content": "$2"}}}, "SBV_title_text": {"message": "इस लिस्टिंग में एक वीडियो विज्ञापन है, एक प्रकार का PPC विज्ञापन जो आमतौर पर सर्च रिजल्ट के बीच में दिखाई देता है"}, "SB_title_text": {"message": "इस लिस्टिंग में एक ब्रांड विज्ञापन है, एक प्रकार का PPC विज्ञापन जो आमतौर पर सर्च रिजल्ट के ऊपर या नीचे दिखाई देता है"}, "SP_title_text": {"message": "इस लिस्टिंग में एक प्रायोजित उत्पाद विज्ञापन है"}, "V_title_text": {"message": "इस लिस्टिंग में एक वीडियो परिचय है"}, "advanced_research": {"message": "उन्नत शोध"}, "agent_ds1688___my_order": {"message": "मेरे आदेश"}, "agent_ds1688__add_to_cart": {"message": "विदेशी खरीद"}, "agent_ds1688__cart": {"message": "शॉपिंग कार्ट"}, "agent_ds1688__desc": {"message": "1688 द्वारा प्रदान किया गया। यह विदेश से प्रत्यक्ष खरीद, USD में भुगतान और चीन में आपके पारगमन गोदाम तक डिलीवरी का समर्थन करता है।"}, "agent_ds1688__freight": {"message": "शिपिंग लागत कैलकुलेटर"}, "agent_ds1688__help": {"message": "मदद"}, "agent_ds1688__packages": {"message": "यात्री की सूची"}, "agent_ds1688__profile": {"message": "व्यक्तिगत केंद्र"}, "agent_ds1688__warehouse": {"message": "मेरा गोदाम"}, "ai_comment_analysis_advantage": {"message": "पेशेवरों"}, "ai_comment_analysis_ai": {"message": "AI समीक्षा विश्लेषण"}, "ai_comment_analysis_available": {"message": "उपलब्ध"}, "ai_comment_analysis_balance": {"message": "अपर्याप्त सिक्के, कृपया टॉप अप करें"}, "ai_comment_analysis_behavior": {"message": "व्यवहार"}, "ai_comment_analysis_characteristic": {"message": "भीड़ की विशेषताएँ"}, "ai_comment_analysis_comment": {"message": "उत्पाद में सटीक निष्कर्ष निकालने के लिए पर्याप्त समीक्षाएँ नहीं हैं, कृपया अधिक समीक्षाओं वाला उत्पाद चुनें।"}, "ai_comment_analysis_consume": {"message": "अनुमानित खपत"}, "ai_comment_analysis_default": {"message": "डिफ़ॉल्ट समीक्षाएँ"}, "ai_comment_analysis_desire": {"message": "ग्राहक अपेक्षाएँ"}, "ai_comment_analysis_disadvantage": {"message": "विपक्षों"}, "ai_comment_analysis_free": {"message": "निःशुल्क प्रयास"}, "ai_comment_analysis_freeNum": {"message": "1 निःशुल्क क्रेडिट का उपयोग किया जाएगा"}, "ai_comment_analysis_go_recharge": {"message": "टॉप अप पर जाएँ"}, "ai_comment_analysis_intelligence": {"message": "बुद्धिमान समीक्षा विश्लेषण"}, "ai_comment_analysis_location": {"message": "स्थान"}, "ai_comment_analysis_motive": {"message": "खरीद प्रेरणा"}, "ai_comment_analysis_network_error": {"message": "नेटवर्क त्रुटि, कृपया पुनः प्रयास करें"}, "ai_comment_analysis_normal": {"message": "फ़ोटो समीक्षाएँ"}, "ai_comment_analysis_number_reviews": {"message": "समीक्षाओं की संख्या: $num$, अनुमानित खपत: $consume$", "placeholders": {"num": {"content": "$1"}, "consume": {"content": "$2"}}}, "ai_comment_analysis_ordinary": {"message": "सामान्य टिप्पणियाँ"}, "ai_comment_analysis_percentage": {"message": "प्रतिशत"}, "ai_comment_analysis_problem": {"message": "भुगतान में समस्याएँ"}, "ai_comment_analysis_reanalysis": {"message": "पुनः विश्लेषण करें"}, "ai_comment_analysis_reason": {"message": "का<PERSON><PERSON>"}, "ai_comment_analysis_recharge": {"message": "टॉप अप"}, "ai_comment_analysis_recharged": {"message": "मैंने टॉप अप कर लिया है"}, "ai_comment_analysis_retry": {"message": "पुनः प्रयास करें"}, "ai_comment_analysis_scene": {"message": "उपयोग परिदृश्य"}, "ai_comment_analysis_start": {"message": "विश्लेषण शुरू करें"}, "ai_comment_analysis_subject": {"message": "विषय"}, "ai_comment_analysis_time": {"message": "उपयोग का समय"}, "ai_comment_analysis_tool": {"message": "एआई उपकरण"}, "ai_comment_analysis_user_portrait": {"message": "उपयोगकर्ता प्रोफ़ाइल"}, "ai_comment_analysis_welcome": {"message": "AI समीक्षा विश्लेषण में आपका स्वागत है"}, "ai_comment_analysis_year": {"message": "पिछले वर्ष की टिप्पणियाँ"}, "ai_listing_Exclude_keywords": {"message": "कीवर्ड बहिष्कृत करें"}, "ai_listing_Login_the_feature": {"message": "सुविधा के लिए लॉगिन आवश्यक है"}, "ai_listing_aI_generation": {"message": "<PERSON> <PERSON>रे<PERSON>न"}, "ai_listing_add_automatic": {"message": "स्वचालित"}, "ai_listing_add_dictionary_new": {"message": "नई लाइब्रेरी बनाएँ"}, "ai_listing_add_enter_keywords": {"message": "कीवर्ड दर्ज करें"}, "ai_listing_add_inputkey_selling": {"message": "विक्रय बिंदु दर्ज करें और जोड़ना समाप्त करने के लिए $key$ दबाएँ", "placeholders": {"key": {"content": "$1"}}}, "ai_listing_add_inputkey_warning": {"message": "सीमा पार हो गई, $amount$ विक्रय बिंदु तक", "placeholders": {"amount": {"content": "$1"}}}, "ai_listing_add_keywords": {"message": "कीवर्ड जोड़ें"}, "ai_listing_add_manually": {"message": "मैन्युअल रूप से जोड़ें"}, "ai_listing_add_selling": {"message": "विक्रय बिंदु जोड़ें"}, "ai_listing_added_keywords": {"message": "जोड़े गए कीवर्ड"}, "ai_listing_added_successfully": {"message": "सफलतापूर्वक जोड़ा गया"}, "ai_listing_addexcluded_keywords": {"message": "बहिष्कृत कीवर्ड दर्ज करें, जोड़ने को समाप्त करने के लिए एंटर दबाएँ।"}, "ai_listing_adding_selling": {"message": "जोड़े गए विक्रय बिंदु"}, "ai_listing_addkeyword_enter": {"message": "कुंजी विशेषता शब्दों में टाइप करें और जोड़ने को समाप्त करने के लिए एंटर दबाएँ"}, "ai_listing_ai_description": {"message": "AI विवरण शब्द लाइब्रेरी"}, "ai_listing_ai_dictionary": {"message": "AI शीर्षक शब्द लाइब्रेरी"}, "ai_listing_ai_title": {"message": "AI शीर्षक"}, "ai_listing_aidescription_repeated": {"message": "AI विवरण शब्द लाइब्रेरी का नाम दोहराया नहीं जा सकता"}, "ai_listing_aititle_repeated": {"message": "AI शीर्षक शब्द लाइब्रेरी का नाम दोहराया नहीं जा सकता"}, "ai_listing_data_comes_from": {"message": "यह डेटा यहां से आता है:"}, "ai_listing_deleted_successfully": {"message": "सफलतापूर्वक हटाया गया"}, "ai_listing_dictionary_name": {"message": "लाइब्रेरी का नाम"}, "ai_listing_edit_dictionary": {"message": "लाइब्रेरी संशोधित करें..."}, "ai_listing_edit_word_library": {"message": "शब्द लाइब्रेरी संपादित करें"}, "ai_listing_enter_keywords": {"message": "कीवर्ड दर्ज करें और जोड़ने के लिए $key$ दबाएँ", "placeholders": {"key": {"content": "$1"}}}, "ai_listing_exceeded_maximum": {"message": "सीमा पार हो गई है, अधिकतम $amount$ कीवर्ड", "placeholders": {"amount": {"content": "$1"}}}, "ai_listing_excluded_word_library": {"message": "बहिष्कृत शब्द लाइब्रेरी"}, "ai_listing_generate_characters": {"message": "अक्षर जनरेट करें"}, "ai_listing_generation_platform": {"message": "जनरेशन प्लेटफ़ॉर्म"}, "ai_listing_help_optimize": {"message": "उत्पाद शीर्षक को अनुकूलित करने में मेरी सहायता करें, मूल शीर्षक है"}, "ai_listing_include_selling": {"message": "अन्य विक्रय बिंदु शामिल हैं:"}, "ai_listing_included_keyword": {"message": "शामिल किए गए कीवर्ड"}, "ai_listing_included_keywords": {"message": "शामिल किए गए कीवर्ड"}, "ai_listing_input_selling": {"message": "विक्रय बिंदु दर्ज करें"}, "ai_listing_input_selling_fit": {"message": "शीर्षक से मिलान करने के लिए विक्रय बिंदु दर्ज करें"}, "ai_listing_input_selling_please": {"message": "कृपया विक्रय बिंदु दर्ज करें"}, "ai_listing_intelligently_title": {"message": "शीर्षक को बुद्धिमानी से जनरेट करने के लिए ऊपर आवश्यक सामग्री दर्ज करें"}, "ai_listing_keyword_product_title": {"message": "कीवर्ड उत्पाद शीर्षक"}, "ai_listing_keywords_repeated": {"message": "कीवर्ड दोहराया नहीं जा सकता"}, "ai_listing_listed_selling_points": {"message": "शामिल किए गए विक्रय बिंदु"}, "ai_listing_long_title_1": {"message": "इसमें ब्रांड नाम, उत्पाद प्रकार, उत्पाद सुविधाएँ आदि जैसी बुनियादी जानकारी शामिल है।"}, "ai_listing_long_title_2": {"message": "मानक उत्पाद शीर्षक के आधार पर, SEO के लिए अनुकूल कीवर्ड जोड़े जाते हैं।"}, "ai_listing_long_title_3": {"message": "ब्रांड नाम, उत्पाद प्रकार, उत्पाद सुविधाएँ और कीवर्ड शामिल करने के अलावा, विशिष्ट, खंडित खोज क्वेरी में उच्च रैंकिंग प्राप्त करने के लिए लॉन्ग-टेल कीवर्ड भी शामिल किए जाते हैं।"}, "ai_listing_longtail_keyword_product_title": {"message": "लॉन्ग-टेल कीवर्ड उत्पाद शीर्षक"}, "ai_listing_manually_enter": {"message": "मैन्युअल रूप से दर्ज करें ..."}, "ai_listing_network_not_working": {"message": "इंटरनेट उपलब्ध नहीं है, ChatGPT तक पहुँचने के लिए VPN की आवश्यकता है"}, "ai_listing_new_dictionary": {"message": "एक नई शब्द लाइब्रेरी बनाएँ ..."}, "ai_listing_new_generate": {"message": "जनरेट करें"}, "ai_listing_optional_words": {"message": "वैकल्पिक शब्द"}, "ai_listing_original_title": {"message": "मूल शीर्षक"}, "ai_listing_other_keywords_included": {"message": "अन्य कीवर्ड शामिल हैं:"}, "ai_listing_please_again": {"message": "कृपया पुन: प्रयास करें"}, "ai_listing_please_select": {"message": "आपके लिए निम्नलिखित शीर्षक जनरेट किए गए हैं, कृपया चुनें:"}, "ai_listing_product_category": {"message": "उत्पाद श्रेणी"}, "ai_listing_product_category_is": {"message": "उत्पाद श्रेणी है"}, "ai_listing_product_category_to": {"message": "उत्पाद किस श्रेणी से संबंधित है?"}, "ai_listing_random_keywords": {"message": "रैंडम $amount$ कीवर्ड", "placeholders": {"amount": {"content": "$1"}}}, "ai_listing_random_selling": {"message": "यादृच्छिक $amount$ विक्रय बिंदु", "placeholders": {"amount": {"content": "$1"}}}, "ai_listing_randomize_keywords": {"message": "शब्द लाइब्रेरी से रैंडमाइज़ करें"}, "ai_listing_search_selling": {"message": "विक्रय बिंदु द्वारा खोजें"}, "ai_listing_select_product_categories": {"message": "स्वचालित रूप से उत्पाद श्रेणियाँ चुनें।"}, "ai_listing_select_product_selling_points": {"message": "स्वचालित रूप से उत्पाद विक्रय बिंदु चुनें"}, "ai_listing_select_word_library": {"message": "शब्द लाइब्रेरी चुनें"}, "ai_listing_selling": {"message": "विक्रय बिंदु"}, "ai_listing_selling_ask": {"message": "शीर्षक के लिए अन्य विक्रय बिंदु आवश्यकताएँ क्या हैं?"}, "ai_listing_selling_optional": {"message": "वैकल्पिक विक्रय बिंदु"}, "ai_listing_selling_repeat": {"message": "बिंदुओं की नकल नहीं की जा सकती"}, "ai_listing_set_excluded": {"message": "बहिष्कृत शब्द लाइब्रेरी के रूप में सेट करें"}, "ai_listing_set_include_selling_points": {"message": "विक्रय बिंदु शामिल करें"}, "ai_listing_set_included": {"message": "शामिल शब्द लाइब्रेरी के रूप में सेट करें"}, "ai_listing_set_selling_dictionary": {"message": "विक्रय बिंदु लाइब्रेरी के रूप में सेट करें"}, "ai_listing_standard_product_title": {"message": "मानक उत्पाद शीर्षक"}, "ai_listing_translated_title": {"message": "अनुवादित शीर्षक"}, "ai_listing_visit_chatGPT": {"message": "ChatGPT पर जाएँ"}, "ai_listing_what_other_keywords": {"message": "शीर्षक के लिए कौन से अन्य कीवर्ड आवश्यक हैं?"}, "aliprice_coupons_apply_again": {"message": "दोबारा आवेदन करें"}, "aliprice_coupons_apply_coupons": {"message": "कूपन लागू करें"}, "aliprice_coupons_apply_success": {"message": "कूपन मिला: $amount$ सहेजें", "placeholders": {"amount": {"content": "$1"}}}, "aliprice_coupons_applying": {"message": "सर्वोत्तम सौदों के लिए परीक्षण कोड..."}, "aliprice_coupons_applying_desc": {"message": "चेक आउट: $code$", "placeholders": {"code": {"content": "$1"}}}, "aliprice_coupons_back_to_checkout": {"message": "चेकआउट करना जारी रखें"}, "aliprice_coupons_found_coupons": {"message": "हमें $amount$ कूपन मिले हैं", "placeholders": {"amount": {"content": "$1"}}}, "aliprice_coupons_found_coupons_desc": {"message": "चेकआउट के लिए तैयार हैं? आइए सुनिश्चित करें कि आपको सबसे अच्छी कीमत मिले!"}, "aliprice_coupons_no_coupon_aviable": {"message": "वे कोड काम नहीं करते थे। कोई बड़ी बात नहीं—आपको पहले से ही सर्वोत्तम मूल्य मिल रहा है।"}, "aliprice_coupons_toolbar_btn": {"message": "कूपन प्राप्त करें"}, "aliww_translate": {"message": "अलीवांगवांग चैट अनुवादक"}, "aliww_translate_supports": {"message": "समर्थन: 1688 और ताओबाओ"}, "amazon_extended_keywords_Keywords": {"message": "कीवर्ड"}, "amazon_extended_keywords_copy_all": {"message": "सभी कॉपी करें"}, "amazon_extended_keywords_more": {"message": "अधिक"}, "an_lei_ji_xiao_liang_pai_xu": {"message": "संचयी बिक्री के आधार पर क्रमबद्ध करें"}, "an_lei_xing_cha_kan": {"message": "प्र<PERSON><PERSON>र"}, "an_yue_dai_xiao_pai_xu": {"message": "ड्रॉपशीपिंग बिक्री द्वारा रैंकिंग"}, "apra_btn__cat_name": {"message": "समीक्षा विश्लेषण"}, "apra_chart__name": {"message": "देश के अनुसार उत्पाद की बिक्री का प्रतिशत"}, "apra_chart__update_at": {"message": "अपडेट समय $time$", "placeholders": {"time": {"content": "$1"}}}, "apra_name": {"message": "देशों के बिक्री आंकड़े"}, "auto_opening": {"message": "$num$ सेकंड में अपने आप खुल रहे हैं", "placeholders": {"num": {"content": "$1"}}}, "auto_page_next_x_pages": {"message": "अगला $autoPaging$ पृष्ठ", "placeholders": {"autoPaging": {"content": "$1"}}}, "average_days_listed": {"message": "शेल्फ दिनों पर औसत"}, "average_hui_fu_lv": {"message": "औसत उत्तर दर"}, "average_ping_gong_ying_shang_deng_ji": {"message": "औसत आपूर्तिकर्ता स्तर"}, "average_price": {"message": "औसत मूल्य"}, "average_qi_ding_liang": {"message": "औसत MOQ"}, "average_rating": {"message": "औसत श्रेणी"}, "average_ren_zheng_gong_ying_nian_chang": {"message": "प्रमाणन के औसत वर्ष"}, "average_revenue": {"message": "औसत आमदनी"}, "average_revenue_per_product": {"message": "कुल राजस्व ÷ उत्पादों की संख्या"}, "average_sales": {"message": "औसत बिक्री"}, "average_sales_per_product": {"message": "कुल बिक्री ÷ उत्पादों की संख्या"}, "bao_han": {"message": "रोकना"}, "bao_zheng_jin": {"message": "अंतर"}, "bian_ti_shu": {"message": "ब<PERSON><PERSON>ाव"}, "biao_ti": {"message": "शीर्षक"}, "blacklist_add_blacklist": {"message": "इस स्टोर को ब्लॉक करें"}, "blacklist_address_incorrect": {"message": "पता गलत है। कृपया यह जाँचें।"}, "blacklist_blacked_out": {"message": "स्टोर को ब्लॉक कर दिया गया है"}, "blacklist_blacklist": {"message": "ब्लैकलिस्ट"}, "blacklist_no_records_yet": {"message": "अभी तक कोई रिकॉर्ड नहीं!"}, "blue_rocket": {"message": "로켓배송"}, "brand_name": {"message": "ब्रांड"}, "btn_aliprice_agent__daigou": {"message": "खरीद मध्यस्थ"}, "btn_aliprice_agent__dropshipping": {"message": "जहाज को डुबोना"}, "btn_have_a_try": {"message": "आजमाइश कीजिये"}, "btn_refresh": {"message": "ताज़ा करना"}, "btn_try_it_now": {"message": "अब इसे आजमाओ"}, "btn_txt_view_on_aliprice": {"message": "अलीप्राइस पर देखें"}, "bu_bao_han": {"message": "शामिल नहीं है"}, "bulk_copy_links": {"message": "थोक कॉपी लिंक"}, "bulk_copy_products": {"message": "थोक कॉपी उत्पाद"}, "cai_gou_zi_xun": {"message": "ग्राहक सेवा"}, "cai_gou_zi_xun__desc": {"message": "विक्रेता की तीन मिनट की प्रतिक्रिया दर"}, "can_ping_lei_xing": {"message": "प्र<PERSON><PERSON>र"}, "cao_zuo": {"message": "संचालन"}, "chan_pin_ID": {"message": "उत्पाद आयडी"}, "chan_pin_e_wai_xin_xi": {"message": "उत्पाद अतिरिक्त जानकारी"}, "chan_pin_lian_jie": {"message": "उत्पाद लिंक"}, "cheng_li_shi_jian": {"message": "स्थापना का समय"}, "city__aba": {"message": "Aba"}, "city__aksu": {"message": "<PERSON><PERSON><PERSON>"}, "city__alaer": {"message": "<PERSON><PERSON><PERSON>"}, "city__altai": {"message": "Altai"}, "city__alxaleague": {"message": "Alxa League"}, "city__ankang": {"message": "Ankan<PERSON>"}, "city__anqing": {"message": "Anqing"}, "city__anshan": {"message": "<PERSON><PERSON>"}, "city__anshun": {"message": "<PERSON><PERSON><PERSON>"}, "city__anyang": {"message": "Anyang"}, "city__baicheng": {"message": "Baicheng"}, "city__baise": {"message": "Baise"}, "city__baisha": {"message": "Baisha"}, "city__baishan": {"message": "Baishan"}, "city__baiyin": {"message": "<PERSON><PERSON><PERSON>"}, "city__baoding": {"message": "<PERSON>od<PERSON>"}, "city__baoji": {"message": "<PERSON><PERSON><PERSON>"}, "city__baoshan": {"message": "<PERSON><PERSON><PERSON>"}, "city__baoting": {"message": "Baoting"}, "city__baotou": {"message": "<PERSON><PERSON><PERSON>"}, "city__bayannur": {"message": "Bayannur"}, "city__bayinguoleng": {"message": "Bayinguoleng"}, "city__bazhong": {"message": "Bazhong"}, "city__beihai": {"message": "Beihai"}, "city__bengbu": {"message": "<PERSON><PERSON><PERSON>"}, "city__benxi": {"message": "Benxi"}, "city__bijie": {"message": "Bijie"}, "city__binzhou": {"message": "Binzhou"}, "city__bortala": {"message": "<PERSON><PERSON><PERSON>"}, "city__bozhou": {"message": "Bozhou"}, "city__cangzhou": {"message": "Cangzhou"}, "city__chamdo": {"message": "<PERSON><PERSON><PERSON>"}, "city__changchun": {"message": "<PERSON><PERSON><PERSON>"}, "city__changde": {"message": "<PERSON><PERSON>"}, "city__changhua": {"message": "Changhua"}, "city__changji": {"message": "Changji"}, "city__changjiang": {"message": "Changjiang"}, "city__changsha": {"message": "Changsha"}, "city__changzhi": {"message": "Changzhi"}, "city__changzhou": {"message": "Changzhou"}, "city__chaohu": {"message": "<PERSON><PERSON>"}, "city__chaoyang": {"message": "Chaoyang"}, "city__chaozhou": {"message": "Chaozhou"}, "city__chengde": {"message": "Chengde"}, "city__chengdu": {"message": "Chengdu"}, "city__chengmai": {"message": "<PERSON><PERSON><PERSON>"}, "city__chenzhou": {"message": "Chenzhou"}, "city__chiayi": {"message": "<PERSON><PERSON><PERSON>"}, "city__chifeng": {"message": "<PERSON><PERSON>"}, "city__chizhou": {"message": "Chizhou"}, "city__chongzuo": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__chuxiong": {"message": "Chuxiong"}, "city__chuzhou": {"message": "Chuzhou"}, "city__dali": {"message": "<PERSON><PERSON>"}, "city__dalian": {"message": "<PERSON><PERSON>"}, "city__dandong": {"message": "Dandong"}, "city__danzhou": {"message": "Danzhou"}, "city__daqing": {"message": "Daqing"}, "city__datong": {"message": "<PERSON><PERSON><PERSON>"}, "city__daxinganling": {"message": "<PERSON><PERSON>'<PERSON>ling"}, "city__dazhou": {"message": "Dazhou"}, "city__dehong": {"message": "<PERSON><PERSON>"}, "city__deyang": {"message": "Deyang"}, "city__dezhou": {"message": "Dezhou"}, "city__dingan": {"message": "Ding'an"}, "city__dingxi": {"message": "Dingxi"}, "city__diqing": {"message": "Diqing"}, "city__dongfang": {"message": "<PERSON><PERSON>g"}, "city__dongguan": {"message": "Dongguan"}, "city__dongying": {"message": "<PERSON><PERSON>"}, "city__enshi": {"message": "<PERSON><PERSON>"}, "city__ezhou": {"message": "Ezhou"}, "city__fangchenggang": {"message": "Fangchenggang"}, "city__foshan": {"message": "<PERSON><PERSON><PERSON>"}, "city__fushun": {"message": "<PERSON><PERSON><PERSON>"}, "city__fuxin": {"message": "Fuxin"}, "city__fuyang": {"message": "Fuyang"}, "city__fuzhou": {"message": "Fuzhou"}, "city__gannan": {"message": "<PERSON><PERSON><PERSON>"}, "city__ganzhou": {"message": "Ganzhou"}, "city__ganzi": {"message": "Ganzi"}, "city__guangan": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__guangyuan": {"message": "Guangyuan"}, "city__guangzhou": {"message": "Guangzhou"}, "city__guigang": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__guilin": {"message": "<PERSON><PERSON><PERSON>"}, "city__guiyang": {"message": "Guiyang"}, "city__guolok": {"message": "Guolok"}, "city__guyuan": {"message": "<PERSON><PERSON>"}, "city__haibei": {"message": "Haibei"}, "city__haidong": {"message": "Haidong"}, "city__haikou": {"message": "Hai<PERSON><PERSON>"}, "city__hainan": {"message": "Hainan"}, "city__haixi": {"message": "Haixi"}, "city__hami": {"message": "<PERSON><PERSON>"}, "city__handan": {"message": "<PERSON><PERSON>"}, "city__hangzhou": {"message": "Hangzhou"}, "city__hanzhong": {"message": "Hanzhong"}, "city__harbin": {"message": "<PERSON><PERSON><PERSON>"}, "city__hebi": {"message": "<PERSON><PERSON>"}, "city__hechi": {"message": "<PERSON><PERSON>"}, "city__hefei": {"message": "<PERSON><PERSON><PERSON>"}, "city__hegang": {"message": "<PERSON><PERSON><PERSON>"}, "city__heihe": {"message": "<PERSON><PERSON><PERSON>"}, "city__hengshui": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__hengyang": {"message": "Hengyang"}, "city__heyuan": {"message": "<PERSON><PERSON>"}, "city__heze": {"message": "<PERSON><PERSON>"}, "city__hezhou": {"message": "Hezhou"}, "city__hohhot": {"message": "Hohhot"}, "city__honghe": {"message": "<PERSON><PERSON>"}, "city__hongkongisland": {"message": "Hong Kong Island"}, "city__hotan": {"message": "<PERSON><PERSON>"}, "city__hsinchu": {"message": "<PERSON><PERSON><PERSON>"}, "city__huaian": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__huaibei": {"message": "<PERSON><PERSON><PERSON>"}, "city__huaihua": {"message": "Huaihua"}, "city__huaisouth": {"message": "Huai South"}, "city__hualien": {"message": "<PERSON><PERSON><PERSON>"}, "city__huanggang": {"message": "<PERSON><PERSON><PERSON>"}, "city__huangnan": {"message": "Huangnan"}, "city__huangshan": {"message": "<PERSON><PERSON>"}, "city__huangshi": {"message": "<PERSON><PERSON>"}, "city__huizhou": {"message": "Huizhou"}, "city__huludao": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__hulunbuir": {"message": "Hulunbuir"}, "city__huzhou": {"message": "Huzhou"}, "city__jiamusi": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__jian": {"message": "<PERSON><PERSON><PERSON>"}, "city__jiangmen": {"message": "Jiangmen"}, "city__jiaozuo": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__jiaxing": {"message": "Jiaxing"}, "city__jiayuguan": {"message": "Jiayuguan"}, "city__jieyang": {"message": "<PERSON><PERSON><PERSON>"}, "city__jilin": {"message": "<PERSON><PERSON>"}, "city__jinan": {"message": "<PERSON><PERSON>"}, "city__jinchang": {"message": "Jinchang"}, "city__jincheng": {"message": "Jincheng"}, "city__jingdezhen": {"message": "Jingdez<PERSON>"}, "city__jingmen": {"message": "Jingmen"}, "city__jingzhou": {"message": "Jingzhou"}, "city__jinhua": {"message": "Jinhua"}, "city__jining": {"message": "Jining"}, "city__jinzhong": {"message": "Jinzhong"}, "city__jinzhou": {"message": "Jinzhou"}, "city__jiujiang": {"message": "Jiujiang"}, "city__jiuquan": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__jixi": {"message": "Ji<PERSON>"}, "city__kaifeng": {"message": "Kaifeng"}, "city__kaohsiung": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__karamay": {"message": "<PERSON><PERSON><PERSON>"}, "city__kashgar": {"message": "Ka<PERSON><PERSON>"}, "city__keelung": {"message": "Keelung"}, "city__kinmen": {"message": "Kinmen"}, "city__kizilsu": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__kowloon": {"message": "Kowloon"}, "city__kunming": {"message": "Ku<PERSON><PERSON>"}, "city__laibin": {"message": "<PERSON><PERSON>"}, "city__laiwu": {"message": "<PERSON><PERSON>"}, "city__langfang": {"message": "<PERSON><PERSON><PERSON>"}, "city__lanzhou": {"message": "Lanzhou"}, "city__ledong": {"message": "<PERSON><PERSON>"}, "city__leshan": {"message": "<PERSON><PERSON>"}, "city__lhasa": {"message": "Lhasa"}, "city__liangshan": {"message": "Liangshan"}, "city__lianyungang": {"message": "Lianyungang"}, "city__liaocheng": {"message": "<PERSON><PERSON><PERSON>"}, "city__liaoyang": {"message": "<PERSON><PERSON><PERSON>"}, "city__liaoyuan": {"message": "<PERSON><PERSON><PERSON>"}, "city__lienchiang": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__lijiang": {"message": "Lijiang"}, "city__lincang": {"message": "Lincang"}, "city__linfen": {"message": "Linfen"}, "city__lingao": {"message": "Lingao"}, "city__lingshui": {"message": "<PERSON><PERSON><PERSON>"}, "city__linxia": {"message": "Lin<PERSON>"}, "city__linyi": {"message": "<PERSON><PERSON>"}, "city__lishui": {"message": "Li<PERSON><PERSON>"}, "city__liupanshui": {"message": "Liupanshu<PERSON>"}, "city__liuzhou": {"message": "Liuzhou"}, "city__longnan": {"message": "<PERSON><PERSON>"}, "city__longyan": {"message": "<PERSON><PERSON>"}, "city__loudi": {"message": "<PERSON><PERSON>"}, "city__luan": {"message": "<PERSON><PERSON><PERSON>"}, "city__luohe": {"message": "<PERSON><PERSON><PERSON>"}, "city__luoyang": {"message": "<PERSON><PERSON><PERSON>"}, "city__luzhou": {"message": "Luzhou"}, "city__lüliang": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__maanshan": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__macauoutlyingislands": {"message": "Macau Outlying Islands"}, "city__macaupeninsula": {"message": "Macau Peninsula"}, "city__maoming": {"message": "<PERSON><PERSON>"}, "city__meishan": {"message": "<PERSON><PERSON>"}, "city__meizhou": {"message": "Meizhou"}, "city__mianyang": {"message": "Mianyang"}, "city__miaoli": {"message": "<PERSON><PERSON>"}, "city__mudanjiang": {"message": "Mudanjiang"}, "city__nagqu": {"message": "<PERSON>g<PERSON>u"}, "city__nanchang": {"message": "Nanchang"}, "city__nanchong": {"message": "<PERSON><PERSON><PERSON>"}, "city__nanjing": {"message": "Nanjing"}, "city__nanning": {"message": "Nanning"}, "city__nanping": {"message": "<PERSON><PERSON>"}, "city__nantong": {"message": "Nantong"}, "city__nantou": {"message": "<PERSON><PERSON><PERSON>"}, "city__nanyang": {"message": "Nanyang"}, "city__neijiang": {"message": "Neijiang"}, "city__newterritories": {"message": "New Territories"}, "city__ngari": {"message": "<PERSON><PERSON>"}, "city__ningbo": {"message": "Ningbo"}, "city__ningde": {"message": "<PERSON><PERSON><PERSON>"}, "city__nujiang": {"message": "Nujiang"}, "city__nyingchi": {"message": "Nyingchi"}, "city__ordos": {"message": "Ordos"}, "city__panjin": {"message": "Panjin"}, "city__panzhihua": {"message": "Pan Zhihua"}, "city__penghu": {"message": "<PERSON><PERSON><PERSON>"}, "city__pingdingshan": {"message": "Pingdingshan"}, "city__pingliang": {"message": "<PERSON><PERSON><PERSON>"}, "city__pingtung": {"message": "<PERSON><PERSON><PERSON>"}, "city__pingxiang": {"message": "<PERSON><PERSON><PERSON>"}, "city__puer": {"message": "<PERSON>u'er"}, "city__putian": {"message": "<PERSON><PERSON>"}, "city__puyang": {"message": "P<PERSON><PERSON>"}, "city__qiandongnan": {"message": "Qiandongnan"}, "city__qianjiang": {"message": "Qianjiang"}, "city__qiannan": {"message": "<PERSON><PERSON><PERSON>"}, "city__qianxinan": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__qingdao": {"message": "Qingdao"}, "city__qingyang": {"message": "Qingyang"}, "city__qingyuan": {"message": "Qingyuan"}, "city__qinhuangdao": {"message": "Qinhuangdao"}, "city__qinzhou": {"message": "Qinzhou"}, "city__qionghai": {"message": "Qionghai"}, "city__qiongzhong": {"message": "Qiongzhong"}, "city__qiqihar": {"message": "Qiqihar"}, "city__qitaihe": {"message": "<PERSON><PERSON><PERSON>"}, "city__quanzhou": {"message": "Quanzhou"}, "city__qujing": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__quzhou": {"message": "Quzhou"}, "city__rizhao": {"message": "<PERSON><PERSON><PERSON>"}, "city__sanmenxia": {"message": "Sanmenxia"}, "city__sanming": {"message": "Sanming"}, "city__sanya": {"message": "Sanya"}, "city__shangluo": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__shangqiu": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__shangrao": {"message": "<PERSON><PERSON><PERSON>"}, "city__shannan": {"message": "Shannan"}, "city__shantou": {"message": "<PERSON><PERSON><PERSON>"}, "city__shanwei": {"message": "Shanwei"}, "city__shaoguan": {"message": "Shaoguan"}, "city__shaoxing": {"message": "Shaoxing"}, "city__shaoyang": {"message": "Shaoyang"}, "city__shennongjiaforestarea": {"message": "Shennongjia Forest Area"}, "city__shenyang": {"message": "Shenyang"}, "city__shenzhen": {"message": "Shenzhen"}, "city__shigatse": {"message": "Shigat<PERSON>"}, "city__shihezi": {"message": "<PERSON><PERSON><PERSON>"}, "city__shijiazhuang": {"message": "Shijiazhuang"}, "city__shiyan": {"message": "<PERSON><PERSON>"}, "city__shizuishan": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__shuangyashan": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__shuozhou": {"message": "Shuozhou"}, "city__siping": {"message": "<PERSON><PERSON>"}, "city__songyuan": {"message": "Songyuan"}, "city__suihua": {"message": "Su<PERSON>ua"}, "city__suining": {"message": "Suining"}, "city__suizhou": {"message": "<PERSON><PERSON><PERSON>"}, "city__suqian": {"message": "<PERSON><PERSON><PERSON>"}, "city__suzhou": {"message": "Suzhou"}, "city__tacheng": {"message": "Tacheng"}, "city__taian": {"message": "Tai'an"}, "city__taichung": {"message": "Taichung"}, "city__tainan": {"message": "Tainan"}, "city__taipei": {"message": "Taipei"}, "city__taitung": {"message": "<PERSON><PERSON><PERSON>"}, "city__taiyuan": {"message": "Taiyuan"}, "city__taizhou": {"message": "Taizhou"}, "city__tangshan": {"message": "Tangshan"}, "city__taoyuan": {"message": "Taoyuan"}, "city__tianmen": {"message": "Tianmen"}, "city__tianshui": {"message": "<PERSON><PERSON><PERSON>"}, "city__tieling": {"message": "Tieling"}, "city__tongchuan": {"message": "<PERSON><PERSON><PERSON>"}, "city__tonghua": {"message": "Tonghua"}, "city__tongliao": {"message": "<PERSON><PERSON><PERSON>"}, "city__tongling": {"message": "Tongling"}, "city__tongren": {"message": "<PERSON><PERSON>"}, "city__tumushuke": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__tunchang": {"message": "Tunchang"}, "city__turpan": {"message": "<PERSON><PERSON><PERSON>"}, "city__ulanqab": {"message": "Ulanqab"}, "city__urumqi": {"message": "Urumqi"}, "city__wanning": {"message": "Wanning"}, "city__weifang": {"message": "<PERSON><PERSON><PERSON>"}, "city__weihai": {"message": "Weihai"}, "city__weinan": {"message": "Weinan"}, "city__wenchang": {"message": "Wenchang"}, "city__wenshan": {"message": "<PERSON><PERSON>"}, "city__wenzhou": {"message": "Wenzhou"}, "city__wuhai": {"message": "Wu<PERSON>"}, "city__wuhan": {"message": "<PERSON><PERSON>"}, "city__wuhu": {"message": "<PERSON><PERSON>"}, "city__wujiaqu": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__wuwei": {"message": "<PERSON><PERSON>"}, "city__wuxi": {"message": "Wuxi"}, "city__wuzhishan": {"message": "Wuzhishan"}, "city__wuzhong": {"message": "Wuzhong"}, "city__wuzhou": {"message": "Wuzhou"}, "city__xiamen": {"message": "Xiamen"}, "city__xian": {"message": "Xi'<PERSON>"}, "city__xiangfan": {"message": "<PERSON><PERSON><PERSON>"}, "city__xiangtan": {"message": "Xiangtan"}, "city__xiangxi": {"message": "Xiangxi"}, "city__xianning": {"message": "Xianning"}, "city__xiantao": {"message": "Xiantao"}, "city__xianyang": {"message": "<PERSON><PERSON><PERSON>"}, "city__xiaogan": {"message": "<PERSON><PERSON>"}, "city__xilingol": {"message": "Xilingol"}, "city__xinganleague": {"message": "Xing'an League"}, "city__xingtai": {"message": "Xingtai"}, "city__xining": {"message": "Xining"}, "city__xinxiang": {"message": "Xinxiang"}, "city__xinyang": {"message": "Xinyang"}, "city__xinyu": {"message": "Xinyu"}, "city__xinzhou": {"message": "Xinzhou"}, "city__xishuangbanna": {"message": "Xishuangbanna"}, "city__xuancheng": {"message": "Xuancheng"}, "city__xuchang": {"message": "Xuchang"}, "city__xuzhou": {"message": "Xuzhou"}, "city__yaan": {"message": "Ya'an"}, "city__yanan": {"message": "Yan'<PERSON>"}, "city__yanbian": {"message": "Yanbian"}, "city__yancheng": {"message": "Yancheng"}, "city__yangjiang": {"message": "Yangjiang"}, "city__yangquan": {"message": "<PERSON><PERSON><PERSON>"}, "city__yangzhou": {"message": "Yangzhou"}, "city__yantai": {"message": "Yantai"}, "city__yibin": {"message": "<PERSON><PERSON>"}, "city__yichang": {"message": "Yichang"}, "city__yichun": {"message": "<PERSON><PERSON><PERSON>"}, "city__yilan": {"message": "Yilan"}, "city__yili": {"message": "<PERSON><PERSON>"}, "city__yinchuan": {"message": "<PERSON><PERSON><PERSON>"}, "city__yingkou": {"message": "<PERSON><PERSON><PERSON>"}, "city__yingtan": {"message": "Yingtan"}, "city__yiwu": {"message": "<PERSON><PERSON>"}, "city__yiyang": {"message": "Yiyang"}, "city__yongzhou": {"message": "Yongzhou"}, "city__yueyang": {"message": "<PERSON><PERSON><PERSON>"}, "city__yulin": {"message": "Yulin"}, "city__yuncheng": {"message": "Yuncheng"}, "city__yunfu": {"message": "<PERSON><PERSON>"}, "city__yunlin": {"message": "<PERSON><PERSON>"}, "city__yushu": {"message": "Yushu"}, "city__yuxi": {"message": "Yuxi"}, "city__zaozhuang": {"message": "Zaozhuang"}, "city__zhangjiajie": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__zhangjiakou": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__zhangye": {"message": "<PERSON><PERSON>"}, "city__zhangzhou": {"message": "Zhangzhou"}, "city__zhanjiang": {"message": "Zhanjiang"}, "city__zhaoqing": {"message": "Zhaoqing"}, "city__zhaotong": {"message": "Zhaotong"}, "city__zhengzhou": {"message": "Zhengzhou"}, "city__zhenjiang": {"message": "Zhenjiang"}, "city__zhongshan": {"message": "Zhongshan"}, "city__zhongwei": {"message": "Zhongwei"}, "city__zhoukou": {"message": "<PERSON><PERSON><PERSON>"}, "city__zhoushan": {"message": "Zhoushan"}, "city__zhuhai": {"message": "Zhu<PERSON>"}, "city__zhumadian": {"message": "Zhumadian"}, "city__zhuzhou": {"message": "Zhuzhou"}, "city__zibo": {"message": "Zibo"}, "city__zigong": {"message": "Zigong"}, "city__ziyang": {"message": "<PERSON><PERSON><PERSON>"}, "city__zunyi": {"message": "<PERSON><PERSON><PERSON>"}, "click_copy_product_info": {"message": "उत्पाद जानकारी कॉपी करें पर क्लिक करें"}, "commmon_txt_expired": {"message": "समय सीमा समाप्त"}, "common__date_range_12m": {"message": "1 वर्ष"}, "common__date_range_1m": {"message": "1 महीना"}, "common__date_range_1w": {"message": "1 सप्ताह"}, "common__date_range_2w": {"message": "2 सप्ताह"}, "common__date_range_3m": {"message": "3 महीने"}, "common__date_range_3w": {"message": "3 सप्ताह"}, "common__date_range_6m": {"message": "6 महीने"}, "common_btn_cancel": {"message": "र<PERSON><PERSON><PERSON> करना"}, "common_btn_close": {"message": "बं<PERSON> करे"}, "common_btn_save": {"message": "सहेजें"}, "common_btn_setting": {"message": "सेट अप"}, "common_email": {"message": "ईमेल"}, "common_error_msg_no_data": {"message": "कोई आकड़ा उपलब्ध नहीं है"}, "common_error_msg_no_result": {"message": "क्षमा करें, कोई परिणाम नहीं मिला।"}, "common_favorites": {"message": "पसंदीदा"}, "common_feedback": {"message": "प्रतिपुष्टि"}, "common_help": {"message": "मदद"}, "common_loading": {"message": "लोड हो रहा है"}, "common_login": {"message": "लॉग इन करें"}, "common_logout": {"message": "लॉग आउट"}, "common_no": {"message": "नहीं"}, "common_powered_by_aliprice": {"message": "AliPrice.com द्वारा संचालित"}, "common_setting": {"message": "स्थापना"}, "common_sign_up": {"message": "साइन अप करें"}, "common_system_upgrading_title": {"message": "प्रणाली उन्नयन"}, "common_system_upgrading_txt": {"message": "कृपया बाद में प्रयास करें"}, "common_txt__currency": {"message": "मुद्रा"}, "common_txt__video_tutorial": {"message": "वीडियो ट्यूटोरियल"}, "common_txt_ago_time": {"message": "$time$ दिन पहले", "placeholders": {"time": {"content": "$1"}}}, "common_txt_all_product": {"message": "सभी"}, "common_txt_analysis": {"message": "विश्लेषण"}, "common_txt_basically_used": {"message": "लगभग कभी उपयोग नहीं किया गया"}, "common_txt_biaoti_link": {"message": "शीर्षक+लिंक"}, "common_txt_biaoti_link_dian_pu": {"message": "शीर्षक+लिंक+स्टोर का नाम"}, "common_txt_blacklist": {"message": "ब्लॉक सूची"}, "common_txt_cancel": {"message": "र<PERSON><PERSON><PERSON> करना"}, "common_txt_category": {"message": "वर्ग"}, "common_txt_chakan": {"message": "जाँच करना"}, "common_txt_colors": {"message": "रंग की"}, "common_txt_confirm": {"message": "पुष्टि करना"}, "common_txt_copied": {"message": "कॉपी किया गया"}, "common_txt_copy": {"message": "प्रतिलिपि"}, "common_txt_copy_link": {"message": "लिंक की प्रतिलिपि करें"}, "common_txt_copy_title": {"message": "कॉपी शीर्षक"}, "common_txt_copy_title__link": {"message": "शीर्षक और लिंक कॉपी करें"}, "common_txt_day": {"message": "आक<PERSON><PERSON>"}, "common_txt_day__short": {"message": "D"}, "common_txt_delete": {"message": "हटाएं"}, "common_txt_dian_pu_link": {"message": "स्टोर का नाम + लिंक कॉपी करें"}, "common_txt_download": {"message": "डाउनलोड"}, "common_txt_downloaded": {"message": "डाउनलोड करना"}, "common_txt_export_as_csv": {"message": "Excel निर्यात करें"}, "common_txt_export_as_txt": {"message": "Txt निर्यात करें"}, "common_txt_fail": {"message": "असफल"}, "common_txt_format": {"message": "प्रारूप"}, "common_txt_get": {"message": "पाना"}, "common_txt_incert_selection": {"message": "उलट चयन"}, "common_txt_install": {"message": "इंस्टॉल"}, "common_txt_load_failed": {"message": "लोड करने में विफल"}, "common_txt_month": {"message": "महीना"}, "common_txt_more": {"message": "अधिक"}, "common_txt_new_unused": {"message": "एकदम नया, अप्रयुक्त"}, "common_txt_next": {"message": "आगे"}, "common_txt_no_limit": {"message": "असीमित"}, "common_txt_no_noticeable": {"message": "कोई खरोंच या गंदगी दिखाई नहीं देती"}, "common_txt_on_sale": {"message": "उपलब्ध"}, "common_txt_opt_in_out": {"message": "बंद"}, "common_txt_order": {"message": "गण"}, "common_txt_others": {"message": "अन्य"}, "common_txt_overall_poor_condition": {"message": "कुल मिलाकर ख़राब हालत"}, "common_txt_patterns": {"message": "पैटर्न"}, "common_txt_platform": {"message": "प्लेटफार्म"}, "common_txt_please_select": {"message": "कृपया चुनें"}, "common_txt_prev": {"message": "पिछला"}, "common_txt_price": {"message": "कीमत"}, "common_txt_privacy_policy": {"message": "गोपनीयता नीति"}, "common_txt_product_condition": {"message": "उत्पाद स्थिति"}, "common_txt_rating": {"message": "रेटिंग"}, "common_txt_ratings": {"message": "रेटिंग्स"}, "common_txt_reload": {"message": "पुनः लोड करें"}, "common_txt_reset": {"message": "रीसेट"}, "common_txt_retail": {"message": "खुदरा"}, "common_txt_review": {"message": "समीक्षा"}, "common_txt_sale": {"message": "उपलब्ध"}, "common_txt_same": {"message": "वही"}, "common_txt_scratches_and_dirt": {"message": "खरोंच और गंदगी के साथ"}, "common_txt_search_title": {"message": "खोज शीर्षक"}, "common_txt_select_all": {"message": "सबका चयन करें"}, "common_txt_selected": {"message": "गिने चुने"}, "common_txt_share": {"message": "शेयर"}, "common_txt_sold": {"message": "बेचा"}, "common_txt_sold_out": {"message": "बिक गया"}, "common_txt_some_scratches": {"message": "कुछ खरोंच और गंदगी"}, "common_txt_sort_by": {"message": "इसके अनुसार क्रमबद्ध करें"}, "common_txt_state": {"message": "स्थिति"}, "common_txt_success": {"message": "सफलता"}, "common_txt_sys_err": {"message": "सिस्टम में गड़बड़ी"}, "common_txt_today": {"message": "आज"}, "common_txt_total": {"message": "सब"}, "common_txt_unselect_all": {"message": "उलट चयन"}, "common_txt_upload_image": {"message": "तस्वीर डालिये"}, "common_txt_visit": {"message": "मिलने जाना"}, "common_txt_whitelist": {"message": "श्वेतसूची"}, "common_txt_wholesale": {"message": "थोक"}, "common_txt_wildberries": {"message": "Wildberries"}, "common_txt_year": {"message": "वर्ष"}, "common_yes": {"message": "हाँ"}, "compare_tool_btn_clear_all": {"message": "सभी साफ करें"}, "compare_tool_btn_compare": {"message": "तुलना"}, "compare_tool_btn_contact": {"message": "संपर्क करें"}, "compare_tool_tips_max_compared": {"message": "$maxComparedCount$ तक जोड़ें", "placeholders": {"maxComparedCount": {"content": "$1"}}}, "configure_notifiactions": {"message": "सूचनाएँ कॉन्फ़िगर करें"}, "contact_us": {"message": "संपर्क करें"}, "context_menu_screenshot_search": {"message": "छवि द्वारा खोज करने के लिए कैप्चर करें"}, "context_menus_aliprice_search_by_image": {"message": "अलीप्राइस पर इमेज सर्च करें"}, "context_menus_goote_trans": {"message": "पेज का अनुवाद करें/मूल दिखाएँ"}, "context_menus_search_by_image": {"message": "$storeName$ पर छवि द्वारा खोजें", "placeholders": {"storeName": {"content": "$1"}}}, "context_menus_search_by_image_capture": {"message": "$storeName$ पर कब्जा", "placeholders": {"storeName": {"content": "$1"}}}, "context_menus_translator": {"message": "अनुवाद करने के लिए कैप्चर करें"}, "converter_modal_amount_placeholder": {"message": "यहां राशि दर्ज करें"}, "converter_modal_btn_convert": {"message": "बदलना"}, "converter_modal_exchange_rate_source": {"message": "डेटा $boc$ विदेशी विनिमय दर अपडेट समय से आता है: $updatedAt$", "placeholders": {"boc": {"content": "$1"}, "updatedAt": {"content": "$2"}}}, "converter_modal_name": {"message": "मुद्रा रूपांतरण"}, "converter_modal_search_placeholder": {"message": "मुद्रा खोजें"}, "copy_all_contact_us_notice": {"message": "यह साइट इस समय समर्थित नहीं है, कृपया हमसे संपर्क करें"}, "copy_product_info": {"message": "उत्पाद जानकारी कॉपी करें"}, "copy_suggest_search_kw": {"message": "ड्रॉपडाउन सूचियों की प्रतिलिपि बनाएँ"}, "country__han_gou": {"message": "दक्षिण कोरिया"}, "country__ri_ben": {"message": "जा<PERSON><PERSON>न"}, "country__yue_nan": {"message": "वियतनाम"}, "currency_convert__custom": {"message": "कस्टम विनिमय दर"}, "currency_convert__sync_server": {"message": "सर्वर को सिंक्रनाइज़ करें"}, "dang_ri_fa_huo": {"message": "उसी दिन शिपिंग"}, "dao_chu_quan_dian_shang_pin": {"message": "सभी स्टोर उत्पाद निर्यात करें"}, "dao_chu_wei_CSV": {"message": "निर्यात"}, "dao_chu_zi_duan": {"message": "निर्यात फ़ील्ड"}, "delivery_address": {"message": "शिपिंग पता"}, "delivery_company": {"message": "डिलीवरी कंपनी"}, "di_zhi": {"message": "पता"}, "dian_ji_cha_xun": {"message": "पूछताछ के लिए क्लिक करें"}, "dian_pu_ID": {"message": "स्टोर आईडी"}, "dian_pu_di_zhi": {"message": "स्टोर का पता"}, "dian_pu_lian_jie": {"message": "स्टोर लिंक"}, "dian_pu_ming": {"message": "स्टोर नाम"}, "dian_pu_ming_cheng": {"message": "स्टोर नाम"}, "dian_pu_shang_pin_zong_hsu": {"message": "स्टोर में उत्पादों की कुल संख्या: $num$", "placeholders": {"num": {"content": "$1"}}}, "dian_pu_xin_xi": {"message": "दुकान की जानकारी"}, "ding_zai_zuo_ce": {"message": "बाईं ओर कील"}, "disable_old_version_tips_disable_btn_title": {"message": "पुराना संस्करण अक्षम करें"}, "download_image__SKU_variant_images": {"message": "SKU वैरिएंट छवियाँ"}, "download_image__assume": {"message": "उदाहरण के लिए, हमारे पास 2 इमेज हैं, product1.jpg और product2.gif.\nimg_{$no$} का नाम बदलकर img_01.jpg, img_02.gif कर दिया जाएगा;\n{$group$}_{$no$} का नाम बदलकर main_image_01.jpg, main_image_02.gif कर दिया जाएगा;", "placeholders": {"no": {"content": "$3"}, "group": {"content": "$2"}}}, "download_image__batch_download": {"message": "बैच डाउनलोड"}, "download_image__combined_image": {"message": "संयुक्त उत्पाद विवरण छवि"}, "download_image__continue_downloading": {"message": "डाउनलोड करना जारी रखें"}, "download_image__description_images": {"message": "विवरण छवियाँ"}, "download_image__download_combined_image": {"message": "संयुक्त उत्पाद विवरण छवि डाउनलोड करें"}, "download_image__download_zip": {"message": "ज़िप डाउनलोड करें"}, "download_image__enlarge_check": {"message": "केवल JPEG, JPG, GIF और PNG छवियों का समर्थन करता है, एकल छवि का अधिकतम आकार: 1600 * 1600"}, "download_image__enlarge_image": {"message": "छवि को बड़ा करें"}, "download_image__export": {"message": "निर्यात लिंक"}, "download_image__height": {"message": "ऊंचाई"}, "download_image__ignore_videos": {"message": "वीडियो को अनदेखा कर दिया गया है, क्योंकि इसे निर्यात नहीं किया जा सकता"}, "download_image__img_translate": {"message": "छवि अनुवाद"}, "download_image__main_image": {"message": "मुख्य छवि"}, "download_image__multi_folder": {"message": "मल्टी-फ़ोल्डर"}, "download_image__name": {"message": "छवि डाउनलोड करें"}, "download_image__notice_content": {"message": "कृपया अपने ब्राउज़र की डाउनलोड सेटिंग में \"डाउनलोड करने से पहले पूछें कि प्रत्येक फ़ाइल को कहाँ सहेजना है\" को चेक न करें!!! अन्यथा बहुत सारे डायलॉग बॉक्स होंगे।"}, "download_image__notice_ignore": {"message": "इस संदेश के लिए फिर से संकेत न करें"}, "download_image__order_number": {"message": "{$no$} सीरियल नंबर; {$group$} समूह का नाम; {$date$} टाइमस्टैम्प", "placeholders": {"no": {"content": "$1"}, "group": {"content": "$2"}, "date": {"content": "$3"}}}, "download_image__overview": {"message": "अवलोकन"}, "download_image__prompt_download_zip": {"message": "बहुत सारी छवियाँ हैं, बेहतर होगा कि आप उन्हें ज़िप फ़ोल्डर के रूप में डाउनलोड करें।"}, "download_image__rename": {"message": "नाम बदलें"}, "download_image__rule": {"message": "नामकरण नियम"}, "download_image__single_folder": {"message": "एकल फ़ोल्डर"}, "download_image__sku_image": {"message": "एसकेयू छवियां"}, "download_image__video": {"message": "वीडियो"}, "download_image__width": {"message": "चौड़ाई"}, "download_reviews__download_images": {"message": "समीक्षा छवि डाउनलोड करें"}, "download_reviews__dropdown_title": {"message": "समीक्षा छवि डाउनलोड करें"}, "download_reviews__export_csv": {"message": "सीएसवी निर्यात करें"}, "download_reviews__no_images": {"message": "नमूना प्रति: डाउनलोड करने के लिए 0 चित्र"}, "download_reviews__no_reviews": {"message": "डाउनलोड करने के लिए कोई समीक्षा नहीं!"}, "download_reviews__notice": {"message": "बख्शीश:"}, "download_reviews__notice__chrome_settings": {"message": "क्रोम ब्राउज़र को यह पूछने के लिए सेट करें कि डाउनलोड करने से पहले प्रत्येक फ़ाइल को कहाँ सहेजना है, \"ऑफ़\" पर सेट करें"}, "download_reviews__notice__wait": {"message": "समीक्षाओं की संख्या के आधार पर, प्रतीक्षा समय अधिक हो सकता है"}, "download_reviews__pages_list__all": {"message": "सभी"}, "download_reviews__pages_list__page": {"message": "पिछले $page$ पृष्ठ", "placeholders": {"page": {"content": "$1"}}}, "download_reviews__pages_list_title": {"message": "चयन सीमा"}, "export_shopping_cart__csv_filed__details_url": {"message": "उत्पाद लिंक"}, "export_shopping_cart__csv_filed__details_url_with_sku": {"message": "इको SKU लिंक"}, "export_shopping_cart__csv_filed__images": {"message": "छवि लिंक"}, "export_shopping_cart__csv_filed__quantity": {"message": "मात्रा"}, "export_shopping_cart__csv_filed__sale_price": {"message": "कीमत"}, "export_shopping_cart__csv_filed__specs": {"message": "विशेष विवरण"}, "export_shopping_cart__csv_filed__store_name": {"message": "स्टोर नाम"}, "export_shopping_cart__csv_filed__store_url": {"message": "स्टोर लिंक"}, "export_shopping_cart__csv_filed__title": {"message": "प्रोडक्ट का नाम"}, "export_shopping_cart__export_btn": {"message": "निर्यात"}, "export_shopping_cart__export_empty": {"message": "कृपया एक उत्पाद चुनें!"}, "fa_huo_shi_jian": {"message": "शिपिंग"}, "favorite_add_email": {"message": "ईमेल जोड़ें"}, "favorite_add_favorites": {"message": "पसंदीदा में जोड़ें"}, "favorite_added": {"message": "जोड़ा गया"}, "favorite_btn_add": {"message": "मूल्य ड्रॉप चेतावनी।"}, "favorite_btn_notify": {"message": "ट्रैक की कीमत"}, "favorite_cate_name_all": {"message": "सभी प्रोडक्ट"}, "favorite_current_price": {"message": "वर्तमान मूल्य"}, "favorite_due_date": {"message": "देय तिथि"}, "favorite_enable_notification": {"message": "कृपया ईमेल अधिसूचना सक्षम करें"}, "favorite_expired": {"message": "समाप्त हो गई"}, "favorite_go_to_enable": {"message": "सक्षम करने के लिए जाएँ"}, "favorite_msg_add_success": {"message": "पसंदीदा में जोड़ा गया"}, "favorite_msg_del_success": {"message": "पसंदीदा से हटा दिया गया"}, "favorite_msg_failure": {"message": "असफल! पृष्ठ को ताज़ा करें और फिर से प्रयास करें।"}, "favorite_please_add_email": {"message": "कृपया ईमेल जोड़ें"}, "favorite_price_drop": {"message": "नीचे"}, "favorite_price_rise": {"message": "ऊपर"}, "favorite_price_untracked": {"message": "कीमत ट्रैक नहीं की गई"}, "favorite_saved_price": {"message": "सहेजी गई कीमत"}, "favorite_stop_tracking": {"message": "ट्रैक करना बंद करें"}, "favorite_sub_email_address": {"message": "सदस्यता ईमेल पता"}, "favorite_tracking_period": {"message": "ट्रैकिंग अवधि"}, "favorite_tracking_prices": {"message": "कीमतों पर नज़र रखना"}, "favorite_verify_email": {"message": "ईमेल पता सत्यापित करें"}, "favorites_list_remove_prompt_msg": {"message": "क्या आप इसे हटाना सुनिश्चित कर रहे हैं?"}, "favorites_update_button": {"message": "अभी कीमतें अपडेट करें"}, "fen_lei": {"message": "वर्ग"}, "fen_xia_yan_xuan": {"message": "वितरक की पसंद"}, "find_similar": {"message": "समान खोजें"}, "first_ali_price_date": {"message": "वह दिनांक जब पहली बार अलीप्राइस क्रॉलर द्वारा कैप्चर किया गया था"}, "fooview_coupons_modal_no_data": {"message": "कोई कूपन नहीं"}, "fooview_coupons_modal_title": {"message": "कूपन"}, "fooview_favorites__product_sub__tooltip_l1": {"message": "कीमत < $lowerPrice$", "placeholders": {"lowerPrice": {"content": "$1"}}}, "fooview_favorites__product_sub__tooltip_l2": {"message": "या कीमत > $higherPrice$", "placeholders": {"higherPrice": {"content": "$1"}}}, "fooview_favorites__product_sub__tooltip_l3": {"message": "समय सीमा"}, "fooview_favorites_error_msg_no_favorites": {"message": "मूल्य ड्रॉप अलर्ट प्राप्त करने के लिए यहां पसंदीदा उत्पाद जोड़ें।"}, "fooview_favorites_filter_latest": {"message": "नवीनतम"}, "fooview_favorites_filter_price_drop": {"message": "कीमतों में कटौती"}, "fooview_favorites_filter_price_up": {"message": "मूल्य वृद्धि"}, "fooview_favorites_modal_title": {"message": "मेरे पसंदीदा"}, "fooview_favorites_modal_title_title": {"message": "<PERSON><PERSON><PERSON> पसंदीदा पर जाएं"}, "fooview_favorites_track_price": {"message": "कीमत को ट्रैक करने के लिए"}, "fooview_price_history_app_price": {"message": "एपीपी मूल्य:"}, "fooview_price_history_title": {"message": "मूल्य इतिहास"}, "fooview_product_list_feedback": {"message": "प्रतिपुष्टि"}, "fooview_product_list_orders": {"message": "आदेश"}, "fooview_product_list_price": {"message": "कीमत"}, "fooview_reviews_error_msg_no_review": {"message": "हमें इस उत्पाद के लिए कोई समीक्षा नहीं मिली।"}, "fooview_reviews_filter_buyer_reviews": {"message": "खरीदारों की तस्वीरें"}, "fooview_reviews_modal_title": {"message": "समीक्षा"}, "fooview_same_product_choose_category": {"message": "वर्ग चुने"}, "fooview_same_product_filter_feedback": {"message": "प्रतिपुष्टि"}, "fooview_same_product_filter_orders": {"message": "आदेश"}, "fooview_same_product_filter_price": {"message": "कीमत"}, "fooview_same_product_filter_rating": {"message": "रेटिंग"}, "fooview_same_product_modal_title": {"message": "एक ही उत्पाद का पता लगाएं"}, "fooview_same_product_search_by_image": {"message": "छवि द्वारा खोजें"}, "fooview_seller_analysis_modal_title": {"message": "विक्रेता विश्लेषण"}, "for_12_months": {"message": "1 वर्ष के लिए"}, "for_12_months_list_pro": {"message": "12 महीने"}, "for_12_months_nei": {"message": "12 महीने के अंदर"}, "for_1_months": {"message": "1 महीना"}, "for_1_months_nei": {"message": "1 महीने के अंदर"}, "for_3_months": {"message": "3 माह के लिए"}, "for_3_months_nei": {"message": "3 महीने के अंदर"}, "for_6_months": {"message": "6 महीने के लिए"}, "for_6_months_nei": {"message": "6 महीने के अंदर"}, "for_9_months": {"message": "9 महीने"}, "for_9_months_nei": {"message": "9 महीने के अंदर"}, "fu_gou_lv": {"message": "पुनर्खरीद दर"}, "gao_liang_bu_tong_dian": {"message": "मतभेदों को उजागर करें"}, "gao_liang_guang_gao_chan_pin": {"message": "विज्ञापन उत्पादों को हाइलाइट करें"}, "geng_duo_xin_xi": {"message": "और जानकारी"}, "geng_xin_shi_jian": {"message": "अद्यतन समय"}, "get_store_products_fail_tip": {"message": "सामान्य पहुंच सुनिश्चित करने के लिए सत्यापन पर जाने हेतु ओके पर क्लिक करें"}, "gong_x_kuan_shang_pin": {"message": "कुल $amount$ उत्पाद", "placeholders": {"amount": {"content": "$1"}}}, "gong_ying_shang": {"message": "देने वाला"}, "gong_ying_shang_ID": {"message": "आपूर्तिकर्ता आईडी"}, "gong_ying_shang_deng_ji": {"message": "आपूर्तिकर्ता रेटिंग"}, "gong_ying_shang_nian_zhan": {"message": "आपूर्तिकर्ता पुराना है"}, "gong_ying_shang_xin_xi": {"message": "आपूर्तिकर्ता की जानकारी"}, "gong_ying_shang_zhu_ye_lian_jie": {"message": "आपूर्तिकर्ता होमपेज लिंक"}, "green_rocket": {"message": "로켓프레시"}, "grey_rocket": {"message": "로켓설치"}, "gu_ji_shou_jia": {"message": "अनुमानित विक्रय मूल्य"}, "guan_jian_zi": {"message": "कीवर्ड"}, "guang_gao_chan_pin": {"message": "विज्ञापन उत्पाद"}, "guang_gao_zhan_bi": {"message": "विज्ञापन अनुपात"}, "guo_ji_wu_liu_yun_fei": {"message": "अंतर्राष्ट्रीय शिपिंग शुल्क"}, "guo_lv_tiao_jian": {"message": "फ़िल्टर"}, "hao_ping_lv": {"message": "सकारात्मक रेटिंग"}, "highest_price": {"message": "उच्च"}, "historical_trend": {"message": "ऐतिहासिक प्रवृत्ति"}, "how_to_screenshot": {"message": "क्षेत्र का चयन करने के लिए बाएँ माउस बटन को दबाए रखें, स्क्रीनशॉट से बाहर निकलने के लिए दाएँ माउस बटन या Esc कुंजी को टैप करें"}, "howt_it_works": {"message": "यह काम किस प्रकार करता है"}, "hui_fu_lv": {"message": "प्रतिक्रिया की दर"}, "hui_tou_lv": {"message": "वापसी की दर"}, "inquire_freightFee": {"message": "फ्रेट पूछताछ"}, "inquire_freightFee_Yuan": {"message": "फ्रेट/युआन"}, "inquire_freightFee_province": {"message": "प्रांत"}, "inquire_freightFee_the": {"message": "फ्रेट $num$ है, जिसका अर्थ है कि इस क्षेत्र में मुफ़्त शिपिंग है।", "placeholders": {"num": {"content": "$1"}}}, "is_ad": {"message": "विज्ञापन."}, "jia_ge": {"message": "कीमत"}, "jia_ge_dan_wei": {"message": "इकाई"}, "jia_ge_qu_shi": {"message": "रुझान"}, "jia_zai_n_ge_shang_pin": {"message": "$num$ उत्पाद लोड करें", "placeholders": {"num": {"content": "$1"}}}, "jin_30_tian_xiao_liang_zhan_bi": {"message": "पिछले 30 दिनों में बिक्री मात्रा का प्रतिशत"}, "jin_30_tian_xiao_shou_e_zhan_bi": {"message": "पिछले 30 दिनों में राजस्व का प्रतिशत"}, "jin_30d_xiao_liang": {"message": "बिक<PERSON>री"}, "jin_30d_xiao_liang__desc": {"message": "पिछले 30 दिनों में कुल बिक्री"}, "jin_30d_xiao_shou_e": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "jin_30d_xiao_shou_e__desc": {"message": "पिछले 30 दिनों में कुल कारोबार"}, "jin_90_tian_mai_jia_shu": {"message": "पिछले 90 दिनों में खरीदार"}, "jin_90_tian_xiao_shou_liang": {"message": "पिछले 90 दिनों में बिक्री"}, "jing_xuan_huo_yuan": {"message": "चयनित स्रोत"}, "jing_ying_mo_shi": {"message": "व्यापार मॉडल"}, "jing_ying_mo_shi__gong_chang": {"message": "उत्पादक"}, "jiu_fen_jie_jue": {"message": "विवाद समाधान"}, "jiu_fen_jie_jue__desc": {"message": "विक्रेताओं के स्टोर अधिकार विवादों का लेखांकन"}, "jiu_fen_lv": {"message": "विवाद दर"}, "jiu_fen_lv__desc": {"message": "पिछले 30 दिनों में पूरी की गई शिकायतों के साथ ऑर्डर का अनुपात और विक्रेता या दोनों पक्षों की जिम्मेदारी माना गया"}, "kai_dian_ri_qi": {"message": "खुलने की तिथि"}, "keywords": {"message": "कीवर्ड"}, "kua_jin_Select_pan_huo": {"message": "सीमा पार चयन"}, "last15_days": {"message": "पिछले 15 दिन"}, "last180_days": {"message": "पिछले 180 दिन"}, "last30_days": {"message": "पिछले 30 दिनों में"}, "last360_days": {"message": "पिछले 360 दिन"}, "last45_days": {"message": "पिछले 45 दिन"}, "last60_days": {"message": "पिछले 60 दिन"}, "last7_days": {"message": "पिछले 7 दिन"}, "last90_days": {"message": "पिछले 90 दिन"}, "last_30d_sales": {"message": "पिछले 30 दिनों की बिक्री"}, "lei_ji": {"message": "संचयी"}, "lei_ji_xiao_liang": {"message": "कुल"}, "lei_ji_xiao_liang__desc": {"message": "शेल्फ पर उत्पाद के बाद की सभी बिक्री"}, "lei_ji_xiao_liang_pai_xu__desc": {"message": "पिछले 30 दिनों में संचयी बिक्री की मात्रा, उच्च से निम्न तक क्रमबद्ध"}, "lian_xi_fang_shi": {"message": "संपर्क जानकारी"}, "list_time": {"message": "शेल्फ तिथि पर"}, "load_more": {"message": "अधिक लोड करें"}, "login_to_aliprice": {"message": "अलीप्राइस में लॉग इन करें"}, "long_link": {"message": "लंबा लिंक"}, "lowest_price": {"message": "कम"}, "mai_jia_shu": {"message": "विक्रेता संख्या"}, "mao_li_lv": {"message": "सकल लाभ"}, "mobile_view__dkxbqy": {"message": "नया टैब खोलें"}, "mobile_view__sjdxq": {"message": "ऐप में विवरण"}, "mobile_view__sjdxqy": {"message": "ऐप में विवरण पृष्ठ"}, "mobile_view__smck": {"message": "देखने के लिए स्कैन करें"}, "mobile_view__smckms": {"message": "स्कैन करने और देखने के लिए कृपया कैमरा या ऐप का उपयोग करें"}, "modified_failed": {"message": "संशोधन विफल रहा"}, "modified_successfully": {"message": "सफलतापूर्वक संशोधित किया गया"}, "nav_btn_favorites": {"message": "मेरे संग्रह"}, "nav_btn_package": {"message": "पैकेज"}, "nav_btn_product_info": {"message": "उत्पाद के बारे में"}, "nav_btn_viewed": {"message": "देखा गया"}, "no_suggest_search_kw": {"message": "Loading..."}, "none": {"message": "कोई नहीं"}, "normal_link": {"message": "सामान्य लिंक"}, "notice": {"message": "संकेत"}, "number_reviews": {"message": "समीक्षाएँ"}, "only_show_num": {"message": "कुल उत्पाद: $allnum$, छिपा हुआ: $hidenum2$", "placeholders": {"allnum": {"content": "$1"}, "hidenum2": {"content": "$2"}}}, "only_show_selected": {"message": "हटाएँ अनचेक"}, "open": {"message": "खोलें"}, "open_links": {"message": "लिंक खोलें"}, "options_page_tab_check_links": {"message": "लिंक की जाँच करें"}, "options_page_tab_gernal": {"message": "सामान्य"}, "options_page_tab_notifications": {"message": "सूचनाएं"}, "options_page_tab_others": {"message": "अन्य"}, "options_page_tab_sbi": {"message": "छवि द्वारा खोजें"}, "options_page_tab_shortcuts": {"message": "शॉर्टकट"}, "options_page_tab_shortcuts_title": {"message": "शॉर्टकट के लिए फ़ॉन्ट आकार"}, "options_page_tab_similar_products": {"message": "एक ही उत्पाद"}, "orange_rocket": {"message": "판매자로켓"}, "order_list_open_links_tip": {"message": "कई उत्पाद लिंक खुलने वाले हैं"}, "order_list_sku_show_title": {"message": "शेयर किए गए लिंक में चयनित वेरिएंट दिखाएं"}, "orders_last30_days": {"message": "पिछले 30 दिनों में ऑर्डर की संख्या"}, "pTutorial_favorites_block1_desc1": {"message": "आपके द्वारा ट्रैक किए गए उत्पाद यहां सूचीबद्ध हैं"}, "pTutorial_favorites_block1_title": {"message": "पसंदीदा"}, "pTutorial_popup_block1_desc1": {"message": "एक हरे रंग की लेबल का मतलब है कि मूल्य गिराए गए उत्पाद हैं"}, "pTutorial_popup_block1_title": {"message": "शॉर्टकट और पसंदीदा"}, "pTutorial_price_history_block1_desc1": {"message": "ट्रैक मूल्य पर क्लिक करें, उत्पादों को पसंदीदा में जोड़ें। एक बार उनकी कीमतें गिर जाने के बाद, आपको सूचनाएं मिलेंगी"}, "pTutorial_price_history_block1_title": {"message": "ट्रैक की कीमत"}, "pTutorial_reviews_block1_desc1": {"message": "खरीदारों की Itao से समीक्षा और AliExpress प्रतिक्रिया से वास्तविक तस्वीरें"}, "pTutorial_reviews_block1_title": {"message": "समीक्षा"}, "pTutorial_reviews_block2_desc1": {"message": "यह हमेशा अन्य खरीदारों से समीक्षा की जाँच करने में मददगार होता है"}, "pTutorial_same_products_block1_desc1": {"message": "आप उन्हें सबसे अच्छा विकल्प बनाने के लिए तुलना कर सकते हैं"}, "pTutorial_same_products_block1_desc2": {"message": "छवि के आधार पर खोजें पर क्लिक करें"}, "pTutorial_same_products_block1_title": {"message": "एक ही उत्पाद"}, "pTutorial_same_products_by_image_search_block1_desc1": {"message": "उत्पाद छवि को वहां छोड़ें और एक श्रेणी चुनें"}, "pTutorial_same_products_by_image_search_block1_title": {"message": "छवि द्वारा खोजें"}, "pTutorial_seller_analysis_block1_desc1": {"message": "विक्रेता की सकारात्मक प्रतिक्रिया दर, प्रतिक्रिया स्कोर और विक्रेता कितने समय तक बाजार पर रहा है"}, "pTutorial_seller_analysis_block1_title": {"message": "विक्रेता रेटिंग"}, "pTutorial_seller_analysis_block2_desc2": {"message": "विक्रेता रेटिंग 3 अनुक्रमितों पर आधारित है: आइटम के रूप में वर्णित, संचार नौवहन गति"}, "pTutorial_seller_analysis_block3_desc3": {"message": "हम विक्रेताओं के विश्वास स्तर को इंगित करने के लिए 3 रंगों और आइकन का उपयोग करते हैं"}, "page_count": {"message": "पृष्ठों की संख्या"}, "pai_chu": {"message": "बहिष्कृत"}, "pai_chu_HK_bu_yi_xia_xiaoshou": {"message": "हांगकांग-प्रतिबंधित को बाहर निकालें"}, "pai_chu_JP_bu_yi_xia_xiaoshou": {"message": "जापान-प्रतिबंधित को बाहर निकालें"}, "pai_chu_KR_bu_yi_xia_xiaoshou": {"message": "कोरिया-प्रतिबंधित को बाहर निकालें"}, "pai_chu_KZ_bu_yi_xia_xiaoshou": {"message": "कजाकिस्तान-प्रतिबंधित को बाहर निकालें"}, "pai_chu_MO_bu_yi_xia_xiaoshou": {"message": "मकाऊ-प्रतिबंधित को बाहर निकालें"}, "pai_chu_RU_bu_yi_xia_xiaoshou": {"message": "पूर्वी यूरोप-प्रतिबंधित को बाहर निकालें"}, "pai_chu_SA_bu_yi_xia_xiaoshou": {"message": "सऊदी अरब-प्रतिबंधित को बाहर निकालें"}, "pai_chu_TW_bu_yi_xia_xiaoshou": {"message": "ताइवान-प्रतिबंधित को बाहर निकालें"}, "pai_chu_USA_bu_yi_xia_xiaoshou": {"message": "अमेरिका-प्रतिबंधित को बाहर निकालें"}, "pai_chu_VN_bu_yi_xia_xiaoshou": {"message": "वियतनाम-प्रतिबंधित को बाहर निकालें"}, "pai_chu_bu_yi_xia_xiaoshou": {"message": "प्रतिबंधित आइटम को बाहर निकालें"}, "payable_price_formula": {"message": "कीमत + शिपिंग + छूट"}, "pdd_check_retail_btn_txt": {"message": "खुदरा की जाँच करें"}, "pdd_pifa_to_retail_btn_txt": {"message": "रिटेल में खरीदें"}, "pdp_copy_fail": {"message": "कॉपी विफल!"}, "pdp_copy_success": {"message": "कॉपी सफल!"}, "pdp_share_modal_subtitle": {"message": "स्क्रीनशॉट साझा करें, वह आपकी पसंद को देखेगा।"}, "pdp_share_modal_title": {"message": "अपनी पसंद साझा करें"}, "pdp_share_screenshot": {"message": "स्क्रीनशॉट साझा करें"}, "pei_song": {"message": "शिपिंग विधि"}, "pin_lei": {"message": "श्रेणी"}, "pin_zhi_ti_yan": {"message": "उत्पाद की गुणवत्ता"}, "pin_zhi_ti_yan__desc": {"message": "विक्रेता के स्टोर की गुणवत्ता वापसी दर"}, "pin_zhi_tui_kuan_lv": {"message": "वापसी दर"}, "pin_zhi_tui_kuan_lv__desc": {"message": "उन ऑर्डरों का अनुपात जिन्हें केवल पिछले 30 दिनों में वापस कर दिया गया है"}, "ping_fen": {"message": "रेटिंग"}, "ping_jun_fa_huo_su_du": {"message": "औसत शिपिंग गति"}, "pkgInfo_hide": {"message": "रस<PERSON> जानकारी: चालू/बंद"}, "pkgInfo_no_trace": {"message": "कोई लॉजिस्टिक जानकारी नहीं"}, "platform_name__1688": {"message": "1688"}, "platform_name__17zwd": {"message": "17zwd.com"}, "platform_name__51taoyang": {"message": "51taoyang.com"}, "platform_name__5ts": {"message": "5ts.com"}, "platform_name__91jf": {"message": "91jf.com"}, "platform_name__alibaba": {"message": "Alibaba.com"}, "platform_name__aliexpress": {"message": "AliExpress"}, "platform_name__amazon": {"message": "Amazon"}, "platform_name__bao66": {"message": "bao66.cn"}, "platform_name__chinagoods": {"message": "chinagoods.com"}, "platform_name__dhgate": {"message": "DHgate"}, "platform_name__e3hui": {"message": "e3hui.com"}, "platform_name__ebay": {"message": "EBAY"}, "platform_name__hznzcn": {"message": "hznzcn.com"}, "platform_name__jd": {"message": "JD"}, "platform_name__juyi5": {"message": "juyi5.cn"}, "platform_name__naver_shopping": {"message": "Naver Shopping"}, "platform_name__pinduoduo": {"message": "पिंडुओदुओ"}, "platform_name__pinduoduo_pifa": {"message": "Pinduoduo थोक"}, "platform_name__shopee": {"message": "<PERSON>ee"}, "platform_name__sjxzw": {"message": "571xz.com"}, "platform_name__sooxie": {"message": "sooxie.com"}, "platform_name__taobao": {"message": "ताओबाओ"}, "platform_name__taobao_lite": {"message": "Taobao Lite"}, "platform_name__vvic": {"message": "vvic.com"}, "platform_name__walmart": {"message": "Walmart"}, "platform_name__wsy": {"message": "wsy.com"}, "platform_name__xingfujie": {"message": "xingfujie.cn"}, "platform_name__yiwugo": {"message": "Yiwugo.com"}, "platform_name__yunchepin": {"message": "yunchepin.cn"}, "platform_name__zhaojiafang": {"message": "zhaojiafang.com"}, "popup_go_to_home": {"message": "घर"}, "popup_go_to_platform": {"message": "$name$ पर जाएं", "placeholders": {"name": {"content": "$1"}}}, "popup_search_placeholder": {"message": "मैं खरीदारी कर रहा हूं ..."}, "popup_track_package_btn_track": {"message": "धावन पथ"}, "popup_track_package_desc": {"message": "सभी में एक पैकेज ट्रैकिंग"}, "popup_track_package_search_placeholder": {"message": "खोज संख्या"}, "popup_translate_search_placeholder": {"message": "$searchOn$ . पर अनुवाद करें और खोजें", "placeholders": {"searchOn": {"content": "$1"}}}, "price_history": {"message": "मूल्य इतिहास"}, "price_history_chart_tip_ae": {"message": "टिप: ऑर्डर की संख्या लॉन्च के बाद से ऑर्डर की संचयी संख्या है"}, "price_history_chart_tip_coupang": {"message": "टिप: कूपांग धोखाधड़ी वाले ऑर्डर की ऑर्डर संख्या को हटा देगा"}, "price_history_inm_1688_l1": {"message": "कृपया स्थापित करें"}, "price_history_inm_1688_l2": {"message": "1688 के लिए अलीप्राइस शॉपिंग असिस्टेंट"}, "price_history_panel_lowest_price": {"message": "सबसे कम दाम:"}, "price_history_panel_tab_price_tracking": {"message": "मूल्य इतिहास"}, "price_history_panel_tab_seller_analysis": {"message": "विक्रेता विश्लेषण"}, "price_history_pro_modal_title": {"message": "मूल्य इतिहास और ऑर्डर इतिहास"}, "privacy_consent__btn_agree": {"message": "डेटा संग्रह सहमति पर दोबारा गौर करें"}, "privacy_consent__btn_disable_all": {"message": "स्वीकार नही"}, "privacy_consent__btn_enable_all": {"message": "सभी को सक्षम करें"}, "privacy_consent__btn_uninstall": {"message": "हटाना"}, "privacy_consent__desc_privacy": {"message": "ध्यान दें कि, डेटा या कुकीज़ के बिना कुछ कार्य बंद हो जाएंगे क्योंकि उन कार्यों को डेटा या कुकीज़ के स्पष्टीकरण की आवश्यकता होती है, लेकिन आप अभी भी अन्य कार्यों का उपयोग कर सकते हैं।"}, "privacy_consent__desc_privacy_L1": {"message": "दुर्भाग्य से, डेटा या कुकीज़ के बिना यह काम नहीं करेगा क्योंकि हमें डेटा या कुकीज़ के स्पष्टीकरण की आवश्यकता है।"}, "privacy_consent__desc_privacy_L2": {"message": "यदि आप हमें इन सूचनाओं को एकत्र करने की अनुमति नहीं देते हैं, तो कृपया इसे हटा दें।"}, "privacy_consent__item_cookies_desc": {"message": "कुकी, हम केवल मूल्य इतिहास दिखाने के लिए ऑनलाइन खरीदारी करते समय कुकीज़ में आपका मुद्रा डेटा प्राप्त करते हैं।"}, "privacy_consent__item_cookies_title": {"message": "आवश्यक कुकीज़"}, "privacy_consent__item_functional_desc_L1": {"message": "1. अपने कंप्यूटर या डिवाइस की पहचान करने के लिए ब्राउज़र में कुकीज़ जोड़ें।"}, "privacy_consent__item_functional_desc_L2": {"message": "2. फ़ंक्शन के साथ काम करने के लिए ऐड-ऑन में कार्यात्मक डेटा जोड़ें।"}, "privacy_consent__item_functional_title": {"message": "कार्यात्मक और विश्लेषिकी कुकीज़"}, "privacy_consent__more_desc": {"message": "कृपया जान लें कि हम आपके व्यक्तिगत डेटा को अन्य कंपनियों के साथ साझा नहीं करते हैं और कोई भी विज्ञापन कंपनी हमारी सेवा के माध्यम से डेटा एकत्र नहीं करती है।"}, "privacy_consent__options__btn__desc": {"message": "सभी सुविधाओं का उपयोग करने के लिए, आपको इसे चालू करना होगा।"}, "privacy_consent__options__btn__label": {"message": "इसे चालू करो"}, "privacy_consent__options__desc_L1": {"message": "हम निम्नलिखित डेटा एकत्र करेंगे जो व्यक्तिगत रूप से आपकी पहचान करता है:"}, "privacy_consent__options__desc_L2": {"message": "- कुकीज, मूल्य की जानकारी दिखाने के लिए ऑनलाइन खरीदारी करते समय हमें केवल आपकी मुद्रा का डेटा कुकीज़ में मिलता है।"}, "privacy_consent__options__desc_L3": {"message": "- और अपने कंप्यूटर या डिवाइस की पहचान करने के लिए ब्राउज़र में कुकीज़ जोड़ें।"}, "privacy_consent__options__desc_L4": {"message": "- अन्य अनाम डेटा इस एक्सटेंशन को अधिक सुविधाजनक बनाते हैं।"}, "privacy_consent__options__desc_L5": {"message": "कृपया ध्यान दें कि हम आपके व्यक्तिगत डेटा को अन्य कंपनियों के साथ साझा नहीं करते हैं और कोई भी विज्ञापन कंपनियां हमारी सेवा के माध्यम से डेटा एकत्र नहीं करती हैं।"}, "privacy_consent__privacy_preferences": {"message": "गोपनीयता प्राथमिकताएँ"}, "privacy_consent__read_more": {"message": "और पढ़ें >>"}, "privacy_consent__title_privacy": {"message": "एकांत"}, "product_info": {"message": "उत्पाद जानकारी"}, "product_recommend__name": {"message": "एक ही उत्पाद"}, "product_research": {"message": "उत्पाद अनुसंधान"}, "product_sub__email_desc": {"message": "मूल्य चेतावनी ईमेल"}, "product_sub__email_edit": {"message": "संपादित करें"}, "product_sub__email_not_verified": {"message": "कृपया ईमेल सत्यापित करें"}, "product_sub__email_required": {"message": "कृपया ईमेल प्रदान करें"}, "product_sub__form_countdown": {"message": "$seconds$ सेकंड के बाद स्वतः बंद हो जाता है", "placeholders": {"seconds": {"content": "$1"}}}, "product_sub__form_fail": {"message": "रिमाइंडर जोड़ने में विफल!"}, "product_sub__form_input_price": {"message": "इनपुट मूल्य"}, "product_sub__form_item_country": {"message": "राष्ट्र"}, "product_sub__form_item_current_price": {"message": "मौजूदा कीमत"}, "product_sub__form_item_duration": {"message": "संकरा रास्ता"}, "product_sub__form_item_higher_price": {"message": "या कीमत>"}, "product_sub__form_item_invalid_higher_price": {"message": "कीमत $price$ . से ज़्यादा होनी चाहिए", "placeholders": {"price": {"content": "$1"}}}, "product_sub__form_item_invalid_lower_price": {"message": "कीमत $price$ . से कम होनी चाहिए", "placeholders": {"price": {"content": "$1"}}}, "product_sub__form_item_lower_price": {"message": "जब कीमत <"}, "product_sub__form_submit": {"message": "प्रस्तुत करना"}, "product_sub__form_success": {"message": "रिमाइंडर जोड़ने में सफल रहा!"}, "product_sub__high_price_notify": {"message": "मूल्य वृद्धि के बारे में मुझे सूचित करें"}, "product_sub__low_price_notify": {"message": "कीमतों में कटौती के बारे में मुझे सूचित करें"}, "product_sub__modal_title": {"message": "सदस्यता मूल्य परिवर्तन अनुस्मारक"}, "province__an_hui": {"message": "<PERSON><PERSON>"}, "province__ao_men": {"message": "Macau"}, "province__bei_jing": {"message": "Beijing"}, "province__chong_qing": {"message": "Chongqing"}, "province__fu_jian": {"message": "Fujian"}, "province__gan_su": {"message": "Gansu"}, "province__guang_dong": {"message": "Guangdong"}, "province__guang_xi": {"message": "Guangxi"}, "province__gui_zhou": {"message": "Guizhou"}, "province__hai_nan": {"message": "Hainan"}, "province__he_bei": {"message": "Hebei"}, "province__he_nan": {"message": "<PERSON><PERSON>"}, "province__hei_long_jiang": {"message": "Heilongjiang"}, "province__hu_bei": {"message": "Hubei"}, "province__hu_nan": {"message": "Hunan"}, "province__ji_lin": {"message": "<PERSON><PERSON>"}, "province__jiang_su": {"message": "Jiangsu"}, "province__jiang_xi": {"message": "Jiangxi"}, "province__liao_ning": {"message": "Liaoning"}, "province__nei_meng_gu": {"message": "Inner Mongolia"}, "province__ning_xia": {"message": "Ningxia"}, "province__qing_hai": {"message": "Qinghai"}, "province__shan_dong": {"message": "Shandong"}, "province__shan_xi": {"message": "Shaanxi"}, "province__shang_hai": {"message": "Shanghai"}, "province__si_chuan": {"message": "Sichuan"}, "province__tai_wan": {"message": "Taiwan"}, "province__tian_jin": {"message": "Tianjin"}, "province__xi_zhang": {"message": "Tibet"}, "province__xiang_gang": {"message": "Hong Kong"}, "province__xin_jiang": {"message": "Xinjiang"}, "province__yun_nan": {"message": "Yunnan"}, "province__zhe_jiang": {"message": "Zhejiang"}, "purple_rocket": {"message": "로켓직구"}, "qi_ding_liang": {"message": "MOQ"}, "qi_ding_liang_qi_ding_jia": {"message": "MOQ और MOP"}, "qi_ye_mian_ji": {"message": "उद्यम क्षेत्र"}, "qing_zhi_shao_xuan_ze_yi_ge_chan_pin": {"message": "कृपया कम से कम एक उत्पाद चयनित करें"}, "qing_zhi_shao_xuan_ze_yi_ge_zi_duan": {"message": "कृपया कम से कम एक फ़ील्ड चुनें"}, "qu_deng_lu": {"message": "लॉग इन करें"}, "quan_guo_yan_xuan": {"message": "वैश्विक विकल्प"}, "recommendation_popup_banner_btn_install": {"message": "इसे स्थापित करो"}, "recommendation_popup_banner_desc": {"message": "3/6 महीने के भीतर मूल्य इतिहास प्रदर्शित करें, और मूल्य ड्रॉप सूचना"}, "region__all": {"message": "सभी क्षेत्र"}, "region__hai_wai": {"message": "Overseas"}, "region__hua_bei_qu": {"message": "North China"}, "region__hua_dong_qu": {"message": "East China"}, "region__hua_nan_qu": {"message": "South China"}, "region__hua_zhong_qu": {"message": "Central China"}, "region__jiang_zhe_lu": {"message": "Jiangsu, Zhejiang and Shanghai"}, "remove_web_limits": {"message": "राइट क्लिक सक्षम करें"}, "ren_zheng_gong_chang": {"message": "प्रमाणित फ़ैक्टरी"}, "ren_zheng_gong_ying_shang_nian_zhan": {"message": "प्रमाणित आपूर्तिकर्ता के रूप में वर्ष"}, "required_to_aliprice_login": {"message": "<PERSON><PERSON><PERSON> में लॉग इन करने की आवश्यकता है"}, "revenue_last30_days": {"message": "पिछले 30 दिनों में बिक्री की मात्रा"}, "review_counts": {"message": "संग्राहकों की संख्या"}, "rocket": {"message": "로켓"}, "ru_zhu_nian_xian": {"message": "प्रवेश अवधि"}, "sales_amount_last30_days": {"message": "पिछले 30 दिनों में कुल बिक्री"}, "sales_last30_days": {"message": "पिछले 30 दिनों में बिक्री"}, "sbi_alibaba_cate__accessories": {"message": "सामान"}, "sbi_alibaba_cate__aqfk": {"message": "सुरक्षा"}, "sbi_alibaba_cate__bags_cases": {"message": "बैग और मामले"}, "sbi_alibaba_cate__beauty": {"message": "सुंदरता"}, "sbi_alibaba_cate__beverage": {"message": "पेय पदार्थ"}, "sbi_alibaba_cate__bgwh": {"message": "कार्यालय संस्कृति"}, "sbi_alibaba_cate__bz": {"message": "पैकेट"}, "sbi_alibaba_cate__ccyj": {"message": "बरतन"}, "sbi_alibaba_cate__clothes": {"message": "परिधान"}, "sbi_alibaba_cate__cmgd": {"message": "मीडिया प्रसारण"}, "sbi_alibaba_cate__coat_jacket": {"message": "कोट जैकेट"}, "sbi_alibaba_cate__consumer_electronics": {"message": "उपभोक्ता इलेक्ट्रॉनिक्स"}, "sbi_alibaba_cate__cryp": {"message": "वयस्क उत्पाद"}, "sbi_alibaba_cate__csyp": {"message": "बिस्तर के अस्तर"}, "sbi_alibaba_cate__cwyy": {"message": "पालतू बागवानी"}, "sbi_alibaba_cate__cysx": {"message": "खानपान ताजा"}, "sbi_alibaba_cate__dgdq": {"message": "बिजली मिस्त्री"}, "sbi_alibaba_cate__dl": {"message": "अभिनय"}, "sbi_alibaba_cate__dress_suits": {"message": "पोशाक और सूट"}, "sbi_alibaba_cate__dszm": {"message": "प्रक<PERSON>श"}, "sbi_alibaba_cate__dzqj": {"message": "इलेक्ट्रॉनिक उपकरण"}, "sbi_alibaba_cate__essb": {"message": "उपयोग किए हुए उपकरण"}, "sbi_alibaba_cate__food": {"message": "खाना"}, "sbi_alibaba_cate__fspj": {"message": "वस्त्र और सहायक सामग्री"}, "sbi_alibaba_cate__furniture": {"message": "फर्नीचर"}, "sbi_alibaba_cate__fzpg": {"message": "कपड़ा चमड़ा"}, "sbi_alibaba_cate__ghjq": {"message": "व्यक्तिगत देखभाल"}, "sbi_alibaba_cate__gt": {"message": "इस्पात"}, "sbi_alibaba_cate__gyp": {"message": "शिल्प"}, "sbi_alibaba_cate__hb": {"message": "पर्यावरण के अनुकूल"}, "sbi_alibaba_cate__hfcz": {"message": "त्वचा की देखभाल मेकअप"}, "sbi_alibaba_cate__hg": {"message": "रसायन उद्योग"}, "sbi_alibaba_cate__jg": {"message": "प्रसंस्करण"}, "sbi_alibaba_cate__jianccai": {"message": "निर्माण सामग्री"}, "sbi_alibaba_cate__jichuang": {"message": "मशीन औज़ार"}, "sbi_alibaba_cate__jjry": {"message": "घरेलू दैनिक उपयोग"}, "sbi_alibaba_cate__jtys": {"message": "परिवहन"}, "sbi_alibaba_cate__jxsb": {"message": "उपकरण"}, "sbi_alibaba_cate__jxwj": {"message": "यांत्रिक हार्डवेयर"}, "sbi_alibaba_cate__jydq": {"message": "घर का सामान"}, "sbi_alibaba_cate__jzjc": {"message": "गृह सुधार निर्माण सामग्री"}, "sbi_alibaba_cate__jzjf": {"message": "घरेलू टेक्स्टाइल"}, "sbi_alibaba_cate__mj": {"message": "तौलिया"}, "sbi_alibaba_cate__myyp": {"message": "छोटे उत्पाद"}, "sbi_alibaba_cate__nanz": {"message": "पुरुषों के लिए"}, "sbi_alibaba_cate__nvz": {"message": "महिलाओं के वस्त्र"}, "sbi_alibaba_cate__ny": {"message": "ऊर्जा"}, "sbi_alibaba_cate__others": {"message": "अन्य"}, "sbi_alibaba_cate__qcyp": {"message": "ऑटो उपकरण"}, "sbi_alibaba_cate__qmpj": {"message": "ऑटो भाग"}, "sbi_alibaba_cate__shoes": {"message": "जूते"}, "sbi_alibaba_cate__smdn": {"message": "डिजिटल कम्प्यूटर"}, "sbi_alibaba_cate__snqj": {"message": "भंडारण और सफाई"}, "sbi_alibaba_cate__spjs": {"message": "भोजन पेय"}, "sbi_alibaba_cate__swfw": {"message": "व्यापार सेवाएं"}, "sbi_alibaba_cate__toys_hobbies": {"message": "खिलौने"}, "sbi_alibaba_cate__trousers_skirt": {"message": "पतलून और स्कर्ट"}, "sbi_alibaba_cate__txcp": {"message": "संचार उत्पाद"}, "sbi_alibaba_cate__tz": {"message": "बच्चों के कपड़े"}, "sbi_alibaba_cate__underwear": {"message": "अंडरवियर"}, "sbi_alibaba_cate__wjgj": {"message": "हार्डवेयर उपकरण"}, "sbi_alibaba_cate__xgpi": {"message": "चमड़े के बैग"}, "sbi_alibaba_cate__xmhz": {"message": "परियोजना सहयोग"}, "sbi_alibaba_cate__xs": {"message": "रबड़"}, "sbi_alibaba_cate__ydfs": {"message": "खेलों"}, "sbi_alibaba_cate__ydhw": {"message": "बाहर का खेल"}, "sbi_alibaba_cate__yjkc": {"message": "धातुकर्म खनिज"}, "sbi_alibaba_cate__yqyb": {"message": "उपकरण"}, "sbi_alibaba_cate__ys": {"message": "छाप"}, "sbi_alibaba_cate__yyby": {"message": "चिकित्सा देखभाल"}, "sbi_alibaba_cn_kj_90mjs": {"message": "पिछले 90 दिनों में खरीदारों की संख्या"}, "sbi_alibaba_cn_kj_90xsl": {"message": "पिछले 90 दिनों में बिक्री की मात्रा"}, "sbi_alibaba_cn_kj_gjsj": {"message": "अनुमानित दाम"}, "sbi_alibaba_cn_kj_gjwlyf": {"message": "अंतर्राष्ट्रीय शिपिंग शुल्क"}, "sbi_alibaba_cn_kj_gjyf": {"message": "शिपिंग शुल्क"}, "sbi_alibaba_cn_kj_gssj": {"message": "अनुमानित दाम"}, "sbi_alibaba_cn_kj_lr": {"message": "फायदा"}, "sbi_alibaba_cn_kj_lrgs": {"message": "लाभ = अनुमानित मूल्य x लाभ मार्जिन"}, "sbi_alibaba_cn_kj_pjfhsd": {"message": "औसत वितरण गति"}, "sbi_alibaba_cn_kj_qtfy": {"message": "अन्य शुल्क"}, "sbi_alibaba_cn_kj_qtfygs": {"message": "अन्य लागत = अनुमानित मूल्य x अन्य लागत अनुपात"}, "sbi_alibaba_cn_kj_spjg": {"message": "कीमत"}, "sbi_alibaba_cn_kj_spzl": {"message": "वजन"}, "sbi_alibaba_cn_kj_szd": {"message": "स्थान"}, "sbi_alibaba_cn_kj_unit_ge": {"message": "टुकड़े टुकड़े"}, "sbi_alibaba_cn_kj_unit_jian": {"message": "टुकड़े टुकड़े"}, "sbi_alibaba_cn_kj_unit_ke": {"message": "ग्राम"}, "sbi_alibaba_cn_kj_unit_ren": {"message": "खरीदार"}, "sbi_alibaba_cn_kj_unit_tai": {"message": "टुकड़े टुकड़े"}, "sbi_alibaba_cn_kj_unit_tao": {"message": "सेट"}, "sbi_alibaba_cn_kj_unit_tian": {"message": "दिन"}, "sbi_alibaba_cn_kj_zwbj": {"message": "कोई कीमत नहीं"}, "sbi_aliprice_alibaba_cn__jiage": {"message": "कीमत"}, "sbi_aliprice_alibaba_cn__keshou": {"message": "बिक्री के लिए उपलब्ध"}, "sbi_aliprice_alibaba_cn__moren": {"message": "चूक जाना"}, "sbi_aliprice_alibaba_cn__queding": {"message": "ज़रूर"}, "sbi_aliprice_alibaba_cn__xiaoliang": {"message": "बिक<PERSON>री"}, "sbi_aliprice_alibaba_cn_cate__jiaju": {"message": "फर्नीचर"}, "sbi_aliprice_alibaba_cn_cate__linghsi": {"message": "नाश्ता"}, "sbi_aliprice_alibaba_cn_cate__meizhuang": {"message": "श्रृंगार"}, "sbi_aliprice_alibaba_cn_cate__neiyi": {"message": "अंडरवियर"}, "sbi_aliprice_alibaba_cn_cate__peishi": {"message": "सामान"}, "sbi_aliprice_alibaba_cn_cate__pinchui": {"message": "बोतलबंद पेय"}, "sbi_aliprice_alibaba_cn_cate__qita": {"message": "अन्य"}, "sbi_aliprice_alibaba_cn_cate__qunzhuang": {"message": "स्कर्ट"}, "sbi_aliprice_alibaba_cn_cate__shangyi": {"message": "जैकेट"}, "sbi_aliprice_alibaba_cn_cate__shuma": {"message": "इलेक्ट्रानिक्स"}, "sbi_aliprice_alibaba_cn_cate__wanju": {"message": "खिलौने"}, "sbi_aliprice_alibaba_cn_cate__xiangbao": {"message": "सामान"}, "sbi_aliprice_alibaba_cn_cate__xiazhuang": {"message": "नीचे"}, "sbi_aliprice_alibaba_cn_cate__xiezi": {"message": "जूता"}, "sbi_aliprice_cate__apparel": {"message": "परिधान"}, "sbi_aliprice_cate__automobiles_motorcycles": {"message": "ऑटोमोबाइल और मोटरसाइकिल"}, "sbi_aliprice_cate__beauty_health": {"message": "संदुरता और स्वास्थ्य"}, "sbi_aliprice_cate__cellphones_telecommunications": {"message": "सेलफोन और दूरसंचार"}, "sbi_aliprice_cate__computer_office": {"message": "कंप्यूटर और कार्यालय"}, "sbi_aliprice_cate__consumer_electronics": {"message": "उपभोक्ता इलेक्ट्रॉनिक्स"}, "sbi_aliprice_cate__education_office_supplies": {"message": "शिक्षा और कार्यालय आपूर्तियाँ"}, "sbi_aliprice_cate__electronic_components_supplies": {"message": "इलेक्ट्रॉनिक अवयव और आपूर्ति"}, "sbi_aliprice_cate__furniture": {"message": "फर्नीचर"}, "sbi_aliprice_cate__hair_extensions_wigs": {"message": "बाल एक्सटेंशन और विग"}, "sbi_aliprice_cate__home_garden": {"message": "घर और बगीचा"}, "sbi_aliprice_cate__home_improvement": {"message": "घर में सुधार"}, "sbi_aliprice_cate__jewelry_accessories": {"message": "आभूषण और सहायक उपकरण"}, "sbi_aliprice_cate__luggage_bags": {"message": "समान और थैले"}, "sbi_aliprice_cate__mother_kids": {"message": "माँ और बच्चे"}, "sbi_aliprice_cate__novelty_special_use": {"message": "नवीनता और विशेष उपयोग"}, "sbi_aliprice_cate__security_protection": {"message": "सुरक्षा बचाव"}, "sbi_aliprice_cate__shoes": {"message": "जूते"}, "sbi_aliprice_cate__sports_entertainment": {"message": "खेल और मनोरंजन"}, "sbi_aliprice_cate__toys_hobbies": {"message": "खिलौने और शौक"}, "sbi_aliprice_cate__watches": {"message": "घड़ियों"}, "sbi_aliprice_cate__weddings_events": {"message": "शादियाँ और कार्यक्रम"}, "sbi_btn_capture_txt": {"message": "कब्जा"}, "sbi_btn_source_now_txt": {"message": "स्रोत अब"}, "sbi_button__chat_with_me": {"message": "मुझसे बात करो"}, "sbi_button__contact_supplier": {"message": "संपर्क करें"}, "sbi_button__hide_on_this_site": {"message": "इस साइट पर न दिखाएं"}, "sbi_button__open_settings": {"message": "छवि द्वारा खोज कॉन्फ़िगर करें"}, "sbi_capture_shortcut_tip": {"message": "या कीबोर्ड पर \"एंटर\" कुंजी दबाएं"}, "sbi_capturing_tip": {"message": "वश में कर लेना"}, "sbi_composed_rating_45": {"message": "4.5 - 5.0 सितारे"}, "sbi_crop_and_search": {"message": "खोज"}, "sbi_crop_start": {"message": "स्क्रीनशॉट का उपयोग करें"}, "sbi_err_captcha_action": {"message": "सत्यापित करें"}, "sbi_err_captcha_for_alibaba_cn": {"message": "सत्यापन की आवश्यकता है, कृपया सत्यापित करने के लिए एक चित्र अपलोड करें। ($video_tutorial$ देखें या कुकीज़ साफ़ करने का प्रयास करें)", "placeholders": {"feedback": {"content": "$1"}, "video_tutorial": {"content": "$1"}}}, "sbi_err_captcha_for_aliprice": {"message": "असामान्य ट्रैफ़िक, कृपया सत्यापित करें"}, "sbi_err_captcha_for_taobao": {"message": "Taobao आपसे सत्यापित करने का अनुरोध करता है, कृपया मैन्युअल रूप से एक तस्वीर अपलोड करें और इसे सत्यापित करने के लिए खोजें। यह त्रुटि \"छवि द्वारा TaoBao खोज\" नई सत्यापन नीति के कारण है, हम आपको सुझाव देते हैं कि Taobao $feedback$ पर शिकायत बहुत बार सत्यापित करें।", "placeholders": {"feedback": {"content": "$1"}}}, "sbi_err_captcha_for_taobao_feedback": {"message": "प्रतिपुष्टि"}, "sbi_err_captcha_msg": {"message": "$platform$ के लिए आपको खोज करने के लिए एक छवि अपलोड करनी होगी या खोज प्रतिबंध हटाने के लिए सुरक्षा सत्यापन पूरा करना होगा", "placeholders": {"platform": {"content": "$1"}}}, "sbi_err_checking_if_low_version": {"message": "जांचें कि क्या यह नवीनतम संस्करण है"}, "sbi_err_cookie_btn_clear": {"message": "कुकी साफ़ करें"}, "sbi_err_cookie_for_alibaba_cn": {"message": "1688 कुकी साफ़ करें?(फिर से लॉगिन करने की आवश्यकता है)"}, "sbi_err_desperate_feature_pdd": {"message": "छवि खोज फ़ंक्शन को छवि एक्सटेंशन द्वारा Pinduoduo खोज में स्थानांतरित कर दिया गया है।"}, "sbi_err_hidden_skill_of_improve_search_rate": {"message": "छवि खोज की सफलता दर कैसे सुधारें?"}, "sbi_err_img_undersize": {"message": "छवि > $imgMinimumSize$", "placeholders": {"imgMinimumSize": {"content": "$1"}}}, "sbi_err_login_required": {"message": "लॉग इन करें $loginSite$", "placeholders": {"loginSite": {"content": "$1"}}}, "sbi_err_login_required_action": {"message": "लॉग इन करें"}, "sbi_err_low_version": {"message": "नवीनतम संस्करण स्थापित करें ($latestVersion$)", "placeholders": {"latestVersion": {"content": "$1"}}}, "sbi_err_low_version_action": {"message": "डाउनलोड"}, "sbi_err_need_help": {"message": "मदद चाहिए"}, "sbi_err_network": {"message": "नेटवर्क त्रुटि, सुनिश्चित करें कि आप वेबसाइट पर जा सकते हैं"}, "sbi_err_not_low_version": {"message": "नवीनतम संस्करण स्थापित किया गया है ($latestVersion$)", "placeholders": {"latestVersion": {"content": "$1"}}}, "sbi_err_try_again": {"message": "पुनः प्रयास करें"}, "sbi_err_try_again_action": {"message": "पुनः प्रयास करें"}, "sbi_err_visit_and_try": {"message": "पुनः प्रयास करें, या पुनः प्रयास करने के लिए $website$ पर जाएँ", "placeholders": {"website": {"content": "$1"}}}, "sbi_err_visit_site": {"message": "$siteName$ होम पेज पर जाएँ", "placeholders": {"siteName": {"content": "$1"}}}, "sbi_fail_tip": {"message": "लोड हो रहा है, कृपया पृष्ठ को रीफ्रेश करें और पुनः प्रयास करें।"}, "sbi_kuajing_filter_area": {"message": "क्षेत्र"}, "sbi_kuajing_filter_au": {"message": "ऑस्ट्रेलिया"}, "sbi_kuajing_filter_btn_confirm": {"message": "पुष्टि करना"}, "sbi_kuajing_filter_de": {"message": "जर्मनी"}, "sbi_kuajing_filter_destination_country": {"message": "गंतव्य देश"}, "sbi_kuajing_filter_es": {"message": "स्पेन"}, "sbi_kuajing_filter_estimate": {"message": "आकलन"}, "sbi_kuajing_filter_estimate_price": {"message": "अनुमानित दाम"}, "sbi_kuajing_filter_estimate_price_formula": {"message": "अनुमानित मूल्य सूत्र = (वस्तु मूल्य + अंतर्राष्ट्रीय रसद भाड़ा)/(1 - लाभ मार्जिन - अन्य लागत अनुपात)"}, "sbi_kuajing_filter_fr": {"message": "फ्रांस"}, "sbi_kuajing_filter_kw_placeholder": {"message": "शीर्षक से मेल खाने के लिए कीवर्ड दर्ज करें"}, "sbi_kuajing_filter_logistics": {"message": "रसद टेम्पलेट"}, "sbi_kuajing_filter_logistics_china_post": {"message": "चीन पोस्ट हवाई डाक"}, "sbi_kuajing_filter_logistics_discount": {"message": "रसद छूट"}, "sbi_kuajing_filter_logistics_epacket": {"message": "epacket"}, "sbi_kuajing_filter_logistics_price_formula": {"message": "अंतर्राष्ट्रीय रसद भाड़ा = (वजन x शिपिंग मूल्य + पंजीकरण शुल्क) x (1 - छूट)"}, "sbi_kuajing_filter_others_fee": {"message": "अन्य शुल्क"}, "sbi_kuajing_filter_profit_percent": {"message": "मुनाफे का अंतर"}, "sbi_kuajing_filter_prop": {"message": "गुण"}, "sbi_kuajing_filter_ru": {"message": "रूस"}, "sbi_kuajing_filter_total": {"message": "मैच $count$ समान आइटम", "placeholders": {"count": {"content": "$1"}}}, "sbi_kuajing_filter_uk": {"message": "यू.के"}, "sbi_kuajing_filter_usa": {"message": "अमेरिका"}, "sbi_login_punish_title__pdd_pifa": {"message": "Pinduoduo थोक"}, "sbi_msg_no_result": {"message": "कोई परिणाम नहीं मिला,कृपया $loginSite$ पर लॉग इन करें या कोई अन्य चित्र आज़माएं", "placeholders": {"loginSite": {"content": "$1"}}}, "sbi_msg_no_result_check_support_page_l1": {"message": "सफारी के लिए अस्थायी रूप से उपलब्ध नहीं है, कृपया $supportPage$ का उपयोग करें।", "placeholders": {"supportPage": {"content": "$1"}}}, "sbi_msg_no_result_check_support_page_l2": {"message": "क्रोम ब्राउज़र और उसके एक्सटेंशन"}, "sbi_msg_no_result_reinstall_l1": {"message": "कोई परिणाम नहीं मिला, कृपया $loginSite$ पर लॉग इन करें या दूसरी तस्वीर आज़माएं, या नवीनतम संस्करण $latestExtUrl$", "placeholders": {"loginSite": {"content": "$1"}, "latestExtUrl": {"content": "$2"}}}, "sbi_msg_no_result_reinstall_l2": {"message": "नवीनतम संस्करण", "placeholders": {}}, "sbi_search_by_selected_area": {"message": "चयनित क्षेत्र"}, "sbi_shipping_": {"message": "उसी दिन शिपिंग"}, "sbi_specify_category": {"message": "श्रेणी निर्दिष्ट करें:"}, "sbi_start_crop": {"message": "क्षेत्र का चयन करें"}, "sbi_store_name_alibabaCNKuaJing": {"message": "1688 ओवरसीज"}, "sbi_tutorial_btn_more": {"message": "और तरीके"}, "sbi_tutorial_find_coupons_on_taobao": {"message": "Taobao कूपन खोजें"}, "sbi_txt__empty_retry": {"message": "क्षमा करें, कोई परिणाम नहीं मिला, कृपया पुनः प्रयास करें।"}, "sbi_txt__min_order": {"message": "मिन। गण"}, "sbi_visiting": {"message": "ब्राउजिंग"}, "sbi_yiwugo__jiagexiangtan": {"message": "कीमत के लिए विक्रेता से संपर्क करें"}, "sbi_yiwugo__qigou": {"message": "$num$ टुकड़े (एमओक्यू)", "placeholders": {"num": {"content": "$1"}}}, "sbi_yiwugo__xing": {"message": "सितारे"}, "searchByImage_screenshot": {"message": "एक-क्लिक स्क्रीनशॉट"}, "searchByImage_search": {"message": "समान आइटम के लिए एक-क्लिक खोज"}, "searchByImage_size_type": {"message": "फ़ाइल का आकार $num$ MB से बड़ा नहीं हो सकता, केवल $type$", "placeholders": {"num": {"content": "$1"}, "type": {"content": "$2"}}}, "search_by_image_progress_analysing": {"message": "छवि का विश्लेषण"}, "search_by_image_progress_searching": {"message": "उत्पादों के लिए खोजें"}, "search_by_image_progress_sending": {"message": "छवि भेजना"}, "search_by_image_response_rate": {"message": "प्रतिक्रिया दर: $responseRate$ खरीदारों का, जिन्होंने इस आपूर्तिकर्ता से संपर्क किया, उन्हें $responseInHour$ घंटे के भीतर प्रतिक्रिया मिली।", "placeholders": {"responseRate": {"content": "$1"}, "responseInHour": {"content": "$2"}}}, "search_by_keyword": {"message": "कीवर्ड द्वारा खोज:"}, "select_country_language_modal_title_country": {"message": "देश"}, "select_country_language_modal_title_language": {"message": "भाषा: हिन्दी"}, "select_country_region_modal_title": {"message": "किसी देश / क्षेत्र का चयन करें"}, "select_language_modal_title": {"message": "भाषा चुनें:"}, "select_shop": {"message": "स्टोर का चयन करें"}, "sellers_count": {"message": "वर्तमान पृष्ठ पर विक्रेताओं की संख्या"}, "sellers_count_per_page": {"message": "वर्तमान पृष्ठ पर विक्रेताओं की संख्या"}, "service_score": {"message": "व्यापक सेवा रेटिंग"}, "set_shortcut_keys": {"message": "शॉर्टकट कुंजियाँ सेट करें"}, "setting_logo_title": {"message": "खरीदारी सहायक"}, "setting_modal_options_position_title": {"message": "प्लग-इन स्थिति"}, "setting_modal_options_position_value_left": {"message": "बायाँ कोना"}, "setting_modal_options_position_value_right": {"message": "दांया कोना"}, "setting_modal_options_theme_title": {"message": "थीम का रंग"}, "setting_modal_options_theme_value_dark": {"message": "अंधेरा"}, "setting_modal_options_theme_value_light": {"message": "रोशनी"}, "setting_modal_title": {"message": "समायोजन"}, "setting_options_country_title": {"message": "देश / क्षेत्र"}, "setting_options_hover_zoom_desc": {"message": "ज़ूम इन करने के लिए माउस ले जाएं"}, "setting_options_hover_zoom_title": {"message": "होवर झूम"}, "setting_options_jd_coupon_desc": {"message": "JD.com पर कूपन मिला"}, "setting_options_jd_coupon_title": {"message": "JD.com कूपन"}, "setting_options_language_title": {"message": "भाषा: हिन्दी"}, "setting_options_price_drop_alert_desc": {"message": "जब मेरा पसंदीदा ड्रॉप में उत्पादों की कीमत, आप धक्का अधिसूचना प्राप्त होगा।"}, "setting_options_price_drop_alert_title": {"message": "मूल्य ड्रॉप चेतावनी"}, "setting_options_price_history_on_list_page_desc": {"message": "उत्पाद खोज पृष्ठ पर मूल्य इतिहास प्रदर्शित करें"}, "setting_options_price_history_on_list_page_title": {"message": "मूल्य इतिहास (सूची पृष्ठ)"}, "setting_options_price_history_on_produt_page_desc": {"message": "उत्पाद विवरण पृष्ठ पर उत्पाद इतिहास प्रदर्शित करें"}, "setting_options_price_history_on_produt_page_title": {"message": "मूल्य इतिहास (विवरण पृष्ठ)"}, "setting_options_sales_analysis_desc": {"message": "$platforms$ उत्पाद सूची पृष्ठ पर मूल्य, बिक्री की मात्रा, विक्रेताओं की संख्या और स्टोर बिक्री अनुपात के समर्थन आंकड़े", "placeholders": {"platforms": {"content": "$1"}}}, "setting_options_sales_analysis_title": {"message": "बिक्री विश्लेषण"}, "setting_options_save_success_msg": {"message": "सफलता"}, "setting_options_tacking_price_title": {"message": "मूल्य परिवर्तन चेतावनी"}, "setting_options_value_off": {"message": "बंद"}, "setting_options_value_on": {"message": "पर"}, "setting_pkg_quick_view_desc": {"message": "समर्थन: 1688 और ताओबाओ"}, "setting_saved_message": {"message": "परिवर्तनों को सफलतापूर्वक सहेजा"}, "setting_section_enable_platform_title": {"message": "चालू बंद"}, "setting_section_setting_title": {"message": "समायोजन"}, "setting_section_shortcuts_title": {"message": "शॉर्टकट"}, "settings_aliprice_agent__desc": {"message": "$platforms$ उत्पाद विवरण पृष्ठ पर प्रदर्शित", "placeholders": {"platforms": {"content": "$1"}}}, "settings_aliprice_agent__title": {"message": "मेरे लिए खरीदना"}, "settings_copy_link__desc": {"message": "उत्पाद विवरण पृष्ठ पर प्रदर्शित करें"}, "settings_copy_link__title": {"message": "बटन और खोज शीर्षक कॉपी करें"}, "settings_currency_desc__for_detail": {"message": "समर्थन 1688 उत्पाद विवरण पृष्ठ"}, "settings_currency_desc__for_list": {"message": "छवि के आधार पर खोजें (1688/1688 विदेशी/<PERSON><PERSON> शामिल करें)"}, "settings_currency_desc__for_sbi": {"message": "कीमत चुनें"}, "settings_currency_desc_display_for_list": {"message": "छवि खोज में दिखाया गया (1688/1688 विदेशी/ताओबाओ सहित)"}, "settings_currency_rate_desc": {"message": "\"$currencyRateFrom$\" से विनिमय दर अपडेट हो रही है", "placeholders": {"currencyRateFrom": {"content": "$1"}}}, "settings_currency_rate_from_boc": {"message": "चीन का बैंक"}, "settings_download_images__desc": {"message": "$platforms$ से चित्र डाउनलोड करने के लिए समर्थन", "placeholders": {"platforms": {"content": "$1"}}}, "settings_download_images__title": {"message": "छवि बटन डाउनलोड करें"}, "settings_download_reviews__desc": {"message": "$platforms$ उत्पाद विवरण पृष्ठ पर प्रदर्शित", "placeholders": {"platforms": {"content": "$1"}}}, "settings_download_reviews__title": {"message": "समीक्षा चित्र डाउनलोड करें"}, "settings_google_translate_desc": {"message": "Google अनुवाद बार प्राप्त करने के लिए राइट क्लिक करें"}, "settings_google_translate_title": {"message": "वेब पेज अनुवाद"}, "settings_historical_trend_desc": {"message": "उत्पाद सूची पृष्ठ पर छवि के निचले दाएँ कोने में प्रदर्शित करें"}, "settings_modal_btn_more": {"message": "अधिक सेटिंग्स"}, "settings_productInfo_desc": {"message": "उत्पाद सूची पृष्ठ पर अधिक विस्तृत उत्पाद जानकारी प्रदर्शित करें। इसे सक्षम करने से कंप्यूटर का लोड बढ़ सकता है और पेज लैग हो सकता है। यदि यह प्रदर्शन को प्रभावित करता है, तो इसे अक्षम करने की अनुशंसा की जाती है।"}, "settings_product_recommend__desc": {"message": "$platforms$ उत्पाद विवरण पृष्ठ पर मुख्य छवि के नीचे प्रदर्शित", "placeholders": {"platforms": {"content": "$1"}}}, "settings_product_recommend__name": {"message": "अनुशंसित उत्पाद"}, "settings_research_desc": {"message": "उत्पाद सूची पृष्ठ पर अधिक विस्तृत जानकारी क्वेरी करें"}, "settings_sbi_add_to_list": {"message": "$listType$ . में जोड़ें", "placeholders": {"listType": {"content": "$1"}}}, "settings_sbi_detail_page_icon_title_desc": {"message": "छवि खोज परिणाम थंबनेल"}, "settings_sbi_remove_from_list": {"message": "$listType$ . से निकालें", "placeholders": {"listType": {"content": "$1"}}}, "settings_search_by_image_add_to_blacklist": {"message": "ब्लॉकलिस्ट में जोड़ें"}, "settings_search_by_image_adjust_entrance_entrance": {"message": "प्रवेश की स्थिति समायोजित करें"}, "settings_search_by_image_blacklist_desc": {"message": "ब्लैकलिस्ट में वेबसाइटों पर आइकन न दिखाएं।"}, "settings_search_by_image_blacklist_title": {"message": "ब्लॉक सूची"}, "settings_search_by_image_bottom_left": {"message": "नीचे बाएँ"}, "settings_search_by_image_bottom_right": {"message": "नीचे दाएँ"}, "settings_search_by_image_clear_blacklist": {"message": "स्पष्ट ब्लॉकलिस्ट"}, "settings_search_by_image_detail_page_icon_title": {"message": "थंबनेल"}, "settings_search_by_image_detail_page_icon_val_large": {"message": "बड़ा"}, "settings_search_by_image_detail_page_icon_val_small": {"message": "छोटे"}, "settings_search_by_image_display_button_desc": {"message": "छवि के आधार पर खोजने के लिए आइकन पर एक क्लिक करें"}, "settings_search_by_image_display_button_title": {"message": "छवियों पर चिह्न"}, "settings_search_by_image_sourece_websites_desc": {"message": "इन वेबसाइटों पर स्रोत उत्पाद खोजें"}, "settings_search_by_image_sourece_websites_title": {"message": "छवि परिणाम द्वारा खोजें"}, "settings_search_by_image_top_left": {"message": "ऊपर बाएँ"}, "settings_search_by_image_top_right": {"message": "ऊपर दाएँ"}, "settings_search_keyword_on_x__desc": {"message": "$platform$ . पर शब्द खोजें", "placeholders": {"platform": {"content": "$1"}}}, "settings_search_keyword_on_x__title": {"message": "शब्दों को चुने जाने पर $platform$ आइकन दिखाएं", "placeholders": {"platform": {"content": "$1"}}}, "settings_similar_products_desc": {"message": "(5 के लिए अधिकतम) उन वेबसाइटों में एक ही उत्पाद खोजने की कोशिश करें"}, "settings_similar_products_title": {"message": "एक ही उत्पाद का पता लगाएं"}, "settings_toolbar_expand_title": {"message": "प्लग-इन छोटा करें"}, "settings_top_toolbar_desc": {"message": "पृष्ठ के शीर्ष पर खोज बार"}, "settings_top_toolbar_title": {"message": "खोज पट्टी"}, "settings_translate_search_desc": {"message": "चीनी में अनुवाद करें और खोजें"}, "settings_translate_search_title": {"message": "बहुभाषी खोज"}, "settings_translator_contextmenu_title": {"message": "अनुवाद करने के लिए कैप्चर करें"}, "settings_translator_title": {"message": "अनुवाद करना"}, "shai_xuan_dao_chu": {"message": "निर्यात करने के लिए फ़िल्टर करें"}, "shai_xuan_zi_duan": {"message": "फ़िल्टर फ़ील्ड"}, "shang_jia_shi_jian": {"message": "शेल्फ समय पर"}, "shang_pin_biao_ti": {"message": "उत्पाद शीर्षक"}, "shang_pin_dui_bi": {"message": "उत्पाद तुलना"}, "shang_pin_lian_jie": {"message": "उत्पाद लिंक"}, "shang_pin_xin_xi": {"message": "उत्पाद की जानकारी"}, "share_modal__content": {"message": "अपने मित्रों के साथ साझा करें"}, "share_modal__disable_for_while": {"message": "मैं कुछ भी साझा नहीं करना चाहता"}, "share_modal__title": {"message": "क्या आपको $extensionName$ पसंद है?", "placeholders": {"extensionName": {"content": "$1"}}}, "sheng_yu_ku_cun": {"message": "शेष"}, "shi_fou_ke_ding_zhi": {"message": "क्या यह अनुकूलन योग्य है?"}, "shi_fou_ren_zheng_gong_ying_sha": {"message": "प्रमाणित आपूर्तिकर्ता"}, "shi_fou_ren_zheng_gong_ying_shang_zong_shu": {"message": "प्रमाणित आपूर्तिकर्ता"}, "shi_fou_you_mao_yi_dan_bao": {"message": "व्यापार आश्वासन"}, "shi_fou_you_mao_yi_dan_bao_zong_shu": {"message": "व्यापार गारंटी"}, "shipping_fee": {"message": "शिपिंग शुल्क"}, "shop_followers": {"message": "शॉप फॉलोअर्स"}, "shou_qi": {"message": "कम"}, "similar_products_warn_max_platforms": {"message": "अधिकतम 5"}, "sku_calc_price": {"message": "गणना की गई कीमत"}, "sku_calc_price_settings": {"message": "गणना की गई मूल्य सेटिंग"}, "sku_formula": {"message": "सूत्र"}, "sku_formula_desc": {"message": "सूत्र विवरण"}, "sku_formula_desc_text": {"message": "जटिल गणितीय सूत्रों का समर्थन करता है, जिसमें मूल मूल्य को A द्वारा दर्शाया जाता है और भाड़ा को B द्वारा दर्शाया जाता है\n\n<br/>\n\nकोष्ठक (), प्लस +, माइनस -, गुणन *, और भाग / का समर्थन करता है\n\n<br/>\n\nउदाहरण:\n\n<br/>\n\n1. मूल मूल्य का 1.2 गुना प्राप्त करने और फिर भाड़ा जोड़ने के लिए, सूत्र है: A*1.2+B\n\n<br/>\n\n2. मूल मूल्य को 1 युआन से अधिक प्राप्त करने के लिए, फिर 1.2 गुना से गुणा करें, सूत्र है: (A+1)*1.2\n\n<br/>\n\n3. मूल मूल्य को 10 युआन से अधिक प्राप्त करने के लिए, फिर 1.2 गुना से गुणा करें, और फिर 3 युआन घटाएँ, सूत्र है: (A+10)*1.2-3"}, "sku_in_stock": {"message": "स्टॉक में"}, "sku_invalid_formula_format": {"message": "अमान्य सूत्र प्रारूप"}, "sku_inventory": {"message": "इन्वेंट्री"}, "sku_link_copy_fail": {"message": "सफलतापूर्वक कॉपी किया गया, sku विनिर्देशों और विशेषताओं का चयन नहीं किया गया है"}, "sku_link_copy_success": {"message": "सफलतापूर्वक कॉपी किया गया, sku विनिर्देशों और विशेषताओं का चयन किया गया"}, "sku_list": {"message": "SKU सूची"}, "sku_min_qrder_qty": {"message": "न्यूनतम ऑर्डर मात्रा"}, "sku_name": {"message": "SKU नाम"}, "sku_no": {"message": "सं."}, "sku_original_price": {"message": "मूल मूल्य"}, "sku_price": {"message": "SKU मूल्य"}, "stop_track_time_label": {"message": "ट्रैकिंग समय सीमा:"}, "suo_zai_di_qu": {"message": "जगह"}, "tab_pkg_quick_view": {"message": "रसद मॉनिटर"}, "tab_product_details_price_history": {"message": "मूल्य इतिहास"}, "tab_product_details_reviews": {"message": "फोटो समीक्षा"}, "tab_product_details_seller_analysis": {"message": "विक्रेता विश्लेषण"}, "tab_product_details_similar_products": {"message": "एक ही उत्पाद"}, "total_days_listed_per_product": {"message": "शेल्फ दिनों का योग ÷ उत्पादों की संख्या"}, "total_items": {"message": "उत्पादों की कुल संख्या"}, "total_price_per_product": {"message": "कीमतों का योग ÷ उत्पादों की संख्या"}, "total_rating_per_product": {"message": "रेटिंग का योग ÷ उत्पादों की संख्या"}, "total_revenue": {"message": "कुल मुनाफा"}, "total_revenue40_items": {"message": "वर्तमान पृष्ठ पर $amount$ उत्पादों का कुल राजस्व", "placeholders": {"amount": {"content": "$1"}}}, "total_sales": {"message": "कुल बिक्री"}, "total_sales40_items": {"message": "वर्तमान पृष्ठ पर $amount$ उत्पादों की कुल बिक्री", "placeholders": {"amount": {"content": "$1"}}}, "track_for_12_months": {"message": "के लिए ट्रैक: 1 वर्ष"}, "track_for_3_months": {"message": "के लिए ट्रैक: 3 महीने"}, "track_for_6_months": {"message": "के लिए ट्रैक: 6 महीने"}, "tracking_price_email_add_btn": {"message": "ईमेल जोड़ें"}, "tracking_price_email_edit_btn": {"message": "ईमेल संपादित करें"}, "tracking_price_email_intro": {"message": "हम आपको ईमेल के माध्यम से सूचित करेंगे।"}, "tracking_price_email_invalid": {"message": "कृपया सही ईमेल पता बताएं"}, "tracking_price_email_verified_desc": {"message": "अब आप हमारे मूल्य ड्रॉप अलर्ट प्राप्त कर सकते हैं।"}, "tracking_price_email_verified_title": {"message": "सफलतापूर्वक सत्यापित किया गया"}, "tracking_price_email_verify_desc_line1": {"message": "हमने आपके ईमेल पते पर एक सत्यापन लिंक भेजा है,"}, "tracking_price_email_verify_desc_line2": {"message": "कृपया अपना ईमेल इनबॉक्स देखें।"}, "tracking_price_email_verify_title": {"message": "ईमेल सत्यापित करें"}, "tracking_price_web_push_notification_intro": {"message": "डेस्कटॉप पर: अलीप्राइस आपके लिए किसी भी उत्पाद की निगरानी कर सकता है और कीमत में बदलाव के बाद आपको वेब पुश अधिसूचना भेज सकता है।"}, "tracking_price_web_push_notification_title": {"message": "वेब पुश सूचनाएं"}, "translate_im__login_required": {"message": "<PERSON><PERSON><PERSON> द्वारा अनुवादित, कृपया $loginUrl$ में लॉग इन करें", "placeholders": {"loginUrl": {"content": "$1"}}}, "translate_im__paste_fail": {"message": "अनुवादित और क्लिपबोर्ड पर कॉपी किया गया, लेकिन अलीवांगवांग की सीमा के कारण, आपको इसे मैन्युअल रूप से पेस्ट करना होगा!"}, "translate_im__send": {"message": "अनुवाद करें और भेजें"}, "translate_search": {"message": "अनुवाद और खोज करें"}, "translation_originals_translated": {"message": "मूल और चीनी"}, "translation_translated": {"message": "चीनी"}, "translator_btn_capture_txt": {"message": "अनुवाद करना"}, "translator_language_auto_detect": {"message": "स्वचालित पहचान"}, "translator_language_detected": {"message": "का पता चला"}, "translator_language_search_placeholder": {"message": "भाषा खोजें"}, "try_again": {"message": "पुनः प्रयास करें"}, "tu_pian_chi_cun": {"message": "छवि का आकार:"}, "tu_pian_lian_jie": {"message": "छवि लिंक"}, "tui_huan_ti_yan": {"message": "अनुभव लौटें"}, "tui_huan_ti_yan__desc": {"message": "विक्रेताओं के बिक्री-पश्चात संकेतकों का आकलन करें"}, "tutorial__show_all": {"message": "सभी सुविधाएं"}, "tutorial_ae_popup_title": {"message": "एक्सटेंशन को पिन करें, Aliexpress खोलें"}, "tutorial_aliexpress_reviews_analysis": {"message": "अलीएक्सप्रेस समीक्षा विश्लेषण"}, "tutorial_aliprice_agent_with1688_desc_l1": {"message": "समर्थन USD"}, "tutorial_aliprice_agent_with1688_desc_l2": {"message": "कोरिया/जापान/मुख्यभूमि चीन के लिए शिपिंग"}, "tutorial_aliprice_agent_with1688_title": {"message": "1688 विदेशी खरीद का समर्थन करता है"}, "tutorial_auto_apply_coupon_title": {"message": "स्वत: लागू कूपन"}, "tutorial_btn_end": {"message": "समाप्त"}, "tutorial_btn_example": {"message": "उदाहरण"}, "tutorial_btn_have_a_try": {"message": "ठीक है, एक कोशिश है"}, "tutorial_btn_next": {"message": "आगामी"}, "tutorial_btn_see_more": {"message": "अधिक"}, "tutorial_compare_products": {"message": "उत्पाद की तुलना करें"}, "tutorial_currency_convert_title": {"message": "मुद्रा रूपांतरण"}, "tutorial_export_shopping_cart": {"message": "CSV के रूप में निर्यात करें, Taobao और 1688 का समर्थन करें"}, "tutorial_export_shopping_cart_title": {"message": "निर्यात गाड़ी"}, "tutorial_price_history_pro": {"message": "उत्पाद विवरण पृष्ठ पर प्रदर्शित।\n<PERSON><PERSON>, <PERSON><PERSON><PERSON>, Amazon, Ebay को सपोर्ट करें"}, "tutorial_price_history_pro_title": {"message": "पूरे वर्ष मूल्य इतिहास और आदेश इतिहास"}, "tutorial_sbi_context_menu_screenshot_title": {"message": "छवि द्वारा खोज करने के लिए कैप्चर करें"}, "tutorial_translate_search": {"message": "खोज में अनुवाद करें"}, "tutorial_translate_search_and_package_tracking": {"message": "अनुवाद खोज और पैकेज ट्रैकिंग"}, "unit_bao": {"message": "पीसी"}, "unit_ben": {"message": "पीसी"}, "unit_bi": {"message": "आदेश"}, "unit_chuang": {"message": "पीसी"}, "unit_dai": {"message": "पीसी"}, "unit_dui": {"message": "जोड़ा"}, "unit_fen": {"message": "पीसी"}, "unit_ge": {"message": "पीसी"}, "unit_he": {"message": "पीसी"}, "unit_jian": {"message": "पीसी"}, "unit_li_fang_mi": {"message": "m³"}, "unit_ping": {"message": "पीसी"}, "unit_ping_fang_mi": {"message": "㎡"}, "unit_shuang": {"message": "जोड़ा"}, "unit_tai": {"message": "पीसी"}, "unit_ti": {"message": "पीसी"}, "unit_tiao": {"message": "पीसी"}, "unit_xiang": {"message": "पीसी"}, "unit_zhang": {"message": "पीसी"}, "unit_zhi": {"message": "पीसी"}, "verify_contact_support": {"message": "सहायता से संपर्क करें"}, "verify_human_verification": {"message": "मानव सत्यापन"}, "verify_unusual_access": {"message": "असामान्य पहुँच का पता चला"}, "view_history_clean_all": {"message": "सभी साफ करें"}, "view_history_clean_all_warring": {"message": "सभी देखे गए रिकॉर्ड को साफ करें?"}, "view_history_clean_all_warring_title": {"message": "चेतावनी"}, "view_history_viewd": {"message": "देखा गया"}, "website": {"message": "वेबसाइट"}, "weight": {"message": "वज़न"}, "wu_fa_huo_qu_dao_gai_shu_ju": {"message": "डेटा प्राप्त करने में असमर्थ"}, "wu_liu_shi_xiao": {"message": "समय पर शिपमेंट"}, "wu_liu_shi_xiao__desc": {"message": "विक्रेता के स्टोर की 48 घंटे की संग्रह दर और पूर्ति दर"}, "xia_dan_jia": {"message": "अंतिम मूल्य"}, "xian_xuan_ze_product_attributes": {"message": "उत्पाद विशेषताएँ चुनें"}, "xiao_liang": {"message": "बिक्री की मात्रा"}, "xiao_liang_zhan_bi": {"message": "बिक्री मात्रा का प्रतिशत"}, "xiao_shi": {"message": "$num$ घंटे", "placeholders": {"num": {"content": "$1"}}}, "xiao_shou_e": {"message": "राज<PERSON><PERSON>व"}, "xiao_shou_e_zhan_bi": {"message": "राजस्व का प्रतिशत"}, "xuan_zhong_x_tiao_ji_lu": {"message": "$amount$ रिकॉर्ड चुनें", "placeholders": {"amoun": {"content": "$1"}, "amount": {"content": "$1"}}}, "yan_xuan": {"message": "विकल्प"}, "yi_ding_zai_zuo_ce": {"message": "पिन की गई"}, "yi_jia_zai_wan_suo_you_shang_pin": {"message": "सभी उत्पाद लोड किए गए"}, "yi_nian_xiao_liang": {"message": "वार्षिक बिक्री"}, "yi_nian_xiao_liang_zhan_bi": {"message": "वार्षिक बिक्री हिस्सा"}, "yi_nian_xiao_shou_e": {"message": "वार्षिक कारोबार"}, "yi_nian_xiao_shou_e_zhan_bi": {"message": "वार्षिक कारोबार हिस्सा"}, "yi_shua_xin": {"message": "ताज़ा किया गया"}, "yin_cang_xiang_tong_dian": {"message": "समानताएं छुपाएं"}, "you_xiao_liang": {"message": "बिक्री की मात्रा के साथ"}, "yu_ji_dao_da_shi_jian": {"message": "अनुमानित आगमन समय"}, "yuan_gong_ren_shu": {"message": "कर्मचारियों की संख्या"}, "yue_cheng_jiao": {"message": "मासिक मात्रा"}, "yue_dai_xiao": {"message": "जहाज को डुबोना"}, "yue_dai_xiao__desc": {"message": "पिछले 30 दिनों में ड्रॉपशीपिंग बिक्री"}, "yue_dai_xiao_pai_xu__desc": {"message": "पिछले 30 दिनों में ड्रॉपशीपिंग बिक्री, उच्च से निम्न तक क्रमबद्ध"}, "yue_xiao_liang__desc": {"message": "पिछले 30 दिनों में बिक्री की मात्रा"}, "zhan_kai": {"message": "अधिक"}, "zhe_kou": {"message": "छूट"}, "zhi_chi_yi_jian_dai_fa": {"message": "जहाज को डुबोना"}, "zhi_chi_yi_jian_dai_fa_bao_you": {"message": "मुफ़्त शिपिंग"}, "zhi_fu_ding_dan_shu": {"message": "भुगतान आदेश"}, "zhi_fu_ding_dan_shu__desc": {"message": "इस उत्पाद के ऑर्डर की संख्या (30 दिन)"}, "zhu_ce_xing_zhi": {"message": "पंजीकरण प्रकृति"}, "zi_ding_yi_tiao_jian": {"message": "कस्टम शर्तें"}, "zi_duan": {"message": "फ़ील्ड"}, "zi_ti_xiao_liang": {"message": "विविधता बेची गई"}, "zong_he_fu_wu_fen": {"message": "समग्र रेटिंग"}, "zong_he_fu_wu_fen__desc": {"message": "विक्रेता सेवा की समग्र रेटिंग"}, "zong_he_fu_wu_fen__short": {"message": "रेटिंग"}, "zong_he_ti_yan_fen": {"message": "रेटिंग"}, "zong_he_ti_yan_fen_3": {"message": "4 स्टार से नीचे"}, "zong_he_ti_yan_fen_4": {"message": "4 - 4.5 सितारे"}, "zong_he_ti_yan_fen_4_5": {"message": "4.5 - 5.0 सितारे"}, "zong_he_ti_yan_fen_5": {"message": "5 सितारे"}, "zong_ku_cun": {"message": "कुल सूची"}, "zong_xiao_liang": {"message": "कुल बिक्री"}, "zui_jin_30D_3Min_xiang_ying_lv": {"message": "पिछले 30 दिनों में 3 मिनट की प्रतिक्रिया दर"}, "zui_jin_30D_48H_lan_shou_lv": {"message": "पिछले 30 दिनों में रिकवरी रेट 48H"}, "zui_jin_30D_48H_lv_yue_lv": {"message": "पिछले 30 दिनों में 48H प्रदर्शन दर"}, "zui_jin_30D_jiao_yi_ji_lu": {"message": "ट्रेडिंग रिकॉर्ड(30 दिन)"}, "zui_jin_30D_jiao_yi_ji_lu__desc": {"message": "ट्रेडिंग रिकॉर्ड(30 दिन)"}, "zui_jin_30D_jiu_fen_lv": {"message": "पिछले 30 दिनों में विवाद दर"}, "zui_jin_30D_pin_zhi_tui_kuan_lv": {"message": "पिछले 30 दिनों में गुणवत्ता वापसी दर"}, "zui_jin_30D_zhi_fu_ding_dan_shu": {"message": "पिछले 30 दिनों में भुगतान आदेशों की संख्या"}}