{"EXTENSION_NAME": {"message": "TODO: EXTENSION_NAME"}, "EXTENSION_DESCRIPTION": {"message": "TODO: EXTENSION_DESCRIPTION"}, "1688_cross_border_hot_selling": {"message": "1688 越境ホットセールスポット"}, "1688_shi_li_ren_zheng": {"message": "1688強度認証"}, "1_jian_qi_pi": {"message": "1点販売あり"}, "1year_yi_shang": {"message": "1年以上"}, "24H": {"message": "24H"}, "24H_fa_huo": {"message": "24時間発送"}, "24H_lan_shou_lv": {"message": "24時間出荷率"}, "30D_shang_xin": {"message": "月間新着"}, "30d_sales": {"message": "月間注文件数：$amount$件", "placeholders": {"amount": {"content": "$1"}}}, "3Min_xiang_ying_lv": {"message": "3分以内返信率"}, "3Min_xiang_ying_lv__desc": {"message": "過去30日間アリワンワンで購入者への返信の割合"}, "48H": {"message": "48時間"}, "48H_fa_huo": {"message": "48時間発送"}, "48H_lan_shou_lv": {"message": "48時間出荷率"}, "48H_lan_shou_lv__desc": {"message": "過去 30 日間に購入者が支払い後48時間以内に出荷記録がある注文の割合 (物流会社がWebサイトに同期した出荷情報には、「xx時間xx店舗で集荷した記録」が表示されます。)"}, "48H_lv_yue_lv": {"message": "48h遵守率"}, "48H_lv_yue_lv__desc": {"message": "過去30日間の注文数は48 時間以内に出荷記録がある注文の割合"}, "72H": {"message": "72時間"}, "7D_shang_xin": {"message": "週間新着"}, "7D_wu_li_you": {"message": "7日間のケアフリー"}, "ABS_title_text": {"message": "その商品にはブランドストーリーがあります。"}, "AC_title_text": {"message": "その商品にはAmazon's Choiceマークがあります。"}, "A_title_text": {"message": "その商品にはA+ページがあります。"}, "BS_title_text": {"message": "その商品は$type$カテゴリーの$num$爆売れ商品です。", "placeholders": {"type": {"content": "$1"}, "num": {"content": "$2"}}}, "LTD_title_text": {"message": "Limited time dealの略で、その商品が「7日間プロモーション」に参加していることを示します。"}, "NR_title_text": {"message": "その商品は$type$カテゴリーの$num$爆売れ新品です。", "placeholders": {"type": {"content": "$1"}, "num": {"content": "$2"}}}, "SBV_title_text": {"message": "その商品がビデオ広告を掲載しています。PPC広告の一種で、一般的に検索結果ページの中間に表示されます。"}, "SB_title_text": {"message": "その商品がブランド広告を掲載しています。PPC広告の一種で、一般的に検索結果ページの上部または下部に表示されます。"}, "SP_title_text": {"message": "その商品がSponsored Product広告を掲載しています。"}, "V_title_text": {"message": "その商品にはビデオ紹介があります。"}, "advanced_research": {"message": "詳細検索"}, "agent_ds1688___my_order": {"message": "マイオーダー"}, "agent_ds1688__add_to_cart": {"message": "海外直購"}, "agent_ds1688__cart": {"message": "買い物かご"}, "agent_ds1688__desc": {"message": "1688は海外からの直接購入に対応しており、日本円での支払い、日本/中国の配送センターに直接配送できます。"}, "agent_ds1688__freight": {"message": "送料計算ツール"}, "agent_ds1688__help": {"message": "ヘルプ"}, "agent_ds1688__packages": {"message": "運送状"}, "agent_ds1688__profile": {"message": "パーソナルセンター"}, "agent_ds1688__warehouse": {"message": "私の倉庫"}, "ai_comment_analysis_advantage": {"message": "メリット"}, "ai_comment_analysis_ai": {"message": "AIレビュー分析"}, "ai_comment_analysis_available": {"message": "利用可能"}, "ai_comment_analysis_balance": {"message": "コインが不足しています。チャージしてください。"}, "ai_comment_analysis_behavior": {"message": "行動"}, "ai_comment_analysis_characteristic": {"message": "ユーザー属性"}, "ai_comment_analysis_comment": {"message": "この商品のレビュー数が不足しているため、正確な結論を出すことができません。レビュー数が多い商品を選んで分析してください"}, "ai_comment_analysis_consume": {"message": "予測消費"}, "ai_comment_analysis_default": {"message": "デフォルトレビュー"}, "ai_comment_analysis_desire": {"message": "顧客の期待"}, "ai_comment_analysis_disadvantage": {"message": "デメリット"}, "ai_comment_analysis_free": {"message": "無料使用回数"}, "ai_comment_analysis_freeNum": {"message": "1回の無料利用が消費されます。"}, "ai_comment_analysis_go_recharge": {"message": "チャージする"}, "ai_comment_analysis_intelligence": {"message": "スマートレビュー分析"}, "ai_comment_analysis_location": {"message": "利用場所"}, "ai_comment_analysis_motive": {"message": "購買動機"}, "ai_comment_analysis_network_error": {"message": "ネットワークエラーが発生しました。再試行してください。"}, "ai_comment_analysis_normal": {"message": "画像付きレビュー"}, "ai_comment_analysis_number_reviews": {"message": "レビュー数: $num$、推計消費量: $consume$", "placeholders": {"num": {"content": "$1"}, "consume": {"content": "$2"}}}, "ai_comment_analysis_ordinary": {"message": "通常コメント"}, "ai_comment_analysis_percentage": {"message": "パーセンテージ"}, "ai_comment_analysis_problem": {"message": "支払いに問題が発生しました"}, "ai_comment_analysis_reanalysis": {"message": "再分析"}, "ai_comment_analysis_reason": {"message": "理由"}, "ai_comment_analysis_recharge": {"message": "チャージ"}, "ai_comment_analysis_recharged": {"message": "チャージしました"}, "ai_comment_analysis_retry": {"message": "再試行"}, "ai_comment_analysis_scene": {"message": "使用シーン"}, "ai_comment_analysis_start": {"message": "分析開始"}, "ai_comment_analysis_subject": {"message": "トピック"}, "ai_comment_analysis_time": {"message": "利用時間"}, "ai_comment_analysis_tool": {"message": "AIツール"}, "ai_comment_analysis_user_portrait": {"message": "ユーザーペルソナ"}, "ai_comment_analysis_welcome": {"message": "AIレビュー分析をご利用いただきありがとうございます"}, "ai_comment_analysis_year": {"message": "過去1年のコメント"}, "ai_listing_Exclude_keywords": {"message": "除外キーワード"}, "ai_listing_aI_generation": {"message": "ジェネレーションAI"}, "ai_listing_add_automatic": {"message": "自動"}, "ai_listing_add_enter_keywords": {"message": "キーワードを入力"}, "ai_listing_add_keywords": {"message": "キーワードを追加"}, "ai_listing_add_manually": {"message": "手動で追加する"}, "ai_listing_added_keywords": {"message": "追加したキーワード"}, "ai_listing_added_successfully": {"message": "正しく追加されました"}, "ai_listing_addexcluded_keywords": {"message": "除外キーワードを入力し、エンターキーを押して追加を完了します。"}, "ai_listing_addkeyword_enter": {"message": "キーワードを入力し、エンターキーを押して追加を完了します。"}, "ai_listing_ai_description": {"message": "AI説明語キーワード集"}, "ai_listing_ai_dictionary": {"message": "AIタイトルキーワード集"}, "ai_listing_ai_title": {"message": "AIタイトル"}, "ai_listing_aidescription_repeated": {"message": "AI説明語キーワード集の名称は繰り返せない"}, "ai_listing_aititle_repeated": {"message": "AIタイトルキーワード集の名称は繰り返せない"}, "ai_listing_data_comes_from": {"message": "このデータは以下から得たものである："}, "ai_listing_deleted_successfully": {"message": "正しく削除されました"}, "ai_listing_edit_word_library": {"message": "キーワード集を編集する"}, "ai_listing_enter_keywords": {"message": "キーワードを入力し、$key$を押して追加を完了する", "placeholders": {"key": {"content": "$1"}}}, "ai_listing_exceeded_maximum": {"message": "上限を超えまして、キーワードは $amount$ 個まで", "placeholders": {"amount": {"content": "$1"}}}, "ai_listing_excluded_word_library": {"message": "除外キーワード集"}, "ai_listing_generate_characters": {"message": "作成文字数"}, "ai_listing_generation_platform": {"message": "プラットフォーム"}, "ai_listing_help_optimize": {"message": "商品タイトルの最適化にご協力ください、 原題は【】です。\n\n"}, "ai_listing_included_keyword": {"message": "含まれたキーワード"}, "ai_listing_included_keywords": {"message": "含まれたキーワード"}, "ai_listing_intelligently_title": {"message": "上記の必要事項を記入すると、タイトルがインテリジェントに生成されます。"}, "ai_listing_keyword_product_title": {"message": "キーワード商品タイトル"}, "ai_listing_keywords_repeated": {"message": "キーワードは繰り返せない"}, "ai_listing_long_title_1": {"message": "ブランド名、製品タイプ、製品の特徴などの基本情報を含めています。"}, "ai_listing_long_title_2": {"message": "標準の商品タイトルをベースに、SEOに有効なキーワードを追加します。"}, "ai_listing_long_title_3": {"message": "ブランド名、製品タイプ、製品の特徴、キーワードに加え、特定のセグメント化された検索クエリで上位表示を達成するために、ロングテールキーワードも含まれます。"}, "ai_listing_longtail_keyword_product_title": {"message": "\nロングテールキーワード商品タイトル"}, "ai_listing_manually_enter": {"message": "手入力．．．"}, "ai_listing_new_dictionary": {"message": "新しいキーワード集を作成する．．．"}, "ai_listing_new_generate": {"message": "生成"}, "ai_listing_optional_words": {"message": "選択肢"}, "ai_listing_original_title": {"message": "オリジナルタイトル"}, "ai_listing_other_keywords_included": {"message": "その他のキーワードは以下の通り："}, "ai_listing_please_again": {"message": "もう一度お試しください"}, "ai_listing_please_select": {"message": "以下のタイトルが生成されましたので、選択してください："}, "ai_listing_product_category": {"message": "製品カテゴリー"}, "ai_listing_product_category_is": {"message": "製品カテゴリーは"}, "ai_listing_product_category_to": {"message": "その製品はどのカテゴリーに属しますか？"}, "ai_listing_random_keywords": {"message": "$amount$個のランダム キーワードを生成", "placeholders": {"amount": {"content": "$1"}}}, "ai_listing_randomize_keywords": {"message": "キーワード集からランダム選択"}, "ai_listing_select_word_library": {"message": "キーワード集を選択する"}, "ai_listing_selling_optional": {"message": "オプションセールスポイント"}, "ai_listing_set_excluded": {"message": "除外キーワード集に設定"}, "ai_listing_set_included": {"message": "含むキーワード集に設定"}, "ai_listing_standard_product_title": {"message": "標準商品タイトル"}, "ai_listing_translated_title": {"message": "タイトルを翻訳する"}, "ai_listing_visit_chatGPT": {"message": "ChatGPTをアクセスする"}, "ai_listing_what_other_keywords": {"message": "タイトルに必要な他のキーワードは？"}, "aliprice_coupons_apply_again": {"message": "もう一度適用する"}, "aliprice_coupons_apply_coupons": {"message": "クーポンを適用する"}, "aliprice_coupons_apply_success": {"message": "クーポンが見つかりました: $amount$を保存", "placeholders": {"amount": {"content": "$1"}}}, "aliprice_coupons_applying": {"message": "最もお得なコードをテストする"}, "aliprice_coupons_applying_desc": {"message": "チェックアウト中です: $code$", "placeholders": {"code": {"content": "$1"}}}, "aliprice_coupons_back_to_checkout": {"message": "チェックアウトに進む"}, "aliprice_coupons_found_coupons": {"message": "$amount$のクーポンが見つかりました", "placeholders": {"amount": {"content": "$1"}}}, "aliprice_coupons_found_coupons_desc": {"message": "チェックアウトする準備はできましたか？ベストプライスで購入しましょう"}, "aliprice_coupons_no_coupon_aviable": {"message": "これらのコードは動作しませんでした。でも大丈夫、すでにベストプライスです"}, "aliprice_coupons_toolbar_btn": {"message": "クーポンを取得する"}, "aliww_translate": {"message": "阿里旺旺 チャット翻訳者"}, "aliww_translate_supports": {"message": "サポート: 1688 & タオバオ"}, "amazon_extended_keywords_Keywords": {"message": "キーワード"}, "amazon_extended_keywords_copy_all": {"message": "すべてコピー"}, "amazon_extended_keywords_more": {"message": "もっと見る"}, "an_lei_ji_xiao_liang_pai_xu": {"message": "販売数順に並べ替え"}, "an_lei_xing_cha_kan": {"message": "種類別"}, "an_yue_dai_xiao_pai_xu": {"message": "毎月ドロップシッピング販売によるランキング"}, "apra_btn__cat_name": {"message": "レビュー分析"}, "apra_chart__name": {"message": "国別の製品売上高の割合"}, "apra_chart__update_at": {"message": "更新時間$time$", "placeholders": {"time": {"content": "$1"}}}, "apra_name": {"message": "国の販売統計"}, "auto_opening": {"message": "$num$秒後に自動的に開きます", "placeholders": {"num": {"content": "$1"}}}, "auto_page_next_x_pages": {"message": "次の$autoPaging$ページ", "placeholders": {"autoPaging": {"content": "$1"}}}, "average_days_listed": {"message": "平均出品日数"}, "average_hui_fu_lv": {"message": "平均返信率"}, "average_ping_gong_ying_shang_deng_ji": {"message": "サプライヤーの平均レベル"}, "average_price": {"message": "平均価格"}, "average_qi_ding_liang": {"message": "平均最低発注数量"}, "average_rating": {"message": "平均評価"}, "average_ren_zheng_gong_ying_nian_chang": {"message": "認定の平均年数"}, "average_revenue": {"message": "平均収益"}, "average_revenue_per_product": {"message": "総収益 ÷ 製品数"}, "average_sales": {"message": "平均販売数"}, "average_sales_per_product": {"message": "総販売数 ÷ 製品数"}, "bao_han": {"message": "含む"}, "bao_zheng_jin": {"message": "マージン"}, "bian_ti_shu": {"message": "バリエーション数"}, "biao_ti": {"message": "タイトル"}, "blacklist_add_blacklist": {"message": "ブラックリストに追加"}, "blacklist_address_incorrect": {"message": "アドレスが間違っています。これをチェックしてください。"}, "blacklist_blacked_out": {"message": "追加済み"}, "blacklist_blacklist": {"message": "ブラックリスト"}, "blacklist_no_records_yet": {"message": "まだ記録はありません！"}, "blue_rocket": {"message": "로켓배송"}, "brand_name": {"message": "ブランド名"}, "btn_aliprice_agent__daigou": {"message": "私のために購入エージェント"}, "btn_aliprice_agent__dropshipping": {"message": "ドロップシッピング"}, "btn_have_a_try": {"message": "やってみる"}, "btn_refresh": {"message": "リフレッシュ"}, "btn_try_it_now": {"message": "やってみよう"}, "btn_txt_view_on_aliprice": {"message": "AliPriceで表示"}, "bu_bao_han": {"message": "含まない"}, "bulk_copy_links": {"message": "リンクを一括コピー"}, "bulk_copy_products": {"message": "商品を一括コピー"}, "cai_gou_zi_xun": {"message": "購入相談"}, "cai_gou_zi_xun__desc": {"message": "販売元の3分以内返信率"}, "can_ping_lei_xing": {"message": "商品カテゴリー"}, "cao_zuo": {"message": "操作"}, "chan_pin_ID": {"message": "商品ID"}, "chan_pin_e_wai_xin_xi": {"message": "製品追加情報"}, "chan_pin_lian_jie": {"message": "商品リンク"}, "cheng_li_shi_jian": {"message": "設立時間"}, "city__aba": {"message": "Aba"}, "city__aksu": {"message": "<PERSON><PERSON><PERSON>"}, "city__alaer": {"message": "<PERSON><PERSON><PERSON>"}, "city__altai": {"message": "Altai"}, "city__alxaleague": {"message": "Alxa League"}, "city__ankang": {"message": "Ankan<PERSON>"}, "city__anqing": {"message": "Anqing"}, "city__anshan": {"message": "<PERSON><PERSON>"}, "city__anshun": {"message": "<PERSON><PERSON><PERSON>"}, "city__anyang": {"message": "Anyang"}, "city__baicheng": {"message": "Baicheng"}, "city__baise": {"message": "Baise"}, "city__baisha": {"message": "Baisha"}, "city__baishan": {"message": "Baishan"}, "city__baiyin": {"message": "<PERSON><PERSON><PERSON>"}, "city__baoding": {"message": "<PERSON>od<PERSON>"}, "city__baoji": {"message": "<PERSON><PERSON><PERSON>"}, "city__baoshan": {"message": "<PERSON><PERSON><PERSON>"}, "city__baoting": {"message": "Baoting"}, "city__baotou": {"message": "<PERSON><PERSON><PERSON>"}, "city__bayannur": {"message": "Bayannur"}, "city__bayinguoleng": {"message": "Bayinguoleng"}, "city__bazhong": {"message": "Bazhong"}, "city__beihai": {"message": "Beihai"}, "city__bengbu": {"message": "<PERSON><PERSON><PERSON>"}, "city__benxi": {"message": "Benxi"}, "city__bijie": {"message": "Bijie"}, "city__binzhou": {"message": "Binzhou"}, "city__bortala": {"message": "<PERSON><PERSON><PERSON>"}, "city__bozhou": {"message": "Bozhou"}, "city__cangzhou": {"message": "Cangzhou"}, "city__chamdo": {"message": "<PERSON><PERSON><PERSON>"}, "city__changchun": {"message": "<PERSON><PERSON><PERSON>"}, "city__changde": {"message": "<PERSON><PERSON>"}, "city__changhua": {"message": "Changhua"}, "city__changji": {"message": "Changji"}, "city__changjiang": {"message": "Changjiang"}, "city__changsha": {"message": "Changsha"}, "city__changzhi": {"message": "Changzhi"}, "city__changzhou": {"message": "Changzhou"}, "city__chaohu": {"message": "<PERSON><PERSON>"}, "city__chaoyang": {"message": "Chaoyang"}, "city__chaozhou": {"message": "Chaozhou"}, "city__chengde": {"message": "Chengde"}, "city__chengdu": {"message": "Chengdu"}, "city__chengmai": {"message": "<PERSON><PERSON><PERSON>"}, "city__chenzhou": {"message": "Chenzhou"}, "city__chiayi": {"message": "<PERSON><PERSON><PERSON>"}, "city__chifeng": {"message": "<PERSON><PERSON>"}, "city__chizhou": {"message": "Chizhou"}, "city__chongzuo": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__chuxiong": {"message": "Chuxiong"}, "city__chuzhou": {"message": "Chuzhou"}, "city__dali": {"message": "<PERSON><PERSON>"}, "city__dalian": {"message": "<PERSON><PERSON>"}, "city__dandong": {"message": "Dandong"}, "city__danzhou": {"message": "Danzhou"}, "city__daqing": {"message": "Daqing"}, "city__datong": {"message": "<PERSON><PERSON><PERSON>"}, "city__daxinganling": {"message": "<PERSON><PERSON>'<PERSON>ling"}, "city__dazhou": {"message": "Dazhou"}, "city__dehong": {"message": "<PERSON><PERSON>"}, "city__deyang": {"message": "Deyang"}, "city__dezhou": {"message": "Dezhou"}, "city__dingan": {"message": "Ding'an"}, "city__dingxi": {"message": "Dingxi"}, "city__diqing": {"message": "Diqing"}, "city__dongfang": {"message": "<PERSON><PERSON>g"}, "city__dongguan": {"message": "Dongguan"}, "city__dongying": {"message": "<PERSON><PERSON>"}, "city__enshi": {"message": "<PERSON><PERSON>"}, "city__ezhou": {"message": "Ezhou"}, "city__fangchenggang": {"message": "Fangchenggang"}, "city__foshan": {"message": "<PERSON><PERSON><PERSON>"}, "city__fushun": {"message": "<PERSON><PERSON><PERSON>"}, "city__fuxin": {"message": "Fuxin"}, "city__fuyang": {"message": "Fuyang"}, "city__fuzhou": {"message": "Fuzhou"}, "city__gannan": {"message": "<PERSON><PERSON><PERSON>"}, "city__ganzhou": {"message": "Ganzhou"}, "city__ganzi": {"message": "Ganzi"}, "city__guangan": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__guangyuan": {"message": "Guangyuan"}, "city__guangzhou": {"message": "Guangzhou"}, "city__guigang": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__guilin": {"message": "<PERSON><PERSON><PERSON>"}, "city__guiyang": {"message": "Guiyang"}, "city__guolok": {"message": "Guolok"}, "city__guyuan": {"message": "<PERSON><PERSON>"}, "city__haibei": {"message": "Haibei"}, "city__haidong": {"message": "Haidong"}, "city__haikou": {"message": "Hai<PERSON><PERSON>"}, "city__hainan": {"message": "Hainan"}, "city__haixi": {"message": "Haixi"}, "city__hami": {"message": "<PERSON><PERSON>"}, "city__handan": {"message": "<PERSON><PERSON>"}, "city__hangzhou": {"message": "Hangzhou"}, "city__hanzhong": {"message": "Hanzhong"}, "city__harbin": {"message": "<PERSON><PERSON><PERSON>"}, "city__hebi": {"message": "<PERSON><PERSON>"}, "city__hechi": {"message": "<PERSON><PERSON>"}, "city__hefei": {"message": "<PERSON><PERSON><PERSON>"}, "city__hegang": {"message": "<PERSON><PERSON><PERSON>"}, "city__heihe": {"message": "<PERSON><PERSON><PERSON>"}, "city__hengshui": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__hengyang": {"message": "Hengyang"}, "city__heyuan": {"message": "<PERSON><PERSON>"}, "city__heze": {"message": "<PERSON><PERSON>"}, "city__hezhou": {"message": "Hezhou"}, "city__hohhot": {"message": "Hohhot"}, "city__honghe": {"message": "<PERSON><PERSON>"}, "city__hongkongisland": {"message": "Hong Kong Island"}, "city__hotan": {"message": "<PERSON><PERSON>"}, "city__hsinchu": {"message": "<PERSON><PERSON><PERSON>"}, "city__huaian": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__huaibei": {"message": "<PERSON><PERSON><PERSON>"}, "city__huaihua": {"message": "Huaihua"}, "city__huaisouth": {"message": "Huai South"}, "city__hualien": {"message": "<PERSON><PERSON><PERSON>"}, "city__huanggang": {"message": "<PERSON><PERSON><PERSON>"}, "city__huangnan": {"message": "Huangnan"}, "city__huangshan": {"message": "<PERSON><PERSON>"}, "city__huangshi": {"message": "<PERSON><PERSON>"}, "city__huizhou": {"message": "Huizhou"}, "city__huludao": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__hulunbuir": {"message": "Hulunbuir"}, "city__huzhou": {"message": "Huzhou"}, "city__jiamusi": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__jian": {"message": "<PERSON><PERSON><PERSON>"}, "city__jiangmen": {"message": "Jiangmen"}, "city__jiaozuo": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__jiaxing": {"message": "Jiaxing"}, "city__jiayuguan": {"message": "Jiayuguan"}, "city__jieyang": {"message": "<PERSON><PERSON><PERSON>"}, "city__jilin": {"message": "<PERSON><PERSON>"}, "city__jinan": {"message": "<PERSON><PERSON>"}, "city__jinchang": {"message": "Jinchang"}, "city__jincheng": {"message": "Jincheng"}, "city__jingdezhen": {"message": "Jingdez<PERSON>"}, "city__jingmen": {"message": "Jingmen"}, "city__jingzhou": {"message": "Jingzhou"}, "city__jinhua": {"message": "Jinhua"}, "city__jining": {"message": "Jining"}, "city__jinzhong": {"message": "Jinzhong"}, "city__jinzhou": {"message": "Jinzhou"}, "city__jiujiang": {"message": "Jiujiang"}, "city__jiuquan": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__jixi": {"message": "Ji<PERSON>"}, "city__kaifeng": {"message": "Kaifeng"}, "city__kaohsiung": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__karamay": {"message": "<PERSON><PERSON><PERSON>"}, "city__kashgar": {"message": "Ka<PERSON><PERSON>"}, "city__keelung": {"message": "Keelung"}, "city__kinmen": {"message": "Kinmen"}, "city__kizilsu": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__kowloon": {"message": "Kowloon"}, "city__kunming": {"message": "Ku<PERSON><PERSON>"}, "city__laibin": {"message": "<PERSON><PERSON>"}, "city__laiwu": {"message": "<PERSON><PERSON>"}, "city__langfang": {"message": "<PERSON><PERSON><PERSON>"}, "city__lanzhou": {"message": "Lanzhou"}, "city__ledong": {"message": "<PERSON><PERSON>"}, "city__leshan": {"message": "<PERSON><PERSON>"}, "city__lhasa": {"message": "Lhasa"}, "city__liangshan": {"message": "Liangshan"}, "city__lianyungang": {"message": "Lianyungang"}, "city__liaocheng": {"message": "<PERSON><PERSON><PERSON>"}, "city__liaoyang": {"message": "<PERSON><PERSON><PERSON>"}, "city__liaoyuan": {"message": "<PERSON><PERSON><PERSON>"}, "city__lienchiang": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__lijiang": {"message": "Lijiang"}, "city__lincang": {"message": "Lincang"}, "city__linfen": {"message": "Linfen"}, "city__lingao": {"message": "Lingao"}, "city__lingshui": {"message": "<PERSON><PERSON><PERSON>"}, "city__linxia": {"message": "Lin<PERSON>"}, "city__linyi": {"message": "<PERSON><PERSON>"}, "city__lishui": {"message": "Li<PERSON><PERSON>"}, "city__liupanshui": {"message": "Liupanshu<PERSON>"}, "city__liuzhou": {"message": "Liuzhou"}, "city__longnan": {"message": "<PERSON><PERSON>"}, "city__longyan": {"message": "<PERSON><PERSON>"}, "city__loudi": {"message": "<PERSON><PERSON>"}, "city__luan": {"message": "<PERSON><PERSON><PERSON>"}, "city__luohe": {"message": "<PERSON><PERSON><PERSON>"}, "city__luoyang": {"message": "<PERSON><PERSON><PERSON>"}, "city__luzhou": {"message": "Luzhou"}, "city__lüliang": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__maanshan": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__macauoutlyingislands": {"message": "Macau Outlying Islands"}, "city__macaupeninsula": {"message": "Macau Peninsula"}, "city__maoming": {"message": "<PERSON><PERSON>"}, "city__meishan": {"message": "<PERSON><PERSON>"}, "city__meizhou": {"message": "Meizhou"}, "city__mianyang": {"message": "Mianyang"}, "city__miaoli": {"message": "<PERSON><PERSON>"}, "city__mudanjiang": {"message": "Mudanjiang"}, "city__nagqu": {"message": "<PERSON>g<PERSON>u"}, "city__nanchang": {"message": "Nanchang"}, "city__nanchong": {"message": "<PERSON><PERSON><PERSON>"}, "city__nanjing": {"message": "Nanjing"}, "city__nanning": {"message": "Nanning"}, "city__nanping": {"message": "<PERSON><PERSON>"}, "city__nantong": {"message": "Nantong"}, "city__nantou": {"message": "<PERSON><PERSON><PERSON>"}, "city__nanyang": {"message": "Nanyang"}, "city__neijiang": {"message": "Neijiang"}, "city__newterritories": {"message": "New Territories"}, "city__ngari": {"message": "<PERSON><PERSON>"}, "city__ningbo": {"message": "Ningbo"}, "city__ningde": {"message": "<PERSON><PERSON><PERSON>"}, "city__nujiang": {"message": "Nujiang"}, "city__nyingchi": {"message": "Nyingchi"}, "city__ordos": {"message": "Ordos"}, "city__panjin": {"message": "Panjin"}, "city__panzhihua": {"message": "Pan Zhihua"}, "city__penghu": {"message": "<PERSON><PERSON><PERSON>"}, "city__pingdingshan": {"message": "Pingdingshan"}, "city__pingliang": {"message": "<PERSON><PERSON><PERSON>"}, "city__pingtung": {"message": "<PERSON><PERSON><PERSON>"}, "city__pingxiang": {"message": "<PERSON><PERSON><PERSON>"}, "city__puer": {"message": "<PERSON>u'er"}, "city__putian": {"message": "<PERSON><PERSON>"}, "city__puyang": {"message": "P<PERSON><PERSON>"}, "city__qiandongnan": {"message": "Qiandongnan"}, "city__qianjiang": {"message": "Qianjiang"}, "city__qiannan": {"message": "<PERSON><PERSON><PERSON>"}, "city__qianxinan": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__qingdao": {"message": "Qingdao"}, "city__qingyang": {"message": "Qingyang"}, "city__qingyuan": {"message": "Qingyuan"}, "city__qinhuangdao": {"message": "Qinhuangdao"}, "city__qinzhou": {"message": "Qinzhou"}, "city__qionghai": {"message": "Qionghai"}, "city__qiongzhong": {"message": "Qiongzhong"}, "city__qiqihar": {"message": "Qiqihar"}, "city__qitaihe": {"message": "<PERSON><PERSON><PERSON>"}, "city__quanzhou": {"message": "Quanzhou"}, "city__qujing": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__quzhou": {"message": "Quzhou"}, "city__rizhao": {"message": "<PERSON><PERSON><PERSON>"}, "city__sanmenxia": {"message": "Sanmenxia"}, "city__sanming": {"message": "Sanming"}, "city__sanya": {"message": "Sanya"}, "city__shangluo": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__shangqiu": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__shangrao": {"message": "<PERSON><PERSON><PERSON>"}, "city__shannan": {"message": "Shannan"}, "city__shantou": {"message": "<PERSON><PERSON><PERSON>"}, "city__shanwei": {"message": "Shanwei"}, "city__shaoguan": {"message": "Shaoguan"}, "city__shaoxing": {"message": "Shaoxing"}, "city__shaoyang": {"message": "Shaoyang"}, "city__shennongjiaforestarea": {"message": "Shennongjia Forest Area"}, "city__shenyang": {"message": "Shenyang"}, "city__shenzhen": {"message": "Shenzhen"}, "city__shigatse": {"message": "Shigat<PERSON>"}, "city__shihezi": {"message": "<PERSON><PERSON><PERSON>"}, "city__shijiazhuang": {"message": "Shijiazhuang"}, "city__shiyan": {"message": "<PERSON><PERSON>"}, "city__shizuishan": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__shuangyashan": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__shuozhou": {"message": "Shuozhou"}, "city__siping": {"message": "<PERSON><PERSON>"}, "city__songyuan": {"message": "Songyuan"}, "city__suihua": {"message": "Su<PERSON>ua"}, "city__suining": {"message": "Suining"}, "city__suizhou": {"message": "<PERSON><PERSON><PERSON>"}, "city__suqian": {"message": "<PERSON><PERSON><PERSON>"}, "city__suzhou": {"message": "Suzhou"}, "city__tacheng": {"message": "Tacheng"}, "city__taian": {"message": "Tai'an"}, "city__taichung": {"message": "Taichung"}, "city__tainan": {"message": "Tainan"}, "city__taipei": {"message": "Taipei"}, "city__taitung": {"message": "<PERSON><PERSON><PERSON>"}, "city__taiyuan": {"message": "Taiyuan"}, "city__taizhou": {"message": "Taizhou"}, "city__tangshan": {"message": "Tangshan"}, "city__taoyuan": {"message": "Taoyuan"}, "city__tianmen": {"message": "Tianmen"}, "city__tianshui": {"message": "<PERSON><PERSON><PERSON>"}, "city__tieling": {"message": "Tieling"}, "city__tongchuan": {"message": "<PERSON><PERSON><PERSON>"}, "city__tonghua": {"message": "Tonghua"}, "city__tongliao": {"message": "<PERSON><PERSON><PERSON>"}, "city__tongling": {"message": "Tongling"}, "city__tongren": {"message": "<PERSON><PERSON>"}, "city__tumushuke": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__tunchang": {"message": "Tunchang"}, "city__turpan": {"message": "<PERSON><PERSON><PERSON>"}, "city__ulanqab": {"message": "Ulanqab"}, "city__urumqi": {"message": "Urumqi"}, "city__wanning": {"message": "Wanning"}, "city__weifang": {"message": "<PERSON><PERSON><PERSON>"}, "city__weihai": {"message": "Weihai"}, "city__weinan": {"message": "Weinan"}, "city__wenchang": {"message": "Wenchang"}, "city__wenshan": {"message": "<PERSON><PERSON>"}, "city__wenzhou": {"message": "Wenzhou"}, "city__wuhai": {"message": "Wu<PERSON>"}, "city__wuhan": {"message": "<PERSON><PERSON>"}, "city__wuhu": {"message": "<PERSON><PERSON>"}, "city__wujiaqu": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__wuwei": {"message": "<PERSON><PERSON>"}, "city__wuxi": {"message": "Wuxi"}, "city__wuzhishan": {"message": "Wuzhishan"}, "city__wuzhong": {"message": "Wuzhong"}, "city__wuzhou": {"message": "Wuzhou"}, "city__xiamen": {"message": "Xiamen"}, "city__xian": {"message": "Xi'<PERSON>"}, "city__xiangfan": {"message": "<PERSON><PERSON><PERSON>"}, "city__xiangtan": {"message": "Xiangtan"}, "city__xiangxi": {"message": "Xiangxi"}, "city__xianning": {"message": "Xianning"}, "city__xiantao": {"message": "Xiantao"}, "city__xianyang": {"message": "<PERSON><PERSON><PERSON>"}, "city__xiaogan": {"message": "<PERSON><PERSON>"}, "city__xilingol": {"message": "Xilingol"}, "city__xinganleague": {"message": "Xing'an League"}, "city__xingtai": {"message": "Xingtai"}, "city__xining": {"message": "Xining"}, "city__xinxiang": {"message": "Xinxiang"}, "city__xinyang": {"message": "Xinyang"}, "city__xinyu": {"message": "Xinyu"}, "city__xinzhou": {"message": "Xinzhou"}, "city__xishuangbanna": {"message": "Xishuangbanna"}, "city__xuancheng": {"message": "Xuancheng"}, "city__xuchang": {"message": "Xuchang"}, "city__xuzhou": {"message": "Xuzhou"}, "city__yaan": {"message": "Ya'an"}, "city__yanan": {"message": "Yan'<PERSON>"}, "city__yanbian": {"message": "Yanbian"}, "city__yancheng": {"message": "Yancheng"}, "city__yangjiang": {"message": "Yangjiang"}, "city__yangquan": {"message": "<PERSON><PERSON><PERSON>"}, "city__yangzhou": {"message": "Yangzhou"}, "city__yantai": {"message": "Yantai"}, "city__yibin": {"message": "<PERSON><PERSON>"}, "city__yichang": {"message": "Yichang"}, "city__yichun": {"message": "<PERSON><PERSON><PERSON>"}, "city__yilan": {"message": "Yilan"}, "city__yili": {"message": "<PERSON><PERSON>"}, "city__yinchuan": {"message": "<PERSON><PERSON><PERSON>"}, "city__yingkou": {"message": "<PERSON><PERSON><PERSON>"}, "city__yingtan": {"message": "Yingtan"}, "city__yiwu": {"message": "<PERSON><PERSON>"}, "city__yiyang": {"message": "Yiyang"}, "city__yongzhou": {"message": "Yongzhou"}, "city__yueyang": {"message": "<PERSON><PERSON><PERSON>"}, "city__yulin": {"message": "Yulin"}, "city__yuncheng": {"message": "Yuncheng"}, "city__yunfu": {"message": "<PERSON><PERSON>"}, "city__yunlin": {"message": "<PERSON><PERSON>"}, "city__yushu": {"message": "Yushu"}, "city__yuxi": {"message": "Yuxi"}, "city__zaozhuang": {"message": "Zaozhuang"}, "city__zhangjiajie": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__zhangjiakou": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__zhangye": {"message": "<PERSON><PERSON>"}, "city__zhangzhou": {"message": "Zhangzhou"}, "city__zhanjiang": {"message": "Zhanjiang"}, "city__zhaoqing": {"message": "Zhaoqing"}, "city__zhaotong": {"message": "Zhaotong"}, "city__zhengzhou": {"message": "Zhengzhou"}, "city__zhenjiang": {"message": "Zhenjiang"}, "city__zhongshan": {"message": "Zhongshan"}, "city__zhongwei": {"message": "Zhongwei"}, "city__zhoukou": {"message": "<PERSON><PERSON><PERSON>"}, "city__zhoushan": {"message": "Zhoushan"}, "city__zhuhai": {"message": "Zhu<PERSON>"}, "city__zhumadian": {"message": "Zhumadian"}, "city__zhuzhou": {"message": "Zhuzhou"}, "city__zibo": {"message": "Zibo"}, "city__zigong": {"message": "Zigong"}, "city__ziyang": {"message": "<PERSON><PERSON><PERSON>"}, "city__zunyi": {"message": "<PERSON><PERSON><PERSON>"}, "click_copy_product_info": {"message": "その後、「商品マスタをコピーする\n」をクリックしてください"}, "commmon_txt_expired": {"message": "期限切れ"}, "common__date_range_12m": {"message": "1年"}, "common__date_range_1m": {"message": "1ヶ月"}, "common__date_range_1w": {"message": "1週間"}, "common__date_range_2w": {"message": "2週間"}, "common__date_range_3m": {"message": "3ヶ月"}, "common__date_range_3w": {"message": "3週間"}, "common__date_range_6m": {"message": "6ヵ月"}, "common_btn_cancel": {"message": "キャンセル"}, "common_btn_close": {"message": "閉じる"}, "common_btn_save": {"message": "保存する"}, "common_btn_setting": {"message": "セットアップ"}, "common_email": {"message": "Eメール"}, "common_error_msg_no_data": {"message": "データなし"}, "common_error_msg_no_result": {"message": "結果が見つかりませんでした。"}, "common_favorites": {"message": "お気に入り"}, "common_feedback": {"message": "フィードバック"}, "common_help": {"message": "助けて"}, "common_loading": {"message": "読み込み中"}, "common_login": {"message": "ログインする"}, "common_logout": {"message": "ログアウト"}, "common_no": {"message": "いいえ"}, "common_powered_by_aliprice": {"message": "AliPrice.comを搭載"}, "common_setting": {"message": "設定"}, "common_sign_up": {"message": "サインアップ"}, "common_system_upgrading_title": {"message": "システムのアップグレード"}, "common_system_upgrading_txt": {"message": "後でお試しください"}, "common_txt__currency": {"message": "通貨"}, "common_txt__video_tutorial": {"message": "ビデオチュートリアル"}, "common_txt_ago_time": {"message": "$time$日前", "placeholders": {"time": {"content": "$1"}}}, "common_txt_all_product": {"message": "すべての商品"}, "common_txt_analysis": {"message": "分析"}, "common_txt_basically_used": {"message": "未使用に近い"}, "common_txt_biaoti_link": {"message": "タイトル + リンク"}, "common_txt_biaoti_link_dian_pu": {"message": "タイトル + リンク + ストア名"}, "common_txt_blacklist": {"message": "ブロックリスト"}, "common_txt_cancel": {"message": "キャンセル"}, "common_txt_category": {"message": "カテゴリ"}, "common_txt_chakan": {"message": "チェック"}, "common_txt_colors": {"message": "色"}, "common_txt_confirm": {"message": "確認"}, "common_txt_copied": {"message": "コピー"}, "common_txt_copy": {"message": "コピー"}, "common_txt_copy_link": {"message": "リンクをコピーする"}, "common_txt_copy_title": {"message": "タイトルをコピー"}, "common_txt_copy_title__link": {"message": "タイトル・リンクをコピー"}, "common_txt_day": {"message": "空"}, "common_txt_day__short": {"message": "日"}, "common_txt_delete": {"message": "削除する"}, "common_txt_dian_pu_link": {"message": "ストア名+リンクをコピー"}, "common_txt_download": {"message": "ダウンロード"}, "common_txt_downloaded": {"message": "ダウンロードを続行"}, "common_txt_export_as_csv": {"message": "Excelをエクスポート"}, "common_txt_export_as_txt": {"message": "テキストをエクスポート"}, "common_txt_fail": {"message": "失敗"}, "common_txt_format": {"message": "フォーマット"}, "common_txt_get": {"message": "得る"}, "common_txt_incert_selection": {"message": "反転選択"}, "common_txt_install": {"message": "インストール"}, "common_txt_load_failed": {"message": "読み込みに失敗しました"}, "common_txt_month": {"message": "ヶ月"}, "common_txt_more": {"message": "もっと"}, "common_txt_new_unused": {"message": "新品、未使用"}, "common_txt_next": {"message": "次"}, "common_txt_no_limit": {"message": "無制限"}, "common_txt_no_noticeable": {"message": "目立った傷や汚れなし"}, "common_txt_on_sale": {"message": "贩売中のみ"}, "common_txt_opt_in_out": {"message": "オン/オフ"}, "common_txt_order": {"message": "注文"}, "common_txt_others": {"message": "その他"}, "common_txt_overall_poor_condition": {"message": "全体的に状態が悪い"}, "common_txt_patterns": {"message": "形"}, "common_txt_platform": {"message": "プラットフォーム"}, "common_txt_please_select": {"message": "選択してください"}, "common_txt_prev": {"message": "前へ"}, "common_txt_price": {"message": "価格"}, "common_txt_privacy_policy": {"message": "プライバシーポリシー"}, "common_txt_product_condition": {"message": "商品の状態"}, "common_txt_rating": {"message": "評価"}, "common_txt_ratings": {"message": "評価数"}, "common_txt_reload": {"message": "リロード"}, "common_txt_reset": {"message": "リセット"}, "common_txt_retail": {"message": "小売"}, "common_txt_review": {"message": "レビュー"}, "common_txt_sale": {"message": "贩売中"}, "common_txt_same": {"message": "同じ"}, "common_txt_scratches_and_dirt": {"message": "傷や汚れあり"}, "common_txt_search_title": {"message": "検索タイトル"}, "common_txt_select_all": {"message": "全選択"}, "common_txt_selected": {"message": "選択済み"}, "common_txt_share": {"message": "共有"}, "common_txt_sold": {"message": "売れた"}, "common_txt_sold_out": {"message": "売り切れのみ"}, "common_txt_some_scratches": {"message": "やや傷や汚れあり"}, "common_txt_sort_by": {"message": "ソート基準"}, "common_txt_state": {"message": "状態"}, "common_txt_success": {"message": "成功"}, "common_txt_sys_err": {"message": "システムエラー"}, "common_txt_today": {"message": "今日"}, "common_txt_total": {"message": "合計"}, "common_txt_unselect_all": {"message": "逆選択"}, "common_txt_upload_image": {"message": "イメージのアップロード"}, "common_txt_visit": {"message": "訪問"}, "common_txt_whitelist": {"message": "ホワイトリスト"}, "common_txt_wholesale": {"message": "卸売"}, "common_txt_wildberries": {"message": "Wildberries"}, "common_txt_year": {"message": "年"}, "common_yes": {"message": "はい"}, "compare_tool_btn_clear_all": {"message": "すべてクリア"}, "compare_tool_btn_compare": {"message": "比較する"}, "compare_tool_btn_contact": {"message": "連絡先"}, "compare_tool_tips_max_compared": {"message": "$maxComparedCount$まで追加", "placeholders": {"maxComparedCount": {"content": "$1"}}}, "configure_notifiactions": {"message": "通知を構成する"}, "contact_us": {"message": "お問い合わせ"}, "context_menu_screenshot_search": {"message": "画像で検索するためのキャプチャ"}, "context_menus_aliprice_search_by_image": {"message": "AliPriceで画像を検索"}, "context_menus_goote_trans": {"message": "ページを翻訳／原文を表示"}, "context_menus_search_by_image": {"message": "$storeName$で画像で検索", "placeholders": {"storeName": {"content": "$1"}}}, "context_menus_search_by_image_capture": {"message": "$storeName$にキャプチャ", "placeholders": {"storeName": {"content": "$1"}}}, "context_menus_translator": {"message": "翻訳するためにキャプチャする"}, "converter_modal_amount_placeholder": {"message": "ここに金額を入力します"}, "converter_modal_btn_convert": {"message": "変換"}, "converter_modal_exchange_rate_source": {"message": "データは次のとおりです $boc$ 外国為替レート. アップデート時間: $updatedAt$", "placeholders": {"boc": {"content": "$1"}, "updatedAt": {"content": "$2"}}}, "converter_modal_name": {"message": "通貨変換器"}, "converter_modal_search_placeholder": {"message": "貨幣種類検索"}, "copy_all_contact_us_notice": {"message": "このウェブサイトはサポートされておりません。お問い合わせください。"}, "copy_product_info": {"message": "商品マスタをコピーする\n"}, "copy_suggest_search_kw": {"message": "サジェストキーワード一括取得ツール"}, "country__han_gou": {"message": "韓国"}, "country__ri_ben": {"message": "日本"}, "country__yue_nan": {"message": "ベトナム"}, "currency_convert__custom": {"message": "カスタム為替レート"}, "currency_convert__sync_server": {"message": "サーバーを同期する"}, "dang_ri_fa_huo": {"message": "即日発送"}, "dao_chu_quan_dian_shang_pin": {"message": "店舗商品出力"}, "dao_chu_wei_CSV": {"message": "輸出する"}, "dao_chu_zi_duan": {"message": "フィールドをエクスポート"}, "delivery_address": {"message": "配送先住所"}, "delivery_company": {"message": "配送会社"}, "di_zhi": {"message": "住所"}, "dian_ji_cha_xun": {"message": "お問い合わせはこちら"}, "dian_pu_ID": {"message": "店舗ID"}, "dian_pu_di_zhi": {"message": "店舗住所"}, "dian_pu_lian_jie": {"message": "店舗リンク"}, "dian_pu_ming": {"message": "店舗名"}, "dian_pu_ming_cheng": {"message": "店名"}, "dian_pu_shang_pin_zong_hsu": {"message": "店舗の商品総数 $num$", "placeholders": {"num": {"content": "$1"}}}, "dian_pu_xin_xi": {"message": "ストア情報"}, "ding_zai_zuo_ce": {"message": "左に釘付け"}, "disable_old_version_tips_disable_btn_title": {"message": "古いバージョンを無効にする"}, "download_image__SKU_variant_images": {"message": "SKU属性画像"}, "download_image__assume": {"message": "例えばproduct1.jpg、product2.gif の2つの画像があります。\nimg_{$no$} は img_01.jpg、img_02.gif にリネームされます；\n{$group$}_{$no$} は 主図_01.jpg、主図_02.gif にリネームされます", "placeholders": {"no": {"content": "$3"}, "group": {"content": "$2"}}}, "download_image__batch_download": {"message": "一括ダウンロード"}, "download_image__combined_image": {"message": "連結詳細画像"}, "download_image__continue_downloading": {"message": "ダウンロードを続行"}, "download_image__description_images": {"message": "説明画像"}, "download_image__download_combined_image": {"message": "連結詳細画像をダウンロード"}, "download_image__download_zip": {"message": "圧縮して保存します"}, "download_image__enlarge_check": {"message": "JPEG、JPG、GIF、PNG形式の画像のみ対応、画像の最大サイズ：1600 x 1600"}, "download_image__enlarge_image": {"message": "高解像度でアップスケーリング"}, "download_image__export": {"message": "書き出す"}, "download_image__height": {"message": "高さ"}, "download_image__ignore_videos": {"message": "ビデオはエクスポートできないため、無視されました"}, "download_image__img_translate": {"message": "画像翻訳"}, "download_image__main_image": {"message": "イメージハンド"}, "download_image__multi_folder": {"message": "複数フォルダ"}, "download_image__name": {"message": "イメージダウンロード"}, "download_image__notice_content": {"message": "ブラウザのダウンロード設定で「ダウンロード前に各ファイルの保存場所を確認する」にチェックを入れないでください。 そうしないと、多数のダイアログ ボックスが表示されます。"}, "download_image__notice_ignore": {"message": "このメッセージを表示しない"}, "download_image__order_number": {"message": "{$no$} シリアル番号；{$group$} グループ名；{$date$} タイムスタンプ", "placeholders": {"no": {"content": "$1"}, "group": {"content": "$2"}, "date": {"content": "$3"}}}, "download_image__overview": {"message": "概要"}, "download_image__prompt_download_zip": {"message": "画像の数が多すぎるため、圧縮ファイルとしてのダウンロードをお勧めします。"}, "download_image__rename": {"message": "リネーム"}, "download_image__rule": {"message": "命名ルール"}, "download_image__single_folder": {"message": "単一フォルダ"}, "download_image__sku_image": {"message": "SKUイメージ"}, "download_image__video": {"message": "ビデオ"}, "download_image__width": {"message": "幅"}, "download_reviews__download_images": {"message": "コメント画像ダウンロード"}, "download_reviews__dropdown_title": {"message": "レビュー画像をダウンロード"}, "download_reviews__export_csv": {"message": "CSV をエクスポート"}, "download_reviews__no_images": {"message": "ダウンロード可能な画像は0枚"}, "download_reviews__no_reviews": {"message": "ダウンロードするコメントはありません！"}, "download_reviews__notice": {"message": "ヒント:"}, "download_reviews__notice__chrome_settings": {"message": "ダウンロード前に各ファイルの保存先を尋ねるChromeの設定、\"オフ \"にする"}, "download_reviews__notice__wait": {"message": "コメント数により、待ち時間が長くなる場合があります"}, "download_reviews__pages_list__all": {"message": "すべて"}, "download_reviews__pages_list__page": {"message": "以前の$page$のページ", "placeholders": {"page": {"content": "$1"}}}, "download_reviews__pages_list_title": {"message": "範囲を選択する"}, "export_shopping_cart__csv_filed__details_url": {"message": "製品リンク"}, "export_shopping_cart__csv_filed__details_url_with_sku": {"message": "共有リンクで選択されたバリエーションを表示"}, "export_shopping_cart__csv_filed__images": {"message": "画像リンク"}, "export_shopping_cart__csv_filed__quantity": {"message": "数量"}, "export_shopping_cart__csv_filed__sale_price": {"message": "価格"}, "export_shopping_cart__csv_filed__specs": {"message": "規格"}, "export_shopping_cart__csv_filed__store_name": {"message": "ストア名"}, "export_shopping_cart__csv_filed__store_url": {"message": "ストアリンク"}, "export_shopping_cart__csv_filed__title": {"message": "商品名"}, "export_shopping_cart__export_btn": {"message": "輸出する"}, "export_shopping_cart__export_empty": {"message": "商品を選んでください！"}, "fa_huo_shi_jian": {"message": "配送"}, "favorite_add_email": {"message": "メールを追加"}, "favorite_add_favorites": {"message": "お気に入りに追加"}, "favorite_added": {"message": "追加済み"}, "favorite_btn_add": {"message": "値下げアラート。"}, "favorite_btn_notify": {"message": "価格を追跡"}, "favorite_cate_name_all": {"message": "すべての製品"}, "favorite_current_price": {"message": "現在の価格"}, "favorite_due_date": {"message": "締切日"}, "favorite_enable_notification": {"message": "通知を有効に"}, "favorite_expired": {"message": "期限切れ"}, "favorite_go_to_enable": {"message": "有効化へ"}, "favorite_msg_add_success": {"message": "お気に入りに追加しました"}, "favorite_msg_del_success": {"message": "お気に入りから削除しました"}, "favorite_msg_failure": {"message": "不合格！ページを更新してもう一度お試しください。"}, "favorite_please_add_email": {"message": "メールアドレスを追加してください"}, "favorite_price_drop": {"message": "値下げ"}, "favorite_price_rise": {"message": "値上げ"}, "favorite_price_untracked": {"message": "価格が追跡されていません"}, "favorite_saved_price": {"message": "価格を保存"}, "favorite_stop_tracking": {"message": "追跡を解除"}, "favorite_sub_email_address": {"message": "メール購読"}, "favorite_tracking_period": {"message": "追跡期間"}, "favorite_tracking_prices": {"message": "追跡時の価格"}, "favorite_verify_email": {"message": "メール認証"}, "favorites_list_remove_prompt_msg": {"message": "削除してもよろしいですか？"}, "favorites_update_button": {"message": "すぐ価格を更新する"}, "fen_lei": {"message": "カテゴリ"}, "fen_xia_yan_xuan": {"message": "卸売業者おすすめ"}, "find_similar": {"message": "類似したものを探す"}, "first_ali_price_date": {"message": "AliPriceクローラーによって初めてキャッチされた日付"}, "fooview_coupons_modal_no_data": {"message": "クーポンなし"}, "fooview_coupons_modal_title": {"message": "クーポン"}, "fooview_favorites__product_sub__tooltip_l1": {"message": "価格<$lowerPrice$", "placeholders": {"lowerPrice": {"content": "$1"}}}, "fooview_favorites__product_sub__tooltip_l2": {"message": "または価格> $higherPrice$", "placeholders": {"higherPrice": {"content": "$1"}}}, "fooview_favorites__product_sub__tooltip_l3": {"message": "締め切り"}, "fooview_favorites_error_msg_no_favorites": {"message": "ここにお気に入りの製品を追加して、値下げ通知を受け取ります。"}, "fooview_favorites_filter_latest": {"message": "最新"}, "fooview_favorites_filter_price_drop": {"message": "価格の下落"}, "fooview_favorites_filter_price_up": {"message": "価格の上昇"}, "fooview_favorites_modal_title": {"message": "私のお気に入り"}, "fooview_favorites_modal_title_title": {"message": "AliPriceのお気に入りに移動"}, "fooview_favorites_track_price": {"message": "価格を追跡するには"}, "fooview_price_history_app_price": {"message": "アプリ価格："}, "fooview_price_history_title": {"message": "価格履歴"}, "fooview_product_list_feedback": {"message": "フィードバック"}, "fooview_product_list_orders": {"message": "注文"}, "fooview_product_list_price": {"message": "価格"}, "fooview_reviews_error_msg_no_review": {"message": "この商品のレビューは見つかりませんでした。"}, "fooview_reviews_filter_buyer_reviews": {"message": "バイヤーの写真"}, "fooview_reviews_modal_title": {"message": "レビュー"}, "fooview_same_product_choose_category": {"message": "カテゴリーを選択してください"}, "fooview_same_product_filter_feedback": {"message": "フィードバック"}, "fooview_same_product_filter_orders": {"message": "注文"}, "fooview_same_product_filter_price": {"message": "価格"}, "fooview_same_product_filter_rating": {"message": "評価"}, "fooview_same_product_modal_title": {"message": "同じ製品を探す"}, "fooview_same_product_search_by_image": {"message": "画像で検索"}, "fooview_seller_analysis_modal_title": {"message": "売り手分析"}, "for_12_months": {"message": "1年間"}, "for_12_months_list_pro": {"message": "12ヶ月"}, "for_12_months_nei": {"message": "12ヶ月間"}, "for_1_months": {"message": "1ヶ月"}, "for_1_months_nei": {"message": "1ヶ月間"}, "for_3_months": {"message": "3か月間"}, "for_3_months_nei": {"message": "3ヶ月間"}, "for_6_months": {"message": "6か月間"}, "for_6_months_nei": {"message": "6ヶ月間"}, "for_9_months": {"message": "9ヶ月"}, "for_9_months_nei": {"message": "9ヶ月間"}, "fu_gou_lv": {"message": "再購入率"}, "gao_liang_bu_tong_dian": {"message": "違いを強調する"}, "gao_liang_guang_gao_chan_pin": {"message": "広告商品をハイライトで表示します"}, "geng_duo_xin_xi": {"message": "その他の情報"}, "geng_xin_shi_jian": {"message": "更新日時"}, "get_store_products_fail_tip": {"message": "正常にアクセスするため、「OK」をクリックして認証してください"}, "gong_x_kuan_shang_pin": {"message": "合計 $amount$ 個の商品", "placeholders": {"amount": {"content": "$1"}}}, "gong_ying_shang": {"message": "サプライヤー"}, "gong_ying_shang_ID": {"message": "サプライヤーID"}, "gong_ying_shang_deng_ji": {"message": "サプライヤー評価"}, "gong_ying_shang_nian_zhan": {"message": "サプライヤーの方が古い"}, "gong_ying_shang_xin_xi": {"message": "サプライヤー情報"}, "gong_ying_shang_zhu_ye_lian_jie": {"message": "サプライヤー・ホームページ・リンク"}, "green_rocket": {"message": "로켓프레시"}, "grey_rocket": {"message": "로켓설치"}, "gu_ji_shou_jia": {"message": "推定販売価格"}, "guan_jian_zi": {"message": "キーワード"}, "guang_gao_chan_pin": {"message": "広告商品"}, "guang_gao_zhan_bi": {"message": "広告比率"}, "guo_ji_wu_liu_yun_fei": {"message": "国際配送料"}, "guo_lv_tiao_jian": {"message": "フィルター"}, "hao_ping_lv": {"message": "好評率"}, "highest_price": {"message": "最高値"}, "historical_trend": {"message": "歴史的傾向"}, "how_to_screenshot": {"message": "マウスの左ボタンを押したまま領域を選択し、マウスの右ボタンまたはEscキーをタップしてスクリーンショットを終了する。"}, "howt_it_works": {"message": "使い方"}, "hui_fu_lv": {"message": "レスポンス率"}, "hui_tou_lv": {"message": "返品率"}, "inquire_freightFee": {"message": "送料確認"}, "inquire_freightFee_Yuan": {"message": "送料/元"}, "inquire_freightFee_province": {"message": "省"}, "inquire_freightFee_the": {"message": "送料は$num$であり、この地域は送料無料です。", "placeholders": {"num": {"content": "$1"}}}, "is_ad": {"message": "広告"}, "jia_ge": {"message": "価格"}, "jia_ge_dan_wei": {"message": "価格単位"}, "jia_ge_qu_shi": {"message": "傾向"}, "jia_zai_n_ge_shang_pin": {"message": "$num$ 件の商品を読み込みました", "placeholders": {"num": {"content": "$1"}}}, "jin_30_tian_xiao_liang_zhan_bi": {"message": "直近30日間の販売数比率"}, "jin_30_tian_xiao_shou_e_zhan_bi": {"message": "直近30日間の売上高比率"}, "jin_30d_xiao_liang": {"message": "販売"}, "jin_30d_xiao_liang__desc": {"message": "過去 30 日間の総売上高"}, "jin_30d_xiao_shou_e": {"message": "ひっくり返す"}, "jin_30d_xiao_shou_e__desc": {"message": "過去 30 日間の総売上高"}, "jin_90_tian_mai_jia_shu": {"message": "過去90日間のバイヤー"}, "jin_90_tian_xiao_shou_liang": {"message": "過去90日間の販売量"}, "jing_xuan_huo_yuan": {"message": "選択されたソース"}, "jing_ying_mo_shi": {"message": "ビジネスモード"}, "jing_ying_mo_shi__gong_chang": {"message": "メーカー"}, "jiu_fen_jie_jue": {"message": "論争の解決"}, "jiu_fen_jie_jue__desc": {"message": "販売元権利論争情報"}, "jiu_fen_lv": {"message": "論争率"}, "jiu_fen_lv__desc": {"message": "過去 30 日以内販売者の責任と両当事者の責任であると判断された注文完了の割合"}, "kai_dian_ri_qi": {"message": "\n開設時間"}, "keywords": {"message": "キーワード"}, "kua_jin_Select_pan_huo": {"message": "越境セレクト"}, "last15_days": {"message": "過去15日間"}, "last180_days": {"message": "過去180日間"}, "last30_days": {"message": "過去30日間"}, "last360_days": {"message": "過去360日間"}, "last45_days": {"message": "過去45日間"}, "last60_days": {"message": "過去60日間"}, "last7_days": {"message": "過去7日間"}, "last90_days": {"message": "過去90日間"}, "last_30d_sales": {"message": "過去 30 日間の売上"}, "lei_ji": {"message": "累計"}, "lei_ji_xiao_liang": {"message": "合計"}, "lei_ji_xiao_liang__desc": {"message": "製品発売後の全販売量"}, "lei_ji_xiao_liang_pai_xu__desc": {"message": "過去 30 日間の累計販売量を高い順に並べたもの"}, "lian_xi_fang_shi": {"message": "連絡先"}, "list_time": {"message": "販売日"}, "load_more": {"message": "もっと読み込む"}, "login_to_aliprice": {"message": "AliPriceへのログイン"}, "long_link": {"message": "長いリンク"}, "lowest_price": {"message": "最安値"}, "mai_jia_shu": {"message": "セラー数"}, "mao_li_lv": {"message": "粗利益率"}, "mobile_view__dkxbqy": {"message": "新しいタブで開く"}, "mobile_view__sjdxq": {"message": "モバイル版詳細"}, "mobile_view__sjdxqy": {"message": "モバイル版詳細ページ"}, "mobile_view__smck": {"message": "QRコード読み取り"}, "mobile_view__smckms": {"message": "カメラまたはアプリを使用してスキャンしてください"}, "modified_failed": {"message": "変更に失敗しました"}, "modified_successfully": {"message": "正常に変更されました"}, "nav_btn_favorites": {"message": "私のコレクション"}, "nav_btn_package": {"message": "パッケージ"}, "nav_btn_product_info": {"message": "商品について"}, "nav_btn_viewed": {"message": "閲覧"}, "no_suggest_search_kw": {"message": "Loading..."}, "none": {"message": "なし"}, "normal_link": {"message": "通常のリンク"}, "notice": {"message": "ヒント"}, "number_reviews": {"message": "レビュー数"}, "only_show_num": {"message": "総商品数: $allnum$、非表示商品: $hidenum2$", "placeholders": {"allnum": {"content": "$1"}, "hidenum2": {"content": "$2"}}}, "only_show_selected": {"message": "未選択を削除"}, "open": {"message": "開く"}, "open_links": {"message": "リンクを開く"}, "options_page_tab_check_links": {"message": "リンクをチェック"}, "options_page_tab_gernal": {"message": "一般的な"}, "options_page_tab_notifications": {"message": "お知らせ"}, "options_page_tab_others": {"message": "その他"}, "options_page_tab_sbi": {"message": "画像で検索"}, "options_page_tab_shortcuts": {"message": "ショートカット"}, "options_page_tab_shortcuts_title": {"message": "ショートカットのフォントサイズ"}, "options_page_tab_similar_products": {"message": "同じ製品"}, "orange_rocket": {"message": "판매자로켓"}, "order_list_open_links_tip": {"message": "複数の製品リンクが開こうとしています"}, "order_list_sku_show_title": {"message": "共有リンクで選択されたバリエーションを表示"}, "orders_last30_days": {"message": "過去30日の注文数"}, "pTutorial_favorites_block1_desc1": {"message": "追跡した製品はここにリストされています"}, "pTutorial_favorites_block1_title": {"message": "お気に入り"}, "pTutorial_popup_block1_desc1": {"message": "緑のラベルは、値下げされた製品があることを意味します"}, "pTutorial_popup_block1_title": {"message": "ショートカットとお気に入り"}, "pTutorial_price_history_block1_desc1": {"message": "「「価格を追跡」」をクリックして、製品をお気に入りに追加します。価格が下がると、通知が届きます"}, "pTutorial_price_history_block1_title": {"message": "価格を追跡をクリックして、製品をお気に入りに追加します。価格が下がると、通知が届きます"}, "pTutorial_reviews_block1_desc1": {"message": "価格を追跡"}, "pTutorial_reviews_block1_title": {"message": "ItaoからのバイヤーのレビューとAliExpressフィードバックからの実際の写真"}, "pTutorial_reviews_block2_desc1": {"message": "レビュー"}, "pTutorial_same_products_block1_desc1": {"message": "他のバイヤーからのレビューを確認することは常に役に立ちます"}, "pTutorial_same_products_block1_desc2": {"message": "それらを比較して最良の選択をすることができます"}, "pTutorial_same_products_block1_title": {"message": "「詳細」をクリックして「画像で検索」します"}, "pTutorial_same_products_by_image_search_block1_desc1": {"message": "同じ製品"}, "pTutorial_same_products_by_image_search_block1_title": {"message": "そこに商品画像をドロップしてカテゴリを選択"}, "pTutorial_seller_analysis_block1_desc1": {"message": "画像で検索"}, "pTutorial_seller_analysis_block1_title": {"message": "売り手の正のフィードバック率、フィードバックスコア、売り手が市場に出てからの期間"}, "pTutorial_seller_analysis_block2_desc2": {"message": "売り手の評価"}, "pTutorial_seller_analysis_block3_desc3": {"message": "販売者評価は3つのインデックスに基づいています。"}, "page_count": {"message": "ページ数"}, "pai_chu": {"message": "除外"}, "pai_chu_HK_bu_yi_xia_xiaoshou": {"message": "中国香港制限商品を除外"}, "pai_chu_JP_bu_yi_xia_xiaoshou": {"message": "日本制限商品を除外"}, "pai_chu_KR_bu_yi_xia_xiaoshou": {"message": "韓国制限商品を除外"}, "pai_chu_KZ_bu_yi_xia_xiaoshou": {"message": "カザフスタン制限商品を除外"}, "pai_chu_MO_bu_yi_xia_xiaoshou": {"message": "中国マカオ制限商品を除外"}, "pai_chu_RU_bu_yi_xia_xiaoshou": {"message": "東欧制限商品を除外"}, "pai_chu_SA_bu_yi_xia_xiaoshou": {"message": "サウジアラビア制限商品を除外"}, "pai_chu_TW_bu_yi_xia_xiaoshou": {"message": "中国台湾制限商品を除外"}, "pai_chu_USA_bu_yi_xia_xiaoshou": {"message": "米国制限商品を除外"}, "pai_chu_VN_bu_yi_xia_xiaoshou": {"message": "ベトナム制限商品を除外"}, "pai_chu_bu_yi_xia_xiaoshou": {"message": "制限商品を除外"}, "payable_price_formula": {"message": "価格＋送料＋割引"}, "pdd_check_retail_btn_txt": {"message": "小売を確認する"}, "pdd_pifa_to_retail_btn_txt": {"message": "小売購入"}, "pdp_copy_fail": {"message": "コピーに失敗しました！"}, "pdp_copy_success": {"message": "コピーに成功しました！"}, "pdp_share_modal_subtitle": {"message": "スクリーンショットを共有すると、彼/彼女はあなたのchioceを見るでしょう。"}, "pdp_share_modal_title": {"message": "あなたの選択を共有する"}, "pdp_share_screenshot": {"message": "スクリーンショットを共有する"}, "pei_song": {"message": "配送"}, "pin_lei": {"message": "カテゴリ"}, "pin_zhi_ti_yan": {"message": "品質 体験"}, "pin_zhi_ti_yan__desc": {"message": "販売元の不良返品率"}, "pin_zhi_tui_kuan_lv": {"message": "不良返品率"}, "pin_zhi_tui_kuan_lv__desc": {"message": "過去30日間返金のみと返品した注文完了の割合"}, "ping_fen": {"message": "評価"}, "ping_jun_fa_huo_su_du": {"message": "平均出荷速度"}, "pkgInfo_hide": {"message": "物流情報: オン/オフ"}, "pkgInfo_no_trace": {"message": "物流情報なし"}, "platform_name__1688": {"message": "1688"}, "platform_name__17zwd": {"message": "17zwd.com"}, "platform_name__51taoyang": {"message": "51taoyang.com"}, "platform_name__5ts": {"message": "5ts.com"}, "platform_name__91jf": {"message": "91jf.com"}, "platform_name__alibaba": {"message": "Alibaba.com"}, "platform_name__aliexpress": {"message": "AliExpress"}, "platform_name__amazon": {"message": "アマゾン"}, "platform_name__bao66": {"message": "bao66.cn"}, "platform_name__chinagoods": {"message": "chinagoods.com"}, "platform_name__dhgate": {"message": "DHgate"}, "platform_name__e3hui": {"message": "e3hui.com"}, "platform_name__ebay": {"message": "イーベイ"}, "platform_name__hznzcn": {"message": "hznzcn.com"}, "platform_name__jd": {"message": "JD"}, "platform_name__juyi5": {"message": "juyi5.cn"}, "platform_name__naver_shopping": {"message": "Naver Shopping"}, "platform_name__pinduoduo": {"message": "拼多多"}, "platform_name__pinduoduo_pifa": {"message": "ピンデュオ卸売"}, "platform_name__shopee": {"message": "買い物客"}, "platform_name__sjxzw": {"message": "571xz.com"}, "platform_name__sooxie": {"message": "sooxie.com"}, "platform_name__taobao": {"message": "淘宝網"}, "platform_name__taobao_lite": {"message": "Taobao Lite"}, "platform_name__vvic": {"message": "vvic.com"}, "platform_name__walmart": {"message": "ウォルマート"}, "platform_name__wsy": {"message": "wsy.com"}, "platform_name__xingfujie": {"message": "xingfujie.cn"}, "platform_name__yiwugo": {"message": "Yiwugo.com"}, "platform_name__yunchepin": {"message": "yunchepin.cn"}, "platform_name__zhaojiafang": {"message": "zhaojiafang.com"}, "popup_go_to_home": {"message": "3つの色とアイコンを使用して、販売者の信頼レベルを示しています"}, "popup_go_to_platform": {"message": "ホーム"}, "popup_search_placeholder": {"message": "AliPriceに移動"}, "popup_track_package_btn_track": {"message": "ウィッシュリスト"}, "popup_track_package_desc": {"message": "追跡"}, "popup_track_package_search_placeholder": {"message": "オールインワンのパッケージ追跡"}, "popup_translate_search_placeholder": {"message": "$searchOn$での翻訳と検索", "placeholders": {"searchOn": {"content": "$1"}}}, "price_history": {"message": "価格履歴"}, "price_history_chart_tip_ae": {"message": "ヒント: 注文数は、公開から現在までの累積注文数を示します"}, "price_history_chart_tip_coupang": {"message": "ヒント：Coupang公式は、不正行為による注文数を削除する可能性があります"}, "price_history_inm_1688_l1": {"message": "インストールしてください"}, "price_history_inm_1688_l2": {"message": "1688 の AliPrice ショッピング アシスタント"}, "price_history_panel_lowest_price": {"message": "価格履歴"}, "price_history_panel_tab_price_tracking": {"message": "価格履歴"}, "price_history_panel_tab_seller_analysis": {"message": "売り手分析"}, "price_history_pro_modal_title": {"message": "価格履歴・注文履歴"}, "privacy_consent__btn_agree": {"message": "データ収集の同意を再確認する"}, "privacy_consent__btn_disable_all": {"message": "受け入れない"}, "privacy_consent__btn_enable_all": {"message": "全て可能にする"}, "privacy_consent__btn_uninstall": {"message": "削除する"}, "privacy_consent__desc_privacy": {"message": "データまたはCookieがない場合、それらの機能はデータまたはCookieの説明を必要とするため、一部の機能はオフになりますが、他の機能は引き続き使用できます。"}, "privacy_consent__desc_privacy_L1": {"message": "殘念ながら、データやCookieがないと機能しません。データやCookieの説明が必要だからです。"}, "privacy_consent__desc_privacy_L2": {"message": "これらの情報の収集を許可しない場合は、削除してください。"}, "privacy_consent__item_cookies_desc": {"message": "クッキー、私たちは価格の履歴を表示するためにオンラインで買い物をするときのみクッキーであなたの通貨データを取得します。"}, "privacy_consent__item_cookies_title": {"message": "必要なクッキー"}, "privacy_consent__item_functional_desc_L1": {"message": "1.ブラウザーにCookieを追加して、コンピューターまたはデバイスを匿名で識別します。"}, "privacy_consent__item_functional_desc_L2": {"message": "2.機能を使用するために、アドオンで機能データを追加します。"}, "privacy_consent__item_functional_title": {"message": "機能および分析Cookie"}, "privacy_consent__more_desc": {"message": "私たちはあなたの個人データを他の会社と共有したり、広告会社が私たちのサービスを通じてデータを収集したりしないことを知ってください。"}, "privacy_consent__options__btn__desc": {"message": "すべての機能を使用するには、それをオンにする必要があります。"}, "privacy_consent__options__btn__label": {"message": "それをオン"}, "privacy_consent__options__desc_L1": {"message": "私たちはあなたを個人的に識別する以下のデータを収集します："}, "privacy_consent__options__desc_L2": {"message": "-クッキー、私たちはあなたがオンラインで買い物をして価格履歴を表示しているときにのみクッキーの通貨データを取得します。"}, "privacy_consent__options__desc_L3": {"message": "-ブラウザーにCookieを追加して、コンピューターまたはデバイスを匿名で識別します。"}, "privacy_consent__options__desc_L4": {"message": "-他の匿名データはこの拡張をより便利にします。"}, "privacy_consent__options__desc_L5": {"message": "お客様の個人データを他社と共有したり、広告會社が當社のサービスを通じてデータを収集したりすることはありません。"}, "privacy_consent__privacy_preferences": {"message": "プライバシー設定"}, "privacy_consent__read_more": {"message": "続きを読む>>"}, "privacy_consent__title_privacy": {"message": "プライバシー"}, "product_info": {"message": "商品情報"}, "product_recommend__name": {"message": "同じ製品"}, "product_research": {"message": "製品調査"}, "product_sub__email_desc": {"message": "価格アラートメール"}, "product_sub__email_edit": {"message": "編集"}, "product_sub__email_not_verified": {"message": "メールを確認してください"}, "product_sub__email_required": {"message": "メールを送ってください"}, "product_sub__form_countdown": {"message": "$seconds$秒後の自動クローズ", "placeholders": {"seconds": {"content": "$1"}}}, "product_sub__form_fail": {"message": "リマインダーの追加に失敗しました！"}, "product_sub__form_input_price": {"message": "入力価格"}, "product_sub__form_item_country": {"message": "国家"}, "product_sub__form_item_current_price": {"message": "現在の価格"}, "product_sub__form_item_duration": {"message": "追跡"}, "product_sub__form_item_higher_price": {"message": "または価格>"}, "product_sub__form_item_invalid_higher_price": {"message": "価格は$price$より大きくする必要があります", "placeholders": {"price": {"content": "$1"}}}, "product_sub__form_item_invalid_lower_price": {"message": "価格は$price$より低くする必要があります", "placeholders": {"price": {"content": "$1"}}}, "product_sub__form_item_lower_price": {"message": "価格<"}, "product_sub__form_submit": {"message": "送信"}, "product_sub__form_success": {"message": "リマインダーの追加に成功しました！"}, "product_sub__high_price_notify": {"message": "値上げを通知する"}, "product_sub__low_price_notify": {"message": "値下げの通知を受け取る"}, "product_sub__modal_title": {"message": "サブスクリプション価格変更のリマインダー"}, "province__an_hui": {"message": "<PERSON><PERSON>"}, "province__ao_men": {"message": "Macau"}, "province__bei_jing": {"message": "Beijing"}, "province__chong_qing": {"message": "Chongqing"}, "province__fu_jian": {"message": "Fujian"}, "province__gan_su": {"message": "Gansu"}, "province__guang_dong": {"message": "Guangdong"}, "province__guang_xi": {"message": "Guangxi"}, "province__gui_zhou": {"message": "Guizhou"}, "province__hai_nan": {"message": "Hainan"}, "province__he_bei": {"message": "Hebei"}, "province__he_nan": {"message": "<PERSON><PERSON>"}, "province__hei_long_jiang": {"message": "Heilongjiang"}, "province__hu_bei": {"message": "Hubei"}, "province__hu_nan": {"message": "Hunan"}, "province__ji_lin": {"message": "<PERSON><PERSON>"}, "province__jiang_su": {"message": "Jiangsu"}, "province__jiang_xi": {"message": "Jiangxi"}, "province__liao_ning": {"message": "Liaoning"}, "province__nei_meng_gu": {"message": "Inner Mongolia"}, "province__ning_xia": {"message": "Ningxia"}, "province__qing_hai": {"message": "Qinghai"}, "province__shan_dong": {"message": "Shandong"}, "province__shan_xi": {"message": "Shaanxi"}, "province__shang_hai": {"message": "Shanghai"}, "province__si_chuan": {"message": "Sichuan"}, "province__tai_wan": {"message": "Taiwan"}, "province__tian_jin": {"message": "Tianjin"}, "province__xi_zhang": {"message": "Tibet"}, "province__xiang_gang": {"message": "Hong Kong"}, "province__xin_jiang": {"message": "Xinjiang"}, "province__yun_nan": {"message": "Yunnan"}, "province__zhe_jiang": {"message": "Zhejiang"}, "purple_rocket": {"message": "로켓직구"}, "qi_ding_liang": {"message": "最小注文数量"}, "qi_ding_liang_qi_ding_jia": {"message": "最低注文数量 & 最低注文価格"}, "qi_ye_mian_ji": {"message": "エンタープライズエリア"}, "qing_zhi_shao_xuan_ze_yi_ge_chan_pin": {"message": "少なくとも1つの製品を選択してください"}, "qing_zhi_shao_xuan_ze_yi_ge_zi_duan": {"message": "少なくとも1つのフィールドを選択してください"}, "qu_deng_lu": {"message": "ログインする"}, "quan_guo_yan_xuan": {"message": "グローバルおすすめ"}, "recommendation_popup_banner_btn_install": {"message": "インストールする"}, "recommendation_popup_banner_desc": {"message": "3/6か月以内の価格履歴を表示し、値下げ通知"}, "region__all": {"message": "すべての地域"}, "region__hai_wai": {"message": "Overseas"}, "region__hua_bei_qu": {"message": "North China"}, "region__hua_dong_qu": {"message": "East China"}, "region__hua_nan_qu": {"message": "South China"}, "region__hua_zhong_qu": {"message": "Central China"}, "region__jiang_zhe_lu": {"message": "Jiangsu, Zhejiang and Shanghai"}, "remove_web_limits": {"message": "右クリックを有効にする"}, "ren_zheng_gong_chang": {"message": "認定工場"}, "ren_zheng_gong_ying_shang_nian_zhan": {"message": "認定サプライヤーとしての年数"}, "required_to_aliprice_login": {"message": "AliPriceにログインする必要があります"}, "revenue_last30_days": {"message": "過去30日の販売額"}, "review_counts": {"message": "コレクターの数"}, "rocket": {"message": "로켓"}, "ru_zhu_nian_xian": {"message": "エントリー期間"}, "sales_amount_last30_days": {"message": "過去30日の総販売額"}, "sales_last30_days": {"message": "過去30日の販売数"}, "sbi_alibaba_cate__accessories": {"message": "ファッション小物"}, "sbi_alibaba_cate__aqfk": {"message": "安全"}, "sbi_alibaba_cate__bags_cases": {"message": "カバン"}, "sbi_alibaba_cate__beauty": {"message": "化粧"}, "sbi_alibaba_cate__beverage": {"message": "飲料"}, "sbi_alibaba_cate__bgwh": {"message": "オフィス文化"}, "sbi_alibaba_cate__bz": {"message": "パッケージ"}, "sbi_alibaba_cate__ccyj": {"message": "台所用品"}, "sbi_alibaba_cate__clothes": {"message": "衣服"}, "sbi_alibaba_cate__cmgd": {"message": "メディア放送"}, "sbi_alibaba_cate__coat_jacket": {"message": "コート・ジャケット"}, "sbi_alibaba_cate__consumer_electronics": {"message": "家電"}, "sbi_alibaba_cate__cryp": {"message": "アダルト商品"}, "sbi_alibaba_cate__csyp": {"message": "ベッドライニング"}, "sbi_alibaba_cate__cwyy": {"message": "ペットガーデニング"}, "sbi_alibaba_cate__cysx": {"message": "新鮮なケータリング"}, "sbi_alibaba_cate__dgdq": {"message": "電気技師"}, "sbi_alibaba_cate__dl": {"message": "演技"}, "sbi_alibaba_cate__dress_suits": {"message": "ワンピース・ドレス"}, "sbi_alibaba_cate__dszm": {"message": "点灯"}, "sbi_alibaba_cate__dzqj": {"message": "電子機器"}, "sbi_alibaba_cate__essb": {"message": "中古機器"}, "sbi_alibaba_cate__food": {"message": "食品"}, "sbi_alibaba_cate__fspj": {"message": "アパレル & アクセサリー"}, "sbi_alibaba_cate__furniture": {"message": "家具"}, "sbi_alibaba_cate__fzpg": {"message": "テキスタイルレザー"}, "sbi_alibaba_cate__ghjq": {"message": "パーソナルケア"}, "sbi_alibaba_cate__gt": {"message": "鋼"}, "sbi_alibaba_cate__gyp": {"message": "工芸品"}, "sbi_alibaba_cate__hb": {"message": "環境にやさしい"}, "sbi_alibaba_cate__hfcz": {"message": "スキンケアメイク"}, "sbi_alibaba_cate__hg": {"message": "化学工業"}, "sbi_alibaba_cate__jg": {"message": "処理"}, "sbi_alibaba_cate__jianccai": {"message": "建材"}, "sbi_alibaba_cate__jichuang": {"message": "工作機械"}, "sbi_alibaba_cate__jjry": {"message": "家庭での日常使用"}, "sbi_alibaba_cate__jtys": {"message": "交通手段"}, "sbi_alibaba_cate__jxsb": {"message": "装置"}, "sbi_alibaba_cate__jxwj": {"message": "機械・金具"}, "sbi_alibaba_cate__jydq": {"message": "家庭用器具"}, "sbi_alibaba_cate__jzjc": {"message": "改築建材"}, "sbi_alibaba_cate__jzjf": {"message": "ホームテキスタイル"}, "sbi_alibaba_cate__mj": {"message": "タオル"}, "sbi_alibaba_cate__myyp": {"message": "ベビー用品"}, "sbi_alibaba_cate__nanz": {"message": "男性用"}, "sbi_alibaba_cate__nvz": {"message": "女性の服装"}, "sbi_alibaba_cate__ny": {"message": "エネルギー"}, "sbi_alibaba_cate__others": {"message": "その他"}, "sbi_alibaba_cate__qcyp": {"message": "カーアクセサリー"}, "sbi_alibaba_cate__qmpj": {"message": "車の部品"}, "sbi_alibaba_cate__shoes": {"message": "靴"}, "sbi_alibaba_cate__smdn": {"message": "デジタルコンピュータ"}, "sbi_alibaba_cate__snqj": {"message": "保管とクリーニング"}, "sbi_alibaba_cate__spjs": {"message": "食べ物飲み物"}, "sbi_alibaba_cate__swfw": {"message": "ビジネスサービス"}, "sbi_alibaba_cate__toys_hobbies": {"message": "おもちゃ"}, "sbi_alibaba_cate__trousers_skirt": {"message": "スカート・パンツ"}, "sbi_alibaba_cate__txcp": {"message": "通信製品"}, "sbi_alibaba_cate__tz": {"message": "子供服"}, "sbi_alibaba_cate__underwear": {"message": "下着"}, "sbi_alibaba_cate__wjgj": {"message": "ハードウェア ツール"}, "sbi_alibaba_cate__xgpi": {"message": "レザーバッグ"}, "sbi_alibaba_cate__xmhz": {"message": "プロジェクト協力"}, "sbi_alibaba_cate__xs": {"message": "ゴム"}, "sbi_alibaba_cate__ydfs": {"message": "スポーツウェア"}, "sbi_alibaba_cate__ydhw": {"message": "アウトドアスポーツ"}, "sbi_alibaba_cate__yjkc": {"message": "冶金鉱物"}, "sbi_alibaba_cate__yqyb": {"message": "計装"}, "sbi_alibaba_cate__ys": {"message": "印刷する"}, "sbi_alibaba_cate__yyby": {"message": "医療"}, "sbi_alibaba_cn_kj_90mjs": {"message": "過去90日間の購入者数"}, "sbi_alibaba_cn_kj_90xsl": {"message": "過去90日間の販売量"}, "sbi_alibaba_cn_kj_gjsj": {"message": "見積もり価格"}, "sbi_alibaba_cn_kj_gjwlyf": {"message": "国際配送料"}, "sbi_alibaba_cn_kj_gjyf": {"message": "送料"}, "sbi_alibaba_cn_kj_gssj": {"message": "見積もり価格"}, "sbi_alibaba_cn_kj_lr": {"message": "利益"}, "sbi_alibaba_cn_kj_lrgs": {"message": "利益=推定価格x利益率"}, "sbi_alibaba_cn_kj_pjfhsd": {"message": "平均配達速度"}, "sbi_alibaba_cn_kj_qtfy": {"message": "その他の料金"}, "sbi_alibaba_cn_kj_qtfygs": {"message": "その他の費用=見積価格xその他の費用比率"}, "sbi_alibaba_cn_kj_spjg": {"message": "価格"}, "sbi_alibaba_cn_kj_spzl": {"message": "重量"}, "sbi_alibaba_cn_kj_szd": {"message": "ロケーション"}, "sbi_alibaba_cn_kj_unit_ge": {"message": "ピース"}, "sbi_alibaba_cn_kj_unit_jian": {"message": "ピース"}, "sbi_alibaba_cn_kj_unit_ke": {"message": "グラム"}, "sbi_alibaba_cn_kj_unit_ren": {"message": "バイヤー"}, "sbi_alibaba_cn_kj_unit_tai": {"message": "ピース"}, "sbi_alibaba_cn_kj_unit_tao": {"message": "セット"}, "sbi_alibaba_cn_kj_unit_tian": {"message": "日々"}, "sbi_alibaba_cn_kj_zwbj": {"message": "価格なし"}, "sbi_aliprice_alibaba_cn__jiage": {"message": "価格"}, "sbi_aliprice_alibaba_cn__keshou": {"message": "販売可能"}, "sbi_aliprice_alibaba_cn__moren": {"message": "ディフォルト"}, "sbi_aliprice_alibaba_cn__queding": {"message": "もちろん"}, "sbi_aliprice_alibaba_cn__xiaoliang": {"message": "販売"}, "sbi_aliprice_alibaba_cn_cate__jiaju": {"message": "家具"}, "sbi_aliprice_alibaba_cn_cate__linghsi": {"message": "スナック"}, "sbi_aliprice_alibaba_cn_cate__meizhuang": {"message": "メイク"}, "sbi_aliprice_alibaba_cn_cate__neiyi": {"message": "下着"}, "sbi_aliprice_alibaba_cn_cate__peishi": {"message": "付属品"}, "sbi_aliprice_alibaba_cn_cate__pinchui": {"message": "ボトル入りドリンク"}, "sbi_aliprice_alibaba_cn_cate__qita": {"message": "その他"}, "sbi_aliprice_alibaba_cn_cate__qunzhuang": {"message": "スカート"}, "sbi_aliprice_alibaba_cn_cate__shangyi": {"message": "ジャケット"}, "sbi_aliprice_alibaba_cn_cate__shuma": {"message": "エレクトロニクス"}, "sbi_aliprice_alibaba_cn_cate__wanju": {"message": "おもちゃ"}, "sbi_aliprice_alibaba_cn_cate__xiangbao": {"message": "荷物"}, "sbi_aliprice_alibaba_cn_cate__xiazhuang": {"message": "ボトムス"}, "sbi_aliprice_alibaba_cn_cate__xiezi": {"message": "靴"}, "sbi_aliprice_cate__apparel": {"message": "衣服"}, "sbi_aliprice_cate__automobiles_motorcycles": {"message": "自動車とオートバイ"}, "sbi_aliprice_cate__beauty_health": {"message": "美容と健康"}, "sbi_aliprice_cate__cellphones_telecommunications": {"message": "携帯電話と電気通信"}, "sbi_aliprice_cate__computer_office": {"message": "コンピュータ＆事務所"}, "sbi_aliprice_cate__consumer_electronics": {"message": "家電"}, "sbi_aliprice_cate__education_office_supplies": {"message": "教育および事務用品"}, "sbi_aliprice_cate__electronic_components_supplies": {"message": "電子部品および消耗品"}, "sbi_aliprice_cate__furniture": {"message": "家具"}, "sbi_aliprice_cate__hair_extensions_wigs": {"message": "ヘアエクステンション＆ウィッグ"}, "sbi_aliprice_cate__home_garden": {"message": "ホーム＆ガーデン"}, "sbi_aliprice_cate__home_improvement": {"message": "家の修繕"}, "sbi_aliprice_cate__jewelry_accessories": {"message": "ジュエリー＆アクセサリー"}, "sbi_aliprice_cate__luggage_bags": {"message": "荷物とバッグ"}, "sbi_aliprice_cate__mother_kids": {"message": "マザー＆キッズ"}, "sbi_aliprice_cate__novelty_special_use": {"message": "ノベルティ＆特殊用途"}, "sbi_aliprice_cate__security_protection": {"message": "セキュリティと保護"}, "sbi_aliprice_cate__shoes": {"message": "靴"}, "sbi_aliprice_cate__sports_entertainment": {"message": "スポーツ＆エンターテインメント"}, "sbi_aliprice_cate__toys_hobbies": {"message": "おもちゃ＆ホビー"}, "sbi_aliprice_cate__watches": {"message": "時計"}, "sbi_aliprice_cate__weddings_events": {"message": "結婚式とイベント"}, "sbi_btn_capture_txt": {"message": "キャプチャー"}, "sbi_btn_source_now_txt": {"message": "ソースを今すぐ"}, "sbi_button__chat_with_me": {"message": "でチャット"}, "sbi_button__contact_supplier": {"message": "連絡"}, "sbi_button__hide_on_this_site": {"message": "このサイトに表示しない"}, "sbi_button__open_settings": {"message": "画像による検索を構成する"}, "sbi_capture_shortcut_tip": {"message": "またはキーボードの「Enter」キーを押します"}, "sbi_capturing_tip": {"message": "捕獲"}, "sbi_composed_rating_45": {"message": "星 4.5 ～ 5.0"}, "sbi_crop_and_search": {"message": "探す"}, "sbi_crop_start": {"message": "スクリーンショットを使用する"}, "sbi_err_captcha_action": {"message": "認証する"}, "sbi_err_captcha_for_alibaba_cn": {"message": "確認が必要です。確認する写真をアップロードしてください。 ($video_tutorial$ を表示するか、<PERSON>ie をクリアしてみてください)", "placeholders": {"feedback": {"content": "$1"}, "video_tutorial": {"content": "$1"}}}, "sbi_err_captcha_for_aliprice": {"message": "異常なトラフィックです。確認してください"}, "sbi_err_captcha_for_taobao": {"message": "淘宝網は確認を求めています。手動で写真をアップロードし、検索して確認してください。 このエラーは、「TaoBao画像による検索」の新しい検証ポリシーが原因です。Taobao$feedback$では苦情の検証が多すぎることをお勧めします。", "placeholders": {"feedback": {"content": "$1"}}}, "sbi_err_captcha_for_taobao_feedback": {"message": "フィードバック"}, "sbi_err_captcha_msg": {"message": "検索制限を解除するために、$platform$はウェブサイトにアクセスして画像検索を実行するか、セキュリティ認証を完了することを要求します。", "placeholders": {"platform": {"content": "$1"}}}, "sbi_err_checking_if_low_version": {"message": "最新バージョンかどうかを確認してください"}, "sbi_err_cookie_btn_clear": {"message": "クッキーを消す"}, "sbi_err_cookie_for_alibaba_cn": {"message": "1688のCookieをクリアしてみますか？（再度ログインする必要があります）"}, "sbi_err_desperate_feature_pdd": {"message": "画像検索機能は、画像拡張子による Pinduoduo 検索に移動されました。"}, "sbi_err_hidden_skill_of_improve_search_rate": {"message": "画像検索の成功率を上げるには？"}, "sbi_err_img_undersize": {"message": "画像 > $imgMinimumSize$", "placeholders": {"imgMinimumSize": {"content": "$1"}}}, "sbi_err_login_required": {"message": "ログイン$loginSite$", "placeholders": {"loginSite": {"content": "$1"}}}, "sbi_err_login_required_action": {"message": "ログイン"}, "sbi_err_low_version": {"message": "最新バージョンをインストールします($latestVersion$)", "placeholders": {"latestVersion": {"content": "$1"}}}, "sbi_err_low_version_action": {"message": "ダウンロード"}, "sbi_err_need_help": {"message": "助けが必要"}, "sbi_err_network": {"message": "アクセスできません。接続を確認ください"}, "sbi_err_not_low_version": {"message": "最新バージョンがインストールされています（$latestVersion$）", "placeholders": {"latestVersion": {"content": "$1"}}}, "sbi_err_try_again": {"message": "再試行"}, "sbi_err_try_again_action": {"message": "再試行"}, "sbi_err_visit_and_try": {"message": "もう一度試すか、$website$ にアクセスして再試行してください", "placeholders": {"website": {"content": "$1"}}}, "sbi_err_visit_site": {"message": "$siteName$ ホームページにアクセス", "placeholders": {"siteName": {"content": "$1"}}}, "sbi_fail_tip": {"message": "読み込みに失敗しました。ページを更新して、もう一度お試しください。"}, "sbi_kuajing_filter_area": {"message": "領域"}, "sbi_kuajing_filter_au": {"message": "オーストラリア"}, "sbi_kuajing_filter_btn_confirm": {"message": "確認"}, "sbi_kuajing_filter_de": {"message": "ドイツ"}, "sbi_kuajing_filter_destination_country": {"message": "仕向国"}, "sbi_kuajing_filter_es": {"message": "スペイン"}, "sbi_kuajing_filter_estimate": {"message": "見積もり"}, "sbi_kuajing_filter_estimate_price": {"message": "推定価格"}, "sbi_kuajing_filter_estimate_price_formula": {"message": "推定価格式=（商品価格+国際物流貨物）/（1-利益率-その他のコスト比率）"}, "sbi_kuajing_filter_fr": {"message": "フランス"}, "sbi_kuajing_filter_kw_placeholder": {"message": "タイトルに一致するキーワードを入力してください"}, "sbi_kuajing_filter_logistics": {"message": "ロジスティクステンプレート"}, "sbi_kuajing_filter_logistics_china_post": {"message": "中国のポストの航空便"}, "sbi_kuajing_filter_logistics_discount": {"message": "物流割引"}, "sbi_kuajing_filter_logistics_epacket": {"message": "epacket"}, "sbi_kuajing_filter_logistics_price_formula": {"message": "国際物流貨物=（重量x送料+登録料）x（1-割引）"}, "sbi_kuajing_filter_others_fee": {"message": "その他の料金"}, "sbi_kuajing_filter_profit_percent": {"message": "利益率"}, "sbi_kuajing_filter_prop": {"message": "属性"}, "sbi_kuajing_filter_ru": {"message": "ロシア"}, "sbi_kuajing_filter_total": {"message": "$count$類似アイテムに一致", "placeholders": {"count": {"content": "$1"}}}, "sbi_kuajing_filter_uk": {"message": "イギリス"}, "sbi_kuajing_filter_usa": {"message": "アメリカ"}, "sbi_login_punish_title__pdd_pifa": {"message": "Pinduoduo卸売バージョン"}, "sbi_msg_no_result": {"message": "結果が見つかりません。$loginSite$にログインするか、別の画像を試してください", "placeholders": {"loginSite": {"content": "$1"}}}, "sbi_msg_no_result_check_support_page_l1": {"message": "Safariでは一時的にご利用いただけません。$supportPage$をご利用ください。", "placeholders": {"supportPage": {"content": "$1"}}}, "sbi_msg_no_result_check_support_page_l2": {"message": "Chromeブラウザとその拡張機能"}, "sbi_msg_no_result_reinstall_l1": {"message": "結果が見つかりません。$loginSite$にログインするか、別の画像を試すか、$latestExtUrl$を再インストールしてください。", "placeholders": {"loginSite": {"content": "$1"}, "latestExtUrl": {"content": "$2"}}}, "sbi_msg_no_result_reinstall_l2": {"message": "最新バージョン", "placeholders": {}}, "sbi_search_by_selected_area": {"message": "選択したエリア"}, "sbi_shipping_": {"message": "即日発送"}, "sbi_specify_category": {"message": "カテゴリを指定："}, "sbi_start_crop": {"message": "領域の選択"}, "sbi_store_name_alibabaCNKuaJing": {"message": "1688海外"}, "sbi_tutorial_btn_more": {"message": "使い方2"}, "sbi_tutorial_find_coupons_on_taobao": {"message": "タオバオでクーポン発見"}, "sbi_txt__empty_retry": {"message": "申し訳ありませんが、結果が見つかりません。もう一度お試しください。"}, "sbi_txt__min_order": {"message": "最小 注文"}, "sbi_visiting": {"message": "ブラウジング"}, "sbi_yiwugo__jiagexiangtan": {"message": "価格については販売者にお問い合わせください"}, "sbi_yiwugo__qigou": {"message": "$num$ピース（MOQ）", "placeholders": {"num": {"content": "$1"}}}, "sbi_yiwugo__xing": {"message": "出演者"}, "searchByImage_screenshot": {"message": "ワンクリックでスクリーンショット"}, "searchByImage_search": {"message": "ワンクリックで類似商品を検索"}, "searchByImage_size_type": {"message": "ファイルサイズは$num$MBを超えてはいけません。$type$のみ対応しています。", "placeholders": {"num": {"content": "$1"}, "type": {"content": "$2"}}}, "search_by_image_progress_analysing": {"message": "画像の分析"}, "search_by_image_progress_searching": {"message": "製品を探す"}, "search_by_image_progress_sending": {"message": "画像を送信しています"}, "search_by_image_response_rate": {"message": "回答率：このサプライヤーに連絡した購入者の$responseRate$は、$responseInHour$時間以内に回答を受け取りました。", "placeholders": {"responseRate": {"content": "$1"}, "responseInHour": {"content": "$2"}}}, "search_by_keyword": {"message": "キーワードで探す："}, "select_country_language_modal_title_country": {"message": "国"}, "select_country_language_modal_title_language": {"message": "言語"}, "select_country_region_modal_title": {"message": "国/地域を選択してください"}, "select_language_modal_title": {"message": "言語を選択してください："}, "select_shop": {"message": "店舗を選択"}, "sellers_count": {"message": "現在のページの販売者数"}, "sellers_count_per_page": {"message": "現在のページの販売者数"}, "service_score": {"message": "総合サービス評価"}, "set_shortcut_keys": {"message": "ショートカットキーを設定する"}, "setting_logo_title": {"message": "ショッピングアシスタント"}, "setting_modal_options_position_title": {"message": "プラグイン位置"}, "setting_modal_options_position_value_left": {"message": "左下"}, "setting_modal_options_position_value_right": {"message": "右下"}, "setting_modal_options_theme_title": {"message": "テーマカラー"}, "setting_modal_options_theme_value_dark": {"message": "暗い"}, "setting_modal_options_theme_value_light": {"message": "白い"}, "setting_modal_title": {"message": "設定"}, "setting_options_country_title": {"message": "国/地域"}, "setting_options_hover_zoom_desc": {"message": "マウスオーバーするとズームインします"}, "setting_options_hover_zoom_title": {"message": "ホバーズーム"}, "setting_options_jd_coupon_desc": {"message": "JD.comでクーポンが見つかりました"}, "setting_options_jd_coupon_title": {"message": "JD.comクーポン"}, "setting_options_language_title": {"message": "言語"}, "setting_options_price_drop_alert_desc": {"message": "お気に入り商品の価格が下がるとプッシュ通知が届きます。"}, "setting_options_price_drop_alert_title": {"message": "値下げアラート"}, "setting_options_price_history_on_list_page_desc": {"message": "製品検索ページに価格履歴を表示する"}, "setting_options_price_history_on_list_page_title": {"message": "価格履歴（一覧ページ）"}, "setting_options_price_history_on_produt_page_desc": {"message": "製品詳細ページに製品履歴を表示する"}, "setting_options_price_history_on_produt_page_title": {"message": "価格履歴（詳細ページ）"}, "setting_options_sales_analysis_desc": {"message": "$platforms$商品一覧ページの価格、販売量、販売者数、店舗販売率の統計をサポート", "placeholders": {"platforms": {"content": "$1"}}}, "setting_options_sales_analysis_title": {"message": "売上分析"}, "setting_options_save_success_msg": {"message": "成功"}, "setting_options_tacking_price_title": {"message": "価格変更アラート"}, "setting_options_value_off": {"message": "オフ"}, "setting_options_value_on": {"message": "オン"}, "setting_pkg_quick_view_desc": {"message": "サポート: 1688 & タオバオ"}, "setting_saved_message": {"message": "変更が正常に保存されました"}, "setting_section_enable_platform_title": {"message": "オンオフ"}, "setting_section_setting_title": {"message": "設定"}, "setting_section_shortcuts_title": {"message": "ショートカット"}, "settings_aliprice_agent__desc": {"message": "$platforms$ の商品詳細ページに表示されます", "placeholders": {"platforms": {"content": "$1"}}}, "settings_aliprice_agent__title": {"message": "私のために買ってください"}, "settings_copy_link__desc": {"message": "商品詳細ページに表示"}, "settings_copy_link__title": {"message": "コピーボタン＆タイトルサーチ"}, "settings_currency_desc__for_detail": {"message": "サポート1688製品詳細ページ"}, "settings_currency_desc__for_list": {"message": "画像で検索（海外1688/1688 /淘宝網を含む）"}, "settings_currency_desc__for_sbi": {"message": "価格を選択してください"}, "settings_currency_desc_display_for_list": {"message": "画像検索で表示（1688/1688海外/タオバオ含む）"}, "settings_currency_rate_desc": {"message": "\"$currencyRateFrom$\"からの為替レートの更新", "placeholders": {"currencyRateFrom": {"content": "$1"}}}, "settings_currency_rate_from_boc": {"message": "中国銀行"}, "settings_download_images__desc": {"message": "$platforms$ からの画像のダウンロードのサポート", "placeholders": {"platforms": {"content": "$1"}}}, "settings_download_images__title": {"message": "画像ダウンロードボタン"}, "settings_download_reviews__desc": {"message": "$platforms$ の商品詳細ページに表示されます", "placeholders": {"platforms": {"content": "$1"}}}, "settings_download_reviews__title": {"message": "レビュー画像をダウンロードする"}, "settings_google_translate_desc": {"message": "右クリックしてグーグル翻訳バーを取得"}, "settings_google_translate_title": {"message": "ウェブページの翻訳"}, "settings_historical_trend_desc": {"message": "商品一覧ページの画像の右下隅に表示"}, "settings_modal_btn_more": {"message": "その他の設定"}, "settings_productInfo_desc": {"message": "有効にするとパソコンの負荷が増え、ページが重くなる場合があります。動作に影響が出る場合は、無効にすることをおすすめします。"}, "settings_product_recommend__desc": {"message": "$platforms$ 製品詳細ページのメイン画像の下に表示されます", "placeholders": {"platforms": {"content": "$1"}}}, "settings_product_recommend__name": {"message": "おすすめの製品"}, "settings_research_desc": {"message": "商品リストページで詳細情報を表示する"}, "settings_sbi_add_to_list": {"message": "$listType$に追加します", "placeholders": {"listType": {"content": "$1"}}}, "settings_sbi_detail_page_icon_title_desc": {"message": "画像検索結果サムネイル"}, "settings_sbi_remove_from_list": {"message": "$listType$から削除します", "placeholders": {"listType": {"content": "$1"}}}, "settings_search_by_image_add_to_blacklist": {"message": "ブロックリストに追加"}, "settings_search_by_image_adjust_entrance_entrance": {"message": "入口位置を調整する"}, "settings_search_by_image_blacklist_desc": {"message": "ブラックリストのウェブサイトにアイコンを表示しない"}, "settings_search_by_image_blacklist_title": {"message": "ブロックリスト"}, "settings_search_by_image_bottom_left": {"message": "左下に表示"}, "settings_search_by_image_bottom_right": {"message": "右下に表示"}, "settings_search_by_image_clear_blacklist": {"message": "ブロックリストをクリア"}, "settings_search_by_image_detail_page_icon_title": {"message": "サムネイル"}, "settings_search_by_image_detail_page_icon_val_large": {"message": "大きい"}, "settings_search_by_image_detail_page_icon_val_small": {"message": "小さい"}, "settings_search_by_image_display_button_desc": {"message": "アイコンをワンクリックして画像で検索"}, "settings_search_by_image_display_button_title": {"message": "画像上のアイコン"}, "settings_search_by_image_sourece_websites_desc": {"message": "これらのWebサイトでソース製品を見つけてください"}, "settings_search_by_image_sourece_websites_title": {"message": "画像結果で検索"}, "settings_search_by_image_top_left": {"message": "左上に表示"}, "settings_search_by_image_top_right": {"message": "右上に表示"}, "settings_search_keyword_on_x__desc": {"message": "$platform$で単語検索", "placeholders": {"platform": {"content": "$1"}}}, "settings_search_keyword_on_x__title": {"message": "単語を選択すると、$platform$アイコン表示", "placeholders": {"platform": {"content": "$1"}}}, "settings_similar_products_desc": {"message": "これらのウェブサイトで同じ製品を見つけてみてください（最大5）。"}, "settings_similar_products_title": {"message": "同じ製品を探す"}, "settings_toolbar_expand_title": {"message": "プラグインの最小化"}, "settings_top_toolbar_desc": {"message": "ページ上部の検索バー"}, "settings_top_toolbar_title": {"message": "検索バー"}, "settings_translate_search_desc": {"message": "中国語に翻訳して検索します"}, "settings_translate_search_title": {"message": "マルチランサーチ"}, "settings_translator_contextmenu_title": {"message": "翻訳するためにキャプチャする"}, "settings_translator_title": {"message": "翻訳"}, "shai_xuan_dao_chu": {"message": "フィルター輸出"}, "shai_xuan_zi_duan": {"message": "絞り込み項目"}, "shang_jia_shi_jian": {"message": "オンシェルフ時間"}, "shang_pin_biao_ti": {"message": "商品名"}, "shang_pin_dui_bi": {"message": "製品比較"}, "shang_pin_lian_jie": {"message": "製品リンク"}, "shang_pin_xin_xi": {"message": "商品情報"}, "share_modal__content": {"message": "お友達と共有する"}, "share_modal__disable_for_while": {"message": "何も共有したくない"}, "share_modal__title": {"message": "あなたは$extensionName$が好きですか？", "placeholders": {"extensionName": {"content": "$1"}}}, "sheng_yu_ku_cun": {"message": "残り数量"}, "shi_fou_ke_ding_zhi": {"message": "カスタマイズ可能ですか?"}, "shi_fou_ren_zheng_gong_ying_sha": {"message": "認定サプライヤーの有無"}, "shi_fou_ren_zheng_gong_ying_shang_zong_shu": {"message": "認定サプライヤー"}, "shi_fou_you_mao_yi_dan_bao": {"message": "取引保証の有無"}, "shi_fou_you_mao_yi_dan_bao_zong_shu": {"message": "取引保証"}, "shipping_fee": {"message": "送料"}, "shop_followers": {"message": "ショップのフォロワー数"}, "shou_qi": {"message": "折りたたみ"}, "similar_products_warn_max_platforms": {"message": "最大5"}, "sku_calc_price": {"message": "計算後の価格"}, "sku_calc_price_settings": {"message": "価格計算設定"}, "sku_formula": {"message": "計算式"}, "sku_formula_desc": {"message": "計算式の説明"}, "sku_formula_desc_text": {"message": "複雑な数式に対応しており、元の価格を A、送料を B で表します。\n<br/>\n括弧 ()、加算 +、減算 -、乗算 *、除算 / をサポートします。\n<br/>\n例：\n<br/>\n1.元の価格を1.2倍し、送料を加える場合：A*1.2+B\n<br/>\n2.元の価格に1元を加え、1.2倍する場合：(A+1)*1.2\n<br/>\n3.元の価格に10元を加え、1.2倍し、さらに3元を引く場合：(A+10)*1.2-3"}, "sku_in_stock": {"message": "在庫あり"}, "sku_invalid_formula_format": {"message": "計算式の形式が正しくありません"}, "sku_inventory": {"message": "在庫数"}, "sku_link_copy_fail": {"message": "正常にコピーされました。SKU 仕様と属性は選択されていません"}, "sku_link_copy_success": {"message": "正常にコピーされ、SKU 仕様と属性が選択されました"}, "sku_list": {"message": "SKUリスト"}, "sku_min_qrder_qty": {"message": "最小注文数"}, "sku_name": {"message": "SKU名"}, "sku_no": {"message": "番号"}, "sku_original_price": {"message": "元の価格"}, "sku_price": {"message": "SKU価格"}, "stop_track_time_label": {"message": "追跡期限："}, "suo_zai_di_qu": {"message": "位置"}, "tab_pkg_quick_view": {"message": "物流追跡"}, "tab_product_details_price_history": {"message": "価格履歴"}, "tab_product_details_reviews": {"message": "写真のレビュー"}, "tab_product_details_seller_analysis": {"message": "売り手分析"}, "tab_product_details_similar_products": {"message": "同じ製品"}, "total_days_listed_per_product": {"message": "出品日数の合計 ÷ 製品数"}, "total_items": {"message": "商品の総数"}, "total_price_per_product": {"message": "価格の合計 ÷ 製品数"}, "total_rating_per_product": {"message": "評価の合計 ÷ 製品数"}, "total_revenue": {"message": "総収益"}, "total_revenue40_items": {"message": "現在のページの$amount$商品の総収益", "placeholders": {"amount": {"content": "$1"}}}, "total_sales": {"message": "総販売数"}, "total_sales40_items": {"message": "現在のページの$amount$商品の総販売数", "placeholders": {"amount": {"content": "$1"}}}, "track_for_12_months": {"message": "追跡期間：1年間"}, "track_for_3_months": {"message": "追跡期間：3か月"}, "track_for_6_months": {"message": "追跡期間：6か月"}, "tracking_price_email_add_btn": {"message": "メールを追加"}, "tracking_price_email_edit_btn": {"message": "メールを編集"}, "tracking_price_email_intro": {"message": "メールでお知らせします。"}, "tracking_price_email_invalid": {"message": "有効なメールアドレスを入力してください"}, "tracking_price_email_verified_desc": {"message": "これで、価格低下アラートを受け取ることができます。"}, "tracking_price_email_verified_title": {"message": "正常に確認されました"}, "tracking_price_email_verify_desc_line1": {"message": "確認リンクをあなたのメールアドレスに送信しました、"}, "tracking_price_email_verify_desc_line2": {"message": "メールの受信トレイを確認してください。"}, "tracking_price_email_verify_title": {"message": "Eメールを確認します"}, "tracking_price_web_push_notification_intro": {"message": "デスクトップ：AliPriceは、製品を追跡し、価格変更後にWebプッシュ通知を送ることができる。"}, "tracking_price_web_push_notification_title": {"message": "Webプッシュ通知"}, "translate_im__login_required": {"message": "AliPrice によって翻訳されました。$loginUrl$ にログインしてください。", "placeholders": {"loginUrl": {"content": "$1"}}}, "translate_im__paste_fail": {"message": "翻訳されてクリップボードにコピーされましたが、<PERSON><PERSON><PERSON> の制限により、手動で貼り付ける必要があります。"}, "translate_im__send": {"message": "翻訳＆送信"}, "translate_search": {"message": "翻訳して検索します"}, "translation_originals_translated": {"message": "原文と訳文"}, "translation_translated": {"message": "訳文のみ"}, "translator_btn_capture_txt": {"message": "翻訳"}, "translator_language_auto_detect": {"message": "自動検出"}, "translator_language_detected": {"message": "検出されました"}, "translator_language_search_placeholder": {"message": "言語検索"}, "try_again": {"message": "再試行"}, "tu_pian_chi_cun": {"message": "画像サイズ:"}, "tu_pian_lian_jie": {"message": "画像リンク"}, "tui_huan_ti_yan": {"message": "返品交換"}, "tui_huan_ti_yan__desc": {"message": "評価販売元アフターサービス指標"}, "tutorial__show_all": {"message": "すべての機能"}, "tutorial_ae_popup_title": {"message": "拡張機能を固定し、Aliexpress を開きます"}, "tutorial_aliexpress_reviews_analysis": {"message": "AliExpressによる分析のレビュ"}, "tutorial_aliprice_agent_with1688_desc_l1": {"message": "サポート JPY"}, "tutorial_aliprice_agent_with1688_desc_l2": {"message": "韓国/日本/中国本土への配送"}, "tutorial_aliprice_agent_with1688_title": {"message": "1688は海外購入をサポートします"}, "tutorial_auto_apply_coupon_title": {"message": "クーポンの自動適用"}, "tutorial_btn_end": {"message": "完了"}, "tutorial_btn_example": {"message": "例"}, "tutorial_btn_have_a_try": {"message": "さて、試してみる"}, "tutorial_btn_next": {"message": "次に"}, "tutorial_btn_see_more": {"message": "もっと見る"}, "tutorial_compare_products": {"message": "商品比較"}, "tutorial_currency_convert_title": {"message": "通貨換算"}, "tutorial_export_shopping_cart": {"message": "CSVでエクスポート、タオバオと1688をサポートします"}, "tutorial_export_shopping_cart_title": {"message": "カートのエクスポート"}, "tutorial_price_history_pro": {"message": "商品詳細ページに表示されます\nShopee、Lazada、Amazon、Ebayに対応"}, "tutorial_price_history_pro_title": {"message": "1年の価格履歴と注文履歴"}, "tutorial_sbi_context_menu_screenshot_title": {"message": "画像で検索するためのキャプチャ"}, "tutorial_translate_search": {"message": "翻訳して検索"}, "tutorial_translate_search_and_package_tracking": {"message": "翻訳検索と宅配追跡"}, "unit_bao": {"message": "個"}, "unit_ben": {"message": "個"}, "unit_bi": {"message": "注文"}, "unit_chuang": {"message": "個"}, "unit_dai": {"message": "個"}, "unit_dui": {"message": "ペア"}, "unit_fen": {"message": "個"}, "unit_ge": {"message": "個"}, "unit_he": {"message": "個"}, "unit_jian": {"message": "個"}, "unit_li_fang_mi": {"message": "m³"}, "unit_ping": {"message": "個"}, "unit_ping_fang_mi": {"message": "㎡"}, "unit_shuang": {"message": "ペア"}, "unit_tai": {"message": "個"}, "unit_ti": {"message": "個"}, "unit_tiao": {"message": "個"}, "unit_xiang": {"message": "個"}, "unit_zhang": {"message": "個"}, "unit_zhi": {"message": "個"}, "verify_contact_support": {"message": "カスタマーサービス"}, "verify_human_verification": {"message": "ロボット確認"}, "verify_unusual_access": {"message": "ご利用になれません"}, "view_history_clean_all": {"message": "すべてクリーン"}, "view_history_clean_all_warring": {"message": "表示されたすべてのレコードを消去しますか？"}, "view_history_clean_all_warring_title": {"message": "警告"}, "view_history_viewd": {"message": "閲覧"}, "website": {"message": "Webサイト"}, "weight": {"message": "重量"}, "wu_fa_huo_qu_dao_gai_shu_ju": {"message": "データ取得できません"}, "wu_liu_shi_xiao": {"message": "配達適時性"}, "wu_liu_shi_xiao__desc": {"message": "販売元の48h出荷率と遵守率"}, "xia_dan_jia": {"message": "注文価格"}, "xian_xuan_ze_product_attributes": {"message": "まず、商品属性を選択してください\n"}, "xiao_liang": {"message": "販売量"}, "xiao_liang_zhan_bi": {"message": "販売数比率"}, "xiao_shi": {"message": "$num$ 時間", "placeholders": {"num": {"content": "$1"}}}, "xiao_shou_e": {"message": "売上高"}, "xiao_shou_e_zhan_bi": {"message": "売上高比率"}, "xuan_zhong_x_tiao_ji_lu": {"message": "$amount$つのレコードを選択します", "placeholders": {"amoun": {"content": "$1"}, "amount": {"content": "$1"}}}, "yan_xuan": {"message": "おすすめ"}, "yi_ding_zai_zuo_ce": {"message": "ピン留め"}, "yi_jia_zai_wan_suo_you_shang_pin": {"message": "すべての商品が読み込まれました"}, "yi_nian_xiao_liang": {"message": "年間販売数"}, "yi_nian_xiao_liang_zhan_bi": {"message": "年間販売数比率"}, "yi_nian_xiao_shou_e": {"message": "年間売上高"}, "yi_nian_xiao_shou_e_zhan_bi": {"message": "年間売上高比率"}, "yi_shua_xin": {"message": "再読み込み成功"}, "yin_cang_xiang_tong_dian": {"message": "類似点を隠す"}, "you_xiao_liang": {"message": "販売量あり"}, "yu_ji_dao_da_shi_jian": {"message": "到着予定時間"}, "yuan_gong_ren_shu": {"message": "就業者数"}, "yue_cheng_jiao": {"message": "月間取引量"}, "yue_dai_xiao": {"message": "ドロップシッピング"}, "yue_dai_xiao__desc": {"message": "過去30日間のドロップシッピング販売量"}, "yue_dai_xiao_pai_xu__desc": {"message": "過去30日間のドロップシッピング販売量を高い順に並びます"}, "yue_xiao_liang__desc": {"message": "過去 30 日間の販売量"}, "zhan_kai": {"message": "展開する"}, "zhe_kou": {"message": "割引率"}, "zhi_chi_yi_jian_dai_fa": {"message": "ドロップシッピング サポート"}, "zhi_chi_yi_jian_dai_fa_bao_you": {"message": "送料無料(ドロップシッピング 送料無料)"}, "zhi_fu_ding_dan_shu": {"message": "支払い注文数"}, "zhi_fu_ding_dan_shu__desc": {"message": "過去30日間の注文数"}, "zhu_ce_xing_zhi": {"message": "登録性"}, "zi_ding_yi_tiao_jian": {"message": "カスタム条件"}, "zi_duan": {"message": "フィールド"}, "zi_ti_xiao_liang": {"message": "子ASIN販売数"}, "zong_he_fu_wu_fen": {"message": "総合レビュー"}, "zong_he_fu_wu_fen__desc": {"message": "販売元サービスのレビュー"}, "zong_he_fu_wu_fen__short": {"message": "評価"}, "zong_he_ti_yan_fen": {"message": "評価"}, "zong_he_ti_yan_fen_3": {"message": "星 4 つ未満"}, "zong_he_ti_yan_fen_4": {"message": "4 ～ 4.5 つ星"}, "zong_he_ti_yan_fen_4_5": {"message": "星 4.5 ～ 5.0"}, "zong_he_ti_yan_fen_5": {"message": "5つ星"}, "zong_ku_cun": {"message": "総在庫数"}, "zong_xiao_liang": {"message": "総売上高"}, "zui_jin_30D_3Min_xiang_ying_lv": {"message": "過去 30 日間の 3 分応答率"}, "zui_jin_30D_48H_lan_shou_lv": {"message": "過去 30 日間の 48 時間の回復率"}, "zui_jin_30D_48H_lv_yue_lv": {"message": "過去 30 日間の 48 時間のパフォーマンス率"}, "zui_jin_30D_jiao_yi_ji_lu": {"message": "過去30日間の取引記録"}, "zui_jin_30D_jiao_yi_ji_lu__desc": {"message": "過去30日間の取引記録"}, "zui_jin_30D_jiu_fen_lv": {"message": "過去 30 日間の紛争発生率"}, "zui_jin_30D_pin_zhi_tui_kuan_lv": {"message": "過去 30 日間の品質払い戻し率"}, "zui_jin_30D_zhi_fu_ding_dan_shu": {"message": "過去 30 日間の支払い注文の数"}}