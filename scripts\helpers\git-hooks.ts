/**
 * @fileoverview Git Hooks 的核心逻辑
 * @description 该脚本由 husky 在 post-merge 和 post-checkout 事件中调用。
 *              它会检查两次 HEAD 之间的文件变更，如果变更涉及关键路径，则自动清理构建缓存。
 */

import { execSync } from 'child_process';
import { GIT_HOOK_CRITICAL_PATHS } from './constants.js';
import { cleanExtensionConfigs } from '../extension-config-manager/utils.js';
import { createLogger } from './logger.js';

const logger = createLogger('GitHook');

/**
 * 检查上一个 HEAD 和当前 HEAD 之间是否有关键文件发生变更。
 * @returns {boolean} 如果有关键文件变更则返回 true，否则返回 false。
 */
function hasCriticalFileChanges(): boolean {
  try {
    // 获取上一个 HEAD 和当前 HEAD 之间发生变化的文件列表
    // HEAD@{1} 是指 "HEAD 的上一个位置"，这能可靠地捕获 checkout 和 merge 操作前后的状态
    const changedFiles = execSync('git diff --name-only HEAD@{1} HEAD', { encoding: 'utf-8' });

    if (!changedFiles.trim()) {
      logger.info('No file changes detected between previous and current HEAD.');
      return false;
    }

    const changedFilesList = changedFiles.trim().split('\n');
    logger.info(`Detected file changes: \n${changedFilesList.join('\n')}`);

    for (const path of GIT_HOOK_CRITICAL_PATHS) {
      if (changedFilesList.some((file) => file.startsWith(path))) {
        logger.info(`Critical change detected in path starting with: "${path}"`);
        return true;
      }
    }

    logger.info('No critical file changes detected.');
    return false;
  } catch (error) {
    // 如果 git diff 命令失败（例如在一个全新的仓库中），我们保守地认为需要清理缓存。
    logger.error(
      'Error checking for file changes, proceeding with cache invalidation as a precaution.',
      error,
    );
    return true;
  }
}

/**
 * 主执行函数
 */
async function run() {
  logger.info(
    'Running Git Hook: Checking for critical file changes to determine cache validity...',
  );
  if (hasCriticalFileChanges()) {
    logger.info('Critical file changes detected. Invalidating build cache...');
    await cleanExtensionConfigs();
    logger.success('Build cache invalidated successfully.');
  } else {
    logger.success('No critical changes detected. Build cache remains valid.');
  }
}

run();
