#!/usr/bin/env node
import { excel2i18n, i18n2excel } from './convert-excel-and-messages.js';
import { updateI18n, commonTransforms } from './updateI18n.js';

/**
 * i18n 管理工具的命令行接口
 *
 * 提供 Excel 和 JSON 语言包文件之间的转换功能：
 * - excel2i18n: 将 Excel 文件转换为 JSON 语言包
 * - i18n2excel: 将 JSON 语言包聚合到 Excel 文件
 * - updateI18n: 批量更新 JSON 语言包文件
 */

import path from 'path';
import { projectPaths } from '../helpers/utils.js';

/**
 * 获取语言包目录的绝对路径
 * @param p - 路径参数
 * @returns 绝对路径
 */
const getLocalesDir = (p: string): string => {
  if (p === 'locales') {
    return projectPaths.sharedLocales;
  }
  if (!/[\\/\\]+/.test(p)) {
    return path.join(projectPaths.extensions, p, 'locales');
  }
  return p;
};

/**
 * 主函数 - 执行 i18n 工具操作
 */
// excel 转换为 i18n
async function excel2i18nMain() {
  await excel2i18n(getLocalesDir('locales'), {
    autoAddNewLocale: false, // 是否自动添加新语言
    sheetName: 'translate-2025-06-30', // Excel 工作表名称
  });
}

// i18n 转换为 excel
async function i18n2excelMain() {
  await i18n2excel(getLocalesDir('locales'), {
    sourceLocale: 'en',
    includeKeys: ['EXTENSION_NAME', 'EXTENSION_DESCRIPTION', 'context_menu_screenshot_search'],
    excludeKeys: [],
    duplicateSheetAction: 'overwrite',
  });
}

// 批量更新 i18n 文件
async function updateI18nMain() {
  await updateI18n(getLocalesDir('locales'), (key, value, locale) => {
    // return commonTransforms.updateValue('EXTENSION_NAME', '')(key, value, locale);
    return [key, value];
  });
}

// excel2i18nMain();
// i18n2excelMain();
// updateI18nMain();
