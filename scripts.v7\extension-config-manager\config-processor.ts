/**
 * @fileoverview 扩展配置管理系统 v7 - 第一阶段配置处理器
 * @description 处理原始的 extension.config.ts 配置，进行基础验证、配置合并和自动字段生成
 */

import type {
  ExtensionConfig,
  VariantConfig,
  SimpleProcessedVariantConfig,
  ValidationResult,
  ManifestVersionType,
  WebstoreType,
  VariantType,
} from './types.js';
import {
  scanLocalesDirectory,
  extensionExists,
  mergeI18nConfig,
  mergeManifestConfig,
  generateVariantTarget,
  generateVariantChannel,
  generateWebstoreCN,
} from './utils.js';

// #region --- 配置验证 ---

/**
 * 验证扩展配置的基本结构和必填字段
 * @param config 扩展配置对象
 * @returns 验证结果
 */
export function validateExtensionConfig(config: ExtensionConfig): ValidationResult {
  const errors: string[] = [];
  const warnings: string[] = [];

  // 验证必填字段
  if (!config.name || typeof config.name !== 'string') {
    errors.push('插件名称 (name) 是必填字段且必须是字符串');
  } else if (!/^[a-z0-9_-]+$/.test(config.name)) {
    errors.push('插件名称只能包含小写字母、数字、下划线和连字符');
  }

  if (!config.version || typeof config.version !== 'string') {
    errors.push('插件版本 (version) 是必填字段且必须是字符串');
  } else if (!/^\d+\.\d+\.\d+$/.test(config.version)) {
    warnings.push('建议使用语义化版本格式 (如: 1.0.0)');
  }

  if (!config.variants || !Array.isArray(config.variants) || config.variants.length === 0) {
    errors.push('变体配置 (variants) 是必填字段且必须是非空数组');
  }

  // 验证可选字段的类型
  if (config.manifestVersion !== undefined && ![2, 3].includes(config.manifestVersion)) {
    errors.push('manifestVersion 必须是 2 或 3');
  }

  if (config.defaultLocale !== undefined && typeof config.defaultLocale !== 'string') {
    errors.push('defaultLocale 必须是字符串');
  }

  // 检查插件是否存在
  if (config.name && !extensionExists(config.name)) {
    warnings.push(`插件目录不存在: packages/extensions/${config.name}`);
  }

  return {
    isValid: errors.length === 0,
    errors,
    warnings,
  };
}

/**
 * 验证单个变体配置
 * @param variant 变体配置对象
 * @param index 变体在数组中的索引（用于错误提示）
 * @returns 验证结果
 */
export function validateVariantConfig(variant: VariantConfig, index: number): ValidationResult {
  const errors: string[] = [];
  const warnings: string[] = [];
  const prefix = `变体 [${index}]`;

  // 验证必填字段
  if (!variant.variantId || typeof variant.variantId !== 'string') {
    errors.push(`${prefix}: variantId 是必填字段且必须是字符串`);
  }

  if (!variant.variantName || typeof variant.variantName !== 'string') {
    errors.push(`${prefix}: variantName 是必填字段且必须是字符串`);
  }

  if (!variant.variantType || typeof variant.variantType !== 'string') {
    errors.push(`${prefix}: variantType 是必填字段且必须是字符串`);
  } else {
    const validTypes: VariantType[] = ['master', 'tm', 'tmBeta', 'dba', 'offline'];
    if (!validTypes.includes(variant.variantType as VariantType)) {
      errors.push(`${prefix}: variantType 必须是以下值之一: ${validTypes.join(', ')}`);
    }
  }

  if (!variant.webstore || typeof variant.webstore !== 'string') {
    errors.push(`${prefix}: webstore 是必填字段且必须是字符串`);
  } else {
    const validWebstores: WebstoreType[] = [
      'chrome',
      'firefox',
      'browser360',
      'safari',
      'adspower',
      'opera',
      'edge',
    ];
    if (!validWebstores.includes(variant.webstore as WebstoreType)) {
      errors.push(`${prefix}: webstore 必须是以下值之一: ${validWebstores.join(', ')}`);
    }
  }

  // 验证可选字段的类型
  if (variant.manifestVersion !== undefined && ![2, 3].includes(variant.manifestVersion)) {
    errors.push(`${prefix}: manifestVersion 必须是 2 或 3`);
  }

  if (variant.defaultLocale !== undefined && typeof variant.defaultLocale !== 'string') {
    errors.push(`${prefix}: defaultLocale 必须是字符串`);
  }

  if (variant.webstoreId !== undefined && typeof variant.webstoreId !== 'string') {
    errors.push(`${prefix}: webstoreId 必须是字符串`);
  }

  if (variant.webstoreUrl !== undefined && typeof variant.webstoreUrl !== 'string') {
    errors.push(`${prefix}: webstoreUrl 必须是字符串`);
  }

  if (variant.measurementId !== undefined && typeof variant.measurementId !== 'string') {
    errors.push(`${prefix}: measurementId 必须是字符串`);
  }

  return {
    isValid: errors.length === 0,
    errors,
    warnings,
  };
}

/**
 * 验证必填字段的完整性
 * @param config 扩展配置对象
 * @returns 验证结果
 */
export function validateRequiredFields(config: ExtensionConfig): ValidationResult {
  const errors: string[] = [];
  const warnings: string[] = [];

  // 检查变体配置中的重复 variantId
  const variantIds = config.variants.map((v) => v.variantId);
  const duplicateIds = variantIds.filter((id, index) => variantIds.indexOf(id) !== index);
  if (duplicateIds.length > 0) {
    errors.push(`发现重复的 variantId: ${[...new Set(duplicateIds)].join(', ')}`);
  }

  // 检查变体配置中的重复 variantTarget（需要先生成）
  const variantTargets: string[] = [];
  for (const variant of config.variants) {
    if (variant.variantType && variant.webstore) {
      const manifestVersion = variant.manifestVersion || config.manifestVersion || 3;
      const target = generateVariantTarget(
        variant.webstore as WebstoreType,
        manifestVersion as ManifestVersionType,
        variant.variantType as VariantType,
      );
      if (variantTargets.includes(target)) {
        errors.push(`发现重复的 variantTarget: ${target}`);
      }
      variantTargets.push(target);
    }
  }

  return {
    isValid: errors.length === 0,
    errors,
    warnings,
  };
}

// #endregion

// #region --- 配置处理 ---

/**
 * 处理基础配置，设置默认值
 * @param config 原始扩展配置
 * @returns 处理后的基础配置
 */
export function processBaseConfig(config: ExtensionConfig): {
  name: string;
  version: string;
  manifestVersion: ManifestVersionType;
  defaultLocale: string;
  measurementId?: string;
} {
  return {
    name: config.name,
    version: config.version,
    manifestVersion: config.manifestVersion || 3,
    defaultLocale: config.defaultLocale || 'en',
    measurementId: config.measurementId,
  };
}

/**
 * 自动填充语言列表
 * @param extensionName 插件名称
 * @param i18nConfig i18n 配置
 * @returns 自动扫描的语言列表
 */
export async function autoFillLocales(
  extensionName: string,
  i18nConfig: any = {},
): Promise<string[]> {
  // 如果已经手动指定了 locales，则使用手动指定的
  if (i18nConfig.locales && Array.isArray(i18nConfig.locales) && i18nConfig.locales.length > 0) {
    return i18nConfig.locales;
  }

  // 否则自动扫描 locales 目录
  return await scanLocalesDirectory(extensionName);
}

/**
 * 生成变体的自动字段
 * @param variant 变体配置
 * @param manifestVersion 最终确定的 manifest 版本
 * @returns 包含自动生成字段的对象
 */
export function generateAutoFields(variant: VariantConfig, manifestVersion: ManifestVersionType) {
  const variantTarget = generateVariantTarget(
    variant.webstore as WebstoreType,
    manifestVersion,
    variant.variantType as VariantType,
  );

  const variantChannel = generateVariantChannel(
    variant.webstore as WebstoreType,
    variant.variantType as VariantType,
  );

  const webstoreCN = generateWebstoreCN(variant.webstore as WebstoreType);

  return {
    variantTarget,
    variantChannel,
    webstoreCN,
  };
}

/**
 * 处理单个变体配置
 * @param baseConfig 基础配置
 * @param variant 变体配置
 * @param autoScannedLocales 自动扫描的语言列表
 * @returns 处理后的变体配置
 */
export async function processVariantConfig(
  baseConfig: ReturnType<typeof processBaseConfig>,
  variant: VariantConfig,
  autoScannedLocales: string[],
): Promise<SimpleProcessedVariantConfig> {
  // 确定最终的配置值（变体配置覆盖基础配置）
  const finalManifestVersion = variant.manifestVersion || baseConfig.manifestVersion;
  const finalDefaultLocale = variant.defaultLocale || baseConfig.defaultLocale;
  const finalMeasurementId = variant.measurementId || baseConfig.measurementId;

  // 生成自动字段
  const autoFields = generateAutoFields(variant, finalManifestVersion);

  // 合并配置
  const mergedI18n = mergeI18nConfig(
    baseConfig.name ? { locales: autoScannedLocales } : {},
    variant.i18n,
    autoScannedLocales,
  );

  const mergedManifest = mergeManifestConfig(
    {}, // 基础 manifest 配置暂时为空，后续可以从 config.manifest 传入
    variant.manifest,
  );

  return {
    name: baseConfig.name,
    version: baseConfig.version,
    manifestVersion: finalManifestVersion,
    defaultLocale: finalDefaultLocale,

    variantId: variant.variantId,
    variantName: variant.variantName,
    variantType: variant.variantType as VariantType,
    variantChannel: autoFields.variantChannel,
    variantTarget: autoFields.variantTarget,
    webstoreCN: autoFields.webstoreCN,
    webstore: variant.webstore as WebstoreType,

    webstoreId: variant.webstoreId,
    webstoreUrl: variant.webstoreUrl,
    measurementId: finalMeasurementId,

    i18n: mergedI18n,
    manifest: mergedManifest,
  };
}

// #endregion

// #region --- 主处理函数 ---

/**
 * 第一阶段主入口函数：处理原始配置并返回简单处理后的变体配置
 * @param config 原始扩展配置
 * @returns 以 variantTarget 为键的处理后变体配置对象
 */
export async function defineExtensionConfig(
  config: ExtensionConfig,
): Promise<Record<string, SimpleProcessedVariantConfig>> {
  // 1. 验证输入配置
  const configValidation = validateExtensionConfig(config);
  if (!configValidation.isValid) {
    throw new Error(`配置验证失败:\n${configValidation.errors.join('\n')}`);
  }

  // 输出警告信息
  if (configValidation.warnings.length > 0) {
    console.warn('配置警告:\n', configValidation.warnings.join('\n'));
  }

  // 2. 验证变体配置
  for (let i = 0; i < config.variants.length; i++) {
    const variantValidation = validateVariantConfig(config.variants[i], i);
    if (!variantValidation.isValid) {
      throw new Error(`变体配置验证失败:\n${variantValidation.errors.join('\n')}`);
    }
    if (variantValidation.warnings.length > 0) {
      console.warn(`变体配置警告:\n${variantValidation.warnings.join('\n')}`);
    }
  }

  // 3. 验证必填字段和重复性
  const requiredFieldsValidation = validateRequiredFields(config);
  if (!requiredFieldsValidation.isValid) {
    throw new Error(`必填字段验证失败:\n${requiredFieldsValidation.errors.join('\n')}`);
  }

  // 4. 处理基础配置
  const baseConfig = processBaseConfig(config);

  // 5. 自动扫描语言文件
  const autoScannedLocales = await autoFillLocales(config.name, config.i18n);

  // 6. 处理每个变体配置
  const result: Record<string, SimpleProcessedVariantConfig> = {};

  for (const variant of config.variants) {
    const processedVariant = await processVariantConfig(baseConfig, variant, autoScannedLocales);
    result[processedVariant.variantTarget] = processedVariant;
  }

  return result;
}

// #endregion
