import {
  defineExtensionConfig,
  getBaseExtensionConfig,
} from '../../../scripts/extension-config-manager';

const extensionConfig = defineExtensionConfig({
  ...getBaseExtensionConfig(),
  name: 'cookies_manager',
  version: '3.4.9',
  manifestVersion: 3,
  i18n: {
    locales: [],
  },

  variants: [
    {
      variantId: '1077',
      variantName: '主渠道',
      variantType: 'master',
      webstore: 'chrome',
      webstoreId: 'fgfdgrifnekkkcbapdfandpixbdfhh',
      measurementId: 'G-PJYG13CSDE',
      defaultLocale: 'zh_TW',
    },
    {
      variantId: '10353',
      variantName: '天猫渠道',
      variantType: 'tm',
      webstore: 'chrome',
      webstoreId: 'aadbahhifnekkkcbapdfandpimaoacmj',
      measurementId: 'G-PJYG13CDGZ',
      manifest: {},
    },
  ],
});

// console.log(JSON.stringify(extensionConfig, null, 2));
export default extensionConfig;
