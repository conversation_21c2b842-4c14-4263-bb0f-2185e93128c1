#!/usr/bin/env node

/**
 * 变更日志发布系统命令行接口
 *
 * 该文件定义了发布系统的 CLI 接口，提供以下功能：
 * - preview 命令：预览指定插件的待发布变更日志
 * - run 命令：执行完整的发布流程
 * - 支持详细输出模式、预演模式、强制发布等选项
 * - 提供完整的帮助信息和使用示例
 *
 * 使用方式：
 * - 预览变更：release-cli preview <extensionName>
 * - 执行发布：release-cli run [options]
 * - 查看帮助：release-cli --help
 *
 * 工作流程：
 * 1. 开发者提交代码时，需遵循特定格式，包含 Issue-ID 和 Applies-To
 * 2. 发布前，从 Excel 导出 issue 状态到项目根目录的 'issue_status.json' 文件
 * 3. 运行 'run' 命令，脚本将自动生成发布计划
 * 4. 确认计划后，脚本将自动更新版本号、生成 changelog、提交并打上 git 标签
 */

import yargs from 'yargs';
import { hideBin } from 'yargs/helpers';
import { Logger } from '../helpers/logger.js';
import { previewChangelog } from './preview.js';
import { runRelease } from './release.js';

yargs(hideBin(process.argv))
  .scriptName('release-cli')
  .usage(
    `用法: $0 <command> [options]

插件发布与变更日志管理工具`,
  )
  .option('verbose', {
    alias: 'V',
    type: 'boolean',
    describe: '显示详细输出信息',
    default: false,
    global: true,
  })
  .middleware((argv) => {
    Logger.setVerbose(argv.verbose);
  })
  .example('$0 preview cookies_manager', '预览 cookies_manager 插件的待发布变更')
  .example('$0 run', '自动检测所有插件并执行发布流程')
  .example('$0 run --dry-run', '预演发布流程，不实际修改任何文件')
  .example('$0 run --include cookies_manager', '强制发布 cookies_manager，即使未检测到变更')
  .example(
    '$0 run --set-version cookies_manager@2.1.0',
    '手动指定 cookies_manager 的发布版本为 2.1.0',
  )
  .example('$0 run --force', '强制发布，忽略未完成的 issue 状态警告')
  .command(
    'preview <extensionName>',
    '预览指定插件的待发布变更日志',
    (yargs) => {
      return yargs.positional('extensionName', {
        describe: '要预览的插件名 (例如: cookies_manager)',
        type: 'string',
        demandOption: true,
      });
    },
    async (argv) => {
      await previewChangelog(argv.extensionName);
    },
  )
  .command(
    'run',
    '执行发布流程',
    (yargs) => {
      return yargs
        .option('dry-run', {
          type: 'boolean',
          describe: '预演发布流程，不写入文件或创建 git 标签',
          default: false,
        })
        .option('force', {
          type: 'boolean',
          describe: '强制发布，忽略 issue 状态警告',
          default: false,
        })
        .option('include', {
          type: 'string',
          describe: '强制包含某个插件在发布计划中',
        })
        .option('set-version', {
          type: 'string',
          describe: '手动指定插件版本 (格式: <extension>@<version>)',
          array: true,
        });
    },
    async (argv) => {
      await runRelease(argv);
    },
  )
  .demandCommand(1, '请至少提供一个命令 (preview 或 run)')
  .strict()
  .help('help', '显示帮助信息')
  .alias('help', 'h')
  .version('1.0.0')
  .alias('version', 'v')
  .epilog(
    `工作流程说明:
  1. 开发者提交代码时，需遵循特定格式，包含 Issue-ID 和 Applies-To。
  2. 发布前，从 Excel 导出 issue 状态到项目根目录的 'issue_status.json' 文件。
  3. 运行 'run' 命令，脚本将自动生成发布计划。
  4. 确认计划后，脚本将自动更新版本号、生成 changelog、提交并打上 git 标签。`,
  )
  .parse();
