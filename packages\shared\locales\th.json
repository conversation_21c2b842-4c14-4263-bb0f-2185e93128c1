{"EXTENSION_NAME": {"message": "TODO: EXTENSION_NAME"}, "EXTENSION_DESCRIPTION": {"message": "TODO: EXTENSION_DESCRIPTION"}, "1688_cross_border_hot_selling": {"message": "1688 จุดขายสินค้าข้ามพรมแดนยอดนิยม"}, "1688_shi_li_ren_zheng": {"message": "การรับรองความแข็งแกร่ง 1688"}, "1_jian_qi_pi": {"message": "MOQ: 1"}, "1year_yi_shang": {"message": "มากกว่า 1 ปี"}, "24H": {"message": "24H"}, "24H_fa_huo": {"message": "จัดส่งภายใน 24 ชั่วโมง"}, "24H_lan_shou_lv": {"message": "อัตราการบรรจุตลอด 24 ชั่วโมง"}, "30D_shang_xin": {"message": "สินค้ามาใหม่รายเดือน"}, "30d_sales": {"message": "$amount$ ขายไปแล้วใน 30 วัน", "placeholders": {"amount": {"content": "$1"}}}, "3Min_xiang_ying_lv": {"message": "ตอบกลับภายใน 3 นาที"}, "3Min_xiang_ying_lv__desc": {"message": "สัดส่วนการตอบกลับข้อความสอบถามผู้ซื้ออย่างมีประสิทธิภาพภายใน 3 นาทีในช่วง 30 วันที่ผ่านมา"}, "48H": {"message": "48ชม"}, "48H_fa_huo": {"message": "จัดส่งภายใน 48 ชั่วโมง"}, "48H_lan_shou_lv": {"message": "อัตราการบรรจุ 48 ชั่วโมง"}, "48H_lan_shou_lv__desc": {"message": "อัตราส่วนของหมายเลขคำสั่งซื้อที่รับภายใน 48 ชั่วโมงต่อจำนวนคำสั่งซื้อทั้งหมด"}, "48H_lv_yue_lv": {"message": "อัตราประสิทธิภาพ 48 ชั่วโมง"}, "48H_lv_yue_lv__desc": {"message": "อัตราส่วนของหมายเลขคำสั่งซื้อที่รับหรือจัดส่งภายใน 48 ชั่วโมงต่อจำนวนคำสั่งซื้อทั้งหมด"}, "72H": {"message": "72ชม"}, "7D_shang_xin": {"message": "สินค้ามาใหม่รายสัปดาห์"}, "7D_wu_li_you": {"message": "7 วัน ไร้กังวล"}, "ABS_title_text": {"message": "รายการนี้มีเรื่องราวของแบรนด์"}, "AC_title_text": {"message": "รายการนี้มีป้าย Amazon's Choice"}, "A_title_text": {"message": "รายการนี้มีหน้าเนื้อหา A+"}, "BS_title_text": {"message": "รายการนี้มีอันดับเป็นสินค้าขายดี $num$ ในหมวดหมู่ $type$", "placeholders": {"type": {"content": "$1"}, "num": {"content": "$2"}}}, "LTD_title_text": {"message": "LTD (ข้อเสนอจำกัดเวลา) หมายความว่ารายการนี้เป็นส่วนหนึ่งของกิจกรรม \"โปรโมชัน 7 วัน\""}, "NR_title_text": {"message": "รายการนี้มีอันดับเป็นสินค้าใหม่ $num$ ในหมวดหมู่ $type$", "placeholders": {"type": {"content": "$1"}, "num": {"content": "$2"}}}, "SBV_title_text": {"message": "รายการนี้มีโฆษณาวิดีโอ ซึ่งเป็นประเภทของโฆษณา PPC ที่มักจะปรากฏตรงกลางผลการค้นหา"}, "SB_title_text": {"message": "รายการนี้มีโฆษณาแบรนด์ ซึ่งเป็นประเภทของโฆษณา PPC ที่มักจะปรากฏที่ด้านบนหรือด้านล่างของผลการค้นหา"}, "SP_title_text": {"message": "รายการนี้มีโฆษณาผลิตภัณฑ์ที่ได้รับการสนับสนุน"}, "V_title_text": {"message": "รายการนี้มีวิดีโอแนะนำ"}, "advanced_research": {"message": "การวิจัยขั้นสูง"}, "agent_ds1688___my_order": {"message": "คำสั่งซื้อของฉัน"}, "agent_ds1688__add_to_cart": {"message": "ซื้อจากต่างประเทศ"}, "agent_ds1688__cart": {"message": "ตะกร้าสินค้า"}, "agent_ds1688__desc": {"message": "ให้บริการโดย 1688 รองรับการซื้อโดยตรงจากต่างประเทศ ชำระเงินเป็น USD และจัดส่งไปยังคลังสินค้าขนส่งของคุณในประเทศจีน"}, "agent_ds1688__freight": {"message": "เครื่องคำนวณค่าจัดส่ง"}, "agent_ds1688__help": {"message": "ช่วย"}, "agent_ds1688__packages": {"message": "ใบนำส่งสินค้า"}, "agent_ds1688__profile": {"message": "ศูนย์ส่วนบุคคล"}, "agent_ds1688__warehouse": {"message": "โกดังของฉัน"}, "ai_comment_analysis_advantage": {"message": "ข้อดี"}, "ai_comment_analysis_ai": {"message": "วิเคราะห์รีวิวโดย AI"}, "ai_comment_analysis_available": {"message": "พร้อมใช้งาน"}, "ai_comment_analysis_balance": {"message": "เหรียญไม่เพียงพอ โปรดเติมเงิน"}, "ai_comment_analysis_behavior": {"message": "พฤติกรรม"}, "ai_comment_analysis_characteristic": {"message": "ลักษณะของกลุ่มลูกค้า"}, "ai_comment_analysis_comment": {"message": "สินค้าไม่มีรีวิวเพียงพอที่จะสรุปผลได้อย่างแม่นยำ โปรดเลือกสินค้าที่มีรีวิวเพิ่มเติม"}, "ai_comment_analysis_consume": {"message": "การบริโภคโดยประมาณ"}, "ai_comment_analysis_default": {"message": "รีวิวเริ่มต้น"}, "ai_comment_analysis_desire": {"message": "ความคาดหวังของลูกค้า"}, "ai_comment_analysis_disadvantage": {"message": "ข้อเสีย"}, "ai_comment_analysis_free": {"message": "ทดลองใช้ฟรี"}, "ai_comment_analysis_freeNum": {"message": "จะใช้เครดิตฟรี 1 เครดิต"}, "ai_comment_analysis_go_recharge": {"message": "ไปที่เติมเงิน"}, "ai_comment_analysis_intelligence": {"message": "วิเคราะห์รีวิวอย่างชาญฉลาด"}, "ai_comment_analysis_location": {"message": "สถานที่"}, "ai_comment_analysis_motive": {"message": "แรงจูงใจในการซื้อ"}, "ai_comment_analysis_network_error": {"message": "ข้อผิดพลาดของเครือข่าย โปรดลองอีกครั้ง"}, "ai_comment_analysis_normal": {"message": "รีวิวแบบภาพถ่าย"}, "ai_comment_analysis_number_reviews": {"message": "จำนวนรีวิว: $num$, การบริโภคโดยประมาณ: $consume$", "placeholders": {"num": {"content": "$1"}, "consume": {"content": "$2"}}}, "ai_comment_analysis_ordinary": {"message": "ความคิดเห็นทั่วไป"}, "ai_comment_analysis_percentage": {"message": "เปอร์เซ็นต์"}, "ai_comment_analysis_problem": {"message": "ปัญหาการชำระเงิน"}, "ai_comment_analysis_reanalysis": {"message": "วิเคราะห์ใหม่"}, "ai_comment_analysis_reason": {"message": "เหตุผล"}, "ai_comment_analysis_recharge": {"message": "เติมเงิน"}, "ai_comment_analysis_recharged": {"message": "ฉันเติมเงินแล้ว"}, "ai_comment_analysis_retry": {"message": "ลองใหม่"}, "ai_comment_analysis_scene": {"message": "สถานการณ์การใช้งาน"}, "ai_comment_analysis_start": {"message": "เริ่มวิเคราะห์"}, "ai_comment_analysis_subject": {"message": "หัวข้อ"}, "ai_comment_analysis_time": {"message": "ระยะเวลาใช้งาน"}, "ai_comment_analysis_tool": {"message": "เครื่องมือเอไอ"}, "ai_comment_analysis_user_portrait": {"message": "โปรไฟล์ผู้ใช้"}, "ai_comment_analysis_welcome": {"message": "ยินดีต้อนรับสู่การวิเคราะห์รีวิวโดย AI"}, "ai_comment_analysis_year": {"message": "ความคิดเห็นจากปีที่ผ่านมา"}, "ai_listing_Exclude_keywords": {"message": "ยกเว้นคำหลัก"}, "ai_listing_Login_the_feature": {"message": "จำเป็นต้องเข้าสู่ระบบสำหรับคุณสมบัตินี้"}, "ai_listing_aI_generation": {"message": "การสร้างเอไอ"}, "ai_listing_add_automatic": {"message": "อัตโนมัติ"}, "ai_listing_add_dictionary_new": {"message": "สร้างห้องสมุดใหม่"}, "ai_listing_add_enter_keywords": {"message": "ป้อนคำหลัก"}, "ai_listing_add_inputkey_selling": {"message": "ป้อนจุดขายแล้วกด $key$ เพื่อเพิ่มให้เสร็จสิ้น", "placeholders": {"key": {"content": "$1"}}}, "ai_listing_add_inputkey_warning": {"message": "เกินขีดจำกัด จุดขายสูงสุด $amount$", "placeholders": {"amount": {"content": "$1"}}}, "ai_listing_add_keywords": {"message": "เพิ่มคำหลัก"}, "ai_listing_add_manually": {"message": "เพิ่มด้วยตนเอง"}, "ai_listing_add_selling": {"message": "เพิ่มจุดขาย"}, "ai_listing_added_keywords": {"message": "เพิ่มคำหลักแล้ว"}, "ai_listing_added_successfully": {"message": "เพิ่มเรียบร้อยแล้ว"}, "ai_listing_addexcluded_keywords": {"message": "ป้อนคำหลักที่ยกเว้น กด Enter เพื่อเพิ่มให้เสร็จสิ้น"}, "ai_listing_adding_selling": {"message": "เพิ่มจุดขายแล้ว"}, "ai_listing_addkeyword_enter": {"message": "พิมพ์คำแอตทริบิวต์คีย์แล้วกด Enter เพื่อเสร็จสิ้นการเพิ่ม"}, "ai_listing_ai_description": {"message": "ไลบรารีคำศัพท์คำอธิบาย AI"}, "ai_listing_ai_dictionary": {"message": "ไลบรารีคำชื่อเรื่อง AI"}, "ai_listing_ai_title": {"message": "ชื่อเอไอ"}, "ai_listing_aidescription_repeated": {"message": "ชื่อไลบรารีคำคำอธิบาย AI ไม่สามารถทำซ้ำได้"}, "ai_listing_aititle_repeated": {"message": "ชื่อไลบรารีคำชื่อเรื่อง AI ไม่สามารถทำซ้ำได้"}, "ai_listing_data_comes_from": {"message": "ข้อมูลมาจาก:"}, "ai_listing_deleted_successfully": {"message": "ลบเรียบร้อยแล้ว"}, "ai_listing_dictionary_name": {"message": "ชื่อห้องสมุด"}, "ai_listing_edit_dictionary": {"message": "แก้ไขห้องสมุด..."}, "ai_listing_edit_word_library": {"message": "แก้ไขไลบรารีคำ"}, "ai_listing_enter_keywords": {"message": "ป้อนคำหลักแล้วกด $key$ เพื่อเพิ่มให้เสร็จสิ้น", "placeholders": {"key": {"content": "$1"}}}, "ai_listing_exceeded_maximum": {"message": "เกินขีดจำกัด คำหลักสูงสุด $amount$", "placeholders": {"amount": {"content": "$1"}}}, "ai_listing_excluded_word_library": {"message": "ห้องสมุดคำที่ไม่รวม"}, "ai_listing_generate_characters": {"message": "สร้างตัวละคร"}, "ai_listing_generation_platform": {"message": "แพลตฟอร์มการสร้าง"}, "ai_listing_help_optimize": {"message": "ช่วยฉันเพิ่มประสิทธิภาพชื่อผลิตภัณฑ์ ชื่อเดิมคือ"}, "ai_listing_include_selling": {"message": "จุดขายอื่นๆ ได้แก่:"}, "ai_listing_included_keyword": {"message": "รวมคำหลัก"}, "ai_listing_included_keywords": {"message": "รวมคำหลัก"}, "ai_listing_input_selling": {"message": "ป้อนจุดขาย"}, "ai_listing_input_selling_fit": {"message": "ป้อนจุดขายให้ตรงกับชื่อ"}, "ai_listing_input_selling_please": {"message": "กรุณากรอกจุดขาย"}, "ai_listing_intelligently_title": {"message": "ป้อนเนื้อหาที่จำเป็นด้านบนเพื่อสร้างชื่ออย่างชาญฉลาด"}, "ai_listing_keyword_product_title": {"message": "ชื่อผลิตภัณฑ์คำสำคัญ"}, "ai_listing_keywords_repeated": {"message": "คำหลักไม่สามารถทำซ้ำได้"}, "ai_listing_listed_selling_points": {"message": "รวมจุดขาย"}, "ai_listing_long_title_1": {"message": "ประกอบด้วยข้อมูลพื้นฐาน เช่น ชื่อแบรนด์ ประเภทผลิตภัณฑ์ คุณลักษณะของผลิตภัณฑ์ เป็นต้น"}, "ai_listing_long_title_2": {"message": "บนพื้นฐานของชื่อผลิตภัณฑ์มาตรฐาน จะมีการเพิ่มคำหลักที่เอื้อต่อ SEO"}, "ai_listing_long_title_3": {"message": "นอกเหนือจากการระบุชื่อแบรนด์ ประเภทผลิตภัณฑ์ คุณลักษณะของผลิตภัณฑ์ และคำหลักแล้ว ยังมีการรวมคำหลักแบบหางยาวเพื่อให้ได้รับการจัดอันดับที่สูงขึ้นในคำค้นหาเฉพาะเจาะจงแบบแบ่งกลุ่ม"}, "ai_listing_longtail_keyword_product_title": {"message": "ชื่อผลิตภัณฑ์คำหลักหางยาว"}, "ai_listing_manually_enter": {"message": "ป้อนด้วยตนเอง ..."}, "ai_listing_network_not_working": {"message": "อินเทอร์เน็ตไม่พร้อมใช้งาน ต้องใช้ VPN เพื่อเข้าถึง ChatGPT"}, "ai_listing_new_dictionary": {"message": "สร้างคลังคำใหม่ ..."}, "ai_listing_new_generate": {"message": "สร้าง"}, "ai_listing_optional_words": {"message": "คำที่ไม่บังคับ"}, "ai_listing_original_title": {"message": "ชื่อเดิม"}, "ai_listing_other_keywords_included": {"message": "รวมคำหลักอื่น ๆ :"}, "ai_listing_please_again": {"message": "กรุณาลองอีกครั้ง"}, "ai_listing_please_select": {"message": "ชื่อต่อไปนี้ถูกสร้างขึ้นสำหรับคุณ โปรดเลือก:"}, "ai_listing_product_category": {"message": "ประเภทสินค้า"}, "ai_listing_product_category_is": {"message": "หมวดหมู่สินค้าคือ"}, "ai_listing_product_category_to": {"message": "สินค้าอยู่ในหมวดหมู่ใด?"}, "ai_listing_random_keywords": {"message": "สุ่มคำหลัก $amount$", "placeholders": {"amount": {"content": "$1"}}}, "ai_listing_random_selling": {"message": "สุ่มจุดขาย $amount$", "placeholders": {"amount": {"content": "$1"}}}, "ai_listing_randomize_keywords": {"message": "สุ่มจากคลังคำ"}, "ai_listing_search_selling": {"message": "ค้นหาจากจุดขาย"}, "ai_listing_select_product_categories": {"message": "เลือกหมวดหมู่ผลิตภัณฑ์โดยอัตโนมัติ"}, "ai_listing_select_product_selling_points": {"message": "เลือกจุดขายสินค้าอัตโนมัติ"}, "ai_listing_select_word_library": {"message": "เลือกไลบรารีคำ"}, "ai_listing_selling": {"message": "จุดขาย"}, "ai_listing_selling_ask": {"message": "มีข้อกำหนดจุดขายอื่นใดสำหรับชื่อนี้บ้าง?"}, "ai_listing_selling_optional": {"message": "จุดขายทางเลือก"}, "ai_listing_selling_repeat": {"message": "คะแนนไม่สามารถทำซ้ำได้"}, "ai_listing_set_excluded": {"message": "ตั้งเป็นไลบรารีคำที่แยกออก"}, "ai_listing_set_include_selling_points": {"message": "รวมจุดขาย"}, "ai_listing_set_included": {"message": "ตั้งเป็นไลบรารีคำศัพท์ที่รวมไว้"}, "ai_listing_set_selling_dictionary": {"message": "ตั้งเป็นไลบรารีจุดขาย"}, "ai_listing_standard_product_title": {"message": "ชื่อผลิตภัณฑ์มาตรฐาน"}, "ai_listing_translated_title": {"message": "ชื่อเรื่องที่แปล"}, "ai_listing_visit_chatGPT": {"message": "เยี่ยมชม ChatGPT"}, "ai_listing_what_other_keywords": {"message": "ต้องใช้คีย์เวิร์ดอะไรอีกสำหรับชื่อเรื่อง?"}, "aliprice_coupons_apply_again": {"message": "สมัครอีกครั้ง"}, "aliprice_coupons_apply_coupons": {"message": "ใช้คูปอง"}, "aliprice_coupons_apply_success": {"message": "พบคูปอง: บันทึก $amount$", "placeholders": {"amount": {"content": "$1"}}}, "aliprice_coupons_applying": {"message": "รหัสการทดสอบสำหรับข้อเสนอที่ดีที่สุด..."}, "aliprice_coupons_applying_desc": {"message": "กำลังชำระเงิน: $code$", "placeholders": {"code": {"content": "$1"}}}, "aliprice_coupons_back_to_checkout": {"message": "ดำเนินการต่อเพื่อชำระเงิน"}, "aliprice_coupons_found_coupons": {"message": "เราพบ $amount$ คูปอง", "placeholders": {"amount": {"content": "$1"}}}, "aliprice_coupons_found_coupons_desc": {"message": "พร้อมที่จะชำระเงิน? ตรวจสอบให้แน่ใจว่าคุณได้รับราคาที่ดีที่สุด!"}, "aliprice_coupons_no_coupon_aviable": {"message": "รหัสเหล่านั้นใช้งานไม่ได้ ไม่ต้องคิดมาก—คุณได้ราคาที่ดีที่สุดแล้ว"}, "aliprice_coupons_toolbar_btn": {"message": "รับคูปอง"}, "aliww_translate": {"message": "นักแปลแชท <PERSON><PERSON><PERSON>"}, "aliww_translate_supports": {"message": "สนับสนุน: 1688 และ Taobao"}, "amazon_extended_keywords_Keywords": {"message": "คำหลัก"}, "amazon_extended_keywords_copy_all": {"message": "คัดลอกทั้งหมด"}, "amazon_extended_keywords_more": {"message": "เพิ่มเติม"}, "an_lei_ji_xiao_liang_pai_xu": {"message": "เรียงตามยอดขายสะสม"}, "an_lei_xing_cha_kan": {"message": "ดูตามประเภท"}, "an_yue_dai_xiao_pai_xu": {"message": "จัดอันดับตามยอดขาย dropshipping"}, "apra_btn__cat_name": {"message": "การวิเคราะห์รีวิว"}, "apra_chart__name": {"message": "เปอร์เซ็นต์การจำหน่ายสินค้าตามประเทศ"}, "apra_chart__update_at": {"message": "เวลาอัปเดต $time$", "placeholders": {"time": {"content": "$1"}}}, "apra_name": {"message": "สถิติการขายของประเทศต่างๆ"}, "auto_opening": {"message": "จะเปิดโดยอัตโนมัติใน $num$ วินาที", "placeholders": {"num": {"content": "$1"}}}, "auto_page_next_x_pages": {"message": "ถัดไป $autoPaging$ pages", "placeholders": {"autoPaging": {"content": "$1"}}}, "average_days_listed": {"message": "เฉลี่ยในวันที่เก็บ"}, "average_hui_fu_lv": {"message": "อัตราการตอบกลับโดยเฉลี่ย"}, "average_ping_gong_ying_shang_deng_ji": {"message": "ระดับซัพพลายเออร์โดยเฉลี่ย"}, "average_price": {"message": "ราคาเฉลี่ย"}, "average_qi_ding_liang": {"message": "ขั้นต่ำเฉลี่ย"}, "average_rating": {"message": "คะแนนเฉลี่ย"}, "average_ren_zheng_gong_ying_nian_chang": {"message": "ปีเฉลี่ยของการรับรอง"}, "average_revenue": {"message": "รายได้เฉลี่ย"}, "average_revenue_per_product": {"message": "รายได้รวม ÷ จำนวนผลิตภัณฑ์"}, "average_sales": {"message": "ยอดขายเฉลี่ย"}, "average_sales_per_product": {"message": "ยอดขายรวม ÷ จำนวนผลิตภัณฑ์"}, "bao_han": {"message": "ประกอบด้วย"}, "bao_zheng_jin": {"message": "ระยะขอบ"}, "bian_ti_shu": {"message": "รูปแบบต่างๆ"}, "biao_ti": {"message": "ชื่อ"}, "blacklist_add_blacklist": {"message": "บล็อกร้านค้านี้"}, "blacklist_address_incorrect": {"message": "ที่อยู่ไม่ถูกต้อง โปรดตรวจสอบ"}, "blacklist_blacked_out": {"message": "ร้านค้าถูกบล็อกแล้ว"}, "blacklist_blacklist": {"message": "บัญชีดำ"}, "blacklist_no_records_yet": {"message": "ยังไม่มีบันทึก!"}, "blue_rocket": {"message": "로켓배송"}, "brand_name": {"message": "แบรนด์"}, "btn_aliprice_agent__daigou": {"message": "ตัวกลางจัดซื้อ"}, "btn_aliprice_agent__dropshipping": {"message": "ดรอปชิปปิ้ง"}, "btn_have_a_try": {"message": "พยายามต่อไป"}, "btn_refresh": {"message": "รีเฟรช"}, "btn_try_it_now": {"message": "ลองเลย"}, "btn_txt_view_on_aliprice": {"message": "ดูใน AliPrice"}, "bu_bao_han": {"message": "ไม่มี"}, "bulk_copy_links": {"message": "ลิงก์แบบจำนวนมาก"}, "bulk_copy_products": {"message": "สินค้าแบบจำนวนมาก"}, "cai_gou_zi_xun": {"message": "บริการลูกค้า"}, "cai_gou_zi_xun__desc": {"message": "อัตราการตอบกลับสามนาทีของผู้ขาย"}, "can_ping_lei_xing": {"message": "ประเภท"}, "cao_zuo": {"message": "การดำเนินการ"}, "chan_pin_ID": {"message": "รหัสผลิตภัณฑ์"}, "chan_pin_e_wai_xin_xi": {"message": "ข้อมูลเพิ่มเติมผลิตภัณฑ์"}, "chan_pin_lian_jie": {"message": "ลิงค์สินค้า"}, "cheng_li_shi_jian": {"message": "เวลาก่อตั้ง"}, "city__aba": {"message": "Aba"}, "city__aksu": {"message": "<PERSON><PERSON><PERSON>"}, "city__alaer": {"message": "<PERSON><PERSON><PERSON>"}, "city__altai": {"message": "Altai"}, "city__alxaleague": {"message": "Alxa League"}, "city__ankang": {"message": "Ankan<PERSON>"}, "city__anqing": {"message": "Anqing"}, "city__anshan": {"message": "<PERSON><PERSON>"}, "city__anshun": {"message": "<PERSON><PERSON><PERSON>"}, "city__anyang": {"message": "Anyang"}, "city__baicheng": {"message": "Baicheng"}, "city__baise": {"message": "Baise"}, "city__baisha": {"message": "Baisha"}, "city__baishan": {"message": "Baishan"}, "city__baiyin": {"message": "<PERSON><PERSON><PERSON>"}, "city__baoding": {"message": "<PERSON>od<PERSON>"}, "city__baoji": {"message": "<PERSON><PERSON><PERSON>"}, "city__baoshan": {"message": "<PERSON><PERSON><PERSON>"}, "city__baoting": {"message": "Baoting"}, "city__baotou": {"message": "<PERSON><PERSON><PERSON>"}, "city__bayannur": {"message": "Bayannur"}, "city__bayinguoleng": {"message": "Bayinguoleng"}, "city__bazhong": {"message": "Bazhong"}, "city__beihai": {"message": "Beihai"}, "city__bengbu": {"message": "<PERSON><PERSON><PERSON>"}, "city__benxi": {"message": "Benxi"}, "city__bijie": {"message": "Bijie"}, "city__binzhou": {"message": "Binzhou"}, "city__bortala": {"message": "<PERSON><PERSON><PERSON>"}, "city__bozhou": {"message": "Bozhou"}, "city__cangzhou": {"message": "Cangzhou"}, "city__chamdo": {"message": "<PERSON><PERSON><PERSON>"}, "city__changchun": {"message": "<PERSON><PERSON><PERSON>"}, "city__changde": {"message": "<PERSON><PERSON>"}, "city__changhua": {"message": "Changhua"}, "city__changji": {"message": "Changji"}, "city__changjiang": {"message": "Changjiang"}, "city__changsha": {"message": "Changsha"}, "city__changzhi": {"message": "Changzhi"}, "city__changzhou": {"message": "Changzhou"}, "city__chaohu": {"message": "<PERSON><PERSON>"}, "city__chaoyang": {"message": "Chaoyang"}, "city__chaozhou": {"message": "Chaozhou"}, "city__chengde": {"message": "Chengde"}, "city__chengdu": {"message": "Chengdu"}, "city__chengmai": {"message": "<PERSON><PERSON><PERSON>"}, "city__chenzhou": {"message": "Chenzhou"}, "city__chiayi": {"message": "<PERSON><PERSON><PERSON>"}, "city__chifeng": {"message": "<PERSON><PERSON>"}, "city__chizhou": {"message": "Chizhou"}, "city__chongzuo": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__chuxiong": {"message": "Chuxiong"}, "city__chuzhou": {"message": "Chuzhou"}, "city__dali": {"message": "<PERSON><PERSON>"}, "city__dalian": {"message": "<PERSON><PERSON>"}, "city__dandong": {"message": "Dandong"}, "city__danzhou": {"message": "Danzhou"}, "city__daqing": {"message": "Daqing"}, "city__datong": {"message": "<PERSON><PERSON><PERSON>"}, "city__daxinganling": {"message": "<PERSON><PERSON>'<PERSON>ling"}, "city__dazhou": {"message": "Dazhou"}, "city__dehong": {"message": "<PERSON><PERSON>"}, "city__deyang": {"message": "Deyang"}, "city__dezhou": {"message": "Dezhou"}, "city__dingan": {"message": "Ding'an"}, "city__dingxi": {"message": "Dingxi"}, "city__diqing": {"message": "Diqing"}, "city__dongfang": {"message": "<PERSON><PERSON>g"}, "city__dongguan": {"message": "Dongguan"}, "city__dongying": {"message": "<PERSON><PERSON>"}, "city__enshi": {"message": "<PERSON><PERSON>"}, "city__ezhou": {"message": "Ezhou"}, "city__fangchenggang": {"message": "Fangchenggang"}, "city__foshan": {"message": "<PERSON><PERSON><PERSON>"}, "city__fushun": {"message": "<PERSON><PERSON><PERSON>"}, "city__fuxin": {"message": "Fuxin"}, "city__fuyang": {"message": "Fuyang"}, "city__fuzhou": {"message": "Fuzhou"}, "city__gannan": {"message": "<PERSON><PERSON><PERSON>"}, "city__ganzhou": {"message": "Ganzhou"}, "city__ganzi": {"message": "Ganzi"}, "city__guangan": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__guangyuan": {"message": "Guangyuan"}, "city__guangzhou": {"message": "Guangzhou"}, "city__guigang": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__guilin": {"message": "<PERSON><PERSON><PERSON>"}, "city__guiyang": {"message": "Guiyang"}, "city__guolok": {"message": "Guolok"}, "city__guyuan": {"message": "<PERSON><PERSON>"}, "city__haibei": {"message": "Haibei"}, "city__haidong": {"message": "Haidong"}, "city__haikou": {"message": "Hai<PERSON><PERSON>"}, "city__hainan": {"message": "Hainan"}, "city__haixi": {"message": "Haixi"}, "city__hami": {"message": "<PERSON><PERSON>"}, "city__handan": {"message": "<PERSON><PERSON>"}, "city__hangzhou": {"message": "Hangzhou"}, "city__hanzhong": {"message": "Hanzhong"}, "city__harbin": {"message": "<PERSON><PERSON><PERSON>"}, "city__hebi": {"message": "<PERSON><PERSON>"}, "city__hechi": {"message": "<PERSON><PERSON>"}, "city__hefei": {"message": "<PERSON><PERSON><PERSON>"}, "city__hegang": {"message": "<PERSON><PERSON><PERSON>"}, "city__heihe": {"message": "<PERSON><PERSON><PERSON>"}, "city__hengshui": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__hengyang": {"message": "Hengyang"}, "city__heyuan": {"message": "<PERSON><PERSON>"}, "city__heze": {"message": "<PERSON><PERSON>"}, "city__hezhou": {"message": "Hezhou"}, "city__hohhot": {"message": "Hohhot"}, "city__honghe": {"message": "<PERSON><PERSON>"}, "city__hongkongisland": {"message": "Hong Kong Island"}, "city__hotan": {"message": "<PERSON><PERSON>"}, "city__hsinchu": {"message": "<PERSON><PERSON><PERSON>"}, "city__huaian": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__huaibei": {"message": "<PERSON><PERSON><PERSON>"}, "city__huaihua": {"message": "Huaihua"}, "city__huaisouth": {"message": "Huai South"}, "city__hualien": {"message": "<PERSON><PERSON><PERSON>"}, "city__huanggang": {"message": "<PERSON><PERSON><PERSON>"}, "city__huangnan": {"message": "Huangnan"}, "city__huangshan": {"message": "<PERSON><PERSON>"}, "city__huangshi": {"message": "<PERSON><PERSON>"}, "city__huizhou": {"message": "Huizhou"}, "city__huludao": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__hulunbuir": {"message": "Hulunbuir"}, "city__huzhou": {"message": "Huzhou"}, "city__jiamusi": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__jian": {"message": "<PERSON><PERSON><PERSON>"}, "city__jiangmen": {"message": "Jiangmen"}, "city__jiaozuo": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__jiaxing": {"message": "Jiaxing"}, "city__jiayuguan": {"message": "Jiayuguan"}, "city__jieyang": {"message": "<PERSON><PERSON><PERSON>"}, "city__jilin": {"message": "<PERSON><PERSON>"}, "city__jinan": {"message": "<PERSON><PERSON>"}, "city__jinchang": {"message": "Jinchang"}, "city__jincheng": {"message": "Jincheng"}, "city__jingdezhen": {"message": "Jingdez<PERSON>"}, "city__jingmen": {"message": "Jingmen"}, "city__jingzhou": {"message": "Jingzhou"}, "city__jinhua": {"message": "Jinhua"}, "city__jining": {"message": "Jining"}, "city__jinzhong": {"message": "Jinzhong"}, "city__jinzhou": {"message": "Jinzhou"}, "city__jiujiang": {"message": "Jiujiang"}, "city__jiuquan": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__jixi": {"message": "Ji<PERSON>"}, "city__kaifeng": {"message": "Kaifeng"}, "city__kaohsiung": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__karamay": {"message": "<PERSON><PERSON><PERSON>"}, "city__kashgar": {"message": "Ka<PERSON><PERSON>"}, "city__keelung": {"message": "Keelung"}, "city__kinmen": {"message": "Kinmen"}, "city__kizilsu": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__kowloon": {"message": "Kowloon"}, "city__kunming": {"message": "Ku<PERSON><PERSON>"}, "city__laibin": {"message": "<PERSON><PERSON>"}, "city__laiwu": {"message": "<PERSON><PERSON>"}, "city__langfang": {"message": "<PERSON><PERSON><PERSON>"}, "city__lanzhou": {"message": "Lanzhou"}, "city__ledong": {"message": "<PERSON><PERSON>"}, "city__leshan": {"message": "<PERSON><PERSON>"}, "city__lhasa": {"message": "Lhasa"}, "city__liangshan": {"message": "Liangshan"}, "city__lianyungang": {"message": "Lianyungang"}, "city__liaocheng": {"message": "<PERSON><PERSON><PERSON>"}, "city__liaoyang": {"message": "<PERSON><PERSON><PERSON>"}, "city__liaoyuan": {"message": "<PERSON><PERSON><PERSON>"}, "city__lienchiang": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__lijiang": {"message": "Lijiang"}, "city__lincang": {"message": "Lincang"}, "city__linfen": {"message": "Linfen"}, "city__lingao": {"message": "Lingao"}, "city__lingshui": {"message": "<PERSON><PERSON><PERSON>"}, "city__linxia": {"message": "Lin<PERSON>"}, "city__linyi": {"message": "<PERSON><PERSON>"}, "city__lishui": {"message": "Li<PERSON><PERSON>"}, "city__liupanshui": {"message": "Liupanshu<PERSON>"}, "city__liuzhou": {"message": "Liuzhou"}, "city__longnan": {"message": "<PERSON><PERSON>"}, "city__longyan": {"message": "<PERSON><PERSON>"}, "city__loudi": {"message": "<PERSON><PERSON>"}, "city__luan": {"message": "<PERSON><PERSON><PERSON>"}, "city__luohe": {"message": "<PERSON><PERSON><PERSON>"}, "city__luoyang": {"message": "<PERSON><PERSON><PERSON>"}, "city__luzhou": {"message": "Luzhou"}, "city__lüliang": {"message": "<PERSON>眉liang"}, "city__maanshan": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__macauoutlyingislands": {"message": "Macau Outlying Islands"}, "city__macaupeninsula": {"message": "Macau Peninsula"}, "city__maoming": {"message": "<PERSON><PERSON>"}, "city__meishan": {"message": "<PERSON><PERSON>"}, "city__meizhou": {"message": "Meizhou"}, "city__mianyang": {"message": "Mianyang"}, "city__miaoli": {"message": "<PERSON><PERSON>"}, "city__mudanjiang": {"message": "Mudanjiang"}, "city__nagqu": {"message": "<PERSON>g<PERSON>u"}, "city__nanchang": {"message": "Nanchang"}, "city__nanchong": {"message": "<PERSON><PERSON><PERSON>"}, "city__nanjing": {"message": "Nanjing"}, "city__nanning": {"message": "Nanning"}, "city__nanping": {"message": "<PERSON><PERSON>"}, "city__nantong": {"message": "Nantong"}, "city__nantou": {"message": "<PERSON><PERSON><PERSON>"}, "city__nanyang": {"message": "Nanyang"}, "city__neijiang": {"message": "Neijiang"}, "city__newterritories": {"message": "New Territories"}, "city__ngari": {"message": "<PERSON><PERSON>"}, "city__ningbo": {"message": "Ningbo"}, "city__ningde": {"message": "<PERSON><PERSON><PERSON>"}, "city__nujiang": {"message": "Nujiang"}, "city__nyingchi": {"message": "Nyingchi"}, "city__ordos": {"message": "Ordos"}, "city__panjin": {"message": "Panjin"}, "city__panzhihua": {"message": "Pan Zhihua"}, "city__penghu": {"message": "<PERSON><PERSON><PERSON>"}, "city__pingdingshan": {"message": "Pingdingshan"}, "city__pingliang": {"message": "<PERSON><PERSON><PERSON>"}, "city__pingtung": {"message": "<PERSON><PERSON><PERSON>"}, "city__pingxiang": {"message": "<PERSON><PERSON><PERSON>"}, "city__puer": {"message": "<PERSON>u'er"}, "city__putian": {"message": "<PERSON><PERSON>"}, "city__puyang": {"message": "P<PERSON><PERSON>"}, "city__qiandongnan": {"message": "Qiandongnan"}, "city__qianjiang": {"message": "Qianjiang"}, "city__qiannan": {"message": "<PERSON><PERSON><PERSON>"}, "city__qianxinan": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__qingdao": {"message": "Qingdao"}, "city__qingyang": {"message": "Qingyang"}, "city__qingyuan": {"message": "Qingyuan"}, "city__qinhuangdao": {"message": "Qinhuangdao"}, "city__qinzhou": {"message": "Qinzhou"}, "city__qionghai": {"message": "Qionghai"}, "city__qiongzhong": {"message": "Qiongzhong"}, "city__qiqihar": {"message": "Qiqihar"}, "city__qitaihe": {"message": "<PERSON><PERSON><PERSON>"}, "city__quanzhou": {"message": "Quanzhou"}, "city__qujing": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__quzhou": {"message": "Quzhou"}, "city__rizhao": {"message": "<PERSON><PERSON><PERSON>"}, "city__sanmenxia": {"message": "Sanmenxia"}, "city__sanming": {"message": "Sanming"}, "city__sanya": {"message": "Sanya"}, "city__shangluo": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__shangqiu": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__shangrao": {"message": "<PERSON><PERSON><PERSON>"}, "city__shannan": {"message": "Shannan"}, "city__shantou": {"message": "<PERSON><PERSON><PERSON>"}, "city__shanwei": {"message": "Shanwei"}, "city__shaoguan": {"message": "Shaoguan"}, "city__shaoxing": {"message": "Shaoxing"}, "city__shaoyang": {"message": "Shaoyang"}, "city__shennongjiaforestarea": {"message": "Shennongjia Forest Area"}, "city__shenyang": {"message": "Shenyang"}, "city__shenzhen": {"message": "Shenzhen"}, "city__shigatse": {"message": "Shigat<PERSON>"}, "city__shihezi": {"message": "<PERSON><PERSON><PERSON>"}, "city__shijiazhuang": {"message": "Shijiazhuang"}, "city__shiyan": {"message": "<PERSON><PERSON>"}, "city__shizuishan": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__shuangyashan": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__shuozhou": {"message": "Shuozhou"}, "city__siping": {"message": "<PERSON><PERSON>"}, "city__songyuan": {"message": "Songyuan"}, "city__suihua": {"message": "Su<PERSON>ua"}, "city__suining": {"message": "Suining"}, "city__suizhou": {"message": "<PERSON><PERSON><PERSON>"}, "city__suqian": {"message": "<PERSON><PERSON><PERSON>"}, "city__suzhou": {"message": "Suzhou"}, "city__tacheng": {"message": "Tacheng"}, "city__taian": {"message": "Tai'an"}, "city__taichung": {"message": "Taichung"}, "city__tainan": {"message": "Tainan"}, "city__taipei": {"message": "Taipei"}, "city__taitung": {"message": "<PERSON><PERSON><PERSON>"}, "city__taiyuan": {"message": "Taiyuan"}, "city__taizhou": {"message": "Taizhou"}, "city__tangshan": {"message": "Tangshan"}, "city__taoyuan": {"message": "Taoyuan"}, "city__tianmen": {"message": "Tianmen"}, "city__tianshui": {"message": "<PERSON><PERSON><PERSON>"}, "city__tieling": {"message": "Tieling"}, "city__tongchuan": {"message": "<PERSON><PERSON><PERSON>"}, "city__tonghua": {"message": "Tonghua"}, "city__tongliao": {"message": "<PERSON><PERSON><PERSON>"}, "city__tongling": {"message": "Tongling"}, "city__tongren": {"message": "<PERSON><PERSON>"}, "city__tumushuke": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__tunchang": {"message": "Tunchang"}, "city__turpan": {"message": "<PERSON><PERSON><PERSON>"}, "city__ulanqab": {"message": "Ulanqab"}, "city__urumqi": {"message": "Urumqi"}, "city__wanning": {"message": "Wanning"}, "city__weifang": {"message": "<PERSON><PERSON><PERSON>"}, "city__weihai": {"message": "Weihai"}, "city__weinan": {"message": "Weinan"}, "city__wenchang": {"message": "Wenchang"}, "city__wenshan": {"message": "<PERSON><PERSON>"}, "city__wenzhou": {"message": "Wenzhou"}, "city__wuhai": {"message": "Wu<PERSON>"}, "city__wuhan": {"message": "<PERSON><PERSON>"}, "city__wuhu": {"message": "<PERSON><PERSON>"}, "city__wujiaqu": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__wuwei": {"message": "<PERSON><PERSON>"}, "city__wuxi": {"message": "Wuxi"}, "city__wuzhishan": {"message": "Wuzhishan"}, "city__wuzhong": {"message": "Wuzhong"}, "city__wuzhou": {"message": "Wuzhou"}, "city__xiamen": {"message": "Xiamen"}, "city__xian": {"message": "Xi'<PERSON>"}, "city__xiangfan": {"message": "<PERSON><PERSON><PERSON>"}, "city__xiangtan": {"message": "Xiangtan"}, "city__xiangxi": {"message": "Xiangxi"}, "city__xianning": {"message": "Xianning"}, "city__xiantao": {"message": "Xiantao"}, "city__xianyang": {"message": "<PERSON><PERSON><PERSON>"}, "city__xiaogan": {"message": "<PERSON><PERSON>"}, "city__xilingol": {"message": "Xilingol"}, "city__xinganleague": {"message": "Xing'an League"}, "city__xingtai": {"message": "Xingtai"}, "city__xining": {"message": "Xining"}, "city__xinxiang": {"message": "Xinxiang"}, "city__xinyang": {"message": "Xinyang"}, "city__xinyu": {"message": "Xinyu"}, "city__xinzhou": {"message": "Xinzhou"}, "city__xishuangbanna": {"message": "Xishuangbanna"}, "city__xuancheng": {"message": "Xuancheng"}, "city__xuchang": {"message": "Xuchang"}, "city__xuzhou": {"message": "Xuzhou"}, "city__yaan": {"message": "Ya'an"}, "city__yanan": {"message": "Yan'<PERSON>"}, "city__yanbian": {"message": "Yanbian"}, "city__yancheng": {"message": "Yancheng"}, "city__yangjiang": {"message": "Yangjiang"}, "city__yangquan": {"message": "<PERSON><PERSON><PERSON>"}, "city__yangzhou": {"message": "Yangzhou"}, "city__yantai": {"message": "Yantai"}, "city__yibin": {"message": "<PERSON><PERSON>"}, "city__yichang": {"message": "Yichang"}, "city__yichun": {"message": "<PERSON><PERSON><PERSON>"}, "city__yilan": {"message": "Yilan"}, "city__yili": {"message": "<PERSON><PERSON>"}, "city__yinchuan": {"message": "<PERSON><PERSON><PERSON>"}, "city__yingkou": {"message": "<PERSON><PERSON><PERSON>"}, "city__yingtan": {"message": "Yingtan"}, "city__yiwu": {"message": "<PERSON><PERSON>"}, "city__yiyang": {"message": "Yiyang"}, "city__yongzhou": {"message": "Yongzhou"}, "city__yueyang": {"message": "<PERSON><PERSON><PERSON>"}, "city__yulin": {"message": "Yulin"}, "city__yuncheng": {"message": "Yuncheng"}, "city__yunfu": {"message": "<PERSON><PERSON>"}, "city__yunlin": {"message": "<PERSON><PERSON>"}, "city__yushu": {"message": "Yushu"}, "city__yuxi": {"message": "Yuxi"}, "city__zaozhuang": {"message": "Zaozhuang"}, "city__zhangjiajie": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__zhangjiakou": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__zhangye": {"message": "<PERSON><PERSON>"}, "city__zhangzhou": {"message": "Zhangzhou"}, "city__zhanjiang": {"message": "Zhanjiang"}, "city__zhaoqing": {"message": "Zhaoqing"}, "city__zhaotong": {"message": "Zhaotong"}, "city__zhengzhou": {"message": "Zhengzhou"}, "city__zhenjiang": {"message": "Zhenjiang"}, "city__zhongshan": {"message": "Zhongshan"}, "city__zhongwei": {"message": "Zhongwei"}, "city__zhoukou": {"message": "<PERSON><PERSON><PERSON>"}, "city__zhoushan": {"message": "Zhoushan"}, "city__zhuhai": {"message": "Zhu<PERSON>"}, "city__zhumadian": {"message": "Zhumadian"}, "city__zhuzhou": {"message": "Zhuzhou"}, "city__zibo": {"message": "Zibo"}, "city__zigong": {"message": "Zigong"}, "city__ziyang": {"message": "<PERSON><PERSON><PERSON>"}, "city__zunyi": {"message": "<PERSON><PERSON><PERSON>"}, "click_copy_product_info": {"message": "คลิกคัดลอกข้อมูลผลิตภัณฑ์"}, "commmon_txt_expired": {"message": "หมดอายุ"}, "common__date_range_12m": {"message": "1 ปี"}, "common__date_range_1m": {"message": "1 เดือน"}, "common__date_range_1w": {"message": "1 สัปดาห์"}, "common__date_range_2w": {"message": "2 สัปดาห์"}, "common__date_range_3m": {"message": "3 เดือน"}, "common__date_range_3w": {"message": "3 สัปดาห์"}, "common__date_range_6m": {"message": "6 เดือน"}, "common_btn_cancel": {"message": "ยกเลิก"}, "common_btn_close": {"message": "ปิด"}, "common_btn_save": {"message": "บันทึก"}, "common_btn_setting": {"message": "ติดตั้ง"}, "common_email": {"message": "อีเมล์"}, "common_error_msg_no_data": {"message": "ไม่มีข้อมูล"}, "common_error_msg_no_result": {"message": "ขออภัยไม่พบผลลัพธ์"}, "common_favorites": {"message": "รายการโปรด"}, "common_feedback": {"message": "ข้อเสนอแนะ"}, "common_help": {"message": "ช่วยด้วย"}, "common_loading": {"message": "กำลังโหลด"}, "common_login": {"message": "เข้าสู่ระบบ"}, "common_logout": {"message": "ออกจากระบบ"}, "common_no": {"message": "ไม่"}, "common_powered_by_aliprice": {"message": "ขับเคลื่อนโดย AliPrice.com"}, "common_setting": {"message": "การตั้งค่า"}, "common_sign_up": {"message": "ลงชื่อ"}, "common_system_upgrading_title": {"message": "การอัพเกรดระบบ"}, "common_system_upgrading_txt": {"message": "โปรดลองอีกครั้งในภายหลัง"}, "common_txt__currency": {"message": "สกุลเงิน"}, "common_txt__video_tutorial": {"message": "วิดีโอสอน"}, "common_txt_ago_time": {"message": "$time$ วันที่ผ่านมา", "placeholders": {"time": {"content": "$1"}}}, "common_txt_all_product": {"message": "ทั้งหมด"}, "common_txt_analysis": {"message": "การวิเคราะห์"}, "common_txt_basically_used": {"message": "แทบไม่เคยใช้เลย"}, "common_txt_biaoti_link": {"message": "ชื่อเรื่อง+ลิงค์"}, "common_txt_biaoti_link_dian_pu": {"message": "ชื่อเรื่อง+ลิงค์+ชื่อร้านค้า"}, "common_txt_blacklist": {"message": "รายการบล็อก"}, "common_txt_cancel": {"message": "ยกเลิก"}, "common_txt_category": {"message": "หมวดหมู่"}, "common_txt_chakan": {"message": "ตรวจสอบ"}, "common_txt_colors": {"message": "สี"}, "common_txt_confirm": {"message": "ยืนยัน"}, "common_txt_copied": {"message": "คัดลอกแล้ว"}, "common_txt_copy": {"message": "สำเนา"}, "common_txt_copy_link": {"message": "คัดลอกลิงค์"}, "common_txt_copy_title": {"message": "สำเนาชื่อเรื่อง"}, "common_txt_copy_title__link": {"message": "คัดลอกชื่อและลิงก์"}, "common_txt_day": {"message": "ท้องฟ้า"}, "common_txt_day__short": {"message": "D"}, "common_txt_delete": {"message": "ลบ"}, "common_txt_dian_pu_link": {"message": "คัดลอกชื่อร้าน+ลิงค์"}, "common_txt_download": {"message": "ดาวน์โหลด"}, "common_txt_downloaded": {"message": "ดาวน์โหลด"}, "common_txt_export_as_csv": {"message": "ส่งออก Excel"}, "common_txt_export_as_txt": {"message": "ส่งออก Txt"}, "common_txt_fail": {"message": "ล้มเหลว"}, "common_txt_format": {"message": "รูปแบบ"}, "common_txt_get": {"message": "รับ"}, "common_txt_incert_selection": {"message": "สลับการเลือก"}, "common_txt_install": {"message": "ติดตั้ง"}, "common_txt_load_failed": {"message": "โหลดไม่สำเร็จ"}, "common_txt_month": {"message": "เดือน"}, "common_txt_more": {"message": "มากกว่า"}, "common_txt_new_unused": {"message": "ใหม่เอี่ยม ไม่เคยใช้งาน"}, "common_txt_next": {"message": "ต่อไป"}, "common_txt_no_limit": {"message": "ไม่ จำกัด"}, "common_txt_no_noticeable": {"message": "ไม่มีรอยขีดข่วนหรือสิ่งสกปรกที่มองเห็นได้"}, "common_txt_on_sale": {"message": "มีอยู่"}, "common_txt_opt_in_out": {"message": "เปิดปิด"}, "common_txt_order": {"message": "ใบสั่ง"}, "common_txt_others": {"message": "อื่น ๆ"}, "common_txt_overall_poor_condition": {"message": "โดยรวมสภาพไม่ดี"}, "common_txt_patterns": {"message": "รูปแบบ"}, "common_txt_platform": {"message": "แพลตฟอร์ม"}, "common_txt_please_select": {"message": "กรุณาเลือก"}, "common_txt_prev": {"message": "ก่อนหน้า"}, "common_txt_price": {"message": "ราคา"}, "common_txt_privacy_policy": {"message": "นโยบายความเป็นส่วนตัว"}, "common_txt_product_condition": {"message": "สถานะสินค้า"}, "common_txt_rating": {"message": "เรตติ้ง"}, "common_txt_ratings": {"message": "คะแนน"}, "common_txt_reload": {"message": "โหลดซ้ำ"}, "common_txt_reset": {"message": "รีเซ็ต"}, "common_txt_retail": {"message": "ค้าปลีก"}, "common_txt_review": {"message": "ทบทวน"}, "common_txt_sale": {"message": "มีอยู่"}, "common_txt_same": {"message": "เหมือนกัน"}, "common_txt_scratches_and_dirt": {"message": "มีรอยขีดข่วนและสิ่งสกปรก"}, "common_txt_search_title": {"message": "ค้นหาชื่อ"}, "common_txt_select_all": {"message": "เลือกทั้งหมด"}, "common_txt_selected": {"message": "เลือกแล้ว"}, "common_txt_share": {"message": "แบ่งปัน"}, "common_txt_sold": {"message": "ขายแล้ว"}, "common_txt_sold_out": {"message": "ขายหมดแล้ว"}, "common_txt_some_scratches": {"message": "มีรอยขีดข่วนและสิ่งสกปรกบ้าง"}, "common_txt_sort_by": {"message": "เรียงโดย"}, "common_txt_state": {"message": "สถานะ"}, "common_txt_success": {"message": "ความสำเร็จ"}, "common_txt_sys_err": {"message": "ระบบผิดพลาด"}, "common_txt_today": {"message": "วันนี้"}, "common_txt_total": {"message": "ทั้งหมด"}, "common_txt_unselect_all": {"message": "สลับการเลือก"}, "common_txt_upload_image": {"message": "อัพโหลดภาพ"}, "common_txt_visit": {"message": "เยี่ยม"}, "common_txt_whitelist": {"message": "ไวท์ลิสต์"}, "common_txt_wholesale": {"message": "ขายส่ง"}, "common_txt_wildberries": {"message": "Wildberries"}, "common_txt_year": {"message": "ปี"}, "common_yes": {"message": "ใช่"}, "compare_tool_btn_clear_all": {"message": "ลบทั้งหมด"}, "compare_tool_btn_compare": {"message": "เปรียบเทียบ"}, "compare_tool_btn_contact": {"message": "ติดต่อ"}, "compare_tool_tips_max_compared": {"message": "Thêm tối đa $maxComparedCount$", "placeholders": {"maxComparedCount": {"content": "$1"}}}, "configure_notifiactions": {"message": "กำหนดค่าการแจ้งเตือน"}, "contact_us": {"message": "ติดต่อเรา"}, "context_menu_screenshot_search": {"message": "ค้นหาภาพหน้าจอสำหรับสไตล์เดียวกัน"}, "context_menus_aliprice_search_by_image": {"message": "ค้นหารูปภาพใน AliPrice"}, "context_menus_goote_trans": {"message": "แปลหน้า/แสดงต้นฉบับ"}, "context_menus_search_by_image": {"message": "ค้นหาตามภาพใน $storeName$", "placeholders": {"storeName": {"content": "$1"}}}, "context_menus_search_by_image_capture": {"message": "จับภาพไปที่ $storeName$", "placeholders": {"storeName": {"content": "$1"}}}, "context_menus_translator": {"message": "จับภาพเพื่อแปล"}, "converter_modal_amount_placeholder": {"message": "ใส่จำนวนเงินที่นี่"}, "converter_modal_btn_convert": {"message": "แปลง"}, "converter_modal_exchange_rate_source": {"message": "ข้อมูลมาจากอัตราแลกเปลี่ยนเงินตราต่างประเทศ $boc$ เวลาอัปเดต: $updatedAt$", "placeholders": {"boc": {"content": "$1"}, "updatedAt": {"content": "$2"}}}, "converter_modal_name": {"message": "การแปลงสกุลเงิน"}, "converter_modal_search_placeholder": {"message": "ค้นหาสกุลเงิน"}, "copy_all_contact_us_notice": {"message": "เว็บไซต์นี้ไม่ได้รับการสนับสนุนในขณะนี้ กรุณาติดต่อเรา"}, "copy_product_info": {"message": "คัดลอกข้อมูลผลิตภัณฑ์"}, "copy_suggest_search_kw": {"message": "คัดลอกรายการแบบดรอปดาวน์"}, "country__han_gou": {"message": "เกาหลีใต้"}, "country__ri_ben": {"message": "ประเทศญี่ปุ่น"}, "country__yue_nan": {"message": "เวียดนาม"}, "currency_convert__custom": {"message": "อัตราแลกเปลี่ยนที่กำหนดเอง"}, "currency_convert__sync_server": {"message": "ซิงค์เซิร์ฟเวอร์"}, "dang_ri_fa_huo": {"message": "จัดส่งในวันเดียวกัน"}, "dao_chu_quan_dian_shang_pin": {"message": "ส่งออกผลิตภัณฑ์ทั้งหมดในร้านค้า"}, "dao_chu_wei_CSV": {"message": "ส่งออก"}, "dao_chu_zi_duan": {"message": "ส่งออกฟิลด์"}, "delivery_address": {"message": "ที่อยู่จัดส่ง"}, "delivery_company": {"message": "บริษัทจัดส่ง"}, "di_zhi": {"message": "ที่อยู่"}, "dian_ji_cha_xun": {"message": "คลิกเพื่อสอบถาม"}, "dian_pu_ID": {"message": "รหัสร้านค้า"}, "dian_pu_di_zhi": {"message": "ที่อยู่ร้านค้า"}, "dian_pu_lian_jie": {"message": "ลิงค์ร้านค้า"}, "dian_pu_ming": {"message": "ชื่อร้าน"}, "dian_pu_ming_cheng": {"message": "ชื่อร้าน"}, "dian_pu_shang_pin_zong_hsu": {"message": "จำนวนผลิตภัณฑ์ทั้งหมดในร้านค้า: $num$", "placeholders": {"num": {"content": "$1"}}}, "dian_pu_xin_xi": {"message": "เก็บข้อมูล"}, "ding_zai_zuo_ce": {"message": "ตอกไปทางซ้าย"}, "disable_old_version_tips_disable_btn_title": {"message": "ปิดการใช้งานเวอร์ชันเก่า"}, "download_image__SKU_variant_images": {"message": "รูปภาพตัวแปร SKU"}, "download_image__assume": {"message": "ตัวอย่างเช่น เรามีรูปภาพ 2 รูป คือ product1.jpg และ product2.gif\nimg_{$no$} จะถูกเปลี่ยนชื่อเป็น img_01.jpg, img_02.gif;\n{$group$}_{$no$} จะถูกเปลี่ยนชื่อเป็น main_image_01.jpg, main_image_02.gif;", "placeholders": {"no": {"content": "$3"}, "group": {"content": "$2"}}}, "download_image__batch_download": {"message": "การดาวน์โหลดแบบแบตช์"}, "download_image__combined_image": {"message": "ภาพรวมรายละเอียดสินค้า"}, "download_image__continue_downloading": {"message": "ดาวน์โหลดต่อ"}, "download_image__description_images": {"message": "รูปภาพคำอธิบาย"}, "download_image__download_combined_image": {"message": "ดาวน์โหลดภาพรวมรายละเอียดสินค้า"}, "download_image__download_zip": {"message": "ดาวน์โหลด zip"}, "download_image__enlarge_check": {"message": "รองรับเฉพาะภาพ JPEG, JPG, GIF และ PNG ขนาดภาพสูงสุด: 1600 * 1600"}, "download_image__enlarge_image": {"message": "ขยายภาพ"}, "download_image__export": {"message": "ลิงค์ส่งออก"}, "download_image__height": {"message": "ความสูง"}, "download_image__ignore_videos": {"message": "วิดีโอถูกละเว้นเนื่องจากไม่สามารถส่งออกได้"}, "download_image__img_translate": {"message": "แปลรูปภาพ"}, "download_image__main_image": {"message": "ภาพหลัก"}, "download_image__multi_folder": {"message": "โฟลเดอร์หลายรายการ"}, "download_image__name": {"message": "ดาวน์โหลดภาพ"}, "download_image__notice_content": {"message": "กรุณาอย่าเลือก \"ถามตำแหน่งที่จะบันทึกแต่ละไฟล์ก่อนดาวน์โหลด\" ในการตั้งค่าการดาวน์โหลดของเบราว์เซอร์!!! มิฉะนั้นจะมีกล่องโต้ตอบมากมาย"}, "download_image__notice_ignore": {"message": "ไม่ต้องถามหาข้อความนี้อีก"}, "download_image__order_number": {"message": "หมายเลขซีเรียล {$no$}; ชื่อกลุ่ม {$group$}; ไทม์สแตมป์ {$date$}", "placeholders": {"no": {"content": "$1"}, "group": {"content": "$2"}, "date": {"content": "$3"}}}, "download_image__overview": {"message": "ภาพรวม"}, "download_image__prompt_download_zip": {"message": "รูปภาพมีมากเกินไป คุณควรดาวน์โหลดเป็นโฟลเดอร์ zip"}, "download_image__rename": {"message": "การเปลี่ยนชื่อ"}, "download_image__rule": {"message": "กฎการตั้งชื่อ"}, "download_image__single_folder": {"message": "โฟลเดอร์เดียว"}, "download_image__sku_image": {"message": "SKU รูปภาพ"}, "download_image__video": {"message": "วีดีโอ"}, "download_image__width": {"message": "ความกว้าง"}, "download_reviews__download_images": {"message": "ดาวน์โหลดภาพรีวิว"}, "download_reviews__dropdown_title": {"message": "ดาวน์โหลดภาพรีวิว"}, "download_reviews__export_csv": {"message": "ส่งออก CSV"}, "download_reviews__no_images": {"message": "0 ภาพที่สามารถดาวน์โหลดได้"}, "download_reviews__no_reviews": {"message": "ไม่มีรีวิวให้ดาวน์โหลด!"}, "download_reviews__notice": {"message": "เคล็ดลับ:"}, "download_reviews__notice__chrome_settings": {"message": "ตั้งค่าเบราว์เซอร์ Chrome ให้ถามตำแหน่งที่จะบันทึกแต่ละไฟล์ก่อนดาวน์โหลด ตั้งค่าเป็น \"ปิด\""}, "download_reviews__notice__wait": {"message": "เวลารออาจนานขึ้นขึ้นอยู่กับจำนวนรีวิว"}, "download_reviews__pages_list__all": {"message": "ทั้งหมด"}, "download_reviews__pages_list__page": {"message": "$page$ หน้าที่แล้ว", "placeholders": {"page": {"content": "$1"}}}, "download_reviews__pages_list_title": {"message": "ช่วงการเลือก"}, "export_shopping_cart__csv_filed__details_url": {"message": "<PERSON><PERSON><PERSON><PERSON> bağlantısı"}, "export_shopping_cart__csv_filed__details_url_with_sku": {"message": "ลิงก์ Echo SKU"}, "export_shopping_cart__csv_filed__images": {"message": "<PERSON>sim bağlantısı"}, "export_shopping_cart__csv_filed__quantity": {"message": "<PERSON><PERSON><PERSON>"}, "export_shopping_cart__csv_filed__sale_price": {"message": "<PERSON><PERSON><PERSON>"}, "export_shopping_cart__csv_filed__specs": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "export_shopping_cart__csv_filed__store_name": {"message": "<PERSON><PERSON><PERSON>n adı"}, "export_shopping_cart__csv_filed__store_url": {"message": "Mağaza bağlantısı"}, "export_shopping_cart__csv_filed__title": {"message": "<PERSON><PERSON><PERSON><PERSON> adı"}, "export_shopping_cart__export_btn": {"message": "ส่งออก"}, "export_shopping_cart__export_empty": {"message": "กรุณาเลือกสินค้า!"}, "fa_huo_shi_jian": {"message": "การส่งสินค้า"}, "favorite_add_email": {"message": "เพิ่มที่อยู่อีเมล"}, "favorite_add_favorites": {"message": "เพิ่มไปยังรายการโปรด"}, "favorite_added": {"message": "เพิ่ม"}, "favorite_btn_add": {"message": "การแจ้งเตือนการลดราคา"}, "favorite_btn_notify": {"message": "ติดตามราคา"}, "favorite_cate_name_all": {"message": "สินค้าทั้งหมด"}, "favorite_current_price": {"message": "ราคาปัจจุบัน"}, "favorite_due_date": {"message": "วันครบกำหนด"}, "favorite_enable_notification": {"message": "กรุณาเปิดใช้งานการแจ้งเตือนทางอีเมล"}, "favorite_expired": {"message": "หมดอายุ"}, "favorite_go_to_enable": {"message": "ไปที่เปิดใช้งาน"}, "favorite_msg_add_success": {"message": "เพิ่มในรายการโปรดแล้ว"}, "favorite_msg_del_success": {"message": "ลบออกจากรายการโปรด"}, "favorite_msg_failure": {"message": "ล้มเหลว! รีเฟรชหน้าแล้วลองอีกครั้ง"}, "favorite_please_add_email": {"message": "กรุณาเพิ่มที่อยู่อีเมล"}, "favorite_price_drop": {"message": "ลดลง"}, "favorite_price_rise": {"message": "เพิ่มขึ้น"}, "favorite_price_untracked": {"message": "ราคาที่ยังไม่ได้ติดตาม"}, "favorite_saved_price": {"message": "ราคาที่บันทึกไว้"}, "favorite_stop_tracking": {"message": "หยุดการติดตาม"}, "favorite_sub_email_address": {"message": "ที่อยู่อีเมลสำหรับสมัครสมาชิก"}, "favorite_tracking_period": {"message": "ระยะเวลาการติดตาม"}, "favorite_tracking_prices": {"message": "ติดตามราคา"}, "favorite_verify_email": {"message": "ยืนยันที่อยู่อีเมล"}, "favorites_list_remove_prompt_msg": {"message": "แน่ใจหรือว่าจะลบ"}, "favorites_update_button": {"message": "อัพเดทราคาตอนนี้ครับ"}, "fen_lei": {"message": "หมวดหมู่"}, "fen_xia_yan_xuan": {"message": "ตัวเลือกของผู้จัดจำหน่าย"}, "find_similar": {"message": "ค้นหาที่คล้ายกัน"}, "first_ali_price_date": {"message": "วันที่ที่โปรแกรมรวบรวมข้อมูล AliPrice บันทึกครั้งแรก"}, "fooview_coupons_modal_no_data": {"message": "ไม่มีคูปอง"}, "fooview_coupons_modal_title": {"message": "คูปอง"}, "fooview_favorites__product_sub__tooltip_l1": {"message": "ราคา < $lowerPrice$", "placeholders": {"lowerPrice": {"content": "$1"}}}, "fooview_favorites__product_sub__tooltip_l2": {"message": "หรือราคา > $higherPrice$", "placeholders": {"higherPrice": {"content": "$1"}}}, "fooview_favorites__product_sub__tooltip_l3": {"message": "เส้นตาย"}, "fooview_favorites_error_msg_no_favorites": {"message": "เพิ่มสินค้าโปรดที่นี่เพื่อรับการแจ้งเตือนการลดราคา"}, "fooview_favorites_filter_latest": {"message": "ล่าสุด"}, "fooview_favorites_filter_price_drop": {"message": "ลดราคา"}, "fooview_favorites_filter_price_up": {"message": "ขึ้นราคา"}, "fooview_favorites_modal_title": {"message": "สิ่งที่ฉันชอบ"}, "fooview_favorites_modal_title_title": {"message": "ไปที่ AliPrice Favorite"}, "fooview_favorites_track_price": {"message": "เพื่อติดตามราคา"}, "fooview_price_history_app_price": {"message": "ราคา APP:"}, "fooview_price_history_title": {"message": "ประวัติราคา"}, "fooview_product_list_feedback": {"message": "ข้อเสนอแนะ"}, "fooview_product_list_orders": {"message": "คำสั่งซื้อ"}, "fooview_product_list_price": {"message": "ราคา"}, "fooview_reviews_error_msg_no_review": {"message": "เราไม่พบความคิดเห็นใด ๆ สำหรับผลิตภัณฑ์นี้"}, "fooview_reviews_filter_buyer_reviews": {"message": "รูปภาพของผู้ซื้อ"}, "fooview_reviews_modal_title": {"message": "บทวิจารณ์"}, "fooview_same_product_choose_category": {"message": "เลือกหมวดหมู่"}, "fooview_same_product_filter_feedback": {"message": "ข้อเสนอแนะ"}, "fooview_same_product_filter_orders": {"message": "คำสั่งซื้อ"}, "fooview_same_product_filter_price": {"message": "ราคา"}, "fooview_same_product_filter_rating": {"message": "คะแนน"}, "fooview_same_product_modal_title": {"message": "ค้นหาผลิตภัณฑ์เดียวกัน"}, "fooview_same_product_search_by_image": {"message": "ค้นหาจากภาพ"}, "fooview_seller_analysis_modal_title": {"message": "การวิเคราะห์ผู้ขาย"}, "for_12_months": {"message": "เป็นเวลา 1 ปี"}, "for_12_months_list_pro": {"message": "12 เดือน"}, "for_12_months_nei": {"message": "ภายใน 12 เดือน"}, "for_1_months": {"message": "1 เดือน"}, "for_1_months_nei": {"message": "ภายใน 1 เดือน"}, "for_3_months": {"message": "เป็นเวลา 3 เดือน"}, "for_3_months_nei": {"message": "ภายใน 3 เดือน"}, "for_6_months": {"message": "เป็นเวลา 6 เดือน"}, "for_6_months_nei": {"message": "ภายใน 6 เดือน"}, "for_9_months": {"message": "9 เดือน"}, "for_9_months_nei": {"message": "ภายใน 9 เดือน"}, "fu_gou_lv": {"message": "อัตราการซื้อคืน"}, "gao_liang_bu_tong_dian": {"message": "เน้นความแตกต่าง"}, "gao_liang_guang_gao_chan_pin": {"message": "ไฮไลท์ผลิตภัณฑ์โฆษณา"}, "geng_duo_xin_xi": {"message": "ข้อมูลเพิ่มเติม"}, "geng_xin_shi_jian": {"message": "เวลาอัปเดต"}, "get_store_products_fail_tip": {"message": "คลิกตกลงเพื่อไปยังการตรวจสอบเพื่อให้แน่ใจว่าสามารถเข้าถึงได้ตามปกติ"}, "gong_x_kuan_shang_pin": {"message": "ผลิตภัณฑ์ทั้งหมด $amount$", "placeholders": {"amount": {"content": "$1"}}}, "gong_ying_shang": {"message": "ผู้จัดหา"}, "gong_ying_shang_ID": {"message": "รหัสซัพพลายเออร์"}, "gong_ying_shang_deng_ji": {"message": "การให้คะแนนซัพพลายเออร์"}, "gong_ying_shang_nian_zhan": {"message": "ซัพพลายเออร์มีอายุมากกว่า"}, "gong_ying_shang_xin_xi": {"message": "ข้อมูลซัพพลายเออร์"}, "gong_ying_shang_zhu_ye_lian_jie": {"message": "ลิงค์หน้าแรกของซัพพลายเออร์"}, "green_rocket": {"message": "로켓프레시"}, "grey_rocket": {"message": "로켓설치"}, "gu_ji_shou_jia": {"message": "ราคาขายโดยประมาณ"}, "guan_jian_zi": {"message": "คำสำคัญ"}, "guang_gao_chan_pin": {"message": "โฆษณาสินค้า"}, "guang_gao_zhan_bi": {"message": "อัตราส่วนโฆษณา"}, "guo_ji_wu_liu_yun_fei": {"message": "ค่าธรรมเนียมการจัดส่งระหว่างประเทศ"}, "guo_lv_tiao_jian": {"message": "ตัวกรอง"}, "hao_ping_lv": {"message": "คะแนนเชิงบวก"}, "highest_price": {"message": "สูง"}, "historical_trend": {"message": "แนวโน้มทางประวัติศาสตร์"}, "how_to_screenshot": {"message": "กดปุ่มซ้ายของเมาส์ค้างไว้เพื่อเลือกพื้นที่ แตะปุ่มเมาส์ขวาหรือปุ่ม Esc เพื่อออกจากภาพหน้าจอ"}, "howt_it_works": {"message": "มันทำงานอย่างไร"}, "hui_fu_lv": {"message": "อัตราการตอบสนอง"}, "hui_tou_lv": {"message": "อัตราผลตอบแทน"}, "inquire_freightFee": {"message": "คำร้องขอการขนส่ง"}, "inquire_freightFee_Yuan": {"message": "ค่าจัดส่ง/หยวน"}, "inquire_freightFee_province": {"message": "จังหวัดต่างๆ"}, "inquire_freightFee_the": {"message": "ค่าจัดส่งคือ $num$ ซึ่งหมายความว่ามีการจัดส่งฟรีในภูมิภาคนั้น", "placeholders": {"num": {"content": "$1"}}}, "is_ad": {"message": "โฆษณา"}, "jia_ge": {"message": "ราคา"}, "jia_ge_dan_wei": {"message": "ยูนิต"}, "jia_ge_qu_shi": {"message": "แนวโน้ม"}, "jia_zai_n_ge_shang_pin": {"message": "โหลดผลิตภัณฑ์ $num$", "placeholders": {"num": {"content": "$1"}}}, "jin_30_tian_xiao_liang_zhan_bi": {"message": "เปอร์เซ็นต์ของยอดขายใน 30 วันที่ผ่านมา"}, "jin_30_tian_xiao_shou_e_zhan_bi": {"message": "เปอร์เซ็นต์ของรายได้ใน 30 วันที่ผ่านมา"}, "jin_30d_xiao_liang": {"message": "ฝ่ายขาย"}, "jin_30d_xiao_liang__desc": {"message": "ปริมาณการขายรวมในช่วง 30 วันที่ผ่านมา"}, "jin_30d_xiao_shou_e": {"message": "มูลค่าการซื้อขาย"}, "jin_30d_xiao_shou_e__desc": {"message": "มูลค่าการซื้อขายรวมในช่วง 30 วันที่ผ่านมา"}, "jin_90_tian_mai_jia_shu": {"message": "ผู้ซื้อใน 90 วันที่ผ่านมา"}, "jin_90_tian_xiao_shou_liang": {"message": "ยอดขายใน 90 วันที่ผ่านมา"}, "jing_xuan_huo_yuan": {"message": "แหล่งที่มาที่เลือก"}, "jing_ying_mo_shi": {"message": "รูปแบบธุรกิจ"}, "jing_ying_mo_shi__gong_chang": {"message": "ผู้ผลิต"}, "jiu_fen_jie_jue": {"message": "การระงับข้อพิพาท"}, "jiu_fen_jie_jue__desc": {"message": "การบัญชีข้อพิพาทเกี่ยวกับสิทธิในร้านค้าของผู้ขาย"}, "jiu_fen_lv": {"message": "อัตราข้อพิพาท"}, "jiu_fen_lv__desc": {"message": "สัดส่วนการสั่งซื้อที่มีการร้องเรียนเสร็จสิ้นภายใน 30 วันที่ผ่านมา และถือเป็นความรับผิดชอบของผู้ขายหรือทั้งสองฝ่าย"}, "kai_dian_ri_qi": {"message": "วันที่เปิดทำการ"}, "keywords": {"message": "คำหลัก"}, "kua_jin_Select_pan_huo": {"message": "เลือกข้ามพรมแดน"}, "last15_days": {"message": "15 วันที่ผ่านมา"}, "last180_days": {"message": "180 วันที่ผ่านมา"}, "last30_days": {"message": "ในช่วง 30 วันที่ผ่านมา"}, "last360_days": {"message": "360 วันที่ผ่านมา"}, "last45_days": {"message": "45 วันที่ผ่านมา"}, "last60_days": {"message": "60 วันที่ผ่านมา"}, "last7_days": {"message": "7 วันที่ผ่านมา"}, "last90_days": {"message": "90 วันที่ผ่านมา"}, "last_30d_sales": {"message": "ยอดขาย 30 วันที่ผ่านมา"}, "lei_ji": {"message": "สะสม"}, "lei_ji_xiao_liang": {"message": "ทั้งหมด"}, "lei_ji_xiao_liang__desc": {"message": "ยอดขายทั้งหมดหลังสินค้าบนชั้นวาง"}, "lei_ji_xiao_liang_pai_xu__desc": {"message": "ปริมาณการขายสะสมในช่วง 30 วันที่ผ่านมา เรียงจากมากไปน้อย"}, "lian_xi_fang_shi": {"message": "ข้อมูลติดต่อ"}, "list_time": {"message": "วันที่จัดเก็บ"}, "load_more": {"message": "โหลดเพิ่มเติม"}, "login_to_aliprice": {"message": "เข้าสู่ระบบ AliPrice"}, "long_link": {"message": "ลิงค์ยาว"}, "lowest_price": {"message": "ต่ำ"}, "mai_jia_shu": {"message": "จำนวนผู้ขาย"}, "mao_li_lv": {"message": "อัตรากำไรขั้นต้น"}, "mobile_view__dkxbqy": {"message": "เปิดแท็บใหม่"}, "mobile_view__sjdxq": {"message": "รายละเอียดในแอป"}, "mobile_view__sjdxqy": {"message": "หน้ารายละเอียดในแอป"}, "mobile_view__smck": {"message": "สแกนเพื่อดู"}, "mobile_view__smckms": {"message": "โปรดใช้กล้องหรือแอปเพื่อสแกนและดู"}, "modified_failed": {"message": "การปรับเปลี่ยนล้มเหลว"}, "modified_successfully": {"message": "แก้ไขเรียบร้อยแล้ว"}, "nav_btn_favorites": {"message": "คอลเล็กชันของฉัน"}, "nav_btn_package": {"message": "แพ็คเกจ"}, "nav_btn_product_info": {"message": "เกี่ยวกับผลิตภัณฑ์"}, "nav_btn_viewed": {"message": "ดูแล้ว"}, "no_suggest_search_kw": {"message": "Loading..."}, "none": {"message": "ไม่มี"}, "normal_link": {"message": "ลิงค์ปกติ"}, "notice": {"message": "คำใบ้"}, "number_reviews": {"message": "รีวิว"}, "only_show_num": {"message": "สินค้าทั้งหมด: $allnum$, ซ่อน: $hidenum2$", "placeholders": {"allnum": {"content": "$1"}, "hidenum2": {"content": "$2"}}}, "only_show_selected": {"message": "ลบส่วนที่ไม่ได้เลือก"}, "open": {"message": "เปิด"}, "open_links": {"message": "เปิดลิงก์"}, "options_page_tab_check_links": {"message": "ตรวจสอบลิงก์"}, "options_page_tab_gernal": {"message": "ทั่วไป"}, "options_page_tab_notifications": {"message": "การแจ้งเตือน"}, "options_page_tab_others": {"message": "อื่น ๆ"}, "options_page_tab_sbi": {"message": "ค้นหาจากภาพ"}, "options_page_tab_shortcuts": {"message": "ทางลัด"}, "options_page_tab_shortcuts_title": {"message": "ขนาดตัวอักษรสำหรับทางลัด"}, "options_page_tab_similar_products": {"message": "ผลิตภัณฑ์เดียวกัน"}, "orange_rocket": {"message": "판매자로켓"}, "order_list_open_links_tip": {"message": "ลิงก์ผลิตภัณฑ์หลายรายการกำลังจะเปิดขึ้น"}, "order_list_sku_show_title": {"message": "แสดงตัวแปรที่เลือกในลิงก์ที่แชร์"}, "orders_last30_days": {"message": "จำนวนคำสั่งซื้อในช่วง 30 วันที่ผ่านมา"}, "pTutorial_favorites_block1_desc1": {"message": "ผลิตภัณฑ์ที่คุณติดตามแสดงไว้ที่นี่"}, "pTutorial_favorites_block1_title": {"message": "รายการโปรด"}, "pTutorial_popup_block1_desc1": {"message": "ฉลากสีเขียวหมายความว่ามีสินค้าลดราคา"}, "pTutorial_popup_block1_title": {"message": "ทางลัดและรายการโปรด"}, "pTutorial_price_history_block1_desc1": {"message": "คลิก \"ติดตามราคา\" เพิ่มสินค้าในรายการโปรด เมื่อราคาลดลงคุณจะได้รับการแจ้งเตือน"}, "pTutorial_price_history_block1_title": {"message": "ติดตามราคา"}, "pTutorial_reviews_block1_desc1": {"message": "ความคิดเห็นของผู้ซื้อจาก Itao และภาพถ่ายจริงจากข้อเสนอแนะ AliExpress"}, "pTutorial_reviews_block1_title": {"message": "บทวิจารณ์"}, "pTutorial_reviews_block2_desc1": {"message": "การตรวจสอบความเห็นจากผู้ซื้อรายอื่นจะเป็นประโยชน์เสมอ"}, "pTutorial_same_products_block1_desc1": {"message": "คุณสามารถเปรียบเทียบเพื่อเป็นทางเลือกที่ดีที่สุด"}, "pTutorial_same_products_block1_desc2": {"message": "คลิก \"เพิ่มเติม\" เพื่อ \"ค้นหาจากภาพ\""}, "pTutorial_same_products_block1_title": {"message": "ผลิตภัณฑ์เดียวกัน"}, "pTutorial_same_products_by_image_search_block1_desc1": {"message": "วางรูปภาพสินค้าที่นั่นแล้วเลือกหมวดหมู่"}, "pTutorial_same_products_by_image_search_block1_title": {"message": "ค้นหาตามภาพ"}, "pTutorial_seller_analysis_block1_desc1": {"message": "อัตราการตอบรับเชิงบวกของผู้ขายคะแนนตอบรับและระยะเวลาที่ผู้ขายอยู่ในตลาด"}, "pTutorial_seller_analysis_block1_title": {"message": "คะแนนผู้ขาย"}, "pTutorial_seller_analysis_block2_desc2": {"message": "การให้คะแนนผู้ขายขึ้นอยู่กับ 3 ดัชนี ได้แก่ สินค้าตามที่อธิบายไว้, ความเร็วในการจัดส่งการสื่อสาร"}, "pTutorial_seller_analysis_block3_desc3": {"message": "เราใช้ 3 สีและไอคอนเพื่อบ่งบอกระดับความน่าเชื่อถือของผู้ขาย"}, "page_count": {"message": "เลขหน้า"}, "pai_chu": {"message": "ไม่รวม"}, "pai_chu_HK_bu_yi_xia_xiaoshou": {"message": "ไม่รวมสินค้าที่ถูกจำกัดในฮ่องกง"}, "pai_chu_JP_bu_yi_xia_xiaoshou": {"message": "ไม่รวมสินค้าที่ถูกจำกัดในญี่ปุ่น"}, "pai_chu_KR_bu_yi_xia_xiaoshou": {"message": "ไม่รวมสินค้าที่ถูกจำกัดในเกาหลี"}, "pai_chu_KZ_bu_yi_xia_xiaoshou": {"message": "ไม่รวมสินค้าที่ถูกจำกัดในคาซัคสถาน"}, "pai_chu_MO_bu_yi_xia_xiaoshou": {"message": "ไม่รวมสินค้าที่ถูกจำกัดในมาเก๊า"}, "pai_chu_RU_bu_yi_xia_xiaoshou": {"message": "ไม่รวมสินค้าที่ถูกจำกัดในยุโรปตะวันออก"}, "pai_chu_SA_bu_yi_xia_xiaoshou": {"message": "ไม่รวมสินค้าที่ถูกจำกัดในซาอุดีอาระเบีย"}, "pai_chu_TW_bu_yi_xia_xiaoshou": {"message": "ไม่รวมสินค้าที่ถูกจำกัดในไต้หวัน"}, "pai_chu_USA_bu_yi_xia_xiaoshou": {"message": "ไม่รวมสินค้าที่ถูกจำกัดในสหรัฐฯ"}, "pai_chu_VN_bu_yi_xia_xiaoshou": {"message": "ไม่รวมสินค้าที่ถูกจำกัดในเวียดนาม"}, "pai_chu_bu_yi_xia_xiaoshou": {"message": "ไม่รวมสินค้าที่ถูกจำกัด"}, "payable_price_formula": {"message": "ราคา + ค่าจัดส่ง + ส่วนลด"}, "pdd_check_retail_btn_txt": {"message": "ตรวจสอบการขายปลีก"}, "pdd_pifa_to_retail_btn_txt": {"message": "ซื้อปลีก"}, "pdp_copy_fail": {"message": "คัดลอกล้มเหลว!"}, "pdp_copy_success": {"message": "คัดลอกสำเร็จ!"}, "pdp_share_modal_subtitle": {"message": "แชร์ภาพหน้าจอ เขา/เธอจะเห็นตัวเลือกของคุณ"}, "pdp_share_modal_title": {"message": "แบ่งปันทางเลือกของคุณ"}, "pdp_share_screenshot": {"message": "แชร์ภาพหน้าจอ"}, "pei_song": {"message": "วิธีการจัดส่ง"}, "pin_lei": {"message": "หมวดหมู่"}, "pin_zhi_ti_yan": {"message": "คุณภาพของผลิตภัณฑ์"}, "pin_zhi_ti_yan__desc": {"message": "อัตราการคืนเงินคุณภาพของร้านค้าของผู้ขาย"}, "pin_zhi_tui_kuan_lv": {"message": "อัตราการคืนเงิน"}, "pin_zhi_tui_kuan_lv__desc": {"message": "สัดส่วนคำสั่งซื้อที่ได้รับการคืนเงินและคืนสินค้าในช่วง 30 วันที่ผ่านมาเท่านั้น"}, "ping_fen": {"message": "เรตติ้ง"}, "ping_jun_fa_huo_su_du": {"message": "ความเร็วในการจัดส่งโดยเฉลี่ย"}, "pkgInfo_hide": {"message": "ข้อมูลโลจิสติกส์: เปิด/ปิด"}, "pkgInfo_no_trace": {"message": "ไม่มีข้อมูลโลจิสติกส์"}, "platform_name__1688": {"message": "1688"}, "platform_name__17zwd": {"message": "17zwd.com"}, "platform_name__51taoyang": {"message": "51taoyang.com"}, "platform_name__5ts": {"message": "5ts.com"}, "platform_name__91jf": {"message": "91jf.com"}, "platform_name__alibaba": {"message": "Alibaba.com"}, "platform_name__aliexpress": {"message": "AliExpress"}, "platform_name__amazon": {"message": "Amazon"}, "platform_name__bao66": {"message": "bao66.cn"}, "platform_name__chinagoods": {"message": "chinagoods.com"}, "platform_name__dhgate": {"message": "ดีเอชเกต"}, "platform_name__e3hui": {"message": "e3hui.com"}, "platform_name__ebay": {"message": "อีเบย์"}, "platform_name__hznzcn": {"message": "hznzcn.com"}, "platform_name__jd": {"message": "JD"}, "platform_name__juyi5": {"message": "juyi5.cn"}, "platform_name__naver_shopping": {"message": "Naver Shopping"}, "platform_name__pinduoduo": {"message": "พินดูโอดูโอ"}, "platform_name__pinduoduo_pifa": {"message": "พินดูโอดูโอขายส่ง"}, "platform_name__shopee": {"message": "ช้อปปี้"}, "platform_name__sjxzw": {"message": "571xz.com"}, "platform_name__sooxie": {"message": "sooxie.com"}, "platform_name__taobao": {"message": "เถาเป่า"}, "platform_name__taobao_lite": {"message": "Taobao Lite"}, "platform_name__vvic": {"message": "vvic.com"}, "platform_name__walmart": {"message": "วอลมาร์ท"}, "platform_name__wsy": {"message": "wsy.com"}, "platform_name__xingfujie": {"message": "xingfujie.cn"}, "platform_name__yiwugo": {"message": "Yiwugo.com"}, "platform_name__yunchepin": {"message": "yunchepin.cn"}, "platform_name__zhaojiafang": {"message": "zhaojiafang.com"}, "popup_go_to_home": {"message": "บ้าน"}, "popup_go_to_platform": {"message": "ไปที่ $name$", "placeholders": {"name": {"content": "$1"}}}, "popup_search_placeholder": {"message": "ฉันกำลังซื้อของ ..."}, "popup_track_package_btn_track": {"message": "ติดตาม"}, "popup_track_package_desc": {"message": "การติดตามแพ็คเกจแบบครบวงจร"}, "popup_track_package_search_placeholder": {"message": "หมายเลขติดตาม"}, "popup_translate_search_placeholder": {"message": "แปลและค้นหาใน $searchOn$", "placeholders": {"searchOn": {"content": "$1"}}}, "price_history": {"message": "ประวัติราคา"}, "price_history_chart_tip_ae": {"message": "เคล็ดลับ: จำนวนคำสั่งซื้อคือจำนวนคำสั่งซื้อสะสมนับตั้งแต่เปิดตัว"}, "price_history_chart_tip_coupang": {"message": "เคล็ดลับ: <PERSON><PERSON><PERSON> จะลบจำนวนคำสั่งซื้อที่เป็นคำสั่งซื้อฉ้อโกง"}, "price_history_inm_1688_l1": {"message": "กรุณาติดตั้ง"}, "price_history_inm_1688_l2": {"message": "AliPrice Shopping Assistant สำหรับ 1688"}, "price_history_panel_lowest_price": {"message": "ราคาต่ำสุด:"}, "price_history_panel_tab_price_tracking": {"message": "ประวัติราคา"}, "price_history_panel_tab_seller_analysis": {"message": "การวิเคราะห์ผู้ขาย"}, "price_history_pro_modal_title": {"message": "ประวัติราคา & ประวัติการสั่งซื้อ"}, "privacy_consent__btn_agree": {"message": "ทบทวนคำยินยอมในการรวบรวมข้อมูล"}, "privacy_consent__btn_disable_all": {"message": "ไม่ยอมรับ"}, "privacy_consent__btn_enable_all": {"message": "เปิดใช้งานทั้งหมด"}, "privacy_consent__btn_uninstall": {"message": "ลบ"}, "privacy_consent__desc_privacy": {"message": "โปรดทราบว่าหากไม่มีข้อมูลหรือคุกกี้ฟังก์ชันบางอย่างจะถูกปิดเนื่องจากฟังก์ชันเหล่านั้นต้องการคำอธิบายข้อมูลหรือคุกกี้ แต่คุณยังสามารถใช้ฟังก์ชันอื่น ๆ ได้"}, "privacy_consent__desc_privacy_L1": {"message": "ขออภัยหากไม่มีข้อมูลหรือคุกกี้จะไม่สามารถใช้งานได้เนื่องจากเราต้องการคำอธิบายของข้อมูลหรือคุกกี้"}, "privacy_consent__desc_privacy_L2": {"message": "หากคุณไม่อนุญาตให้เรารวบรวมข้อมูลเหล่านี้โปรดลบออก"}, "privacy_consent__item_cookies_desc": {"message": "คุกกี้เราจะรับข้อมูลสกุลเงินของคุณในคุกกี้เมื่อซื้อสินค้าออนไลน์เพื่อแสดงประวัติราคาเท่านั้น"}, "privacy_consent__item_cookies_title": {"message": "คุกกี้ที่จำเป็น"}, "privacy_consent__item_functional_desc_L1": {"message": "1. เพิ่มคุกกี้ในเบราว์เซอร์เพื่อระบุคอมพิวเตอร์หรืออุปกรณ์ของคุณโดยไม่ระบุตัวตน"}, "privacy_consent__item_functional_desc_L2": {"message": "2. เพิ่มข้อมูลการทำงานในโปรแกรมเสริมเพื่อทำงานกับฟังก์ชัน"}, "privacy_consent__item_functional_title": {"message": "คุกกี้การทำงานและการวิเคราะห์"}, "privacy_consent__more_desc": {"message": "โปรดทราบว่าเราไม่เปิดเผยข้อมูลส่วนบุคคลของคุณกับ บริษัท อื่น ๆ และไม่มี บริษัท โฆษณาใดรวบรวมข้อมูลผ่านบริการของเรา"}, "privacy_consent__options__btn__desc": {"message": "ในการใช้คุณสมบัติทั้งหมดคุณต้องเปิดใช้งาน"}, "privacy_consent__options__btn__label": {"message": "เปิด"}, "privacy_consent__options__desc_L1": {"message": "เราจะรวบรวมข้อมูลต่อไปนี้ที่ระบุตัวตนของคุณ:"}, "privacy_consent__options__desc_L2": {"message": "- คุกกี้เราจะรับข้อมูลสกุลเงินของคุณเป็นคุกกี้เท่านั้นเมื่อคุณช็อปปิ้งออนไลน์เพื่อแสดงประวัติราคา"}, "privacy_consent__options__desc_L3": {"message": "- และเพิ่มคุกกี้ในเบราว์เซอร์เพื่อระบุคอมพิวเตอร์หรืออุปกรณ์ของคุณโดยไม่ระบุชื่อ"}, "privacy_consent__options__desc_L4": {"message": "- ข้อมูลที่ไม่ระบุตัวตนอื่น ๆ ทำให้ส่วนขยายนี้สะดวกยิ่งขึ้น"}, "privacy_consent__options__desc_L5": {"message": "โปรดทราบว่าเราไม่เปิดเผยข้อมูลส่วนบุคคลของคุณกับ บริษัท อื่น ๆ และไม่มี บริษัท โฆษณาใดรวบรวมข้อมูลผ่านบริการของเรา"}, "privacy_consent__privacy_preferences": {"message": "การตั้งค่าความเป็นส่วนตัว"}, "privacy_consent__read_more": {"message": "อ่านเพิ่มเติม >>"}, "privacy_consent__title_privacy": {"message": "ความเป็นส่วนตัว"}, "product_info": {"message": "ข้อมูลผลิตภัณฑ์"}, "product_recommend__name": {"message": "ผลิตภัณฑ์เดียวกัน"}, "product_research": {"message": "การวิจัยผลิตภัณฑ์"}, "product_sub__email_desc": {"message": "อีเมลแจ้งเตือนราคา"}, "product_sub__email_edit": {"message": "แก้ไข"}, "product_sub__email_not_verified": {"message": "กรุณายืนยันอีเมล"}, "product_sub__email_required": {"message": "โปรดระบุอีเมล"}, "product_sub__form_countdown": {"message": "ปิดอัตโนมัติหลังจาก $seconds$ seconds", "placeholders": {"seconds": {"content": "$1"}}}, "product_sub__form_fail": {"message": "เพิ่มการเตือนล้มเหลว!"}, "product_sub__form_input_price": {"message": "ราคาอินพุต"}, "product_sub__form_item_country": {"message": "ชาติ"}, "product_sub__form_item_current_price": {"message": "ราคาปัจจุบัน"}, "product_sub__form_item_duration": {"message": "ติดตาม"}, "product_sub__form_item_higher_price": {"message": "หรือราคา >"}, "product_sub__form_item_invalid_higher_price": {"message": "ราคาต้องมากกว่า $price$", "placeholders": {"price": {"content": "$1"}}}, "product_sub__form_item_invalid_lower_price": {"message": "ราคาต้องต่ำกว่า $price$", "placeholders": {"price": {"content": "$1"}}}, "product_sub__form_item_lower_price": {"message": "เมื่อราคา <"}, "product_sub__form_submit": {"message": "ส่ง"}, "product_sub__form_success": {"message": "เพิ่มการเตือนความจำสำเร็จ!"}, "product_sub__high_price_notify": {"message": "แจ้งขึ้นราคาครับ"}, "product_sub__low_price_notify": {"message": "แจ้งลดราคาค่ะ"}, "product_sub__modal_title": {"message": "การแจ้งเตือนการเปลี่ยนแปลงราคาสมัครสมาชิก"}, "province__an_hui": {"message": "<PERSON><PERSON>"}, "province__ao_men": {"message": "Macau"}, "province__bei_jing": {"message": "Beijing"}, "province__chong_qing": {"message": "Chongqing"}, "province__fu_jian": {"message": "Fujian"}, "province__gan_su": {"message": "Gansu"}, "province__guang_dong": {"message": "Guangdong"}, "province__guang_xi": {"message": "Guangxi"}, "province__gui_zhou": {"message": "Guizhou"}, "province__hai_nan": {"message": "Hainan"}, "province__he_bei": {"message": "Hebei"}, "province__he_nan": {"message": "<PERSON><PERSON>"}, "province__hei_long_jiang": {"message": "Heilongjiang"}, "province__hu_bei": {"message": "Hubei"}, "province__hu_nan": {"message": "Hunan"}, "province__ji_lin": {"message": "<PERSON><PERSON>"}, "province__jiang_su": {"message": "Jiangsu"}, "province__jiang_xi": {"message": "Jiangxi"}, "province__liao_ning": {"message": "Liaoning"}, "province__nei_meng_gu": {"message": "Inner Mongolia"}, "province__ning_xia": {"message": "Ningxia"}, "province__qing_hai": {"message": "Qinghai"}, "province__shan_dong": {"message": "Shandong"}, "province__shan_xi": {"message": "Shaanxi"}, "province__shang_hai": {"message": "Shanghai"}, "province__si_chuan": {"message": "Sichuan"}, "province__tai_wan": {"message": "Taiwan"}, "province__tian_jin": {"message": "Tianjin"}, "province__xi_zhang": {"message": "Tibet"}, "province__xiang_gang": {"message": "Hong Kong"}, "province__xin_jiang": {"message": "Xinjiang"}, "province__yun_nan": {"message": "Yunnan"}, "province__zhe_jiang": {"message": "Zhejiang"}, "purple_rocket": {"message": "로켓직구"}, "qi_ding_liang": {"message": "ขั้นต่ำ"}, "qi_ding_liang_qi_ding_jia": {"message": "MOQ & MOP"}, "qi_ye_mian_ji": {"message": "พื้นที่องค์กร"}, "qing_zhi_shao_xuan_ze_yi_ge_chan_pin": {"message": "โปรดเลือกอย่างน้อยหนึ่งผลิตภัณฑ์"}, "qing_zhi_shao_xuan_ze_yi_ge_zi_duan": {"message": "โปรดเลือกอย่างน้อยหนึ่งฟิลด์"}, "qu_deng_lu": {"message": "เข้าสู่ระบบ"}, "quan_guo_yan_xuan": {"message": "ตัวเลือกทั่วโลก"}, "recommendation_popup_banner_btn_install": {"message": "ติดตั้ง"}, "recommendation_popup_banner_desc": {"message": "แสดงประวัติราคาภายใน 3/6 เดือนและการแจ้งเตือนการลดราคา"}, "region__all": {"message": "ทุกภูมิภาค"}, "region__hai_wai": {"message": "Overseas"}, "region__hua_bei_qu": {"message": "North China"}, "region__hua_dong_qu": {"message": "East China"}, "region__hua_nan_qu": {"message": "South China"}, "region__hua_zhong_qu": {"message": "Central China"}, "region__jiang_zhe_lu": {"message": "Jiangsu, Zhejiang and Shanghai"}, "remove_web_limits": {"message": "เปิดใช้งานการคลิกขวา"}, "ren_zheng_gong_chang": {"message": "โรงงานที่ผ่านการรับรอง"}, "ren_zheng_gong_ying_shang_nian_zhan": {"message": "ปีในฐานะซัพพลายเออร์ที่ได้รับการรับรอง"}, "required_to_aliprice_login": {"message": "ต้องเข้าสู่ระบบ AliPrice"}, "revenue_last30_days": {"message": "ยอดขายในช่วง 30 วันที่ผ่านมา"}, "review_counts": {"message": "จำนวนนักสะสม"}, "rocket": {"message": "로켓"}, "ru_zhu_nian_xian": {"message": "ช่วงเข้า"}, "sales_amount_last30_days": {"message": "ยอดขายรวมในช่วง 30 วันที่ผ่านมา"}, "sales_last30_days": {"message": "ยอดขายในช่วง 30 วันที่ผ่านมา"}, "sbi_alibaba_cate__accessories": {"message": "เครื่องประดับ"}, "sbi_alibaba_cate__aqfk": {"message": "ความปลอดภัย"}, "sbi_alibaba_cate__bags_cases": {"message": "กระเป๋าและเคส"}, "sbi_alibaba_cate__beauty": {"message": "ความงาม"}, "sbi_alibaba_cate__beverage": {"message": "เครื่องดื่ม"}, "sbi_alibaba_cate__bgwh": {"message": "วัฒนธรรมสำนักงาน"}, "sbi_alibaba_cate__bz": {"message": "บรรจุุภัณฑ์"}, "sbi_alibaba_cate__ccyj": {"message": "เครื่องครัว"}, "sbi_alibaba_cate__clothes": {"message": "เครื่องแต่งกาย"}, "sbi_alibaba_cate__cmgd": {"message": "สื่อกระจายเสียง"}, "sbi_alibaba_cate__coat_jacket": {"message": "เสื้อโค้ท & แจ็คเก็ต"}, "sbi_alibaba_cate__consumer_electronics": {"message": "เครื่องใช้ไฟฟ้า"}, "sbi_alibaba_cate__cryp": {"message": "ผลิตภัณฑ์สำหรับผู้ใหญ่"}, "sbi_alibaba_cate__csyp": {"message": "ผ้าปูเตียง"}, "sbi_alibaba_cate__cwyy": {"message": "ทำสวนสัตว์เลี้ยง"}, "sbi_alibaba_cate__cysx": {"message": "จัดเลี้ยงสด"}, "sbi_alibaba_cate__dgdq": {"message": "ช่างไฟฟ้า"}, "sbi_alibaba_cate__dl": {"message": "รักษาการ"}, "sbi_alibaba_cate__dress_suits": {"message": "ชุดและชุดสูท"}, "sbi_alibaba_cate__dszm": {"message": "แสงสว่าง"}, "sbi_alibaba_cate__dzqj": {"message": "อุปกรณ์อิเล็กทรอนิกส์"}, "sbi_alibaba_cate__essb": {"message": "อุปกรณ์มือสอง"}, "sbi_alibaba_cate__food": {"message": "อาหาร"}, "sbi_alibaba_cate__fspj": {"message": "เสื้อผ้าและเครื่องประดับ"}, "sbi_alibaba_cate__furniture": {"message": "เฟอร์นิเจอร์"}, "sbi_alibaba_cate__fzpg": {"message": "หนังสิ่งทอ"}, "sbi_alibaba_cate__ghjq": {"message": "การดูแลส่วนบุคคล"}, "sbi_alibaba_cate__gt": {"message": "เหล็ก"}, "sbi_alibaba_cate__gyp": {"message": "หัตถกรรม"}, "sbi_alibaba_cate__hb": {"message": "เป็นมิตรต่อสิ่งแวดล้อม"}, "sbi_alibaba_cate__hfcz": {"message": "แต่งหน้าบำรุงผิว"}, "sbi_alibaba_cate__hg": {"message": "อุตสาหกรรมเคมี"}, "sbi_alibaba_cate__jg": {"message": "กำลังประมวลผล"}, "sbi_alibaba_cate__jianccai": {"message": "วัสดุก่อสร้าง"}, "sbi_alibaba_cate__jichuang": {"message": "เครื่องมือ"}, "sbi_alibaba_cate__jjry": {"message": "ของใช้ประจำวันในครัวเรือน"}, "sbi_alibaba_cate__jtys": {"message": "การขนส่ง"}, "sbi_alibaba_cate__jxsb": {"message": "อุปกรณ์"}, "sbi_alibaba_cate__jxwj": {"message": "ฮาร์ดแวร์เครื่องกล"}, "sbi_alibaba_cate__jydq": {"message": "เครื่องใช้ในครัวเรือน"}, "sbi_alibaba_cate__jzjc": {"message": "วัสดุก่อสร้างปรับปรุงบ้าน"}, "sbi_alibaba_cate__jzjf": {"message": "สิ่งทอที่บ้าน"}, "sbi_alibaba_cate__mj": {"message": "ผ้าขนหนู"}, "sbi_alibaba_cate__myyp": {"message": "ผลิตภัณฑ์สำหรับเด็ก"}, "sbi_alibaba_cate__nanz": {"message": "ผู้ชาย"}, "sbi_alibaba_cate__nvz": {"message": "เสื้อผ้าผู้หญิง"}, "sbi_alibaba_cate__ny": {"message": "พลังงาน"}, "sbi_alibaba_cate__others": {"message": "อื่น ๆ"}, "sbi_alibaba_cate__qcyp": {"message": "ประดับยนต์"}, "sbi_alibaba_cate__qmpj": {"message": "อะไหล่รถยนต์"}, "sbi_alibaba_cate__shoes": {"message": "รองเท้า"}, "sbi_alibaba_cate__smdn": {"message": "ดิจิตอลคอมพิวเตอร์"}, "sbi_alibaba_cate__snqj": {"message": "จัดเก็บและทำความสะอาด"}, "sbi_alibaba_cate__spjs": {"message": "อาหารเครื่องดื่ม"}, "sbi_alibaba_cate__swfw": {"message": "บริการทางธุรกิจ"}, "sbi_alibaba_cate__toys_hobbies": {"message": "ของเล่น"}, "sbi_alibaba_cate__trousers_skirt": {"message": "กางเกงและกระโปรง"}, "sbi_alibaba_cate__txcp": {"message": "ผลิตภัณฑ์สื่อสาร"}, "sbi_alibaba_cate__tz": {"message": "เสื้อผ้าเด็ก"}, "sbi_alibaba_cate__underwear": {"message": "ชุดชั้นใน"}, "sbi_alibaba_cate__wjgj": {"message": "เครื่องมือฮาร์ดแวร์"}, "sbi_alibaba_cate__xgpi": {"message": "กระเป๋าหนัง"}, "sbi_alibaba_cate__xmhz": {"message": "ความร่วมมือโครงการ"}, "sbi_alibaba_cate__xs": {"message": "ยาง"}, "sbi_alibaba_cate__ydfs": {"message": "ชุดกีฬา"}, "sbi_alibaba_cate__ydhw": {"message": "กีฬากลางแจ้ง"}, "sbi_alibaba_cate__yjkc": {"message": "แร่โลหะวิทยา"}, "sbi_alibaba_cate__yqyb": {"message": "เครื่องมือวัด"}, "sbi_alibaba_cate__ys": {"message": "พิมพ์"}, "sbi_alibaba_cate__yyby": {"message": "ดูแลรักษาทางการแพทย์"}, "sbi_alibaba_cn_kj_90mjs": {"message": "จำนวนผู้ซื้อใน 90 วันที่ผ่านมา"}, "sbi_alibaba_cn_kj_90xsl": {"message": "ปริมาณการขายใน 90 วันที่ผ่านมา"}, "sbi_alibaba_cn_kj_gjsj": {"message": "ราคาโดยประมาณ"}, "sbi_alibaba_cn_kj_gjwlyf": {"message": "ค่าขนส่งระหว่างประเทศ"}, "sbi_alibaba_cn_kj_gjyf": {"message": "ค่าขนส่ง"}, "sbi_alibaba_cn_kj_gssj": {"message": "ราคาโดยประมาณ"}, "sbi_alibaba_cn_kj_lr": {"message": "กำไร"}, "sbi_alibaba_cn_kj_lrgs": {"message": "กำไร = ราคาโดยประมาณ x อัตรากำไร"}, "sbi_alibaba_cn_kj_pjfhsd": {"message": "ความเร็วในการส่งเฉลี่ย"}, "sbi_alibaba_cn_kj_qtfy": {"message": "ค่าธรรมเนียมอื่นๆ"}, "sbi_alibaba_cn_kj_qtfygs": {"message": "ต้นทุนอื่น = ราคาโดยประมาณ x อัตราส่วนต้นทุนอื่น"}, "sbi_alibaba_cn_kj_spjg": {"message": "ราคา"}, "sbi_alibaba_cn_kj_spzl": {"message": "น้ำหนัก"}, "sbi_alibaba_cn_kj_szd": {"message": "ที่ตั้ง"}, "sbi_alibaba_cn_kj_unit_ge": {"message": "ชิ้น"}, "sbi_alibaba_cn_kj_unit_jian": {"message": "ชิ้น"}, "sbi_alibaba_cn_kj_unit_ke": {"message": "กรัม"}, "sbi_alibaba_cn_kj_unit_ren": {"message": "ผู้ซื้อ"}, "sbi_alibaba_cn_kj_unit_tai": {"message": "ชิ้น"}, "sbi_alibaba_cn_kj_unit_tao": {"message": "ชุด"}, "sbi_alibaba_cn_kj_unit_tian": {"message": "วัน"}, "sbi_alibaba_cn_kj_zwbj": {"message": "ไม่มีราคา"}, "sbi_aliprice_alibaba_cn__jiage": {"message": "ราคา"}, "sbi_aliprice_alibaba_cn__keshou": {"message": "พร้อมขาย"}, "sbi_aliprice_alibaba_cn__moren": {"message": "ค่าเริ่มต้น"}, "sbi_aliprice_alibaba_cn__queding": {"message": "แน่นอน"}, "sbi_aliprice_alibaba_cn__xiaoliang": {"message": "ฝ่ายขาย"}, "sbi_aliprice_alibaba_cn_cate__jiaju": {"message": "เฟอร์นิเจอร์"}, "sbi_aliprice_alibaba_cn_cate__linghsi": {"message": "อาหารว่าง"}, "sbi_aliprice_alibaba_cn_cate__meizhuang": {"message": "แต่งหน้า"}, "sbi_aliprice_alibaba_cn_cate__neiyi": {"message": "ชุดชั้นใน"}, "sbi_aliprice_alibaba_cn_cate__peishi": {"message": "เครื่องประดับ"}, "sbi_aliprice_alibaba_cn_cate__pinchui": {"message": "เครื่องดื่มบรรจุขวด"}, "sbi_aliprice_alibaba_cn_cate__qita": {"message": "คนอื่น"}, "sbi_aliprice_alibaba_cn_cate__qunzhuang": {"message": "กระโปรง"}, "sbi_aliprice_alibaba_cn_cate__shangyi": {"message": "เสื้อแจ็กเกต"}, "sbi_aliprice_alibaba_cn_cate__shuma": {"message": "อิเล็กทรอนิกส์"}, "sbi_aliprice_alibaba_cn_cate__wanju": {"message": "ของเล่น"}, "sbi_aliprice_alibaba_cn_cate__xiangbao": {"message": "กระเป๋าเดินทาง"}, "sbi_aliprice_alibaba_cn_cate__xiazhuang": {"message": "ท่อนล่าง"}, "sbi_aliprice_alibaba_cn_cate__xiezi": {"message": "รองเท้า"}, "sbi_aliprice_cate__apparel": {"message": "เครื่องแต่งกาย"}, "sbi_aliprice_cate__automobiles_motorcycles": {"message": "รถยนต์และรถจักรยานยนต์"}, "sbi_aliprice_cate__beauty_health": {"message": "ความงามและสุขภาพ"}, "sbi_aliprice_cate__cellphones_telecommunications": {"message": "โทรศัพท์มือถือและการสื่อสารโทรคมนาคม"}, "sbi_aliprice_cate__computer_office": {"message": "คอมพิวเตอร์และสำนักงาน"}, "sbi_aliprice_cate__consumer_electronics": {"message": "เครื่องใช้ไฟฟ้า"}, "sbi_aliprice_cate__education_office_supplies": {"message": "อุปกรณ์การศึกษาและสำนักงาน"}, "sbi_aliprice_cate__electronic_components_supplies": {"message": "ส่วนประกอบอิเล็กทรอนิกส์และอุปกรณ์"}, "sbi_aliprice_cate__furniture": {"message": "เฟอร์นิเจอร์"}, "sbi_aliprice_cate__hair_extensions_wigs": {"message": "ต่อผมและวิกผม"}, "sbi_aliprice_cate__home_garden": {"message": "บ้านและสวน"}, "sbi_aliprice_cate__home_improvement": {"message": "การปรับปรุงบ้าน"}, "sbi_aliprice_cate__jewelry_accessories": {"message": "เครื่องประดับและเครื่องประดับ"}, "sbi_aliprice_cate__luggage_bags": {"message": "กระเป๋าและกระเป๋า"}, "sbi_aliprice_cate__mother_kids": {"message": "แม่และเด็ก"}, "sbi_aliprice_cate__novelty_special_use": {"message": "ความแปลกใหม่และการใช้งานพิเศษ"}, "sbi_aliprice_cate__security_protection": {"message": "ความปลอดภัยและการป้องกัน"}, "sbi_aliprice_cate__shoes": {"message": "รองเท้า"}, "sbi_aliprice_cate__sports_entertainment": {"message": "กีฬาและความบันเทิง"}, "sbi_aliprice_cate__toys_hobbies": {"message": "ของเล่นและงานอดิเรก"}, "sbi_aliprice_cate__watches": {"message": "นาฬิกา"}, "sbi_aliprice_cate__weddings_events": {"message": "งานแต่งงานและงานต่างๆ"}, "sbi_btn_capture_txt": {"message": "การจับกุม"}, "sbi_btn_source_now_txt": {"message": "ที่มาตอนนี้"}, "sbi_button__chat_with_me": {"message": "แชทกับฉัน"}, "sbi_button__contact_supplier": {"message": "ติดต่อ"}, "sbi_button__hide_on_this_site": {"message": "อย่าแสดงบนไซต์นี้"}, "sbi_button__open_settings": {"message": "กำหนดค่าการค้นหาด้วยภาพ"}, "sbi_capture_shortcut_tip": {"message": "หรือกดแป้น \"Enter\" บนแป้นพิมพ์"}, "sbi_capturing_tip": {"message": "การจับภาพ"}, "sbi_composed_rating_45": {"message": "4.5 - 5.0 ดาว"}, "sbi_crop_and_search": {"message": "ค้นหา"}, "sbi_crop_start": {"message": "ใช้ภาพหน้าจอ"}, "sbi_err_captcha_action": {"message": "ตรวจสอบ"}, "sbi_err_captcha_for_alibaba_cn": {"message": "ต้องการการยืนยัน โปรดอัปโหลดรูปภาพเพื่อตรวจสอบ (ดู $video_tutorial$ หรือลองล้างคุกกี้)", "placeholders": {"feedback": {"content": "$1"}, "video_tutorial": {"content": "$1"}}}, "sbi_err_captcha_for_aliprice": {"message": "การจราจรผิดปกติ โปรดตรวจสอบ"}, "sbi_err_captcha_for_taobao": {"message": "Taobao ขอให้คุณตรวจสอบ โปรดอัปโหลดรูปภาพด้วยตนเองและค้นหาเพื่อยืนยัน ข้อผิดพลาดนี้เกิดจากนโยบายการตรวจสอบ \"TaoBao ค้นหาด้วยภาพ\" ใหม่ เราขอแนะนำให้คุณตรวจสอบการร้องเรียนบ่อยเกินไปใน Taobao $feedback$", "placeholders": {"feedback": {"content": "$1"}}}, "sbi_err_captcha_for_taobao_feedback": {"message": "ข้อเสนอแนะ"}, "sbi_err_captcha_msg": {"message": "$platform$ กำหนดให้คุณต้องอัปโหลดรูปภาพเพื่อค้นหาหรือทำการตรวจสอบความปลอดภัยให้เสร็จสิ้นเพื่อลบข้อจำกัดการค้นหา", "placeholders": {"platform": {"content": "$1"}}}, "sbi_err_checking_if_low_version": {"message": "ตรวจสอบว่าเป็นเวอร์ชันล่าสุดหรือไม่"}, "sbi_err_cookie_btn_clear": {"message": "เคลียร์คุกกี้"}, "sbi_err_cookie_for_alibaba_cn": {"message": "ล้างคุกกี้ 1688?(ต้องเข้าสู่ระบบใหม่อีกครั้ง)"}, "sbi_err_desperate_feature_pdd": {"message": "ฟังก์ชั่นค้นหารูปภาพถูกย้ายไปที่ Pinduoduo Search by Image extension"}, "sbi_err_hidden_skill_of_improve_search_rate": {"message": "จะปรับปรุงอัตราความสำเร็จของการค้นหารูปภาพได้อย่างไร"}, "sbi_err_img_undersize": {"message": "รูปภาพ > $imgMinimumSize$", "placeholders": {"imgMinimumSize": {"content": "$1"}}}, "sbi_err_login_required": {"message": "เข้าสู่ระบบ $loginSite$", "placeholders": {"loginSite": {"content": "$1"}}}, "sbi_err_login_required_action": {"message": "เข้าสู่ระบบ"}, "sbi_err_low_version": {"message": "ติดตั้งเวอร์ชันล่าสุด ($latestVersion$)", "placeholders": {"latestVersion": {"content": "$1"}}}, "sbi_err_low_version_action": {"message": "ดาวน์โหลด"}, "sbi_err_need_help": {"message": "ต้องการความช่วยเหลือ"}, "sbi_err_network": {"message": "เครือข่ายเกิดข้อผิดพลาด ตรวจสอบให้แน่ใจว่าคุณสามารถเยี่ยมชมเว็บไซต์ได้"}, "sbi_err_not_low_version": {"message": "ติดตั้งเวอร์ชันล่าสุดแล้ว ($latestVersion$)", "placeholders": {"latestVersion": {"content": "$1"}}}, "sbi_err_try_again": {"message": "ลองอีกครั้ง"}, "sbi_err_try_again_action": {"message": "ลองอีกครั้ง"}, "sbi_err_visit_and_try": {"message": "ลองอีกครั้ง หรือไปที่ $website$ เพื่อลองอีกครั้ง", "placeholders": {"website": {"content": "$1"}}}, "sbi_err_visit_site": {"message": "ไปที่หน้าแรกของ $siteName$", "placeholders": {"siteName": {"content": "$1"}}}, "sbi_fail_tip": {"message": "การโหลดล้มเหลวโปรดรีเฟรชหน้าแล้วลองอีกครั้ง"}, "sbi_kuajing_filter_area": {"message": "พื้นที่"}, "sbi_kuajing_filter_au": {"message": "ออสเตรเลีย"}, "sbi_kuajing_filter_btn_confirm": {"message": "ยืนยัน"}, "sbi_kuajing_filter_de": {"message": "เยอรมนี"}, "sbi_kuajing_filter_destination_country": {"message": "ประเทศปลายทาง"}, "sbi_kuajing_filter_es": {"message": "สเปน"}, "sbi_kuajing_filter_estimate": {"message": "ประมาณการ"}, "sbi_kuajing_filter_estimate_price": {"message": "ราคาโดยประมาณ"}, "sbi_kuajing_filter_estimate_price_formula": {"message": "สูตรราคาโดยประมาณ = (ราคาสินค้า + ค่าขนส่งระหว่างประเทศ)/(1 - อัตรากำไร - อัตราส่วนต้นทุนอื่นๆ)"}, "sbi_kuajing_filter_fr": {"message": "ฝรั่งเศส"}, "sbi_kuajing_filter_kw_placeholder": {"message": "ใส่คีย์เวิร์ดให้ตรงกับชื่อเรื่อง"}, "sbi_kuajing_filter_logistics": {"message": "เทมเพลตโลจิสติกส์"}, "sbi_kuajing_filter_logistics_china_post": {"message": "ไปรษณีย์จีนไปรษณีย์"}, "sbi_kuajing_filter_logistics_discount": {"message": "ส่วนลดค่าขนส่ง"}, "sbi_kuajing_filter_logistics_epacket": {"message": "epacket"}, "sbi_kuajing_filter_logistics_price_formula": {"message": "ค่าขนส่งโลจิสติกส์ระหว่างประเทศ = (น้ำหนัก x ราคาจัดส่ง + ค่าลงทะเบียน) x (1 - ส่วนลด)"}, "sbi_kuajing_filter_others_fee": {"message": "ค่าธรรมเนียมอื่นๆ"}, "sbi_kuajing_filter_profit_percent": {"message": "อัตรากำไร"}, "sbi_kuajing_filter_prop": {"message": "คุณลักษณะ"}, "sbi_kuajing_filter_ru": {"message": "รัสเซีย"}, "sbi_kuajing_filter_total": {"message": "จับคู่ $count$ รายการที่คล้ายกัน", "placeholders": {"count": {"content": "$1"}}}, "sbi_kuajing_filter_uk": {"message": "สหราชอาณาจักร"}, "sbi_kuajing_filter_usa": {"message": "อเมริกา"}, "sbi_login_punish_title__pdd_pifa": {"message": "Pinduoduo ขายส่ง"}, "sbi_msg_no_result": {"message": "ไม่พบผลลัพธ์โปรดเข้าสู่ระบบ $loginSite$ หรือลองใช้รูปภาพอื่น", "placeholders": {"loginSite": {"content": "$1"}}}, "sbi_msg_no_result_check_support_page_l1": {"message": "Safari ไม่สามารถใช้งานได้ชั่วคราวโปรดใช้ $supportPage$", "placeholders": {"supportPage": {"content": "$1"}}}, "sbi_msg_no_result_check_support_page_l2": {"message": "เบราว์เซอร์ Chrome และส่วนขยาย"}, "sbi_msg_no_result_reinstall_l1": {"message": "ไม่พบผลลัพธ์โปรดเข้าสู่ระบบ $loginSite$ หรือลองใช้รูปภาพอื่นหรือติดตั้งเวอร์ชันล่าสุด $latestExtUrl$ ใหม่", "placeholders": {"loginSite": {"content": "$1"}, "latestExtUrl": {"content": "$2"}}}, "sbi_msg_no_result_reinstall_l2": {"message": "รุ่นล่าสุด", "placeholders": {}}, "sbi_search_by_selected_area": {"message": "พื้นที่ที่เลือก"}, "sbi_shipping_": {"message": "จัดส่งในวันเดียวกัน"}, "sbi_specify_category": {"message": "ระบุหมวดหมู่:"}, "sbi_start_crop": {"message": "เลือกพื้นที่"}, "sbi_store_name_alibabaCNKuaJing": {"message": "1688 ในต่างประเทศ"}, "sbi_tutorial_btn_more": {"message": "วิธีอื่น ๆ"}, "sbi_tutorial_find_coupons_on_taobao": {"message": "ค้นหาคูปอง Taobao"}, "sbi_txt__empty_retry": {"message": "ขออภัย ไม่พบผลลัพธ์ โปรดลองอีกครั้ง"}, "sbi_txt__min_order": {"message": "นาที. ใบสั่ง"}, "sbi_visiting": {"message": "การเรียกดู"}, "sbi_yiwugo__jiagexiangtan": {"message": "ติดต่อผู้ขาย"}, "sbi_yiwugo__qigou": {"message": "$num$ ชิ้น (ขั้นต่ำ)", "placeholders": {"num": {"content": "$1"}}}, "sbi_yiwugo__xing": {"message": "ดาว"}, "searchByImage_screenshot": {"message": "ภาพหน้าจอเพียงคลิกเดียว"}, "searchByImage_search": {"message": "ค้นหารายการเดียวกันด้วยคลิกเดียว"}, "searchByImage_size_type": {"message": "ขนาดไฟล์ต้องไม่ใหญ่กว่า $num$ MB, $type$ เท่านั้น", "placeholders": {"num": {"content": "$1"}, "type": {"content": "$2"}}}, "search_by_image_progress_analysing": {"message": "กำลังวิเคราะห์ภาพ"}, "search_by_image_progress_searching": {"message": "ค้นหาผลิตภัณฑ์"}, "search_by_image_progress_sending": {"message": "กำลังส่งภาพ"}, "search_by_image_response_rate": {"message": "อัตราการตอบกลับ: $responseRate$ ของผู้ซื้อที่ติดต่อซัพพลายเออร์รายนี้ได้รับการตอบกลับภายใน $responseInHour$ ชั่วโมง", "placeholders": {"responseRate": {"content": "$1"}, "responseInHour": {"content": "$2"}}}, "search_by_keyword": {"message": "ค้นหาด้วยคำสำคัญ:"}, "select_country_language_modal_title_country": {"message": "ประเทศ"}, "select_country_language_modal_title_language": {"message": "ภาษา"}, "select_country_region_modal_title": {"message": "เลือกประเทศ / ภูมิภาค"}, "select_language_modal_title": {"message": "เลือกภาษา:"}, "select_shop": {"message": "เลือกร้านค้า"}, "sellers_count": {"message": "จำนวนผู้ขายในหน้าปัจจุบัน"}, "sellers_count_per_page": {"message": "จำนวนผู้ขายในหน้าปัจจุบัน"}, "service_score": {"message": "คะแนนการบริการที่ครอบคลุม"}, "set_shortcut_keys": {"message": "ตั้งค่าปุ่มลัด"}, "setting_logo_title": {"message": "ผู้ช่วยซื้อของ"}, "setting_modal_options_position_title": {"message": "ตำแหน่งปลั๊กอิน"}, "setting_modal_options_position_value_left": {"message": "มุมซ้าย"}, "setting_modal_options_position_value_right": {"message": "มุมขวา"}, "setting_modal_options_theme_title": {"message": "สีของธีม"}, "setting_modal_options_theme_value_dark": {"message": "มืด"}, "setting_modal_options_theme_value_light": {"message": "เบา"}, "setting_modal_title": {"message": "การตั้งค่า"}, "setting_options_country_title": {"message": "ประเทศ / ภูมิภาค"}, "setting_options_hover_zoom_desc": {"message": "เลื่อนเมาส์ไปที่เพื่อซูมเข้า"}, "setting_options_hover_zoom_title": {"message": "โฮเวอร์ซูม"}, "setting_options_jd_coupon_desc": {"message": "พบคูปองบน JD.com"}, "setting_options_jd_coupon_title": {"message": "คูปอง JD.com"}, "setting_options_language_title": {"message": "ภาษา"}, "setting_options_price_drop_alert_desc": {"message": "เมื่อราคาสินค้าในรายการโปรดของฉันลดลงคุณจะได้รับการแจ้งเตือนแบบพุช"}, "setting_options_price_drop_alert_title": {"message": "การแจ้งเตือนการลดราคา"}, "setting_options_price_history_on_list_page_desc": {"message": "แสดงประวัติราคาในหน้าค้นหาสินค้า"}, "setting_options_price_history_on_list_page_title": {"message": "ประวัติราคา (หน้ารายการ)"}, "setting_options_price_history_on_produt_page_desc": {"message": "แสดงประวัติผลิตภัณฑ์ในหน้ารายละเอียดผลิตภัณฑ์"}, "setting_options_price_history_on_produt_page_title": {"message": "ประวัติราคา (หน้ารายละเอียด)"}, "setting_options_sales_analysis_desc": {"message": "รองรับสถิติราคา ปริมาณการขาย จำนวนผู้ขาย และอัตราส่วนการขายในร้านในหน้ารายการผลิตภัณฑ์ $platforms$", "placeholders": {"platforms": {"content": "$1"}}}, "setting_options_sales_analysis_title": {"message": "การวิเคราะห์การขาย"}, "setting_options_save_success_msg": {"message": "ประสบความสำเร็จ"}, "setting_options_tacking_price_title": {"message": "การแจ้งเตือนการเปลี่ยนแปลงราคา"}, "setting_options_value_off": {"message": "ปิด"}, "setting_options_value_on": {"message": "บน"}, "setting_pkg_quick_view_desc": {"message": "สนับสนุน: 1688 และ Taobao"}, "setting_saved_message": {"message": "บันทึกการเปลี่ยนแปลงเรียบร้อยแล้ว"}, "setting_section_enable_platform_title": {"message": "เปิดปิด"}, "setting_section_setting_title": {"message": "การตั้งค่า"}, "setting_section_shortcuts_title": {"message": "ทางลัด"}, "settings_aliprice_agent__desc": {"message": "แสดงในหน้ารายละเอียดผลิตภัณฑ์ของ $platforms$", "placeholders": {"platforms": {"content": "$1"}}}, "settings_aliprice_agent__title": {"message": "ซื้อให้ฉัน"}, "settings_copy_link__desc": {"message": "แสดงบนหน้ารายละเอียดสินค้า"}, "settings_copy_link__title": {"message": "ปุ่มคัดลอกและชื่อการค้นหา"}, "settings_currency_desc__for_detail": {"message": "สนับสนุน 1688 หน้ารายละเอียดผลิตภัณฑ์"}, "settings_currency_desc__for_list": {"message": "ค้นหาจากภาพ (รวม 1688/1688 ต่างประเทศ/Taobao)"}, "settings_currency_desc__for_sbi": {"message": "เลือกราคา"}, "settings_currency_desc_display_for_list": {"message": "แสดงในการค้นหาภาพ (รวม 1688/1688 ในต่างประเทศ/Taobao)"}, "settings_currency_rate_desc": {"message": "กำลังอัปเดตอัตราแลกเปลี่ยนจาก \"$currencyRateFrom$\"", "placeholders": {"currencyRateFrom": {"content": "$1"}}}, "settings_currency_rate_from_boc": {"message": "ธนาคารแห่งประเทศจีน"}, "settings_download_images__desc": {"message": "รองรับการดาวน์โหลดรูปภาพจาก $platforms$", "placeholders": {"platforms": {"content": "$1"}}}, "settings_download_images__title": {"message": "ปุ่มดาวน์โหลดภาพ"}, "settings_download_reviews__desc": {"message": "แสดงในหน้ารายละเอียดผลิตภัณฑ์ของ $platforms$", "placeholders": {"platforms": {"content": "$1"}}}, "settings_download_reviews__title": {"message": "ดาวน์โหลดภาพรีวิว"}, "settings_google_translate_desc": {"message": "คลิกขวาเพื่อรับแถบแปลภาษาของ Google"}, "settings_google_translate_title": {"message": "การแปลหน้าเว็บ"}, "settings_historical_trend_desc": {"message": "แสดงที่มุมขวาล่างของรูปภาพในหน้ารายการผลิตภัณฑ์"}, "settings_modal_btn_more": {"message": "การตั้งค่าเพิ่มเติม"}, "settings_productInfo_desc": {"message": "แสดงข้อมูลผลิตภัณฑ์รายละเอียดเพิ่มเติมในหน้ารายการผลิตภัณฑ์ การเปิดใช้งานนี้อาจเพิ่มภาระงานของคอมพิวเตอร์และทำให้หน้าเพจล่าช้า หากการดำเนินการดังกล่าวส่งผลต่อประสิทธิภาพการทำงาน ขอแนะนำให้ปิดใช้งาน"}, "settings_product_recommend__desc": {"message": "แสดงใต้รูปภาพหลักในหน้ารายละเอียดผลิตภัณฑ์ $platforms$", "placeholders": {"platforms": {"content": "$1"}}}, "settings_product_recommend__name": {"message": "สินค้าแนะนำ"}, "settings_research_desc": {"message": "สอบถามข้อมูลรายละเอียดเพิ่มเติมในหน้ารายการผลิตภัณฑ์"}, "settings_sbi_add_to_list": {"message": "เพิ่มใน $listType$", "placeholders": {"listType": {"content": "$1"}}}, "settings_sbi_detail_page_icon_title_desc": {"message": "ภาพขนาดย่อของผลการค้นหาตามภาพถ่าย"}, "settings_sbi_remove_from_list": {"message": "ลบออกจาก $listType$", "placeholders": {"listType": {"content": "$1"}}}, "settings_search_by_image_add_to_blacklist": {"message": "เพิ่มในรายการบล็อก"}, "settings_search_by_image_adjust_entrance_entrance": {"message": "ปรับตำแหน่งทางเข้า"}, "settings_search_by_image_blacklist_desc": {"message": "อย่าแสดงไอคอนบนเว็บไซต์ในบัญชีดำ"}, "settings_search_by_image_blacklist_title": {"message": "รายการบล็อก"}, "settings_search_by_image_bottom_left": {"message": "ซ้ายล่าง"}, "settings_search_by_image_bottom_right": {"message": "ขวาล่าง"}, "settings_search_by_image_clear_blacklist": {"message": "ล้างรายการบล็อก"}, "settings_search_by_image_detail_page_icon_title": {"message": "มินิมอล"}, "settings_search_by_image_detail_page_icon_val_large": {"message": "ใหญ่ขึ้น"}, "settings_search_by_image_detail_page_icon_val_small": {"message": "เล็กลง"}, "settings_search_by_image_display_button_desc": {"message": "คลิกเดียวที่ไอคอนเพื่อค้นหาตามภาพ"}, "settings_search_by_image_display_button_title": {"message": "ไอคอนบนภาพ"}, "settings_search_by_image_sourece_websites_desc": {"message": "ค้นหาผลิตภัณฑ์ต้นทางบนเว็บไซต์เหล่านี้"}, "settings_search_by_image_sourece_websites_title": {"message": "ค้นหาด้วยผลลัพธ์ภาพ"}, "settings_search_by_image_top_left": {"message": "ซ้ายบน"}, "settings_search_by_image_top_right": {"message": "ขวาบน"}, "settings_search_keyword_on_x__desc": {"message": "ค้นหาคำบน $platform$", "placeholders": {"platform": {"content": "$1"}}}, "settings_search_keyword_on_x__title": {"message": "แสดงไอคอน $platform$ เมื่อเลือกคำ", "placeholders": {"platform": {"content": "$1"}}}, "settings_similar_products_desc": {"message": "พยายามค้นหาผลิตภัณฑ์เดียวกันในเว็บไซต์เหล่านั้น (สูงสุด 5 รายการ)"}, "settings_similar_products_title": {"message": "ค้นหาผลิตภัณฑ์เดียวกัน"}, "settings_toolbar_expand_title": {"message": "ย่อปลั๊กอิน"}, "settings_top_toolbar_desc": {"message": "แถบค้นหาที่ด้านบนของหน้า"}, "settings_top_toolbar_title": {"message": "แถบค้นหา"}, "settings_translate_search_desc": {"message": "แปลเป็นภาษาจีนและค้นหา"}, "settings_translate_search_title": {"message": "ค้นหาหลายภาษา"}, "settings_translator_contextmenu_title": {"message": "จับภาพเพื่อแปล"}, "settings_translator_title": {"message": "แปลภาษา"}, "shai_xuan_dao_chu": {"message": "กรองเพื่อส่งออก"}, "shai_xuan_zi_duan": {"message": "ช่องตัวกรอง"}, "shang_jia_shi_jian": {"message": "เมื่อถึงเวลาเก็บรักษา"}, "shang_pin_biao_ti": {"message": "ชื่อผลิตภัณฑ์"}, "shang_pin_dui_bi": {"message": "การเปรียบเทียบผลิตภัณฑ์"}, "shang_pin_lian_jie": {"message": "ลิงค์ผลิตภัณฑ์"}, "shang_pin_xin_xi": {"message": "ข้อมูลผลิตภัณฑ์"}, "share_modal__content": {"message": "แบ่งปันกับเพื่อนของคุณ"}, "share_modal__disable_for_while": {"message": "ฉันไม่ต้องการแบ่งปันอะไร"}, "share_modal__title": {"message": "คุณชอบ $extensionName$ ไหม?", "placeholders": {"extensionName": {"content": "$1"}}}, "sheng_yu_ku_cun": {"message": "ที่เหลือ"}, "shi_fou_ke_ding_zhi": {"message": "มันปรับแต่งได้หรือไม่?"}, "shi_fou_ren_zheng_gong_ying_sha": {"message": "ซัพพลายเออร์ที่ผ่านการรับรอง"}, "shi_fou_ren_zheng_gong_ying_shang_zong_shu": {"message": "ซัพพลายเออร์ที่ผ่านการรับรอง"}, "shi_fou_you_mao_yi_dan_bao": {"message": "การประกันการค้า"}, "shi_fou_you_mao_yi_dan_bao_zong_shu": {"message": "การค้ำประกันการค้า"}, "shipping_fee": {"message": "ค่าจัดส่ง"}, "shop_followers": {"message": "ผู้ติดตามร้าน"}, "shou_qi": {"message": "น้อยกว่า"}, "similar_products_warn_max_platforms": {"message": "สูงสุดถึง 5"}, "sku_calc_price": {"message": "ราคาที่คำนวณได้"}, "sku_calc_price_settings": {"message": "การตั้งค่าราคาที่คำนวณได้"}, "sku_formula": {"message": "สูตร"}, "sku_formula_desc": {"message": "คำอธิบายสูตร"}, "sku_formula_desc_text": {"message": "รองรับสูตรคณิตศาสตร์ที่ซับซ้อน โดยราคาเดิมแสดงโดย A และค่าขนส่งแสดงโดย B\n\n<br/>\n\nรองรับวงเล็บ () บวก + ลบ - คูณ * และหาร /\n\n<br/>\n\nตัวอย่าง:\n\n<br/>\n\n1. เพื่อให้ได้ราคาเดิม 1.2 เท่า จากนั้นบวกค่าขนส่ง สูตรคือ: A*1.2+B\n\n<br/>\n\n2. เพื่อให้ได้ราคาเดิมบวก 1 หยวน จากนั้นคูณด้วย 1.2 เท่า สูตรคือ: (A+1)*1.2\n\n<br/>\n\n3. เพื่อให้ได้ราคาเดิมบวก 10 หยวน จากนั้นคูณด้วย 1.2 เท่า จากนั้นลบ 3 หยวน สูตรคือ: (A+10)*1.2-3"}, "sku_in_stock": {"message": "มีในสต็อก"}, "sku_invalid_formula_format": {"message": "รูปแบบสูตรไม่ถูกต้อง"}, "sku_inventory": {"message": "สินค้าคงคลัง"}, "sku_link_copy_fail": {"message": "คัดลอกสำเร็จ ไม่ได้เลือกข้อมูลจำเพาะและแอตทริบิวต์ SKU"}, "sku_link_copy_success": {"message": "คัดลอกสำเร็จ เลือกข้อมูลจำเพาะและแอตทริบิวต์ SKU แล้ว"}, "sku_list": {"message": "รายการ SKU"}, "sku_min_qrder_qty": {"message": "ปริมาณการสั่งซื้อขั้นต่ำ"}, "sku_name": {"message": "ชื่อ SKU"}, "sku_no": {"message": "หมายเลข"}, "sku_original_price": {"message": "ราคาเดิม"}, "sku_price": {"message": "ราคา SKU"}, "stop_track_time_label": {"message": "กำหนดเวลาติดตาม:"}, "suo_zai_di_qu": {"message": "ที่ตั้ง"}, "tab_pkg_quick_view": {"message": "การตรวจสอบลอจิสติกส์"}, "tab_product_details_price_history": {"message": "ประวัติราคา"}, "tab_product_details_reviews": {"message": "รีวิวภาพถ่าย"}, "tab_product_details_seller_analysis": {"message": "การวิเคราะห์ผู้ขาย"}, "tab_product_details_similar_products": {"message": "ผลิตภัณฑ์เดียวกัน"}, "total_days_listed_per_product": {"message": "ผลรวมของวันที่วางจำหน่าย ÷ จำนวนผลิตภัณฑ์"}, "total_items": {"message": "จำนวนสินค้าทั้งหมด"}, "total_price_per_product": {"message": "ผลรวมของราคา ÷ จำนวนสินค้า"}, "total_rating_per_product": {"message": "ผลรวมของการให้คะแนน ÷ จำนวนผลิตภัณฑ์"}, "total_revenue": {"message": "รายได้รวม"}, "total_revenue40_items": {"message": "รายได้รวมของผลิตภัณฑ์ $amount$ รายการในหน้าปัจจุบัน", "placeholders": {"amount": {"content": "$1"}}}, "total_sales": {"message": "ยอดขายทั้งหมด"}, "total_sales40_items": {"message": "ยอดขายรวมของผลิตภัณฑ์ $amount$ รายการในหน้าปัจจุบัน", "placeholders": {"amount": {"content": "$1"}}}, "track_for_12_months": {"message": "ติดตาม: 1 ปี"}, "track_for_3_months": {"message": "ติดตาม: 3 เดือน"}, "track_for_6_months": {"message": "ติดตาม: 6 เดือน"}, "tracking_price_email_add_btn": {"message": "เพิ่มอีเมล"}, "tracking_price_email_edit_btn": {"message": "แก้ไขอีเมล"}, "tracking_price_email_intro": {"message": "เราจะแจ้งให้คุณทราบทางอีเมล"}, "tracking_price_email_invalid": {"message": "โปรดระบุอีเมลที่ถูกต้อง"}, "tracking_price_email_verified_desc": {"message": "ตอนนี้คุณสามารถรับการแจ้งเตือนการลดราคาของเราได้"}, "tracking_price_email_verified_title": {"message": "ตรวจสอบเรียบร้อยแล้ว"}, "tracking_price_email_verify_desc_line1": {"message": "เราได้ส่งลิงค์ยืนยันไปยังที่อยู่อีเมลของคุณ"}, "tracking_price_email_verify_desc_line2": {"message": "โปรดตรวจสอบกล่องจดหมายอีเมลของคุณ"}, "tracking_price_email_verify_title": {"message": "ตรวจสอบอีเมล์"}, "tracking_price_web_push_notification_intro": {"message": "บนเดสก์ท็อป: AliPrice สามารถตรวจสอบผลิตภัณฑ์ใด ๆ สำหรับคุณและส่งการแจ้งเตือนทางเว็บเมื่อราคาเปลี่ยนแปลง"}, "tracking_price_web_push_notification_title": {"message": "การแจ้งเตือนผ่านเว็บ"}, "translate_im__login_required": {"message": "แปลโดย AliPrice โปรดเข้าสู่ระบบ $loginUrl$", "placeholders": {"loginUrl": {"content": "$1"}}}, "translate_im__paste_fail": {"message": "แปลและคัดลอกไปยังคลิปบอร์ดแล้ว แต่เนื่องจากข้อจำกัดของ Aliwangwang คุณต้องวางด้วยตนเอง!"}, "translate_im__send": {"message": "แปลและส่ง"}, "translate_search": {"message": "แปลและค้นหา"}, "translation_originals_translated": {"message": "ต้นฉบับและภาษาจีน"}, "translation_translated": {"message": "ชาวจีน"}, "translator_btn_capture_txt": {"message": "แปลภาษา"}, "translator_language_auto_detect": {"message": "การตรวจจับอัตโนมัติ"}, "translator_language_detected": {"message": "ตรวจพบ"}, "translator_language_search_placeholder": {"message": "ค้นหาภาษา"}, "try_again": {"message": "ลองอีกครั้ง"}, "tu_pian_chi_cun": {"message": "ขนาดรูปภาพ:"}, "tu_pian_lian_jie": {"message": "ลิงค์รูปภาพ"}, "tui_huan_ti_yan": {"message": "กลับประสบการณ์"}, "tui_huan_ti_yan__desc": {"message": "ประเมินตัวชี้วัดหลังการขายของผู้ขาย"}, "tutorial__show_all": {"message": "คุณสมบัติทั้งหมด"}, "tutorial_ae_popup_title": {"message": "ปักหมุดส่วนขยายเปิด Aliexpress"}, "tutorial_aliexpress_reviews_analysis": {"message": "การวิเคราะห์รีวิวของ AliExpress"}, "tutorial_aliprice_agent_with1688_desc_l1": {"message": "รองรับ USD"}, "tutorial_aliprice_agent_with1688_desc_l2": {"message": "จัดส่งไปยังเกาหลี/ญี่ปุ่น/จีนแผ่นดินใหญ่"}, "tutorial_aliprice_agent_with1688_title": {"message": "1688 รองรับการซื้อจากต่างประเทศ"}, "tutorial_auto_apply_coupon_title": {"message": "ใช้คูปองอัตโนมัติ"}, "tutorial_btn_end": {"message": "จบ"}, "tutorial_btn_example": {"message": "ตัวอย่าง"}, "tutorial_btn_have_a_try": {"message": "โอเคลองได้แล้ว"}, "tutorial_btn_next": {"message": "ต่อไป"}, "tutorial_btn_see_more": {"message": "มากกว่า"}, "tutorial_compare_products": {"message": "เปรียบเทียบกับสไตล์เดียวกัน"}, "tutorial_currency_convert_title": {"message": "การแปลงอัตราแลกเปลี่ยน"}, "tutorial_export_shopping_cart": {"message": "ส่งออก CSV รองรับ Taobao และ 1688"}, "tutorial_export_shopping_cart_title": {"message": "รถเข็นส่งออก"}, "tutorial_price_history_pro": {"message": "แสดงในหน้ารายละเอียดสินค้า\nรองรับ <PERSON><PERSON>, <PERSON><PERSON><PERSON>, Amazon และ Ebay"}, "tutorial_price_history_pro_title": {"message": "ประวัติราคาทั้งปีและประวัติการสั่งซื้อ"}, "tutorial_sbi_context_menu_screenshot_title": {"message": "ค้นหาภาพหน้าจอสำหรับสไตล์เดียวกัน"}, "tutorial_translate_search": {"message": "แปลเพื่อค้นหา"}, "tutorial_translate_search_and_package_tracking": {"message": "การค้นหาการแปลและการติดตามพัสดุภัณฑ์"}, "unit_bao": {"message": "ชิ้น"}, "unit_ben": {"message": "ชิ้น"}, "unit_bi": {"message": "คำสั่งซื้อ"}, "unit_chuang": {"message": "ชิ้น"}, "unit_dai": {"message": "ชิ้น"}, "unit_dui": {"message": "คู่"}, "unit_fen": {"message": "ชิ้น"}, "unit_ge": {"message": "ชิ้น"}, "unit_he": {"message": "ชิ้น"}, "unit_jian": {"message": "ชิ้น"}, "unit_li_fang_mi": {"message": "m³"}, "unit_ping": {"message": "ชิ้น"}, "unit_ping_fang_mi": {"message": "㎡"}, "unit_shuang": {"message": "คู่"}, "unit_tai": {"message": "ชิ้น"}, "unit_ti": {"message": "ชิ้น"}, "unit_tiao": {"message": "ชิ้น"}, "unit_xiang": {"message": "ชิ้น"}, "unit_zhang": {"message": "ชิ้น"}, "unit_zhi": {"message": "ชิ้น"}, "verify_contact_support": {"message": "ติดต่อฝ่ายสนับสนุน"}, "verify_human_verification": {"message": "การตรวจสอบความเป็นมนุษย์"}, "verify_unusual_access": {"message": "ตรวจพบการเข้าถึงที่ผิดปกติ"}, "view_history_clean_all": {"message": "ทำความสะอาดทั้งหมด"}, "view_history_clean_all_warring": {"message": "ล้างบันทึกที่ดูทั้งหมดหรือไม่"}, "view_history_clean_all_warring_title": {"message": "คำเตือน"}, "view_history_viewd": {"message": "ดูแล้ว"}, "website": {"message": "เว็บไซต์"}, "weight": {"message": "น้ำหนัก"}, "wu_fa_huo_qu_dao_gai_shu_ju": {"message": "ไม่สามารถรับข้อมูลได้"}, "wu_liu_shi_xiao": {"message": "จัดส่งตรงเวลา"}, "wu_liu_shi_xiao__desc": {"message": "อัตราการรวบรวม 48 ชั่วโมงและอัตราการปฏิบัติตามของร้านค้าของผู้ขาย"}, "xia_dan_jia": {"message": "ราคาสุดท้าย"}, "xian_xuan_ze_product_attributes": {"message": "เลือกคุณลักษณะของผลิตภัณฑ์"}, "xiao_liang": {"message": "ปริมาณการขาย"}, "xiao_liang_zhan_bi": {"message": "เปอร์เซ็นต์ของยอดขาย"}, "xiao_shi": {"message": "$num$ ชั่วโมง", "placeholders": {"num": {"content": "$1"}}}, "xiao_shou_e": {"message": "รายได้"}, "xiao_shou_e_zhan_bi": {"message": "เปอร์เซ็นต์ของรายรับ"}, "xuan_zhong_x_tiao_ji_lu": {"message": "เลือก $amount$ ระเบียน", "placeholders": {"amoun": {"content": "$1"}, "amount": {"content": "$1"}}}, "yan_xuan": {"message": "ตัวเลือก"}, "yi_ding_zai_zuo_ce": {"message": "ปักหมุดแล้ว"}, "yi_jia_zai_wan_suo_you_shang_pin": {"message": "โหลดผลิตภัณฑ์ทั้งหมดแล้ว"}, "yi_nian_xiao_liang": {"message": "ยอดขายประจำปี"}, "yi_nian_xiao_liang_zhan_bi": {"message": "ส่วนแบ่งยอดขายประจำปี"}, "yi_nian_xiao_shou_e": {"message": "ยอดขายประจำปี"}, "yi_nian_xiao_shou_e_zhan_bi": {"message": "ส่วนแบ่งยอดขายประจำปี"}, "yi_shua_xin": {"message": "สดชื่น"}, "yin_cang_xiang_tong_dian": {"message": "ซ่อนความคล้ายคลึงกัน"}, "you_xiao_liang": {"message": "ด้วยปริมาณการขาย"}, "yu_ji_dao_da_shi_jian": {"message": "เวลาที่คาดว่าจะมาถึง"}, "yuan_gong_ren_shu": {"message": "จำนวนพนักงาน"}, "yue_cheng_jiao": {"message": "ปริมาณรายเดือน"}, "yue_dai_xiao": {"message": "ดรอปชิป"}, "yue_dai_xiao__desc": {"message": "ยอดขายดรอปชิปในช่วง 30 วันที่ผ่านมา"}, "yue_dai_xiao_pai_xu__desc": {"message": "ยอดขายดรอปชิปในช่วง 30 วันที่ผ่านมา เรียงจากสูงไปต่ำ"}, "yue_xiao_liang__desc": {"message": "ปริมาณการขายในช่วง 30 วันที่ผ่านมา"}, "zhan_kai": {"message": "มากกว่า"}, "zhe_kou": {"message": "ส่วนลด"}, "zhi_chi_yi_jian_dai_fa": {"message": "ดรอปชิป"}, "zhi_chi_yi_jian_dai_fa_bao_you": {"message": "จัดส่งฟรี"}, "zhi_fu_ding_dan_shu": {"message": "คำสั่งซื้อที่ชำระเงินแล้ว"}, "zhi_fu_ding_dan_shu__desc": {"message": "จำนวนคำสั่งซื้อผลิตภัณฑ์นี้ (30 วัน)"}, "zhu_ce_xing_zhi": {"message": "ลักษณะการลงทะเบียน"}, "zi_ding_yi_tiao_jian": {"message": "เงื่อนไขที่กำหนดเอง"}, "zi_duan": {"message": "ฟิลด์"}, "zi_ti_xiao_liang": {"message": "ขายการเปลี่ยนแปลง"}, "zong_he_fu_wu_fen": {"message": "คะแนนโดยรวม"}, "zong_he_fu_wu_fen__desc": {"message": "คะแนนโดยรวมของการบริการผู้ขาย"}, "zong_he_fu_wu_fen__short": {"message": "เรตติ้ง"}, "zong_he_ti_yan_fen": {"message": "เรตติ้ง"}, "zong_he_ti_yan_fen_3": {"message": "ต่ำกว่า 4 ดาว"}, "zong_he_ti_yan_fen_4": {"message": "4 - 4.5 ดาว"}, "zong_he_ti_yan_fen_4_5": {"message": "4.5 - 5.0 ดาว"}, "zong_he_ti_yan_fen_5": {"message": "5 ดาว"}, "zong_ku_cun": {"message": "สินค้าคงคลังทั้งหมด"}, "zong_xiao_liang": {"message": "ยอดขายทั้งหมด"}, "zui_jin_30D_3Min_xiang_ying_lv": {"message": "อัตราการตอบกลับ 3 นาทีในช่วง 30 วันที่ผ่านมา"}, "zui_jin_30D_48H_lan_shou_lv": {"message": "อัตราการฟื้นตัว 48H ในช่วง 30 วันที่ผ่านมา"}, "zui_jin_30D_48H_lv_yue_lv": {"message": "อัตราประสิทธิภาพ 48H ในช่วง 30 วันที่ผ่านมา"}, "zui_jin_30D_jiao_yi_ji_lu": {"message": "บันทึกการซื้อขาย (30 วัน)"}, "zui_jin_30D_jiao_yi_ji_lu__desc": {"message": "บันทึกการซื้อขาย (30 วัน)"}, "zui_jin_30D_jiu_fen_lv": {"message": "อัตราการโต้แย้งใน 30 วันที่ผ่านมา"}, "zui_jin_30D_pin_zhi_tui_kuan_lv": {"message": "อัตราการคืนเงินที่มีคุณภาพใน 30 วันที่ผ่านมา"}, "zui_jin_30D_zhi_fu_ding_dan_shu": {"message": "จำนวนคำสั่งชำระเงินใน 30 วันที่ผ่านมา"}}