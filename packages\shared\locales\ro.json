{"EXTENSION_NAME": {"message": "TODO: EXTENSION_NAME"}, "EXTENSION_DESCRIPTION": {"message": "TODO: EXTENSION_DESCRIPTION"}, "1688_cross_border_hot_selling": {"message": "1688 Punct transfront<PERSON><PERSON> de vân<PERSON>e fier<PERSON>te"}, "1688_shi_li_ren_zheng": {"message": "Certificare de rezistență 1688"}, "1_jian_qi_pi": {"message": "MOQ: 1"}, "1year_yi_shang": {"message": "Mai mult de 1 an"}, "24H": {"message": "24H"}, "24H_fa_huo": {"message": "Livrare in 24 de ore"}, "24H_lan_shou_lv": {"message": "Ta<PERSON><PERSON> de ambalare 24 de ore"}, "30D_shang_xin": {"message": "<PERSON>i sosiri lunare"}, "30d_sales": {"message": "$amount$ vândut în 30 de zile", "placeholders": {"amount": {"content": "$1"}}}, "3Min_xiang_ying_lv": {"message": "Răspuns în 3 min."}, "3Min_xiang_ying_lv__desc": {"message": "Proporția răspunsurilor eficiente ale lui Wangwang la mesajele de întrebări ale cumpărătorului în 3 minute în ultimele 30 de zile"}, "48H": {"message": "48H"}, "48H_fa_huo": {"message": "Livrare in 48 de ore"}, "48H_lan_shou_lv": {"message": "Rata de ambalare la 48 de ore"}, "48H_lan_shou_lv__desc": {"message": "Raportul dintre numărul comenzii ridicate în 48 de ore și numărul total de comenzi"}, "48H_lv_yue_lv": {"message": "Rata de performanță de 48 de ore"}, "48H_lv_yue_lv__desc": {"message": "Raportul dintre numărul comenzii ridicate sau livrate în 48 de ore față de numărul total de comenzi"}, "72H": {"message": "72H"}, "7D_shang_xin": {"message": "Noi sosiri s<PERSON>ă<PERSON>ânale"}, "7D_wu_li_you": {"message": "7 zile f<PERSON><PERSON><PERSON>"}, "ABS_title_text": {"message": "Această listă include o poveste de brand"}, "AC_title_text": {"message": "Această listă are insigna Amazon's Choice"}, "A_title_text": {"message": "Această listă are o pagină de conținut A+"}, "BS_title_text": {"message": "Această listă este clasată ca cea mai bună vânzări $num$ în categoria $type$", "placeholders": {"type": {"content": "$1"}, "num": {"content": "$2"}}}, "LTD_title_text": {"message": "LTD (Ofertă cu durată limitată) înseamnă că această înregistrare face parte dintr-un eveniment de „promoție de 7 zile”."}, "NR_title_text": {"message": "Această înregistrare este clasată ca $num$ Noua lansare în categoria $type$", "placeholders": {"type": {"content": "$1"}, "num": {"content": "$2"}}}, "SBV_title_text": {"message": "Această listă are un anunț video, un tip de anunț PPC care apare de obicei în mijlocul rezultatelor căutării"}, "SB_title_text": {"message": "Această înregistrare are un anunț de marcă, un tip de anunț PPC care apare de obicei în partea de sus sau de jos a rezultatelor căutării"}, "SP_title_text": {"message": "Această înregistrare are un anunț de produs sponsorizat"}, "V_title_text": {"message": "Această listă are o introducere video"}, "advanced_research": {"message": "Cercetare avansată"}, "agent_ds1688___my_order": {"message": "Comenzile mele"}, "agent_ds1688__add_to_cart": {"message": "Achiziție de peste mări"}, "agent_ds1688__cart": {"message": "Căru<PERSON>r de cumpără<PERSON>i"}, "agent_ds1688__desc": {"message": "Furnizat de 1688. Acceptă achiziția directă din străinătate, plata în USD și livrarea la depozitul dvs. de tranzit din China."}, "agent_ds1688__freight": {"message": "Calculator costuri de transport"}, "agent_ds1688__help": {"message": "<PERSON><PERSON><PERSON>"}, "agent_ds1688__packages": {"message": "Foarte de transport"}, "agent_ds1688__profile": {"message": "<PERSON><PERSON><PERSON><PERSON> personal"}, "agent_ds1688__warehouse": {"message": "Depozitul meu"}, "ai_comment_analysis_advantage": {"message": "Pro"}, "ai_comment_analysis_ai": {"message": "Analiza revizuirii AI"}, "ai_comment_analysis_available": {"message": "Disponibil"}, "ai_comment_analysis_balance": {"message": "<PERSON><PERSON> insuficiente, vă rugăm să completați"}, "ai_comment_analysis_behavior": {"message": "Comportament"}, "ai_comment_analysis_characteristic": {"message": "Caracteristicile m<PERSON>ii"}, "ai_comment_analysis_comment": {"message": "Produsul nu are suficiente recenzii pentru a trage concluzii exacte, vă rugăm să selectați un produs cu mai multe recenzii."}, "ai_comment_analysis_consume": {"message": "Consum estimat"}, "ai_comment_analysis_default": {"message": "Re<PERSON>nzii implicite"}, "ai_comment_analysis_desire": {"message": "Așteptările c<PERSON>ilor"}, "ai_comment_analysis_disadvantage": {"message": "Contra"}, "ai_comment_analysis_free": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON> gratuite"}, "ai_comment_analysis_freeNum": {"message": "Se va folosi 1 credit gratuit"}, "ai_comment_analysis_go_recharge": {"message": "Du-te la încărcare"}, "ai_comment_analysis_intelligence": {"message": "Analiză inteligentă de revizuire"}, "ai_comment_analysis_location": {"message": "Locație"}, "ai_comment_analysis_motive": {"message": "Motivația de cumpărare"}, "ai_comment_analysis_network_error": {"message": "<PERSON><PERSON><PERSON> de reț<PERSON>, vă rugăm să încercați din nou"}, "ai_comment_analysis_normal": {"message": "Recenzii de fotografii"}, "ai_comment_analysis_number_reviews": {"message": "Număr de recenzii: $num$, Consum estimat: $consume$", "placeholders": {"num": {"content": "$1"}, "consume": {"content": "$2"}}}, "ai_comment_analysis_ordinary": {"message": "Comentarii generale"}, "ai_comment_analysis_percentage": {"message": "Procent"}, "ai_comment_analysis_problem": {"message": "Probleme cu plata"}, "ai_comment_analysis_reanalysis": {"message": "Reconsidera"}, "ai_comment_analysis_reason": {"message": "Motiv"}, "ai_comment_analysis_recharge": {"message": "Umple"}, "ai_comment_analysis_recharged": {"message": "Am completat"}, "ai_comment_analysis_retry": {"message": "Reîncercați"}, "ai_comment_analysis_scene": {"message": "Scenariul de utilizare"}, "ai_comment_analysis_start": {"message": "Începeți să analizați"}, "ai_comment_analysis_subject": {"message": "Subiecte"}, "ai_comment_analysis_time": {"message": "<PERSON><PERSON> de utiliza<PERSON>"}, "ai_comment_analysis_tool": {"message": "Instrument AI"}, "ai_comment_analysis_user_portrait": {"message": "Profil de utilizator"}, "ai_comment_analysis_welcome": {"message": "Bun venit la analiza analizei AI"}, "ai_comment_analysis_year": {"message": "Comentarii din ultimul an"}, "ai_listing_Exclude_keywords": {"message": "Exclude<PERSON>i cuvinte cheie"}, "ai_listing_Login_the_feature": {"message": "Este necesară autentificarea pentru funcție"}, "ai_listing_aI_generation": {"message": "Generarea AI"}, "ai_listing_add_automatic": {"message": "Automat"}, "ai_listing_add_dictionary_new": {"message": "Creați o nouă bibliotecă"}, "ai_listing_add_enter_keywords": {"message": "Introduceti cuvintele cheie"}, "ai_listing_add_inputkey_selling": {"message": "Introduceți un punct de vânzare și apăsați $key$ pentru a finaliza adăugarea", "placeholders": {"key": {"content": "$1"}}}, "ai_listing_add_inputkey_warning": {"message": "<PERSON><PERSON>, până la $amount$ puncte de vânzare", "placeholders": {"amount": {"content": "$1"}}}, "ai_listing_add_keywords": {"message": "Adăugați cuvinte cheie"}, "ai_listing_add_manually": {"message": "Adăugați manual"}, "ai_listing_add_selling": {"message": "Adăugați puncte de vânzare"}, "ai_listing_added_keywords": {"message": "S-au adăugat cuvinte cheie"}, "ai_listing_added_successfully": {"message": "Adăugat cu succes"}, "ai_listing_addexcluded_keywords": {"message": "Introduceți cuvintele cheie excluse, apă<PERSON><PERSON>i Enter pentru a finaliza adăugarea."}, "ai_listing_adding_selling": {"message": "Puncte de vân<PERSON>e ad<PERSON>"}, "ai_listing_addkeyword_enter": {"message": "Introduceți cuvintele de atribut cheie și apăsați Enter pentru a termina adăugarea"}, "ai_listing_ai_description": {"message": "Biblioteca de cuvinte de descriere AI"}, "ai_listing_ai_dictionary": {"message": "Biblioteca de cuvinte de titlu AI"}, "ai_listing_ai_title": {"message": "Titlul AI"}, "ai_listing_aidescription_repeated": {"message": "Numele bibliotecii de cuvinte de descriere AI nu poate fi repetat"}, "ai_listing_aititle_repeated": {"message": "Numele bibliotecii de cuvinte de titlu AI nu poate fi repetat"}, "ai_listing_data_comes_from": {"message": "Aceste date provin de la:"}, "ai_listing_deleted_successfully": {"message": "S-a șters cu succes"}, "ai_listing_dictionary_name": {"message": "Numele bibliotecii"}, "ai_listing_edit_dictionary": {"message": "Modificați biblioteca..."}, "ai_listing_edit_word_library": {"message": "Editați biblioteca de cuvinte"}, "ai_listing_enter_keywords": {"message": "Introduceți cuvinte cheie și apăsați $key$ pentru a finaliza adăugarea", "placeholders": {"key": {"content": "$1"}}}, "ai_listing_exceeded_maximum": {"message": "Limita a fost depășită, maxim $amount$ cuvinte cheie", "placeholders": {"amount": {"content": "$1"}}}, "ai_listing_excluded_word_library": {"message": "Biblioteca de cuvinte exclusă"}, "ai_listing_generate_characters": {"message": "Generați personaje"}, "ai_listing_generation_platform": {"message": "Platformă de generație"}, "ai_listing_help_optimize": {"message": "Ajută-mă să optimizez titlul produsului, titlul original este"}, "ai_listing_include_selling": {"message": "Alte puncte de vânzare au inclus:"}, "ai_listing_included_keyword": {"message": "<PERSON><PERSON><PERSON><PERSON> cheie incluse"}, "ai_listing_included_keywords": {"message": "<PERSON><PERSON><PERSON><PERSON> cheie incluse"}, "ai_listing_input_selling": {"message": "Introduceți un punct de vânzare"}, "ai_listing_input_selling_fit": {"message": "Introduceți puncte de vânzare pentru a se potrivi cu titlul"}, "ai_listing_input_selling_please": {"message": "<PERSON>ă rugăm să <PERSON>ți punctele de vânzare"}, "ai_listing_intelligently_title": {"message": "Introduceți conținutul necesar mai sus pentru a genera titlul în mod inteligent"}, "ai_listing_keyword_product_title": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON> cheie titlu de produs"}, "ai_listing_keywords_repeated": {"message": "<PERSON><PERSON><PERSON>tele cheie nu pot fi repetate"}, "ai_listing_listed_selling_points": {"message": "Puncte de vânzare incluse"}, "ai_listing_long_title_1": {"message": "Conține informații de bază, cum ar fi numele mărcii, tipul produsului, caracteristicile produsului etc."}, "ai_listing_long_title_2": {"message": "Pe baza titlului standard al produsului, se adaugă cuvinte cheie propice SEO."}, "ai_listing_long_title_3": {"message": "Pe lângă faptul că conțin numele mărcii, tipul de produs, caracteristicile produsului și cuvintele cheie, cuvintele cheie cu coadă lungă sunt, de asemenea, incluse pentru a obține clasamente mai ridicate în interogări de căutare specifice, segmentate."}, "ai_listing_longtail_keyword_product_title": {"message": "Titlul produsului cu cuvinte cheie cu coadă lungă"}, "ai_listing_manually_enter": {"message": "Introduceți manual..."}, "ai_listing_network_not_working": {"message": "Internetul nu este disponibil, VPN este necesar pentru a accesa ChatGPT"}, "ai_listing_new_dictionary": {"message": "Creați o nouă bibliotecă de cuvinte..."}, "ai_listing_new_generate": {"message": "Genera"}, "ai_listing_optional_words": {"message": "Cuvinte opționale"}, "ai_listing_original_title": {"message": "Titlul original"}, "ai_listing_other_keywords_included": {"message": "Alte cuvinte cheie au inclus:"}, "ai_listing_please_again": {"message": "<PERSON>ă rugăm să încercați din nou"}, "ai_listing_please_select": {"message": "Următoarele titluri au fost generate pentru dvs., vă rugăm s<PERSON> selectați:"}, "ai_listing_product_category": {"message": "Categorie produs"}, "ai_listing_product_category_is": {"message": "Categoria de produse este"}, "ai_listing_product_category_to": {"message": "Din ce categorie apartine produsul?"}, "ai_listing_random_keywords": {"message": "Cuvinte cheie aleatorii $amount$", "placeholders": {"amount": {"content": "$1"}}}, "ai_listing_random_selling": {"message": "$amount$ puncte de vânzare aleatorii", "placeholders": {"amount": {"content": "$1"}}}, "ai_listing_randomize_keywords": {"message": "Randomizați din biblioteca de cuvinte"}, "ai_listing_search_selling": {"message": "Căutați după punctul de vânzare"}, "ai_listing_select_product_categories": {"message": "Selectați automat categoriile de produse."}, "ai_listing_select_product_selling_points": {"message": "Selectați automat punctele de vânzare ale produselor"}, "ai_listing_select_word_library": {"message": "Selectați biblioteca de cuvinte"}, "ai_listing_selling": {"message": "<PERSON>un<PERSON><PERSON> de vânzare"}, "ai_listing_selling_ask": {"message": "Ce alte cerințe privind punctul de vânzare există pentru titlu?"}, "ai_listing_selling_optional": {"message": "Puncte de vânzare opționale"}, "ai_listing_selling_repeat": {"message": "Punctele nu pot fi duplicate"}, "ai_listing_set_excluded": {"message": "Setați ca bibliotecă de cuvinte excluse"}, "ai_listing_set_include_selling_points": {"message": "Includeți punctele de vânzare"}, "ai_listing_set_included": {"message": "Setați ca bibliotecă de cuvinte inclusă"}, "ai_listing_set_selling_dictionary": {"message": "Setați ca bibliotecă de puncte de vânzare"}, "ai_listing_standard_product_title": {"message": "Titlul standard al produsului"}, "ai_listing_translated_title": {"message": "<PERSON><PERSON><PERSON> tradus"}, "ai_listing_visit_chatGPT": {"message": "Vizitați ChatGPT"}, "ai_listing_what_other_keywords": {"message": "Ce alte cuvinte cheie sunt necesare pentru titlu?"}, "aliprice_coupons_apply_again": {"message": "Aplicați din nou"}, "aliprice_coupons_apply_coupons": {"message": "Aplicați cupoane"}, "aliprice_coupons_apply_success": {"message": "Cupon găsit: economisiți $amount$", "placeholders": {"amount": {"content": "$1"}}}, "aliprice_coupons_applying": {"message": "Testarea codurilor pentru cele mai bune oferte..."}, "aliprice_coupons_applying_desc": {"message": "Înregistrare: $code$", "placeholders": {"code": {"content": "$1"}}}, "aliprice_coupons_back_to_checkout": {"message": "Continuați la Checkout"}, "aliprice_coupons_found_coupons": {"message": "Am găsit cupoane $amount$", "placeholders": {"amount": {"content": "$1"}}}, "aliprice_coupons_found_coupons_desc": {"message": "Sunteți gata să plătiți? Să ne asigurăm că obțineți cel mai bun preț!"}, "aliprice_coupons_no_coupon_aviable": {"message": "Aceste coduri nu au funcționat. Nu e mare lucru – primești deja cel mai bun preț."}, "aliprice_coupons_toolbar_btn": {"message": "Obțineți cupoane"}, "aliww_translate": {"message": "Aliwangwang Chat Translator"}, "aliww_translate_supports": {"message": "Suport: 1688 & Taobao"}, "amazon_extended_keywords_Keywords": {"message": "<PERSON><PERSON><PERSON><PERSON> cheie"}, "amazon_extended_keywords_copy_all": {"message": "Copiați tot"}, "amazon_extended_keywords_more": {"message": "<PERSON> mult"}, "an_lei_ji_xiao_liang_pai_xu": {"message": "Sortați după vânz<PERSON>ri cumulate"}, "an_lei_xing_cha_kan": {"message": "Vizualizare du<PERSON> tip"}, "an_yue_dai_xiao_pai_xu": {"message": "Clasament prin v<PERSON><PERSON><PERSON><PERSON> dropshipping"}, "apra_btn__cat_name": {"message": "<PERSON><PERSON><PERSON> recenzii<PERSON>"}, "apra_chart__name": {"message": "Procentul vânzărilor de produse în funcție de țară"}, "apra_chart__update_at": {"message": "Actualizați ora $time$", "placeholders": {"time": {"content": "$1"}}}, "apra_name": {"message": "Statistici de vânzări ale țărilor"}, "auto_opening": {"message": "Se deschide automat în $num$ secunde", "placeholders": {"num": {"content": "$1"}}}, "auto_page_next_x_pages": {"message": "Următoarele pagini $autoPaging$", "placeholders": {"autoPaging": {"content": "$1"}}}, "average_days_listed": {"message": "Medie pe zilele de raft"}, "average_hui_fu_lv": {"message": "Rata medie de răspuns"}, "average_ping_gong_ying_shang_deng_ji": {"message": "<PERSON>vel mediu de fur<PERSON>"}, "average_price": {"message": "Prețul mediu"}, "average_qi_ding_liang": {"message": "MOQ mediu"}, "average_rating": {"message": "Rata medie"}, "average_ren_zheng_gong_ying_nian_chang": {"message": "Ani medii de certificare"}, "average_revenue": {"message": "Venitul mediu"}, "average_revenue_per_product": {"message": "Venitul total ÷ Numărul de produse"}, "average_sales": {"message": "<PERSON>ân<PERSON><PERSON><PERSON> medii"}, "average_sales_per_product": {"message": "Vânzări totale ÷ Număr de produse"}, "bao_han": {"message": "<PERSON><PERSON><PERSON>"}, "bao_zheng_jin": {"message": "<PERSON><PERSON>"}, "bian_ti_shu": {"message": "<PERSON><PERSON><PERSON>"}, "biao_ti": {"message": "Titlu"}, "blacklist_add_blacklist": {"message": "Blocați acest magazin"}, "blacklist_address_incorrect": {"message": "Adresa este incorectă. Te rog verifica."}, "blacklist_blacked_out": {"message": "Magazinul a fost blocat"}, "blacklist_blacklist": {"message": "Lista neagră"}, "blacklist_no_records_yet": {"message": "<PERSON>ci o înregistrare încă!"}, "blue_rocket": {"message": "로켓배송"}, "brand_name": {"message": "<PERSON><PERSON>"}, "btn_aliprice_agent__daigou": {"message": "Intermediar de cumpărare"}, "btn_aliprice_agent__dropshipping": {"message": "Dropshipping"}, "btn_have_a_try": {"message": "Incearca"}, "btn_refresh": {"message": "Reîmprospă<PERSON>"}, "btn_try_it_now": {"message": "Încercați acum"}, "btn_txt_view_on_aliprice": {"message": "Vezi pe AliPrice"}, "bu_bao_han": {"message": "<PERSON>u contine"}, "bulk_copy_links": {"message": "<PERSON>uri de copiere în bloc"}, "bulk_copy_products": {"message": "Produse de copiere în vrac"}, "cai_gou_zi_xun": {"message": "<PERSON><PERSON><PERSON>"}, "cai_gou_zi_xun__desc": {"message": "Rata de răspuns de trei minute a vânzătorului"}, "can_ping_lei_xing": {"message": "Tip"}, "cao_zuo": {"message": "Operațiune"}, "chan_pin_ID": {"message": "ID produs"}, "chan_pin_e_wai_xin_xi": {"message": "Informații suplimentare despre produs"}, "chan_pin_lian_jie": {"message": "Link produs"}, "cheng_li_shi_jian": {"message": "Timpul de înființare"}, "city__aba": {"message": "Aba"}, "city__aksu": {"message": "<PERSON><PERSON><PERSON>"}, "city__alaer": {"message": "<PERSON><PERSON><PERSON>"}, "city__altai": {"message": "Altai"}, "city__alxaleague": {"message": "Alxa League"}, "city__ankang": {"message": "Ankan<PERSON>"}, "city__anqing": {"message": "Anqing"}, "city__anshan": {"message": "<PERSON><PERSON>"}, "city__anshun": {"message": "<PERSON><PERSON><PERSON>"}, "city__anyang": {"message": "Anyang"}, "city__baicheng": {"message": "Baicheng"}, "city__baise": {"message": "Baise"}, "city__baisha": {"message": "Baisha"}, "city__baishan": {"message": "Baishan"}, "city__baiyin": {"message": "<PERSON><PERSON><PERSON>"}, "city__baoding": {"message": "<PERSON>od<PERSON>"}, "city__baoji": {"message": "<PERSON><PERSON><PERSON>"}, "city__baoshan": {"message": "<PERSON><PERSON><PERSON>"}, "city__baoting": {"message": "Baoting"}, "city__baotou": {"message": "<PERSON><PERSON><PERSON>"}, "city__bayannur": {"message": "Bayannur"}, "city__bayinguoleng": {"message": "Bayinguoleng"}, "city__bazhong": {"message": "Bazhong"}, "city__beihai": {"message": "Beihai"}, "city__bengbu": {"message": "<PERSON><PERSON><PERSON>"}, "city__benxi": {"message": "Benxi"}, "city__bijie": {"message": "Bijie"}, "city__binzhou": {"message": "Binzhou"}, "city__bortala": {"message": "<PERSON><PERSON><PERSON>"}, "city__bozhou": {"message": "Bozhou"}, "city__cangzhou": {"message": "Cangzhou"}, "city__chamdo": {"message": "<PERSON><PERSON><PERSON>"}, "city__changchun": {"message": "<PERSON><PERSON><PERSON>"}, "city__changde": {"message": "<PERSON><PERSON>"}, "city__changhua": {"message": "Changhua"}, "city__changji": {"message": "Changji"}, "city__changjiang": {"message": "Changjiang"}, "city__changsha": {"message": "Changsha"}, "city__changzhi": {"message": "Changzhi"}, "city__changzhou": {"message": "Changzhou"}, "city__chaohu": {"message": "<PERSON><PERSON>"}, "city__chaoyang": {"message": "Chaoyang"}, "city__chaozhou": {"message": "Chaozhou"}, "city__chengde": {"message": "Chengde"}, "city__chengdu": {"message": "Chengdu"}, "city__chengmai": {"message": "<PERSON><PERSON><PERSON>"}, "city__chenzhou": {"message": "Chenzhou"}, "city__chiayi": {"message": "<PERSON><PERSON><PERSON>"}, "city__chifeng": {"message": "<PERSON><PERSON>"}, "city__chizhou": {"message": "Chizhou"}, "city__chongzuo": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__chuxiong": {"message": "Chuxiong"}, "city__chuzhou": {"message": "Chuzhou"}, "city__dali": {"message": "<PERSON><PERSON>"}, "city__dalian": {"message": "<PERSON><PERSON>"}, "city__dandong": {"message": "Dandong"}, "city__danzhou": {"message": "Danzhou"}, "city__daqing": {"message": "Daqing"}, "city__datong": {"message": "<PERSON><PERSON><PERSON>"}, "city__daxinganling": {"message": "<PERSON><PERSON>'<PERSON>ling"}, "city__dazhou": {"message": "Dazhou"}, "city__dehong": {"message": "<PERSON><PERSON>"}, "city__deyang": {"message": "Deyang"}, "city__dezhou": {"message": "Dezhou"}, "city__dingan": {"message": "Ding'an"}, "city__dingxi": {"message": "Dingxi"}, "city__diqing": {"message": "Diqing"}, "city__dongfang": {"message": "<PERSON><PERSON>g"}, "city__dongguan": {"message": "Dongguan"}, "city__dongying": {"message": "<PERSON><PERSON>"}, "city__enshi": {"message": "<PERSON><PERSON>"}, "city__ezhou": {"message": "Ezhou"}, "city__fangchenggang": {"message": "Fangchenggang"}, "city__foshan": {"message": "<PERSON><PERSON><PERSON>"}, "city__fushun": {"message": "<PERSON><PERSON><PERSON>"}, "city__fuxin": {"message": "Fuxin"}, "city__fuyang": {"message": "Fuyang"}, "city__fuzhou": {"message": "Fuzhou"}, "city__gannan": {"message": "<PERSON><PERSON><PERSON>"}, "city__ganzhou": {"message": "Ganzhou"}, "city__ganzi": {"message": "Ganzi"}, "city__guangan": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__guangyuan": {"message": "Guangyuan"}, "city__guangzhou": {"message": "Guangzhou"}, "city__guigang": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__guilin": {"message": "<PERSON><PERSON><PERSON>"}, "city__guiyang": {"message": "Guiyang"}, "city__guolok": {"message": "Guolok"}, "city__guyuan": {"message": "<PERSON><PERSON>"}, "city__haibei": {"message": "Haibei"}, "city__haidong": {"message": "Haidong"}, "city__haikou": {"message": "Hai<PERSON><PERSON>"}, "city__hainan": {"message": "Hainan"}, "city__haixi": {"message": "Haixi"}, "city__hami": {"message": "<PERSON><PERSON>"}, "city__handan": {"message": "<PERSON><PERSON>"}, "city__hangzhou": {"message": "Hangzhou"}, "city__hanzhong": {"message": "Hanzhong"}, "city__harbin": {"message": "<PERSON><PERSON><PERSON>"}, "city__hebi": {"message": "<PERSON><PERSON>"}, "city__hechi": {"message": "<PERSON><PERSON>"}, "city__hefei": {"message": "<PERSON><PERSON><PERSON>"}, "city__hegang": {"message": "<PERSON><PERSON><PERSON>"}, "city__heihe": {"message": "<PERSON><PERSON><PERSON>"}, "city__hengshui": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__hengyang": {"message": "Hengyang"}, "city__heyuan": {"message": "<PERSON><PERSON>"}, "city__heze": {"message": "<PERSON><PERSON>"}, "city__hezhou": {"message": "Hezhou"}, "city__hohhot": {"message": "Hohhot"}, "city__honghe": {"message": "<PERSON><PERSON>"}, "city__hongkongisland": {"message": "Hong Kong Island"}, "city__hotan": {"message": "<PERSON><PERSON>"}, "city__hsinchu": {"message": "<PERSON><PERSON><PERSON>"}, "city__huaian": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__huaibei": {"message": "<PERSON><PERSON><PERSON>"}, "city__huaihua": {"message": "Huaihua"}, "city__huaisouth": {"message": "Huai South"}, "city__hualien": {"message": "<PERSON><PERSON><PERSON>"}, "city__huanggang": {"message": "<PERSON><PERSON><PERSON>"}, "city__huangnan": {"message": "Huangnan"}, "city__huangshan": {"message": "<PERSON><PERSON>"}, "city__huangshi": {"message": "<PERSON><PERSON>"}, "city__huizhou": {"message": "Huizhou"}, "city__huludao": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__hulunbuir": {"message": "Hulunbuir"}, "city__huzhou": {"message": "Huzhou"}, "city__jiamusi": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__jian": {"message": "<PERSON><PERSON><PERSON>"}, "city__jiangmen": {"message": "Jiangmen"}, "city__jiaozuo": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__jiaxing": {"message": "Jiaxing"}, "city__jiayuguan": {"message": "Jiayuguan"}, "city__jieyang": {"message": "<PERSON><PERSON><PERSON>"}, "city__jilin": {"message": "<PERSON><PERSON>"}, "city__jinan": {"message": "<PERSON><PERSON>"}, "city__jinchang": {"message": "Jinchang"}, "city__jincheng": {"message": "Jincheng"}, "city__jingdezhen": {"message": "Jingdez<PERSON>"}, "city__jingmen": {"message": "Jingmen"}, "city__jingzhou": {"message": "Jingzhou"}, "city__jinhua": {"message": "Jinhua"}, "city__jining": {"message": "Jining"}, "city__jinzhong": {"message": "Jinzhong"}, "city__jinzhou": {"message": "Jinzhou"}, "city__jiujiang": {"message": "Jiujiang"}, "city__jiuquan": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__jixi": {"message": "Ji<PERSON>"}, "city__kaifeng": {"message": "Kaifeng"}, "city__kaohsiung": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__karamay": {"message": "<PERSON><PERSON><PERSON>"}, "city__kashgar": {"message": "Ka<PERSON><PERSON>"}, "city__keelung": {"message": "Keelung"}, "city__kinmen": {"message": "Kinmen"}, "city__kizilsu": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__kowloon": {"message": "Kowloon"}, "city__kunming": {"message": "Ku<PERSON><PERSON>"}, "city__laibin": {"message": "<PERSON><PERSON>"}, "city__laiwu": {"message": "<PERSON><PERSON>"}, "city__langfang": {"message": "<PERSON><PERSON><PERSON>"}, "city__lanzhou": {"message": "Lanzhou"}, "city__ledong": {"message": "<PERSON><PERSON>"}, "city__leshan": {"message": "<PERSON><PERSON>"}, "city__lhasa": {"message": "Lhasa"}, "city__liangshan": {"message": "Liangshan"}, "city__lianyungang": {"message": "Lianyungang"}, "city__liaocheng": {"message": "<PERSON><PERSON><PERSON>"}, "city__liaoyang": {"message": "<PERSON><PERSON><PERSON>"}, "city__liaoyuan": {"message": "<PERSON><PERSON><PERSON>"}, "city__lienchiang": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__lijiang": {"message": "Lijiang"}, "city__lincang": {"message": "Lincang"}, "city__linfen": {"message": "Linfen"}, "city__lingao": {"message": "Lingao"}, "city__lingshui": {"message": "<PERSON><PERSON><PERSON>"}, "city__linxia": {"message": "Lin<PERSON>"}, "city__linyi": {"message": "<PERSON><PERSON>"}, "city__lishui": {"message": "Li<PERSON><PERSON>"}, "city__liupanshui": {"message": "Liupanshu<PERSON>"}, "city__liuzhou": {"message": "Liuzhou"}, "city__longnan": {"message": "<PERSON><PERSON>"}, "city__longyan": {"message": "<PERSON><PERSON>"}, "city__loudi": {"message": "<PERSON><PERSON>"}, "city__luan": {"message": "<PERSON><PERSON><PERSON>"}, "city__luohe": {"message": "<PERSON><PERSON><PERSON>"}, "city__luoyang": {"message": "<PERSON><PERSON><PERSON>"}, "city__luzhou": {"message": "Luzhou"}, "city__lüliang": {"message": "<PERSON>眉liang"}, "city__maanshan": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__macauoutlyingislands": {"message": "Macau Outlying Islands"}, "city__macaupeninsula": {"message": "Macau Peninsula"}, "city__maoming": {"message": "<PERSON><PERSON>"}, "city__meishan": {"message": "<PERSON><PERSON>"}, "city__meizhou": {"message": "Meizhou"}, "city__mianyang": {"message": "Mianyang"}, "city__miaoli": {"message": "<PERSON><PERSON>"}, "city__mudanjiang": {"message": "Mudanjiang"}, "city__nagqu": {"message": "<PERSON>g<PERSON>u"}, "city__nanchang": {"message": "Nanchang"}, "city__nanchong": {"message": "<PERSON><PERSON><PERSON>"}, "city__nanjing": {"message": "Nanjing"}, "city__nanning": {"message": "Nanning"}, "city__nanping": {"message": "<PERSON><PERSON>"}, "city__nantong": {"message": "Nantong"}, "city__nantou": {"message": "<PERSON><PERSON><PERSON>"}, "city__nanyang": {"message": "Nanyang"}, "city__neijiang": {"message": "Neijiang"}, "city__newterritories": {"message": "New Territories"}, "city__ngari": {"message": "<PERSON><PERSON>"}, "city__ningbo": {"message": "Ningbo"}, "city__ningde": {"message": "<PERSON><PERSON><PERSON>"}, "city__nujiang": {"message": "Nujiang"}, "city__nyingchi": {"message": "Nyingchi"}, "city__ordos": {"message": "Ordos"}, "city__panjin": {"message": "Panjin"}, "city__panzhihua": {"message": "Pan Zhihua"}, "city__penghu": {"message": "<PERSON><PERSON><PERSON>"}, "city__pingdingshan": {"message": "Pingdingshan"}, "city__pingliang": {"message": "<PERSON><PERSON><PERSON>"}, "city__pingtung": {"message": "<PERSON><PERSON><PERSON>"}, "city__pingxiang": {"message": "<PERSON><PERSON><PERSON>"}, "city__puer": {"message": "<PERSON>u'er"}, "city__putian": {"message": "<PERSON><PERSON>"}, "city__puyang": {"message": "P<PERSON><PERSON>"}, "city__qiandongnan": {"message": "Qiandongnan"}, "city__qianjiang": {"message": "Qianjiang"}, "city__qiannan": {"message": "<PERSON><PERSON><PERSON>"}, "city__qianxinan": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__qingdao": {"message": "Qingdao"}, "city__qingyang": {"message": "Qingyang"}, "city__qingyuan": {"message": "Qingyuan"}, "city__qinhuangdao": {"message": "Qinhuangdao"}, "city__qinzhou": {"message": "Qinzhou"}, "city__qionghai": {"message": "Qionghai"}, "city__qiongzhong": {"message": "Qiongzhong"}, "city__qiqihar": {"message": "Qiqihar"}, "city__qitaihe": {"message": "<PERSON><PERSON><PERSON>"}, "city__quanzhou": {"message": "Quanzhou"}, "city__qujing": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__quzhou": {"message": "Quzhou"}, "city__rizhao": {"message": "<PERSON><PERSON><PERSON>"}, "city__sanmenxia": {"message": "Sanmenxia"}, "city__sanming": {"message": "Sanming"}, "city__sanya": {"message": "Sanya"}, "city__shangluo": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__shangqiu": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__shangrao": {"message": "<PERSON><PERSON><PERSON>"}, "city__shannan": {"message": "Shannan"}, "city__shantou": {"message": "<PERSON><PERSON><PERSON>"}, "city__shanwei": {"message": "Shanwei"}, "city__shaoguan": {"message": "Shaoguan"}, "city__shaoxing": {"message": "Shaoxing"}, "city__shaoyang": {"message": "Shaoyang"}, "city__shennongjiaforestarea": {"message": "Shennongjia Forest Area"}, "city__shenyang": {"message": "Shenyang"}, "city__shenzhen": {"message": "Shenzhen"}, "city__shigatse": {"message": "Shigat<PERSON>"}, "city__shihezi": {"message": "<PERSON><PERSON><PERSON>"}, "city__shijiazhuang": {"message": "Shijiazhuang"}, "city__shiyan": {"message": "<PERSON><PERSON>"}, "city__shizuishan": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__shuangyashan": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__shuozhou": {"message": "Shuozhou"}, "city__siping": {"message": "<PERSON><PERSON>"}, "city__songyuan": {"message": "Songyuan"}, "city__suihua": {"message": "Su<PERSON>ua"}, "city__suining": {"message": "Suining"}, "city__suizhou": {"message": "<PERSON><PERSON><PERSON>"}, "city__suqian": {"message": "<PERSON><PERSON><PERSON>"}, "city__suzhou": {"message": "Suzhou"}, "city__tacheng": {"message": "Tacheng"}, "city__taian": {"message": "Tai'an"}, "city__taichung": {"message": "Taichung"}, "city__tainan": {"message": "Tainan"}, "city__taipei": {"message": "Taipei"}, "city__taitung": {"message": "<PERSON><PERSON><PERSON>"}, "city__taiyuan": {"message": "Taiyuan"}, "city__taizhou": {"message": "Taizhou"}, "city__tangshan": {"message": "Tangshan"}, "city__taoyuan": {"message": "Taoyuan"}, "city__tianmen": {"message": "Tianmen"}, "city__tianshui": {"message": "<PERSON><PERSON><PERSON>"}, "city__tieling": {"message": "Tieling"}, "city__tongchuan": {"message": "<PERSON><PERSON><PERSON>"}, "city__tonghua": {"message": "Tonghua"}, "city__tongliao": {"message": "<PERSON><PERSON><PERSON>"}, "city__tongling": {"message": "Tongling"}, "city__tongren": {"message": "<PERSON><PERSON>"}, "city__tumushuke": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__tunchang": {"message": "Tunchang"}, "city__turpan": {"message": "<PERSON><PERSON><PERSON>"}, "city__ulanqab": {"message": "Ulanqab"}, "city__urumqi": {"message": "Urumqi"}, "city__wanning": {"message": "Wanning"}, "city__weifang": {"message": "<PERSON><PERSON><PERSON>"}, "city__weihai": {"message": "Weihai"}, "city__weinan": {"message": "Weinan"}, "city__wenchang": {"message": "Wenchang"}, "city__wenshan": {"message": "<PERSON><PERSON>"}, "city__wenzhou": {"message": "Wenzhou"}, "city__wuhai": {"message": "Wu<PERSON>"}, "city__wuhan": {"message": "<PERSON><PERSON>"}, "city__wuhu": {"message": "<PERSON><PERSON>"}, "city__wujiaqu": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__wuwei": {"message": "<PERSON><PERSON>"}, "city__wuxi": {"message": "Wuxi"}, "city__wuzhishan": {"message": "Wuzhishan"}, "city__wuzhong": {"message": "Wuzhong"}, "city__wuzhou": {"message": "Wuzhou"}, "city__xiamen": {"message": "Xiamen"}, "city__xian": {"message": "Xi'<PERSON>"}, "city__xiangfan": {"message": "<PERSON><PERSON><PERSON>"}, "city__xiangtan": {"message": "Xiangtan"}, "city__xiangxi": {"message": "Xiangxi"}, "city__xianning": {"message": "Xianning"}, "city__xiantao": {"message": "Xiantao"}, "city__xianyang": {"message": "<PERSON><PERSON><PERSON>"}, "city__xiaogan": {"message": "<PERSON><PERSON>"}, "city__xilingol": {"message": "Xilingol"}, "city__xinganleague": {"message": "Xing'an League"}, "city__xingtai": {"message": "Xingtai"}, "city__xining": {"message": "Xining"}, "city__xinxiang": {"message": "Xinxiang"}, "city__xinyang": {"message": "Xinyang"}, "city__xinyu": {"message": "Xinyu"}, "city__xinzhou": {"message": "Xinzhou"}, "city__xishuangbanna": {"message": "Xishuangbanna"}, "city__xuancheng": {"message": "Xuancheng"}, "city__xuchang": {"message": "Xuchang"}, "city__xuzhou": {"message": "Xuzhou"}, "city__yaan": {"message": "Ya'an"}, "city__yanan": {"message": "Yan'<PERSON>"}, "city__yanbian": {"message": "Yanbian"}, "city__yancheng": {"message": "Yancheng"}, "city__yangjiang": {"message": "Yangjiang"}, "city__yangquan": {"message": "<PERSON><PERSON><PERSON>"}, "city__yangzhou": {"message": "Yangzhou"}, "city__yantai": {"message": "Yantai"}, "city__yibin": {"message": "<PERSON><PERSON>"}, "city__yichang": {"message": "Yichang"}, "city__yichun": {"message": "<PERSON><PERSON><PERSON>"}, "city__yilan": {"message": "Yilan"}, "city__yili": {"message": "<PERSON><PERSON>"}, "city__yinchuan": {"message": "<PERSON><PERSON><PERSON>"}, "city__yingkou": {"message": "<PERSON><PERSON><PERSON>"}, "city__yingtan": {"message": "Yingtan"}, "city__yiwu": {"message": "<PERSON><PERSON>"}, "city__yiyang": {"message": "Yiyang"}, "city__yongzhou": {"message": "Yongzhou"}, "city__yueyang": {"message": "<PERSON><PERSON><PERSON>"}, "city__yulin": {"message": "Yulin"}, "city__yuncheng": {"message": "Yuncheng"}, "city__yunfu": {"message": "<PERSON><PERSON>"}, "city__yunlin": {"message": "<PERSON><PERSON>"}, "city__yushu": {"message": "Yushu"}, "city__yuxi": {"message": "Yuxi"}, "city__zaozhuang": {"message": "Zaozhuang"}, "city__zhangjiajie": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__zhangjiakou": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__zhangye": {"message": "<PERSON><PERSON>"}, "city__zhangzhou": {"message": "Zhangzhou"}, "city__zhanjiang": {"message": "Zhanjiang"}, "city__zhaoqing": {"message": "Zhaoqing"}, "city__zhaotong": {"message": "Zhaotong"}, "city__zhengzhou": {"message": "Zhengzhou"}, "city__zhenjiang": {"message": "Zhenjiang"}, "city__zhongshan": {"message": "Zhongshan"}, "city__zhongwei": {"message": "Zhongwei"}, "city__zhoukou": {"message": "<PERSON><PERSON><PERSON>"}, "city__zhoushan": {"message": "Zhoushan"}, "city__zhuhai": {"message": "Zhu<PERSON>"}, "city__zhumadian": {"message": "Zhumadian"}, "city__zhuzhou": {"message": "Zhuzhou"}, "city__zibo": {"message": "Zibo"}, "city__zigong": {"message": "Zigong"}, "city__ziyang": {"message": "<PERSON><PERSON><PERSON>"}, "city__zunyi": {"message": "<PERSON><PERSON><PERSON>"}, "click_copy_product_info": {"message": "Faceți clic pe Copiați informațiile despre produs"}, "commmon_txt_expired": {"message": "Expirat"}, "common__date_range_12m": {"message": "1 an"}, "common__date_range_1m": {"message": "1 lună"}, "common__date_range_1w": {"message": "1 săptămână"}, "common__date_range_2w": {"message": "2 saptamani"}, "common__date_range_3m": {"message": "3 luni"}, "common__date_range_3w": {"message": "3 saptamani"}, "common__date_range_6m": {"message": "6 luni"}, "common_btn_cancel": {"message": "<PERSON><PERSON><PERSON>"}, "common_btn_close": {"message": "<PERSON><PERSON><PERSON>"}, "common_btn_save": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "common_btn_setting": {"message": "Înființat"}, "common_email": {"message": "E-mail"}, "common_error_msg_no_data": {"message": "Nu există date"}, "common_error_msg_no_result": {"message": "Ne pare rău, nu a fost găsit niciun rezultat."}, "common_favorites": {"message": "Favorite"}, "common_feedback": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "common_help": {"message": "<PERSON><PERSON><PERSON>"}, "common_loading": {"message": "Se încarcă"}, "common_login": {"message": "Autentificare"}, "common_logout": {"message": "Deconectați-vă"}, "common_no": {"message": "<PERSON>u"}, "common_powered_by_aliprice": {"message": "Powered by AliPrice.com"}, "common_setting": {"message": "Setare"}, "common_sign_up": {"message": "Inscrie-te"}, "common_system_upgrading_title": {"message": "Actualizarea si<PERSON>i"}, "common_system_upgrading_txt": {"message": "<PERSON>ă rugăm să încercați mai târziu"}, "common_txt__currency": {"message": "Valută"}, "common_txt__video_tutorial": {"message": "Tutorial video"}, "common_txt_ago_time": {"message": "Acum $time$ zile", "placeholders": {"time": {"content": "$1"}}}, "common_txt_all_product": {"message": "toate"}, "common_txt_analysis": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "common_txt_basically_used": {"message": "<PERSON><PERSON><PERSON> folosit"}, "common_txt_biaoti_link": {"message": "Titlu+Link"}, "common_txt_biaoti_link_dian_pu": {"message": "Titlu+Link+<PERSON>ume magazin"}, "common_txt_blacklist": {"message": "Lista de blocati"}, "common_txt_cancel": {"message": "<PERSON><PERSON>"}, "common_txt_category": {"message": "Categorie"}, "common_txt_chakan": {"message": "Verifica"}, "common_txt_colors": {"message": "culori"}, "common_txt_confirm": {"message": "A confirma"}, "common_txt_copied": {"message": "Copiat"}, "common_txt_copy": {"message": "<PERSON><PERSON>"}, "common_txt_copy_link": {"message": "Copiază legătură"}, "common_txt_copy_title": {"message": "Copiați titlul"}, "common_txt_copy_title__link": {"message": "Copiați titlul și linkul"}, "common_txt_day": {"message": "cer"}, "common_txt_day__short": {"message": "D"}, "common_txt_delete": {"message": "Șterge"}, "common_txt_dian_pu_link": {"message": "Copiați numele magazinului + linkul"}, "common_txt_download": {"message": "Descarca"}, "common_txt_downloaded": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "common_txt_export_as_csv": {"message": "Exportați Excel"}, "common_txt_export_as_txt": {"message": "Export Txt"}, "common_txt_fail": {"message": "Eșuează"}, "common_txt_format": {"message": "Format"}, "common_txt_get": {"message": "ob<PERSON><PERSON>"}, "common_txt_incert_selection": {"message": "Inverseaza selectia"}, "common_txt_install": {"message": "Instalare"}, "common_txt_load_failed": {"message": "Incarcarea a esuat"}, "common_txt_month": {"message": "lună"}, "common_txt_more": {"message": "<PERSON>"}, "common_txt_new_unused": {"message": "Nou-nouț, nefolosit"}, "common_txt_next": {"message": "<PERSON><PERSON><PERSON><PERSON> →"}, "common_txt_no_limit": {"message": "Nelimitat"}, "common_txt_no_noticeable": {"message": "Fără zgârieturi vizibile sau murdărie"}, "common_txt_on_sale": {"message": "Disponibil"}, "common_txt_opt_in_out": {"message": "Pornit/Oprit"}, "common_txt_order": {"message": "<PERSON><PERSON>"}, "common_txt_others": {"message": "Alții"}, "common_txt_overall_poor_condition": {"message": "Stare generală proastă"}, "common_txt_patterns": {"message": "modele"}, "common_txt_platform": {"message": "Platforme"}, "common_txt_please_select": {"message": "<PERSON>ă rugăm s<PERSON>i"}, "common_txt_prev": {"message": "Anterior"}, "common_txt_price": {"message": "Preț"}, "common_txt_privacy_policy": {"message": "Politica de confidențialitate"}, "common_txt_product_condition": {"message": "Stare produs"}, "common_txt_rating": {"message": "Evaluare"}, "common_txt_ratings": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "common_txt_reload": {"message": "Reîncă<PERSON><PERSON><PERSON>"}, "common_txt_reset": {"message": "Resetați"}, "common_txt_review": {"message": "Revizuire"}, "common_txt_sale": {"message": "Disponibil"}, "common_txt_same": {"message": "La fel"}, "common_txt_scratches_and_dirt": {"message": "Cu zgârieturi și murdărie"}, "common_txt_search_title": {"message": "Tit<PERSON>l <PERSON>"}, "common_txt_select_all": {"message": "Select<PERSON>z<PERSON> tot"}, "common_txt_selected": {"message": "selectat"}, "common_txt_share": {"message": "Acțiune"}, "common_txt_sold": {"message": "vândut"}, "common_txt_sold_out": {"message": "epuizat"}, "common_txt_some_scratches": {"message": "Unele zgârieturi și murdărie"}, "common_txt_sort_by": {"message": "Filtreaz<PERSON> după"}, "common_txt_state": {"message": "stat"}, "common_txt_success": {"message": "Succes"}, "common_txt_sys_err": {"message": "eroare de sistem"}, "common_txt_today": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "common_txt_total": {"message": "toate"}, "common_txt_unselect_all": {"message": "Inverseaza selectia"}, "common_txt_upload_image": {"message": "Incarca imaginea"}, "common_txt_visit": {"message": "V<PERSON>ita"}, "common_txt_whitelist": {"message": "Listă albă"}, "common_txt_wildberries": {"message": "Wildberries"}, "common_txt_year": {"message": "An"}, "common_yes": {"message": "da"}, "compare_tool_btn_clear_all": {"message": "<PERSON><PERSON><PERSON> tot"}, "compare_tool_btn_compare": {"message": "Comparaţie"}, "compare_tool_btn_contact": {"message": "a lua legatura"}, "compare_tool_tips_max_compared": {"message": "Pridajte ich až $maxComparedCount$", "placeholders": {"maxComparedCount": {"content": "$1"}}}, "configure_notifiactions": {"message": "Configurați notificările"}, "contact_us": {"message": "Contactează-ne"}, "context_menu_screenshot_search": {"message": "Căutare captură de ecran pentru ace<PERSON>ș<PERSON> stil"}, "context_menus_aliprice_search_by_image": {"message": "Căutați imaginea pe AliPrice"}, "context_menus_goote_trans": {"message": "Traduceți pagina/Afișați originalul"}, "context_menus_search_by_image": {"message": "Căutați după imagine pe $storeName$", "placeholders": {"storeName": {"content": "$1"}}}, "context_menus_search_by_image_capture": {"message": "Capturați la $storeName$", "placeholders": {"storeName": {"content": "$1"}}}, "context_menus_translator": {"message": "Captur<PERSON> pentru a traduce"}, "converter_modal_amount_placeholder": {"message": "Introdu suma aici"}, "converter_modal_btn_convert": {"message": "convertit"}, "converter_modal_exchange_rate_source": {"message": "Datele provin de la cursul valutar $boc$ Ora actualizării: $updatedAt$", "placeholders": {"boc": {"content": "$1"}, "updatedAt": {"content": "$2"}}}, "converter_modal_name": {"message": "conversie valutara"}, "converter_modal_search_placeholder": {"message": "caută moneda"}, "copy_all_contact_us_notice": {"message": "Acest site nu este acceptat în acest moment, vă rugăm să ne contactați"}, "copy_product_info": {"message": "Copiați informațiile despre produs"}, "copy_suggest_search_kw": {"message": "Copiați listele derulante"}, "country__han_gou": {"message": "Coreea de Sud"}, "country__ri_ben": {"message": "Japonia"}, "country__yue_nan": {"message": "Vietnam"}, "currency_convert__custom": {"message": "Rata de schimb personalizată"}, "currency_convert__sync_server": {"message": "Sincronizați serverul"}, "dang_ri_fa_huo": {"message": "Livrare in aceeasi zi"}, "dao_chu_quan_dian_shang_pin": {"message": "Exportați toate produsele din magazin"}, "dao_chu_wei_CSV": {"message": "Export"}, "dao_chu_zi_duan": {"message": "Câmpuri de export"}, "delivery_address": {"message": "Adresa de transport"}, "delivery_company": {"message": "Companie de livrări"}, "di_zhi": {"message": "abor<PERSON><PERSON>"}, "dian_ji_cha_xun": {"message": "Faceți clic pentru a interoga"}, "dian_pu_ID": {"message": "ID magazin"}, "dian_pu_di_zhi": {"message": "<PERSON><PERSON><PERSON>i"}, "dian_pu_lian_jie": {"message": "<PERSON> maga<PERSON>"}, "dian_pu_ming": {"message": "Numele magazinului"}, "dian_pu_ming_cheng": {"message": "Numele magazinului"}, "dian_pu_shang_pin_zong_hsu": {"message": "Numărul total de produse din magazin: $num$", "placeholders": {"num": {"content": "$1"}}}, "dian_pu_xin_xi": {"message": "Stocați informații"}, "ding_zai_zuo_ce": {"message": "bătut în cuie la stânga"}, "disable_old_version_tips_disable_btn_title": {"message": "Dezactivați versiunea veche"}, "download_image__SKU_variant_images": {"message": "Imagini cu variante SKU"}, "download_image__assume": {"message": "De exemplu, avem 2 imagini, produs1.jpg și produs2.gif.\nimg_{$no$} va fi redenumit img_01.jpg, img_02.gif;\n{$group$}_{$no$} va fi redenumit în main_image_01.jpg, main_image_02.gif;", "placeholders": {"no": {"content": "$3"}, "group": {"content": "$2"}}}, "download_image__batch_download": {"message": "Descărcare lot"}, "download_image__combined_image": {"message": "Imagine cu detaliile produsului combinat"}, "download_image__continue_downloading": {"message": "Continuați descărcarea"}, "download_image__description_images": {"message": "Imagini <PERSON>rier<PERSON>"}, "download_image__download_combined_image": {"message": "Descărcați imaginea cu detaliile produsului combinat"}, "download_image__download_zip": {"message": "Descărcați zip"}, "download_image__enlarge_check": {"message": "Acceptă numai imagini JPEG, JPG, GIF și PNG, dimensiunea maximă a unei singure imagini: 1600 * 1600"}, "download_image__enlarge_image": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "download_image__export": {"message": "<PERSON>rta<PERSON><PERSON>"}, "download_image__height": {"message": "Înălţime"}, "download_image__ignore_videos": {"message": "Videoclipul a fost ignorat, deoarece nu poate fi exportat"}, "download_image__img_translate": {"message": "<PERSON><PERSON><PERSON><PERSON> imagine"}, "download_image__main_image": {"message": "<PERSON><PERSON> principal<PERSON>"}, "download_image__multi_folder": {"message": "Multi-folder"}, "download_image__name": {"message": "descărcați imaginea"}, "download_image__notice_content": {"message": "Vă rugăm să nu bifați „Întrebați unde să salvați fiecare fișier înainte de a descărca” în setările de descărcare ale browserului dvs.!!! În caz contrar, vor exista o mulțime de casete de dialog."}, "download_image__notice_ignore": {"message": "Nu mai solicita acest mesaj"}, "download_image__order_number": {"message": "{$no$} număr de serie; Numele grupului {$group$}; {$date$} marcaj temporal", "placeholders": {"no": {"content": "$1"}, "group": {"content": "$2"}, "date": {"content": "$3"}}}, "download_image__overview": {"message": "Prezentare generală"}, "download_image__prompt_download_zip": {"message": "Sunt prea multe imagini, ar fi bine să le descărcați ca folder zip."}, "download_image__rename": {"message": "Redenumiți"}, "download_image__rule": {"message": "Reguli de denumire"}, "download_image__single_folder": {"message": "Un singur folder"}, "download_image__sku_image": {"message": "Imagini SKU"}, "download_image__video": {"message": "video"}, "download_image__width": {"message": "Lăţime"}, "download_reviews__download_images": {"message": "Descărcați imaginea recenziei"}, "download_reviews__dropdown_title": {"message": "Descărcați imaginea recenziei"}, "download_reviews__export_csv": {"message": "exportați CSV"}, "download_reviews__no_images": {"message": "0 imagini disponibile pentru descărcare"}, "download_reviews__no_reviews": {"message": "<PERSON><PERSON> recenzie de descărcat!"}, "download_reviews__notice": {"message": "Bacsis:"}, "download_reviews__notice__chrome_settings": {"message": "Setați browserul Chrome să întrebe unde să salvați fiecare fișier înainte de descărcare, setați la „Dezactivat”"}, "download_reviews__notice__wait": {"message": "În funcție de numărul de recenzii, timpul de așteptare poate fi mai lung"}, "download_reviews__pages_list__all": {"message": "Toate"}, "download_reviews__pages_list__page": {"message": "Paginile anterioare $page$", "placeholders": {"page": {"content": "$1"}}}, "download_reviews__pages_list_title": {"message": "Interval de selecție"}, "export_shopping_cart__csv_filed__details_url": {"message": "Link produs"}, "export_shopping_cart__csv_filed__details_url_with_sku": {"message": "Echo SKU link"}, "export_shopping_cart__csv_filed__images": {"message": "<PERSON> imagine"}, "export_shopping_cart__csv_filed__quantity": {"message": "Cantitate"}, "export_shopping_cart__csv_filed__sale_price": {"message": "Preț"}, "export_shopping_cart__csv_filed__specs": {"message": "Specificații"}, "export_shopping_cart__csv_filed__store_name": {"message": "Numele magazinului"}, "export_shopping_cart__csv_filed__store_url": {"message": "<PERSON> maga<PERSON>"}, "export_shopping_cart__csv_filed__title": {"message": "Numele produsului"}, "export_shopping_cart__export_btn": {"message": "Export"}, "export_shopping_cart__export_empty": {"message": "Vă rugăm să selectați un produs!"}, "fa_huo_shi_jian": {"message": "Transport"}, "favorite_add_email": {"message": "Adăugați e-mail"}, "favorite_add_favorites": {"message": "Adăugați la Favorite"}, "favorite_added": {"message": "Adă<PERSON><PERSON>"}, "favorite_btn_add": {"message": "Alertă de scădere a prețului."}, "favorite_btn_notify": {"message": "Urmăriți prețul"}, "favorite_cate_name_all": {"message": "Toate produsele"}, "favorite_current_price": {"message": "Pretul actual"}, "favorite_due_date": {"message": "Data scadenței"}, "favorite_enable_notification": {"message": "Vă rugăm să activați notificările prin e-mail"}, "favorite_expired": {"message": "Expirat"}, "favorite_go_to_enable": {"message": "Activați"}, "favorite_msg_add_success": {"message": "<PERSON><PERSON><PERSON><PERSON> la favorite"}, "favorite_msg_del_success": {"message": "<PERSON><PERSON> din favorite"}, "favorite_msg_failure": {"message": "Nu reușesc! Reîmprospătați pagina și încercați din nou."}, "favorite_please_add_email": {"message": "Vă rugăm să adăugați e-mail"}, "favorite_price_drop": {"message": "<PERSON><PERSON>"}, "favorite_price_rise": {"message": "<PERSON><PERSON>"}, "favorite_price_untracked": {"message": "Prețul nu este urmărit"}, "favorite_saved_price": {"message": "Preț salvat"}, "favorite_stop_tracking": {"message": "Opriți urmărirea"}, "favorite_sub_email_address": {"message": "Adresa de e-mail pentru abonament"}, "favorite_tracking_period": {"message": "Perioada de urmărire"}, "favorite_tracking_prices": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "favorite_verify_email": {"message": "Verificați adresa de e-mail"}, "favorites_list_remove_prompt_msg": {"message": "<PERSON><PERSON>r îl ștergeți?"}, "favorites_update_button": {"message": "Actualizați prețurile acum"}, "fen_lei": {"message": "Categorie"}, "fen_xia_yan_xuan": {"message": "Alegerea distribuitorului"}, "find_similar": {"message": "Găsiți similare"}, "first_ali_price_date": {"message": "Data când a fost capturată prima dată de crawler-ul AliPrice"}, "fooview_coupons_modal_no_data": {"message": "<PERSON><PERSON><PERSON>"}, "fooview_coupons_modal_title": {"message": "Cupoane"}, "fooview_favorites__product_sub__tooltip_l1": {"message": "Preț < $lowerPrice$", "placeholders": {"lowerPrice": {"content": "$1"}}}, "fooview_favorites__product_sub__tooltip_l2": {"message": "sau preț > $higherPrice$", "placeholders": {"higherPrice": {"content": "$1"}}}, "fooview_favorites__product_sub__tooltip_l3": {"message": "Term<PERSON> limita"}, "fooview_favorites_error_msg_no_favorites": {"message": "Adăugați aici produsele preferate pentru a primi o alertă de scădere a prețului."}, "fooview_favorites_filter_latest": {"message": "<PERSON><PERSON> mai recente"}, "fooview_favorites_filter_price_drop": {"message": "pret redus"}, "fooview_favorites_filter_price_up": {"message": "cresterea preturilor"}, "fooview_favorites_modal_title": {"message": "Preferatele mele"}, "fooview_favorites_modal_title_title": {"message": "Accesați AliPrice Favorite"}, "fooview_favorites_track_price": {"message": "Pentru a urmări prețul"}, "fooview_price_history_app_price": {"message": "Preț APP:"}, "fooview_price_history_title": {"message": "Istoricul prețurilor"}, "fooview_product_list_feedback": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "fooview_product_list_orders": {"message": "<PERSON><PERSON><PERSON>"}, "fooview_product_list_price": {"message": "Preț"}, "fooview_reviews_error_msg_no_review": {"message": "Nu am găsit recenzii pentru acest produs."}, "fooview_reviews_filter_buyer_reviews": {"message": "Fotografiile <PERSON>"}, "fooview_reviews_modal_title": {"message": "Recenzii"}, "fooview_same_product_choose_category": {"message": "Alege categoria"}, "fooview_same_product_filter_feedback": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "fooview_same_product_filter_orders": {"message": "<PERSON><PERSON><PERSON>"}, "fooview_same_product_filter_price": {"message": "Preț"}, "fooview_same_product_filter_rating": {"message": "Evaluare"}, "fooview_same_product_modal_title": {"message": "Găsiți același produs"}, "fooview_same_product_search_by_image": {"message": "<PERSON><PERSON><PERSON><PERSON> du<PERSON>"}, "fooview_seller_analysis_modal_title": {"message": "<PERSON><PERSON><PERSON> vânzătorului"}, "for_12_months": {"message": "Timp de 1 an"}, "for_12_months_list_pro": {"message": "12 luni"}, "for_12_months_nei": {"message": "In termen de 12 luni"}, "for_1_months": {"message": "1 lună"}, "for_1_months_nei": {"message": "In termen de 1 luna"}, "for_3_months": {"message": "Timp de 3 luni"}, "for_3_months_nei": {"message": "In termen de 3 luni"}, "for_6_months": {"message": "Timp de 6 luni"}, "for_6_months_nei": {"message": "În termen de 6 luni"}, "for_9_months": {"message": "9 luni"}, "for_9_months_nei": {"message": "In termen de 9 luni"}, "fu_gou_lv": {"message": "Rata de răscumpărare"}, "gao_liang_bu_tong_dian": {"message": "<PERSON><PERSON><PERSON> di<PERSON>"}, "gao_liang_guang_gao_chan_pin": {"message": "Evidențiați produse publicitare"}, "geng_duo_xin_xi": {"message": "Mai multe informatii"}, "geng_xin_shi_jian": {"message": "Actualizare ora"}, "get_store_products_fail_tip": {"message": "Faceți clic pe OK pentru a merge la verificare pentru a asigura accesul normal"}, "gong_x_kuan_shang_pin": {"message": "Un total de $amount$ produse", "placeholders": {"amount": {"content": "$1"}}}, "gong_ying_shang": {"message": "Furnizor"}, "gong_ying_shang_ID": {"message": "ID furnizor"}, "gong_ying_shang_deng_ji": {"message": "Evaluarea furnizorului"}, "gong_ying_shang_nian_zhan": {"message": "Furnizorul este mai în vârstă"}, "gong_ying_shang_xin_xi": {"message": "informatii despre furnizor"}, "gong_ying_shang_zhu_ye_lian_jie": {"message": "Link pentru pagina de pornire a furnizorului"}, "green_rocket": {"message": "로켓프레시"}, "grey_rocket": {"message": "로켓설치"}, "gu_ji_shou_jia": {"message": "Prețul de vânzare estimat"}, "guan_jian_zi": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON> cheie"}, "guang_gao_chan_pin": {"message": "<PERSON><PERSON> produse"}, "guang_gao_zhan_bi": {"message": "<PERSON><PERSON> raport"}, "guo_ji_wu_liu_yun_fei": {"message": "Taxa de livrare internationala"}, "guo_lv_tiao_jian": {"message": "Filtre"}, "hao_ping_lv": {"message": "<PERSON><PERSON><PERSON> p<PERSON>"}, "highest_price": {"message": "<PERSON><PERSON><PERSON>"}, "historical_trend": {"message": "Tendință istorică"}, "how_to_screenshot": {"message": "Țineți apăsat butonul stâng al mouse-ului pentru a selecta zona, atingeți butonul din dreapta al mouse-ului sau tasta Esc pentru a ieși din captura de ecran"}, "howt_it_works": {"message": "Cum functioneaza"}, "hui_fu_lv": {"message": "Rata de raspuns"}, "hui_tou_lv": {"message": "rata de retur"}, "inquire_freightFee": {"message": "<PERSON><PERSON><PERSON>"}, "inquire_freightFee_Yuan": {"message": "Marfă/yuani"}, "inquire_freightFee_province": {"message": "<PERSON><PERSON><PERSON>"}, "inquire_freightFee_the": {"message": "Transportul este de $num$ , ceea ce înseamnă că regiunea are transport gratuit.", "placeholders": {"num": {"content": "$1"}}}, "is_ad": {"message": "<PERSON><PERSON><PERSON>."}, "jia_ge": {"message": "Preț"}, "jia_ge_dan_wei": {"message": "Unitate"}, "jia_ge_qu_shi": {"message": "Tendinţă"}, "jia_zai_n_ge_shang_pin": {"message": "Încărcați $num$ produse", "placeholders": {"num": {"content": "$1"}}}, "jin_30_tian_xiao_liang_zhan_bi": {"message": "Procentul din volumul vânzărilor în ultimele 30 de zile"}, "jin_30_tian_xiao_shou_e_zhan_bi": {"message": "Procentul veniturilor din ultimele 30 de zile"}, "jin_30d_xiao_liang": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "jin_30d_xiao_liang__desc": {"message": "Vânzări totale în ultimele 30 de zile"}, "jin_30d_xiao_shou_e": {"message": "<PERSON><PERSON><PERSON>"}, "jin_30d_xiao_shou_e__desc": {"message": "Cifra de afaceri totala in ultimele 30 de zile"}, "jin_90_tian_mai_jia_shu": {"message": "Cumpărători în ultimele 90 de zile"}, "jin_90_tian_xiao_shou_liang": {"message": "Vânzări în ultimele 90 de zile"}, "jing_xuan_huo_yuan": {"message": "Surse selectate"}, "jing_ying_mo_shi": {"message": "<PERSON> <PERSON>"}, "jing_ying_mo_shi__gong_chang": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "jiu_fen_jie_jue": {"message": "Soluționare a litigiilor"}, "jiu_fen_jie_jue__desc": {"message": "Contabilitatea litigiilor privind drepturile de magazin ale vânzătorilor"}, "jiu_fen_lv": {"message": "Rata de dispute"}, "jiu_fen_lv__desc": {"message": "Proporția comenzilor cu reclamații finalizate în ultimele 30 de zile și considerate a fi responsabilitatea vânzătorului sau a ambelor părți"}, "kai_dian_ri_qi": {"message": "Data deschiderii"}, "keywords": {"message": "<PERSON><PERSON><PERSON><PERSON> cheie"}, "kua_jin_Select_pan_huo": {"message": "Selectare transfrontalieră"}, "last15_days": {"message": "Ultimele 15 zile"}, "last180_days": {"message": "Ultimele 180 de zile"}, "last30_days": {"message": "În ultimele 30 de zile"}, "last360_days": {"message": "Ultimele 360 ​​de zile"}, "last45_days": {"message": "Ultimele 45 de zile"}, "last60_days": {"message": "Ultimele 60 de zile"}, "last7_days": {"message": "Ultimele 7 zile"}, "last90_days": {"message": "Ultimele 90 de zile"}, "last_30d_sales": {"message": "Vânzări din ultimele 30 de zile"}, "lei_ji": {"message": "Cumulativ"}, "lei_ji_xiao_liang": {"message": "Total"}, "lei_ji_xiao_liang__desc": {"message": "Toate vânz<PERSON>rile după produs la raft"}, "lei_ji_xiao_liang_pai_xu__desc": {"message": "Volumul cumulat al vânzărilor din ultimele 30 de zile, sortat de la mare la scăzut"}, "lian_xi_fang_shi": {"message": "Informații de contact"}, "list_time": {"message": "La data de raft"}, "load_more": {"message": "Încărcați mai multe"}, "login_to_aliprice": {"message": "Conectați-vă la AliPrice"}, "long_link": {"message": "<PERSON> lung"}, "lowest_price": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "mai_jia_shu": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "mao_li_lv": {"message": "<PERSON><PERSON>"}, "mobile_view__dkxbqy": {"message": "Deschideți o filă nouă"}, "mobile_view__sjdxq": {"message": "Detalii în aplicație"}, "mobile_view__sjdxqy": {"message": "Pagina de detalii în aplicație"}, "mobile_view__smck": {"message": "Scanare pentru vizualizare"}, "mobile_view__smckms": {"message": "Vă rugăm să utilizați camera sau aplicația pentru a scana și vizualiza"}, "modified_failed": {"message": "Modificarea a eșuat"}, "modified_successfully": {"message": "Modificat cu succes"}, "nav_btn_favorites": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON> mele"}, "nav_btn_package": {"message": "<PERSON><PERSON><PERSON>"}, "nav_btn_product_info": {"message": "Despre produs"}, "nav_btn_viewed": {"message": "Vizualizat"}, "no_suggest_search_kw": {"message": "Loading..."}, "none": {"message": "<PERSON><PERSON> unul"}, "normal_link": {"message": "Legătură normală"}, "notice": {"message": "aluzie"}, "number_reviews": {"message": "Recenzii"}, "only_show_num": {"message": "Total produse: $allnum$, Ascunse: $hidenum2$", "placeholders": {"allnum": {"content": "$1"}, "hidenum2": {"content": "$2"}}}, "only_show_selected": {"message": "Eliminați Nebifat"}, "open": {"message": "Deschide"}, "open_links": {"message": "Deschide<PERSON><PERSON>"}, "options_page_tab_check_links": {"message": "Verificați linkurile"}, "options_page_tab_gernal": {"message": "General"}, "options_page_tab_notifications": {"message": "Notific<PERSON><PERSON>"}, "options_page_tab_others": {"message": "Alții"}, "options_page_tab_sbi": {"message": "<PERSON><PERSON><PERSON><PERSON> du<PERSON>"}, "options_page_tab_shortcuts": {"message": "Comenzi rapide"}, "options_page_tab_shortcuts_title": {"message": "Dimensiunea fontului pentru comenzi rapide"}, "options_page_tab_similar_products": {"message": "<PERSON><PERSON><PERSON><PERSON> produse"}, "orange_rocket": {"message": "판매자로켓"}, "order_list_open_links_tip": {"message": "<PERSON> multe link-uri de produse sunt pe cale să se deschidă"}, "order_list_sku_show_title": {"message": "Afișați variantele selectate în linkurile partajate"}, "orders_last30_days": {"message": "Numărul de comenzi în ultimele 30 de zile"}, "pTutorial_favorites_block1_desc1": {"message": "Produsele pe care le-ați urmărit sunt enumerate aici"}, "pTutorial_favorites_block1_title": {"message": "Favorite"}, "pTutorial_popup_block1_desc1": {"message": "O etichetă verde înseamnă că există produse scăzute de preț"}, "pTutorial_popup_block1_title": {"message": "Comenzi rapide și Favorite"}, "pTutorial_price_history_block1_desc1": {"message": "Faceți clic pe „Urmăriți prețul”, ad<PERSON><PERSON><PERSON><PERSON> produse la Favorite. Odată ce prețurile lor scad, veți primi notificări"}, "pTutorial_price_history_block1_title": {"message": "Urmăriți prețul"}, "pTutorial_reviews_block1_desc1": {"message": "Recenziile cumpărătorilor de la Itao și fotografiile reale din feedback-ul AliExpress"}, "pTutorial_reviews_block1_title": {"message": "Recenzii"}, "pTutorial_reviews_block2_desc1": {"message": "Este întotdeauna util să verificați recenziile de la alți cumpărători"}, "pTutorial_same_products_block1_desc1": {"message": "Le puteți compara pentru a face cea mai bună alegere"}, "pTutorial_same_products_block1_desc2": {"message": "<PERSON><PERSON><PERSON> clic pe „Mai multe” pentru a „Căuta după imagine”"}, "pTutorial_same_products_block1_title": {"message": "<PERSON><PERSON><PERSON><PERSON> produse"}, "pTutorial_same_products_by_image_search_block1_desc1": {"message": "Aruncați imaginea produsului acolo și alegeți o categorie"}, "pTutorial_same_products_by_image_search_block1_title": {"message": "<PERSON><PERSON><PERSON><PERSON> du<PERSON>"}, "pTutorial_seller_analysis_block1_desc1": {"message": "Rata de feedback pozitiv a vânzătorului, scorurile de feedback și cât timp a fost vânzătorul pe piață"}, "pTutorial_seller_analysis_block1_title": {"message": "Ratingul vanzatorului"}, "pTutorial_seller_analysis_block2_desc2": {"message": "Evaluarea vânzătorului se bazează pe 3 indici: articol descris, viteza de expediere a comunicării"}, "pTutorial_seller_analysis_block3_desc3": {"message": "Folosim 3 culori și pictograme pentru a indica nivelul de încredere al vânzătorilor"}, "page_count": {"message": "<PERSON><PERSON><PERSON><PERSON>agi<PERSON>"}, "pai_chu": {"message": "Exclus"}, "pai_chu_HK_bu_yi_xia_xiaoshou": {"message": "Excludeți Hong Kong-Restricționat"}, "pai_chu_JP_bu_yi_xia_xiaoshou": {"message": "Excludeți Japonia-Restricționat"}, "pai_chu_KR_bu_yi_xia_xiaoshou": {"message": "Exclude Coreea-Restricționat"}, "pai_chu_KZ_bu_yi_xia_xiaoshou": {"message": "Excludeți Kazahstan-Restricționat"}, "pai_chu_MO_bu_yi_xia_xiaoshou": {"message": "Excludeți <PERSON>-Restricted"}, "pai_chu_RU_bu_yi_xia_xiaoshou": {"message": "Excludeți Europa de Est-Restricționată"}, "pai_chu_SA_bu_yi_xia_xiaoshou": {"message": "Excludeți Arabia Saudită-Restricționat"}, "pai_chu_TW_bu_yi_xia_xiaoshou": {"message": "Excludeți Taiwan-Restricted"}, "pai_chu_USA_bu_yi_xia_xiaoshou": {"message": "Excludeți S.U.A. cu restricții"}, "pai_chu_VN_bu_yi_xia_xiaoshou": {"message": "Excludeți Vietnam-Restricted"}, "pai_chu_bu_yi_xia_xiaoshou": {"message": "Excludeți articolele restricționate"}, "payable_price_formula": {"message": "Pret + Livrare + Reducere"}, "pdd_check_retail_btn_txt": {"message": "Verificați cu amănuntul"}, "pdd_pifa_to_retail_btn_txt": {"message": "Cumpărați cu amănuntul"}, "pdp_copy_fail": {"message": "Copierea eșuată!"}, "pdp_copy_success": {"message": "Copiere reușită!"}, "pdp_share_modal_subtitle": {"message": "Distribuie captura de ecran, el/ea vă va vedea alegerea."}, "pdp_share_modal_title": {"message": "Împărtășește-ți alegerea"}, "pdp_share_screenshot": {"message": "Partajați captura de ecran"}, "pei_song": {"message": "Liv<PERSON><PERSON>"}, "pin_lei": {"message": "Categorie"}, "pin_zhi_ti_yan": {"message": "Calitatea produsului"}, "pin_zhi_ti_yan__desc": {"message": "Rata de rambursare de calitate a magazinului vânzătorului"}, "pin_zhi_tui_kuan_lv": {"message": "<PERSON><PERSON> <PERSON>"}, "pin_zhi_tui_kuan_lv__desc": {"message": "Proporția comenzilor care au fost rambursate și returnate doar în ultimele 30 de zile"}, "ping_fen": {"message": "Evaluare"}, "ping_jun_fa_huo_su_du": {"message": "Viteza medie de livrare"}, "pkgInfo_hide": {"message": "Informații logistice: pornit/oprit"}, "pkgInfo_no_trace": {"message": "Fără informații de logistică"}, "platform_name__1688": {"message": "1688"}, "platform_name__17zwd": {"message": "17zwd.com"}, "platform_name__51taoyang": {"message": "51taoyang.com"}, "platform_name__5ts": {"message": "5ts.com"}, "platform_name__91jf": {"message": "91jf.com"}, "platform_name__alibaba": {"message": "Alibaba.com"}, "platform_name__aliexpress": {"message": "AliExpress"}, "platform_name__amazon": {"message": "Amazon"}, "platform_name__bao66": {"message": "bao66.cn"}, "platform_name__chinagoods": {"message": "chinagoods.com"}, "platform_name__dhgate": {"message": "DHgate"}, "platform_name__e3hui": {"message": "e3hui.com"}, "platform_name__ebay": {"message": "Ebay"}, "platform_name__hznzcn": {"message": "hznzcn.com"}, "platform_name__jd": {"message": "JD"}, "platform_name__juyi5": {"message": "juyi5.cn"}, "platform_name__naver_shopping": {"message": "Naver Shopping"}, "platform_name__pinduoduo": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "platform_name__pinduoduo_pifa": {"message": "Pinduoduo en-gros"}, "platform_name__shopee": {"message": "<PERSON>ee"}, "platform_name__sjxzw": {"message": "571xz.com"}, "platform_name__sooxie": {"message": "sooxie.com"}, "platform_name__taobao": {"message": "Taobao"}, "platform_name__taobao_lite": {"message": "Taobao Lite"}, "platform_name__vvic": {"message": "vvic.com"}, "platform_name__walmart": {"message": "Walmart"}, "platform_name__wsy": {"message": "wsy.com"}, "platform_name__xingfujie": {"message": "xingfujie.cn"}, "platform_name__yiwugo": {"message": "Yiwugo.com"}, "platform_name__yunchepin": {"message": "yunchepin.cn"}, "platform_name__zhaojiafang": {"message": "zhaojiafang.com"}, "popup_go_to_home": {"message": "Acasă"}, "popup_go_to_platform": {"message": "Accesați $name$", "placeholders": {"name": {"content": "$1"}}}, "popup_search_placeholder": {"message": "<PERSON><PERSON><PERSON><PERSON> pentru ..."}, "popup_track_package_btn_track": {"message": "URMĂRI"}, "popup_track_package_desc": {"message": "URMĂRIREA PachetULUI ALL-IN-ONE"}, "popup_track_package_search_placeholder": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "popup_translate_search_placeholder": {"message": "Traduceți și căutați pe $searchOn$", "placeholders": {"searchOn": {"content": "$1"}}}, "price_history": {"message": "Istoricul prețurilor"}, "price_history_chart_tip_ae": {"message": "Sfat: numărul de comenzi este numărul cumulat de comenzi de la lansare"}, "price_history_chart_tip_coupang": {"message": "Sfat: Coupang va șterge numărul comenzilor frauduloase"}, "price_history_inm_1688_l1": {"message": "<PERSON>ă rugăm să instalați"}, "price_history_inm_1688_l2": {"message": "<PERSON>istent pentru cum<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> AliPrice pentru 1688"}, "price_history_panel_lowest_price": {"message": "Cel mai mic pret:"}, "price_history_panel_tab_price_tracking": {"message": "Istoricul prețurilor"}, "price_history_panel_tab_seller_analysis": {"message": "<PERSON><PERSON><PERSON> vânzătorului"}, "price_history_pro_modal_title": {"message": "Istoricul prețurilor și istoricul comenzilor"}, "privacy_consent__btn_agree": {"message": "Revizuirea consimțământului colectării datelor"}, "privacy_consent__btn_disable_all": {"message": "<PERSON>u accept"}, "privacy_consent__btn_enable_all": {"message": "Permite tuturor"}, "privacy_consent__btn_uninstall": {"message": "Elimina"}, "privacy_consent__desc_privacy": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON> că, f<PERSON><PERSON><PERSON> date sau cookie-uri, unele funcții vor fi dezactivate, deoarece acele funcții au nevoie de explicația datelor sau cookie-urilor, dar puteți utiliza în continuare celelalte funcții."}, "privacy_consent__desc_privacy_L1": {"message": "<PERSON>, f<PERSON><PERSON><PERSON> date sau cookie-uri nu va funcționa, deoarece avem nevoie de explicații despre date sau cookie-uri."}, "privacy_consent__desc_privacy_L2": {"message": "Dacă nu ne permiteți să colectăm aceste informații, vă rugăm să le eliminați."}, "privacy_consent__item_cookies_desc": {"message": "<PERSON><PERSON>, primim datele dvs. valutare în cookie-uri numai atunci când faceți cumpărături online pentru a afișa istoricul prețurilor."}, "privacy_consent__item_cookies_title": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "privacy_consent__item_functional_desc_L1": {"message": "1. <PERSON><PERSON><PERSON><PERSON><PERSON> cookie-uri în browser pentru a vă identifica în mod anonim computerul sau dispozitivul."}, "privacy_consent__item_functional_desc_L2": {"message": "2. <PERSON><PERSON><PERSON><PERSON><PERSON> date funcționale în add-on pentru a lucra cu funcția."}, "privacy_consent__item_functional_title": {"message": "Cookie-uri funcționale și analitice"}, "privacy_consent__more_desc": {"message": "Vă rugăm să știți că nu împărtășim datele dvs. personale cu alte companii și că nicio companie publicitară nu colectează date prin intermediul serviciului nostru."}, "privacy_consent__options__btn__desc": {"message": "Pentru a utiliza toate fun<PERSON><PERSON><PERSON><PERSON>, trebuie să o activați."}, "privacy_consent__options__btn__label": {"message": "Porniți-l"}, "privacy_consent__options__desc_L1": {"message": "Vom colecta următoarele date care vă identifică personal:"}, "privacy_consent__options__desc_L2": {"message": "- cookie-uri, obținem datele dvs. de monedă în cookie-uri atunci când faceți cumpărături online pentru a afișa istoricul prețurilor."}, "privacy_consent__options__desc_L3": {"message": "- și adăugați cookie-uri în browser pentru a vă identifica anonim computerul sau dispozitivul."}, "privacy_consent__options__desc_L4": {"message": "- alte date anonime fac această extensie mai convenabilă."}, "privacy_consent__options__desc_L5": {"message": "Vă rugăm să rețineți că nu împărt<PERSON>șim datele dvs. personale cu alte companii și că nicio companie publicitară nu colectează date prin intermediul serviciului nostru."}, "privacy_consent__privacy_preferences": {"message": "Preferințe de confidențialitate"}, "privacy_consent__read_more": {"message": "Citiți mai multe >>"}, "privacy_consent__title_privacy": {"message": "Confidențialitate"}, "product_info": {"message": "Informații despre produs"}, "product_recommend__name": {"message": "<PERSON><PERSON><PERSON><PERSON> produse"}, "product_research": {"message": "Cercetare de produs"}, "product_sub__email_desc": {"message": "E-mail de alertă de preț"}, "product_sub__email_edit": {"message": "Editați | ×"}, "product_sub__email_not_verified": {"message": "Vă rugăm să verificați adresa de e-mail"}, "product_sub__email_required": {"message": "Vă rugăm să furnizați e-mail"}, "product_sub__form_countdown": {"message": "Închidere automată după $seconds$ secunde", "placeholders": {"seconds": {"content": "$1"}}}, "product_sub__form_fail": {"message": "Adăugarea mementoului nu a reușit!"}, "product_sub__form_input_price": {"message": "Prețul de intrare"}, "product_sub__form_item_country": {"message": "naţiune"}, "product_sub__form_item_current_price": {"message": "Pretul curent"}, "product_sub__form_item_duration": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "product_sub__form_item_higher_price": {"message": "sau pret >"}, "product_sub__form_item_invalid_higher_price": {"message": "Prețul trebuie să fie mai mare decât $price$", "placeholders": {"price": {"content": "$1"}}}, "product_sub__form_item_invalid_lower_price": {"message": "Prețul trebuie să fie mai mic decât $price$", "placeholders": {"price": {"content": "$1"}}}, "product_sub__form_item_lower_price": {"message": "Când prețul <"}, "product_sub__form_submit": {"message": "Trimite"}, "product_sub__form_success": {"message": "Adăugați memento cu succes!"}, "product_sub__high_price_notify": {"message": "Anunta-ma cu privire la cresterile de pret"}, "product_sub__low_price_notify": {"message": "Anunta-ma cu privire la reducerile de pret"}, "product_sub__modal_title": {"message": "Memento de modificare a prețului abonamentului"}, "province__an_hui": {"message": "<PERSON><PERSON>"}, "province__ao_men": {"message": "Macau"}, "province__bei_jing": {"message": "Beijing"}, "province__chong_qing": {"message": "Chongqing"}, "province__fu_jian": {"message": "Fujian"}, "province__gan_su": {"message": "Gansu"}, "province__guang_dong": {"message": "Guangdong"}, "province__guang_xi": {"message": "Guangxi"}, "province__gui_zhou": {"message": "Guizhou"}, "province__hai_nan": {"message": "Hainan"}, "province__he_bei": {"message": "Hebei"}, "province__he_nan": {"message": "<PERSON><PERSON>"}, "province__hei_long_jiang": {"message": "Heilongjiang"}, "province__hu_bei": {"message": "Hubei"}, "province__hu_nan": {"message": "Hunan"}, "province__ji_lin": {"message": "<PERSON><PERSON>"}, "province__jiang_su": {"message": "Jiangsu"}, "province__jiang_xi": {"message": "Jiangxi"}, "province__liao_ning": {"message": "Liaoning"}, "province__nei_meng_gu": {"message": "Inner Mongolia"}, "province__ning_xia": {"message": "Ningxia"}, "province__qing_hai": {"message": "Qinghai"}, "province__shan_dong": {"message": "Shandong"}, "province__shan_xi": {"message": "Shaanxi"}, "province__shang_hai": {"message": "Shanghai"}, "province__si_chuan": {"message": "Sichuan"}, "province__tai_wan": {"message": "Taiwan"}, "province__tian_jin": {"message": "Tianjin"}, "province__xi_zhang": {"message": "Tibet"}, "province__xiang_gang": {"message": "Hong Kong"}, "province__xin_jiang": {"message": "Xinjiang"}, "province__yun_nan": {"message": "Yunnan"}, "province__zhe_jiang": {"message": "Zhejiang"}, "purple_rocket": {"message": "로켓직구"}, "qi_ding_liang": {"message": "MOQ"}, "qi_ding_liang_qi_ding_jia": {"message": "MOQ și MOP"}, "qi_ye_mian_ji": {"message": "Zona Enterprise"}, "qing_zhi_shao_xuan_ze_yi_ge_chan_pin": {"message": "Va rugam sa selectati cel putin un produs"}, "qing_zhi_shao_xuan_ze_yi_ge_zi_duan": {"message": "Vă rugăm să selectați cel puțin un câmp"}, "qu_deng_lu": {"message": "Log in"}, "quan_guo_yan_xuan": {"message": "Alegerea globală"}, "recommendation_popup_banner_btn_install": {"message": "Instalați-l"}, "recommendation_popup_banner_desc": {"message": "Afișați istoricul prețurilor în termen de 3/6 luni și notificarea de scădere a prețului"}, "region__all": {"message": "Toate regi<PERSON>le"}, "region__hai_wai": {"message": "Overseas"}, "region__hua_bei_qu": {"message": "North China"}, "region__hua_dong_qu": {"message": "East China"}, "region__hua_nan_qu": {"message": "South China"}, "region__hua_zhong_qu": {"message": "Central China"}, "region__jiang_zhe_lu": {"message": "Jiangsu, Zhejiang and Shanghai"}, "remove_web_limits": {"message": "Activați clic dreapta"}, "ren_zheng_gong_chang": {"message": "Fabrica certificata"}, "ren_zheng_gong_ying_shang_nian_zhan": {"message": "Ani ca furnizor certificat"}, "required_to_aliprice_login": {"message": "Trebuie să vă conectați la AliPrice"}, "revenue_last30_days": {"message": "Valoarea vânzărilor în ultimele 30 de zile"}, "review_counts": {"message": "Numă<PERSON><PERSON> de colectori"}, "rocket": {"message": "로켓"}, "ru_zhu_nian_xian": {"message": "Perioada de intrare"}, "sales_amount_last30_days": {"message": "Vânzări totale în ultimele 30 de zile"}, "sales_last30_days": {"message": "Vân<PERSON><PERSON><PERSON> în ultimele 30 de zile"}, "sbi_alibaba_cate__accessories": {"message": "Accesorii"}, "sbi_alibaba_cate__aqfk": {"message": "Securitate"}, "sbi_alibaba_cate__bags_cases": {"message": "Genți și huse"}, "sbi_alibaba_cate__beauty": {"message": "Frumuseţe"}, "sbi_alibaba_cate__beverage": {"message": "Băutură"}, "sbi_alibaba_cate__bgwh": {"message": "Cultura de birou"}, "sbi_alibaba_cate__bz": {"message": "<PERSON><PERSON><PERSON>"}, "sbi_alibaba_cate__ccyj": {"message": "Ustensile de bucătărie"}, "sbi_alibaba_cate__clothes": {"message": "Îmbrăcăminte"}, "sbi_alibaba_cate__cmgd": {"message": "Difuzarea media"}, "sbi_alibaba_cate__coat_jacket": {"message": "<PERSON><PERSON> și j<PERSON>"}, "sbi_alibaba_cate__consumer_electronics": {"message": "Electronice de consum"}, "sbi_alibaba_cate__cryp": {"message": "<PERSON><PERSON><PERSON> pentru ad<PERSON>i"}, "sbi_alibaba_cate__csyp": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "sbi_alibaba_cate__cwyy": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> pentru animale de companie"}, "sbi_alibaba_cate__cysx": {"message": "Catering proasp<PERSON>t"}, "sbi_alibaba_cate__dgdq": {"message": "Electrician"}, "sbi_alibaba_cate__dl": {"message": "<PERSON><PERSON>"}, "sbi_alibaba_cate__dress_suits": {"message": "Rochie și costume"}, "sbi_alibaba_cate__dszm": {"message": "Iluminat"}, "sbi_alibaba_cate__dzqj": {"message": "Dispozitiv electronic"}, "sbi_alibaba_cate__essb": {"message": "Echipamente uzate"}, "sbi_alibaba_cate__food": {"message": "Alimente"}, "sbi_alibaba_cate__fspj": {"message": "haine si accesorii"}, "sbi_alibaba_cate__furniture": {"message": "<PERSON><PERSON><PERSON>"}, "sbi_alibaba_cate__fzpg": {"message": "<PERSON><PERSON>il<PERSON>"}, "sbi_alibaba_cate__ghjq": {"message": "Îng<PERSON>jire personală"}, "sbi_alibaba_cate__gt": {"message": "<PERSON><PERSON><PERSON>"}, "sbi_alibaba_cate__gyp": {"message": "Meşteşuguri"}, "sbi_alibaba_cate__hb": {"message": "Prietenos cu mediul"}, "sbi_alibaba_cate__hfcz": {"message": "<PERSON><PERSON><PERSON> a pielii"}, "sbi_alibaba_cate__hg": {"message": "Industria chimica"}, "sbi_alibaba_cate__jg": {"message": "Prelucrare"}, "sbi_alibaba_cate__jianccai": {"message": "Materiale de construcții"}, "sbi_alibaba_cate__jichuang": {"message": "<PERSON><PERSON><PERSON><PERSON> unealtă"}, "sbi_alibaba_cate__jjry": {"message": "Utilizare zilnică casnică"}, "sbi_alibaba_cate__jtys": {"message": "Transport"}, "sbi_alibaba_cate__jxsb": {"message": "Echipamente"}, "sbi_alibaba_cate__jxwj": {"message": "<PERSON><PERSON><PERSON>"}, "sbi_alibaba_cate__jydq": {"message": "Aparate de uz casnic"}, "sbi_alibaba_cate__jzjc": {"message": "Materiale de construcție pentru îmbunătățirea locuinței"}, "sbi_alibaba_cate__jzjf": {"message": "Textile de casă"}, "sbi_alibaba_cate__mj": {"message": "Prosop"}, "sbi_alibaba_cate__myyp": {"message": "Produse pentru copii"}, "sbi_alibaba_cate__nanz": {"message": "a bărbaţilor"}, "sbi_alibaba_cate__nvz": {"message": "Îmbră<PERSON>ăminte pentru femei"}, "sbi_alibaba_cate__ny": {"message": "Energie"}, "sbi_alibaba_cate__others": {"message": "Alții"}, "sbi_alibaba_cate__qcyp": {"message": "Accesorii auto"}, "sbi_alibaba_cate__qmpj": {"message": "Piese auto"}, "sbi_alibaba_cate__shoes": {"message": "<PERSON><PERSON><PERSON>"}, "sbi_alibaba_cate__smdn": {"message": "Calculator digital"}, "sbi_alibaba_cate__snqj": {"message": "Depozitare si curatare"}, "sbi_alibaba_cate__spjs": {"message": "<PERSON><PERSON> bautura"}, "sbi_alibaba_cate__swfw": {"message": "<PERSON><PERSON><PERSON>"}, "sbi_alibaba_cate__toys_hobbies": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "sbi_alibaba_cate__trousers_skirt": {"message": "Pantaloni și fustă"}, "sbi_alibaba_cate__txcp": {"message": "Produse de comunicare"}, "sbi_alibaba_cate__tz": {"message": "Imbracaminte de copii"}, "sbi_alibaba_cate__underwear": {"message": "Lenjerie de corp"}, "sbi_alibaba_cate__wjgj": {"message": "Instrumente hardware"}, "sbi_alibaba_cate__xgpi": {"message": "<PERSON><PERSON>"}, "sbi_alibaba_cate__xmhz": {"message": "cooperare la proiect"}, "sbi_alibaba_cate__xs": {"message": "Cau<PERSON><PERSON>"}, "sbi_alibaba_cate__ydfs": {"message": "Echipament sportiv"}, "sbi_alibaba_cate__ydhw": {"message": "Sport in aer liber"}, "sbi_alibaba_cate__yjkc": {"message": "Minerale metalurgice"}, "sbi_alibaba_cate__yqyb": {"message": "Instrumentaţie"}, "sbi_alibaba_cate__ys": {"message": "Imp<PERSON><PERSON>"}, "sbi_alibaba_cate__yyby": {"message": "Îngrijire medicală"}, "sbi_alibaba_cn_kj_90mjs": {"message": "Numărul de cumpărători din ultimele 90 de zile"}, "sbi_alibaba_cn_kj_90xsl": {"message": "Volumul vânzărilor din ultimele 90 de zile"}, "sbi_alibaba_cn_kj_gjsj": {"message": "Pret estimat"}, "sbi_alibaba_cn_kj_gjwlyf": {"message": "Taxă de transport internațional"}, "sbi_alibaba_cn_kj_gjyf": {"message": "Taxa de expediere"}, "sbi_alibaba_cn_kj_gssj": {"message": "Pret estimat"}, "sbi_alibaba_cn_kj_lr": {"message": "Profit"}, "sbi_alibaba_cn_kj_lrgs": {"message": "Profit = preț estimat x marjă de profit"}, "sbi_alibaba_cn_kj_pjfhsd": {"message": "Viteza medie de livrare"}, "sbi_alibaba_cn_kj_qtfy": {"message": "altă taxă"}, "sbi_alibaba_cn_kj_qtfygs": {"message": "Alt cost = preț estimat x alt raport de cost"}, "sbi_alibaba_cn_kj_spjg": {"message": "Preț"}, "sbi_alibaba_cn_kj_spzl": {"message": "Greutate"}, "sbi_alibaba_cn_kj_szd": {"message": "Locație"}, "sbi_alibaba_cn_kj_unit_ge": {"message": "Piese"}, "sbi_alibaba_cn_kj_unit_jian": {"message": "Piese"}, "sbi_alibaba_cn_kj_unit_ke": {"message": "Gram"}, "sbi_alibaba_cn_kj_unit_ren": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "sbi_alibaba_cn_kj_unit_tai": {"message": "Piese"}, "sbi_alibaba_cn_kj_unit_tao": {"message": "<PERSON><PERSON>"}, "sbi_alibaba_cn_kj_unit_tian": {"message": "Zile"}, "sbi_alibaba_cn_kj_zwbj": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "sbi_aliprice_alibaba_cn__jiage": {"message": "Preț"}, "sbi_aliprice_alibaba_cn__keshou": {"message": "<PERSON>"}, "sbi_aliprice_alibaba_cn__moren": {"message": "Mod implicit"}, "sbi_aliprice_alibaba_cn__queding": {"message": "<PERSON><PERSON><PERSON>"}, "sbi_aliprice_alibaba_cn__xiaoliang": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "sbi_aliprice_alibaba_cn_cate__jiaju": {"message": "mobila"}, "sbi_aliprice_alibaba_cn_cate__linghsi": {"message": "gustare"}, "sbi_aliprice_alibaba_cn_cate__meizhuang": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "sbi_aliprice_alibaba_cn_cate__neiyi": {"message": "<PERSON><PERSON><PERSON>"}, "sbi_aliprice_alibaba_cn_cate__peishi": {"message": "Accesorii"}, "sbi_aliprice_alibaba_cn_cate__pinchui": {"message": "Băutură îmbuteliată"}, "sbi_aliprice_alibaba_cn_cate__qita": {"message": "Alții"}, "sbi_aliprice_alibaba_cn_cate__qunzhuang": {"message": "<PERSON><PERSON><PERSON>"}, "sbi_aliprice_alibaba_cn_cate__shangyi": {"message": "Sacou"}, "sbi_aliprice_alibaba_cn_cate__shuma": {"message": "Electronică"}, "sbi_aliprice_alibaba_cn_cate__wanju": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "sbi_aliprice_alibaba_cn_cate__xiangbao": {"message": "Bagaje"}, "sbi_aliprice_alibaba_cn_cate__xiazhuang": {"message": "<PERSON>uri"}, "sbi_aliprice_alibaba_cn_cate__xiezi": {"message": "pantof"}, "sbi_aliprice_cate__apparel": {"message": "Îmbrăcăminte"}, "sbi_aliprice_cate__automobiles_motorcycles": {"message": "Automobile și motociclete"}, "sbi_aliprice_cate__beauty_health": {"message": "frumusete si sanatate"}, "sbi_aliprice_cate__cellphones_telecommunications": {"message": "Telefoane mobile și telecomunicații"}, "sbi_aliprice_cate__computer_office": {"message": "Computer și birou"}, "sbi_aliprice_cate__consumer_electronics": {"message": "Electronice de consum"}, "sbi_aliprice_cate__education_office_supplies": {"message": "Materiale de învățământ și birou"}, "sbi_aliprice_cate__electronic_components_supplies": {"message": "Componente electronice și consumabile"}, "sbi_aliprice_cate__furniture": {"message": "<PERSON><PERSON><PERSON>"}, "sbi_aliprice_cate__hair_extensions_wigs": {"message": "Extensii de păr și peruci"}, "sbi_aliprice_cate__home_garden": {"message": "casa si gradina"}, "sbi_aliprice_cate__home_improvement": {"message": "Îmbunătățirea locuinței"}, "sbi_aliprice_cate__jewelry_accessories": {"message": "Bijuterii și accesorii"}, "sbi_aliprice_cate__luggage_bags": {"message": "Bagaje și genți"}, "sbi_aliprice_cate__mother_kids": {"message": "Mama și copii"}, "sbi_aliprice_cate__novelty_special_use": {"message": "Noutate și utilizare specială"}, "sbi_aliprice_cate__security_protection": {"message": "Securitate și protecție"}, "sbi_aliprice_cate__shoes": {"message": "<PERSON><PERSON><PERSON>"}, "sbi_aliprice_cate__sports_entertainment": {"message": "Sport și divertisment"}, "sbi_aliprice_cate__toys_hobbies": {"message": "Jucării și Hobby-uri"}, "sbi_aliprice_cate__watches": {"message": "<PERSON><PERSON><PERSON>"}, "sbi_aliprice_cate__weddings_events": {"message": "Nunți și evenimente"}, "sbi_btn_capture_txt": {"message": "Captură"}, "sbi_btn_source_now_txt": {"message": "Sursa acum"}, "sbi_button__chat_with_me": {"message": "Vorbește cu mine"}, "sbi_button__contact_supplier": {"message": "Contact"}, "sbi_button__hide_on_this_site": {"message": "Nu afișați pe acest site"}, "sbi_button__open_settings": {"message": "Configurați căutarea după imagine"}, "sbi_capture_shortcut_tip": {"message": "sau apă<PERSON><PERSON>i tasta „Enter” de pe tastatură"}, "sbi_capturing_tip": {"message": "Capturare"}, "sbi_composed_rating_45": {"message": "4,5 - 5,0 stele"}, "sbi_crop_and_search": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "sbi_crop_start": {"message": "Utilizați captura de ecran"}, "sbi_err_captcha_action": {"message": "Verifica"}, "sbi_err_captcha_for_alibaba_cn": {"message": "Aveți nevoie de verificare, vă rugăm să încărcați o imagine pentru verificare. (Vizualizați $video_tutorial$ sau încercați să ștergeți cookie-urile)", "placeholders": {"feedback": {"content": "$1"}, "video_tutorial": {"content": "$1"}}}, "sbi_err_captcha_for_aliprice": {"message": "Trafic neobișnuit, vă rugăm să verificați"}, "sbi_err_captcha_for_taobao": {"message": "Taobao vă solicită să verificați, vă rugăm să încărcați manual o imagine și să căutați pentru ao verifica. Această eroare se datorează noii politici de verificare „Căutare TaoBao după imagine”, vă sugerăm să verificați reclamația prea frecvent pe Taobao $feedback$.", "placeholders": {"feedback": {"content": "$1"}}}, "sbi_err_captcha_for_taobao_feedback": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "sbi_err_captcha_msg": {"message": "$platform$ vă solicită să încărcați o imagine pentru a căuta sau să finalizați verificarea de securitate pentru a elimina restricțiile de căutare", "placeholders": {"platform": {"content": "$1"}}}, "sbi_err_checking_if_low_version": {"message": "Verificați dacă este cea mai recentă versiune"}, "sbi_err_cookie_btn_clear": {"message": "Șterge cookie-uri"}, "sbi_err_cookie_for_alibaba_cn": {"message": "Ștergeți cookie-urile 1688? (Trebuie să vă conectați din nou)"}, "sbi_err_desperate_feature_pdd": {"message": "Funcția de căutare a imaginilor a fost mutată în extensia Pinduoduo Search by Image."}, "sbi_err_hidden_skill_of_improve_search_rate": {"message": "Cum să îmbunătățiți rata de succes a căutării de imagini?"}, "sbi_err_img_undersize": {"message": "Imagine > $imgMinimumSize$", "placeholders": {"imgMinimumSize": {"content": "$1"}}}, "sbi_err_login_required": {"message": "Conectați-vă $loginSite$", "placeholders": {"loginSite": {"content": "$1"}}}, "sbi_err_login_required_action": {"message": "Autentificare"}, "sbi_err_low_version": {"message": "Instalați cea mai recentă versiune ($latestVersion$)", "placeholders": {"latestVersion": {"content": "$1"}}}, "sbi_err_low_version_action": {"message": "Descarca"}, "sbi_err_need_help": {"message": "Ai nevoie de ajutor"}, "sbi_err_network": {"message": "Eroare de rețea, asigurați-vă că puteți vizita site-ul web"}, "sbi_err_not_low_version": {"message": "A fost instalată cea mai recentă versiune ($latestVersion$)", "placeholders": {"latestVersion": {"content": "$1"}}}, "sbi_err_try_again": {"message": "Încearcă din nou"}, "sbi_err_try_again_action": {"message": "Încearcă din nou"}, "sbi_err_visit_and_try": {"message": "Încercați din nou sau vizitați $website$ pentru a reîncerca", "placeholders": {"website": {"content": "$1"}}}, "sbi_err_visit_site": {"message": "Vizitați pagina de pornire $siteName$", "placeholders": {"siteName": {"content": "$1"}}}, "sbi_fail_tip": {"message": "Încărcarea nu a reușit, actualizați pagina și încercați din nou."}, "sbi_kuajing_filter_area": {"message": "Zonă"}, "sbi_kuajing_filter_au": {"message": "Australia"}, "sbi_kuajing_filter_btn_confirm": {"message": "A confirma"}, "sbi_kuajing_filter_de": {"message": "Germania"}, "sbi_kuajing_filter_destination_country": {"message": "Țara de destinație"}, "sbi_kuajing_filter_es": {"message": "Spania"}, "sbi_kuajing_filter_estimate": {"message": "Estima"}, "sbi_kuajing_filter_estimate_price": {"message": "Pret estimat"}, "sbi_kuajing_filter_estimate_price_formula": {"message": "Formula estimată a prețului = (prețul mărfii + transport logistic internațional) / (1 - marja de profit - alt raport de cost)"}, "sbi_kuajing_filter_fr": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "sbi_kuajing_filter_kw_placeholder": {"message": "Introduceți cuvinte cheie pentru a se potrivi cu titlul"}, "sbi_kuajing_filter_logistics": {"message": "Șablon logistic"}, "sbi_kuajing_filter_logistics_china_post": {"message": "China Post Air Mail"}, "sbi_kuajing_filter_logistics_discount": {"message": "Reducere logistică"}, "sbi_kuajing_filter_logistics_epacket": {"message": "epacket"}, "sbi_kuajing_filter_logistics_price_formula": {"message": "Transport logistic internațional = (greutate x preț de expediere + taxă de înregistrare) x (1 - reducere)"}, "sbi_kuajing_filter_others_fee": {"message": "Alte taxe"}, "sbi_kuajing_filter_profit_percent": {"message": "Marja de profit"}, "sbi_kuajing_filter_prop": {"message": "Atribute"}, "sbi_kuajing_filter_ru": {"message": "Rusia"}, "sbi_kuajing_filter_total": {"message": "Potriviți $count$ articole similare", "placeholders": {"count": {"content": "$1"}}}, "sbi_kuajing_filter_uk": {"message": "REGATUL UNIT"}, "sbi_kuajing_filter_usa": {"message": "America"}, "sbi_login_punish_title__pdd_pifa": {"message": "Pinduoduo en-gros"}, "sbi_msg_no_result": {"message": "Nu s-a găsit niciun rezultat,vă rugăm să vă conectați la $loginSite$ sau să încercați o altă poză", "placeholders": {"loginSite": {"content": "$1"}}}, "sbi_msg_no_result_check_support_page_l1": {"message": "Nu este disponibil temporar pentru <PERSON>, vă rugăm să utilizați $supportPage$.", "placeholders": {"supportPage": {"content": "$1"}}}, "sbi_msg_no_result_check_support_page_l2": {"message": "Browserul Chrome și extensiile acestuia"}, "sbi_msg_no_result_reinstall_l1": {"message": "Nu s-au găsit rezultate, vă rugăm să vă conectați la $loginSite$ sau să încercați o altă imagine sau să reinstalați cea mai recentă versiune $latestExtUrl$", "placeholders": {"loginSite": {"content": "$1"}, "latestExtUrl": {"content": "$2"}}}, "sbi_msg_no_result_reinstall_l2": {"message": "Ultima versiune", "placeholders": {}}, "sbi_search_by_selected_area": {"message": "Zona selectată"}, "sbi_shipping_": {"message": "Livrare in aceeasi zi"}, "sbi_specify_category": {"message": "Specificați categoria:"}, "sbi_start_crop": {"message": "Selectați zona"}, "sbi_store_name_alibabaCNKuaJing": {"message": "1688 în stră<PERSON>"}, "sbi_tutorial_btn_more": {"message": "<PERSON> multe moduri"}, "sbi_tutorial_find_coupons_on_taobao": {"message": "Găsiți cupoane Taobao"}, "sbi_txt__empty_retry": {"message": "Ne pare rău, nu s-au gă<PERSON>t rezultate, încercați din nou."}, "sbi_txt__min_order": {"message": "<PERSON><PERSON>"}, "sbi_visiting": {"message": "Navigare"}, "sbi_yiwugo__jiagexiangtan": {"message": "Contactați vânzătorul pentru preț"}, "sbi_yiwugo__qigou": {"message": "$num$ Piese (MOQ)", "placeholders": {"num": {"content": "$1"}}}, "sbi_yiwugo__xing": {"message": "Stele"}, "searchByImage_screenshot": {"message": "Captură de ecran cu un singur clic"}, "searchByImage_search": {"message": "Căutare cu un singur clic pentru aceleași articole"}, "searchByImage_size_type": {"message": "Dimensiunea fișierului nu poate fi mai mare de $num$ MB, numai $type$", "placeholders": {"num": {"content": "$1"}, "type": {"content": "$2"}}}, "search_by_image_progress_analysing": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "search_by_image_progress_searching": {"message": "Căutați produse"}, "search_by_image_progress_sending": {"message": "Trimiterea imaginii"}, "search_by_image_response_rate": {"message": "Rata de răspuns: $responseRate$ a cumpărătorilor care au contactat acest furnizor au primit un răspuns în termen de $responseInHour$ ore.", "placeholders": {"responseRate": {"content": "$1"}, "responseInHour": {"content": "$2"}}}, "search_by_keyword": {"message": "<PERSON><PERSON><PERSON><PERSON> după cuvânt cheie:"}, "select_country_language_modal_title_country": {"message": "Țară"}, "select_country_language_modal_title_language": {"message": "Limba"}, "select_country_region_modal_title": {"message": "Selectați o țară / regiune"}, "select_language_modal_title": {"message": "Selectați o limbă:"}, "select_shop": {"message": "Selectați magazinul"}, "sellers_count": {"message": "Numărul de vânzători pe pagina curentă"}, "sellers_count_per_page": {"message": "Numărul de vânzători pe pagina curentă"}, "service_score": {"message": "Evaluare cuprinzătoare de servicii"}, "set_shortcut_keys": {"message": "Setați tastele de comandă rapidă"}, "setting_logo_title": {"message": "Asistent la cumpărături"}, "setting_modal_options_position_title": {"message": "Poziția de conectare"}, "setting_modal_options_position_value_left": {"message": "Colțul stâng"}, "setting_modal_options_position_value_right": {"message": "Coltul drept"}, "setting_modal_options_theme_title": {"message": "<PERSON><PERSON><PERSON><PERSON> temei"}, "setting_modal_options_theme_value_dark": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "setting_modal_options_theme_value_light": {"message": "Ușoară"}, "setting_modal_title": {"message": "<PERSON><PERSON><PERSON>"}, "setting_options_country_title": {"message": "Țară / Regiune"}, "setting_options_hover_zoom_desc": {"message": "Treceți cu mouse-ul peste mouse pentru a mări"}, "setting_options_hover_zoom_title": {"message": "Plasați cursorul pe zoom"}, "setting_options_jd_coupon_desc": {"message": "Cupon găsit pe JD.com"}, "setting_options_jd_coupon_title": {"message": "Cupon JD.com"}, "setting_options_language_title": {"message": "Limba"}, "setting_options_price_drop_alert_desc": {"message": "Când prețul produselor din Preferatul meu scade, veți primi o notificare push."}, "setting_options_price_drop_alert_title": {"message": "Alertă de scădere a prețului"}, "setting_options_price_history_on_list_page_desc": {"message": "Afișați istoricul prețurilor în pagina de căutare a produselor"}, "setting_options_price_history_on_list_page_title": {"message": "Istoricul prețurilor (pagina cu listă)"}, "setting_options_price_history_on_produt_page_desc": {"message": "Afișați istoricul produselor pe pagina cu detalii despre produs"}, "setting_options_price_history_on_produt_page_title": {"message": "Istoricul prețurilor (pagina cu detalii)"}, "setting_options_sales_analysis_desc": {"message": "Suport statistici privind prețul, volumul vânzărilor, numărul de vânzători și raportul vânzărilor în magazin pe pagina cu lista de produse $platforms$", "placeholders": {"platforms": {"content": "$1"}}}, "setting_options_sales_analysis_title": {"message": "<PERSON><PERSON><PERSON>"}, "setting_options_save_success_msg": {"message": "Succes"}, "setting_options_tacking_price_title": {"message": "Alertă de modificare a prețului"}, "setting_options_value_off": {"message": "Dezactivat"}, "setting_options_value_on": {"message": "Pe"}, "setting_pkg_quick_view_desc": {"message": "Suport: 1688 & Taobao"}, "setting_saved_message": {"message": "Modificările au fost salvate cu succes"}, "setting_section_enable_platform_title": {"message": "On-off"}, "setting_section_setting_title": {"message": "<PERSON><PERSON><PERSON>"}, "setting_section_shortcuts_title": {"message": "Comenzi rapide"}, "settings_aliprice_agent__desc": {"message": "Afișat pe pagina cu detalii despre produs $platforms$", "placeholders": {"platforms": {"content": "$1"}}}, "settings_aliprice_agent__title": {"message": "Cumpără pentru mine"}, "settings_copy_link__desc": {"message": "Afișați pe pagina cu detaliile produsului"}, "settings_copy_link__title": {"message": "Butonul Copiere și titlul Căutare"}, "settings_currency_desc__for_detail": {"message": "Asistență la pagina de detalii a produsului 1688"}, "settings_currency_desc__for_list": {"message": "<PERSON><PERSON><PERSON><PERSON> după imagine (includeți 1688/1688 în străinătate / Taobao)"}, "settings_currency_desc__for_sbi": {"message": "Selectați prețul"}, "settings_currency_desc_display_for_list": {"message": "Afișat în căutarea de imagini (inclusiv 1688/1688 de peste mări/Taobao)"}, "settings_currency_rate_desc": {"message": "Actualizarea cursului de schimb de la \"$currencyRateFrom$\"", "placeholders": {"currencyRateFrom": {"content": "$1"}}}, "settings_currency_rate_from_boc": {"message": "Banca Chinei"}, "settings_download_images__desc": {"message": "Asistență pentru descărcarea imaginilor de pe $platforms$", "placeholders": {"platforms": {"content": "$1"}}}, "settings_download_images__title": {"message": "butonul de descărcare a imaginii"}, "settings_download_reviews__desc": {"message": "Afișat pe pagina cu detalii despre produs $platforms$", "placeholders": {"platforms": {"content": "$1"}}}, "settings_download_reviews__title": {"message": "Descărcați imagini de recenzii"}, "settings_google_translate_desc": {"message": "Faceți clic dreapta pentru a obține bara de traducere Google"}, "settings_google_translate_title": {"message": "traducerea paginii web"}, "settings_historical_trend_desc": {"message": "Afișați în colțul din dreapta jos al imaginii pe pagina cu lista de produse"}, "settings_modal_btn_more": {"message": "<PERSON> multe setari"}, "settings_productInfo_desc": {"message": "Afișați informații mai detaliate despre produse pe pagina cu lista de produse. Activarea acestui lucru poate crește încărcarea computerului și poate cauza întârziere a paginii. Dacă afectează performanța, se recomandă să-l dezactivați."}, "settings_product_recommend__desc": {"message": "Afișat sub imaginea principală pe pagina cu detaliile produsului $platforms$", "placeholders": {"platforms": {"content": "$1"}}}, "settings_product_recommend__name": {"message": "Produse recomandate"}, "settings_research_desc": {"message": "Solicitați informații mai detaliate pe pagina cu lista de produse"}, "settings_sbi_add_to_list": {"message": "Adăugați în $listType$", "placeholders": {"listType": {"content": "$1"}}}, "settings_sbi_detail_page_icon_title_desc": {"message": "Miniatură rezultat căutare imagine"}, "settings_sbi_remove_from_list": {"message": "Eliminați din $listType$", "placeholders": {"listType": {"content": "$1"}}}, "settings_search_by_image_add_to_blacklist": {"message": "Adăugați la lista de blocuri"}, "settings_search_by_image_adjust_entrance_entrance": {"message": "Reglați poziția de intrare"}, "settings_search_by_image_blacklist_desc": {"message": "Nu afișați pictograma pe site-urile din lista neagră."}, "settings_search_by_image_blacklist_title": {"message": "Lista de blocati"}, "settings_search_by_image_bottom_left": {"message": "Stânga jos"}, "settings_search_by_image_bottom_right": {"message": "<PERSON><PERSON><PERSON> jos"}, "settings_search_by_image_clear_blacklist": {"message": "Ștergeți lista de blocuri"}, "settings_search_by_image_detail_page_icon_title": {"message": "Miniatură"}, "settings_search_by_image_detail_page_icon_val_large": {"message": "<PERSON> mare"}, "settings_search_by_image_detail_page_icon_val_small": {"message": "Mai mica"}, "settings_search_by_image_display_button_desc": {"message": "Un singur clic pe pictogramă pentru a căuta după imagine"}, "settings_search_by_image_display_button_title": {"message": "Pictogramă pe imagini"}, "settings_search_by_image_sourece_websites_desc": {"message": "Găsiți produsul sursă pe aceste site-uri web"}, "settings_search_by_image_sourece_websites_title": {"message": "Căutare după rezultatul imaginii"}, "settings_search_by_image_top_left": {"message": "Sus Stânga"}, "settings_search_by_image_top_right": {"message": "<PERSON><PERSON> dreapta"}, "settings_search_keyword_on_x__desc": {"message": "Căutați cuvinte pe $platform$", "placeholders": {"platform": {"content": "$1"}}}, "settings_search_keyword_on_x__title": {"message": "Afișați pictograma $platform$ când sunt selectate cuvinte", "placeholders": {"platform": {"content": "$1"}}}, "settings_similar_products_desc": {"message": "Încercați să găsiți același produs în acele site-uri web (maximum 5)"}, "settings_similar_products_title": {"message": "Găsiți același produs"}, "settings_toolbar_expand_title": {"message": "Minimizarea plug-in-ului"}, "settings_top_toolbar_desc": {"message": "Bara de căutare din partea de sus a paginii"}, "settings_top_toolbar_title": {"message": "Bara <PERSON>"}, "settings_translate_search_desc": {"message": "Traduceți în chineză și căutați"}, "settings_translate_search_title": {"message": "Căutare multilingvă"}, "settings_translator_contextmenu_title": {"message": "Captur<PERSON> pentru a traduce"}, "settings_translator_title": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "shai_xuan_dao_chu": {"message": "Filtrați pentru a exporta"}, "shai_xuan_zi_duan": {"message": "Filtrați câmpurile"}, "shang_jia_shi_jian": {"message": "La raft"}, "shang_pin_biao_ti": {"message": "titlul produsului"}, "shang_pin_dui_bi": {"message": "Compararea produsului"}, "shang_pin_lian_jie": {"message": "link-ul produsului"}, "shang_pin_xin_xi": {"message": "Informatiile produs<PERSON>i"}, "share_modal__content": {"message": "imparte cu prietenii tai"}, "share_modal__disable_for_while": {"message": "Nu vreau să împărtășesc nimic"}, "share_modal__title": {"message": "Îți place $extensionName$?", "placeholders": {"extensionName": {"content": "$1"}}}, "sheng_yu_ku_cun": {"message": "Rămânând"}, "shi_fou_ke_ding_zhi": {"message": "Este personalizabil?"}, "shi_fou_ren_zheng_gong_ying_sha": {"message": "Furnizor certificat"}, "shi_fou_ren_zheng_gong_ying_shang_zong_shu": {"message": "Furnizori certificati"}, "shi_fou_you_mao_yi_dan_bao": {"message": "Asigurarea Comerțului"}, "shi_fou_you_mao_yi_dan_bao_zong_shu": {"message": "Garanții comerciale"}, "shipping_fee": {"message": "Taxa de expediere"}, "shop_followers": {"message": "Adeptii magazinului"}, "shou_qi": {"message": "<PERSON>"}, "similar_products_warn_max_platforms": {"message": "Max până la 5"}, "sku_calc_price": {"message": "Preț calculat"}, "sku_calc_price_settings": {"message": "Setări preț calculat"}, "sku_formula": {"message": "Formulă"}, "sku_formula_desc": {"message": "Descriere formulă"}, "sku_formula_desc_text": {"message": "Acceptă formule matematice complexe, prețul inițial fiind reprezentat de A și transportul de marfă fiind reprezentat de B\n\n<br/>\n\nAcceptă paranteze (), plus +, minus -, înmulțire * și împărțire /\n\n<br/>\n\nExemplu:\n\n<br/>\n\n1. Pentru a obține de 1,2 ori prețul inițial și apoi adunați transportul de marfă, formula este: A*1,2+B\n\n<br/>\n\n2. <PERSON>tru a obține prețul inițial plus 1 yuan, apoi înmulțiți cu 1,2 ori, formula este: (A+1)*1,2\n\n<br/>\n\n3. Pentru a obține prețul inițial plus 10 yuani, apoi înmulțiți cu 1,2 ori și apoi scădeți 3 yuani, formula este: (A+10)*1,2-3"}, "sku_in_stock": {"message": "În stoc"}, "sku_invalid_formula_format": {"message": "Format formulă nevalid"}, "sku_inventory": {"message": "Inventar"}, "sku_link_copy_fail": {"message": "Copiate cu succes, specificațiile și atributele sku nu sunt selectate"}, "sku_link_copy_success": {"message": "Copiat cu succes, specificațiile sku și atributele selectate"}, "sku_list": {"message": "List SKU"}, "sku_min_qrder_qty": {"message": "Cantitate minimă comandă"}, "sku_name": {"message": "Nume SKU"}, "sku_no": {"message": "Nr."}, "sku_original_price": {"message": "Preț inițial"}, "sku_price": {"message": "Preț SKU"}, "stop_track_time_label": {"message": "Termen limită de urmărire:"}, "suo_zai_di_qu": {"message": "Locație"}, "tab_pkg_quick_view": {"message": "Monitorul logisticii"}, "tab_product_details_price_history": {"message": "Istoricul prețurilor"}, "tab_product_details_reviews": {"message": "Recenzii foto"}, "tab_product_details_seller_analysis": {"message": "<PERSON><PERSON><PERSON> vânzătorului"}, "tab_product_details_similar_products": {"message": "<PERSON><PERSON><PERSON><PERSON> produse"}, "total_days_listed_per_product": {"message": "Suma zilelor la raft ÷ Numărul de produse"}, "total_items": {"message": "Numărul total de produse"}, "total_price_per_product": {"message": "Suma prețurilor ÷ Numărul de produse"}, "total_rating_per_product": {"message": "Suma evaluărilor ÷ Numărul de produse"}, "total_revenue": {"message": "Veniturile totale"}, "total_revenue40_items": {"message": "Venitul total al celor $amount$ de produse de pe pagina curentă", "placeholders": {"amount": {"content": "$1"}}}, "total_sales": {"message": "Vanzari totale"}, "total_sales40_items": {"message": "Vânzările totale ale celor $amount$ de produse de pe pagina curentă", "placeholders": {"amount": {"content": "$1"}}}, "track_for_12_months": {"message": "Track pentru: 1 an"}, "track_for_3_months": {"message": "Track pentru: 3 luni"}, "track_for_6_months": {"message": "Track pentru: 6 luni"}, "tracking_price_email_add_btn": {"message": "Adăugați un e-mail"}, "tracking_price_email_edit_btn": {"message": "Editați adresa de e-mail"}, "tracking_price_email_intro": {"message": "Vă vom notifica prin e-mail."}, "tracking_price_email_invalid": {"message": "Vă rugăm să furnizați un e-mail valid"}, "tracking_price_email_verified_desc": {"message": "Acum puteți primi alerta noastră de scădere a prețului."}, "tracking_price_email_verified_title": {"message": "Verificat cu succes"}, "tracking_price_email_verify_desc_line1": {"message": "Am trimis un link de verificare la adresa dvs. de e-mail,"}, "tracking_price_email_verify_desc_line2": {"message": "vă rugăm să verificați căsuța de e-mail."}, "tracking_price_email_verify_title": {"message": "Verificați e-mail"}, "tracking_price_web_push_notification_intro": {"message": "Pe desktop: AliPrice vă poate monitoriza orice produs și vă poate trimite o notificare Web Push odată ce prețul se modifică."}, "tracking_price_web_push_notification_title": {"message": "Notificări push Web"}, "translate_im__login_required": {"message": "<PERSON><PERSON><PERSON> AliPrice, vă rugăm să vă conectați la $loginUrl$", "placeholders": {"loginUrl": {"content": "$1"}}}, "translate_im__paste_fail": {"message": "Tradus și copiat în clipboard, dar din cauza limit<PERSON><PERSON><PERSON>, trebuie să-l lipiți manual!"}, "translate_im__send": {"message": "Traduceți și trimiteți"}, "translate_search": {"message": "Traduceți și căutați"}, "translation_originals_translated": {"message": "Original și chinezesc"}, "translation_translated": {"message": "chinez"}, "translator_btn_capture_txt": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "translator_language_auto_detect": {"message": "Detectare automată"}, "translator_language_detected": {"message": "Detectat"}, "translator_language_search_placeholder": {"message": "Căutați limba"}, "try_again": {"message": "Încearcă din nou"}, "tu_pian_chi_cun": {"message": "Dimensi<PERSON>a imaginii:"}, "tu_pian_lian_jie": {"message": "<PERSON> imagine"}, "tui_huan_ti_yan": {"message": "Experiență de întoarcere"}, "tui_huan_ti_yan__desc": {"message": "Evaluați indicatorii post-vânzare ai vânzătorilor"}, "tutorial__show_all": {"message": "Toate caracteristicile"}, "tutorial_ae_popup_title": {"message": "Fixați extensia, deschideți Aliexpress"}, "tutorial_aliexpress_reviews_analysis": {"message": "Analiza de revizuire AliExpress"}, "tutorial_aliprice_agent_with1688_desc_l1": {"message": "Suport USD"}, "tutorial_aliprice_agent_with1688_desc_l2": {"message": "Livrare în Coreea/Japonia/China continentală"}, "tutorial_aliprice_agent_with1688_title": {"message": "1688 Acceptă achiziția de peste mări"}, "tutorial_auto_apply_coupon_title": {"message": "Aplicați automat cuponul"}, "tutorial_btn_end": {"message": "Sfârşit"}, "tutorial_btn_example": {"message": "Exemplu"}, "tutorial_btn_see_more": {"message": "<PERSON> mult"}, "tutorial_compare_products": {"message": "Comparați cu același stil"}, "tutorial_currency_convert_title": {"message": "conversia cursului de schimb"}, "tutorial_export_shopping_cart": {"message": "Exportați CSV, acceptă Taobao și 1688"}, "tutorial_export_shopping_cart_title": {"message": "coș de export"}, "tutorial_price_history_pro": {"message": "Afișat pe pagina cu detaliile produsului.\nAsistență Shopee, Lazada, Amazon și Ebay"}, "tutorial_price_history_pro_title": {"message": "Istoricul prețurilor pe întregul an și istoricul comenzilor"}, "tutorial_sbi_context_menu_screenshot_title": {"message": "Căutare captură de ecran pentru ace<PERSON>ș<PERSON> stil"}, "tutorial_translate_search": {"message": "Traduceți în căutare"}, "tutorial_translate_search_and_package_tracking": {"message": "<PERSON><PERSON><PERSON><PERSON> traducere și urmări<PERSON> p<PERSON>t"}, "unit_bao": {"message": "buc"}, "unit_ben": {"message": "buc"}, "unit_bi": {"message": "<PERSON><PERSON><PERSON>"}, "unit_chuang": {"message": "buc"}, "unit_dai": {"message": "buc"}, "unit_dui": {"message": "pair"}, "unit_fen": {"message": "buc"}, "unit_ge": {"message": "buc"}, "unit_he": {"message": "buc"}, "unit_jian": {"message": "buc"}, "unit_li_fang_mi": {"message": "m³"}, "unit_ping": {"message": "buc"}, "unit_ping_fang_mi": {"message": "㎡"}, "unit_shuang": {"message": "pair"}, "unit_tai": {"message": "buc"}, "unit_ti": {"message": "buc"}, "unit_tiao": {"message": "buc"}, "unit_xiang": {"message": "buc"}, "unit_zhang": {"message": "buc"}, "unit_zhi": {"message": "buc"}, "verify_contact_support": {"message": "Contactați asistența"}, "verify_human_verification": {"message": "Verificarea umană"}, "verify_unusual_access": {"message": "A fost detectat acces neobișnuit"}, "view_history_clean_all": {"message": "<PERSON><PERSON><PERSON> tot"}, "view_history_clean_all_warring": {"message": "Ștergeți toate înregistrările vizualizate?"}, "view_history_clean_all_warring_title": {"message": "Avertizare"}, "view_history_viewd": {"message": "Vizualizat"}, "website": {"message": "site-ul web"}, "weight": {"message": "Greutate"}, "wu_fa_huo_qu_dao_gai_shu_ju": {"message": "Nu se pot obține datele"}, "wu_liu_shi_xiao": {"message": "Livrar<PERSON> la timp"}, "wu_liu_shi_xiao__desc": {"message": "Rata de colectare de 48 de ore și rata de onorare a magazinului vânzătorului"}, "xia_dan_jia": {"message": "Pretul final"}, "xian_xuan_ze_product_attributes": {"message": "Selectați atributele produsului"}, "xiao_liang": {"message": "Volum de vânzări"}, "xiao_liang_zhan_bi": {"message": "Procentul din volumul vânzărilor"}, "xiao_shi": {"message": "$num$ ore", "placeholders": {"num": {"content": "$1"}}}, "xiao_shou_e": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "xiao_shou_e_zhan_bi": {"message": "Procentul din venituri"}, "xuan_zhong_x_tiao_ji_lu": {"message": "Selectați $amount$ înregistrări", "placeholders": {"amoun": {"content": "$1"}, "amount": {"content": "$1"}}}, "yan_xuan": {"message": "Alegere"}, "yi_ding_zai_zuo_ce": {"message": "Fixat"}, "yi_jia_zai_wan_suo_you_shang_pin": {"message": "Toate pro<PERSON><PERSON>"}, "yi_nian_xiao_liang": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "yi_nian_xiao_liang_zhan_bi": {"message": "Cota anuală a vânzărilor"}, "yi_nian_xiao_shou_e": {"message": "<PERSON><PERSON><PERSON>"}, "yi_nian_xiao_shou_e_zhan_bi": {"message": "Cota de afaceri an<PERSON>ă"}, "yi_shua_xin": {"message": "Odihnit"}, "yin_cang_xiang_tong_dian": {"message": "ascunde asemănările"}, "you_xiao_liang": {"message": "Cu volum de vânzări"}, "yu_ji_dao_da_shi_jian": {"message": "Ora estimată de sosire"}, "yuan_gong_ren_shu": {"message": "<PERSON><PERSON><PERSON>"}, "yue_cheng_jiao": {"message": "Volumul lunar"}, "yue_dai_xiao": {"message": "Dropshipping"}, "yue_dai_xiao__desc": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON> dropshipping în ultimele 30 de zile"}, "yue_dai_xiao_pai_xu__desc": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON> dropshipping în ultimele 30 de zile, sortate de la mare la scăzut"}, "yue_xiao_liang__desc": {"message": "Volumul vânzărilor în ultimele 30 de zile"}, "zhan_kai": {"message": "<PERSON> mult"}, "zhe_kou": {"message": "Reducere"}, "zhi_chi_yi_jian_dai_fa": {"message": "Dropshipping"}, "zhi_chi_yi_jian_dai_fa_bao_you": {"message": "Transport gratuit"}, "zhi_fu_ding_dan_shu": {"message": "<PERSON><PERSON><PERSON> p<PERSON>"}, "zhi_fu_ding_dan_shu__desc": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON> de comenzi pentru acest produs (30 de zile)"}, "zhu_ce_xing_zhi": {"message": "Natura de înregistrare"}, "zi_ding_yi_tiao_jian": {"message": "Condiții personalizate"}, "zi_duan": {"message": "Câmpuri"}, "zi_ti_xiao_liang": {"message": "<PERSON><PERSON><PERSON>"}, "zong_he_fu_wu_fen": {"message": "Evaluare generală"}, "zong_he_fu_wu_fen__desc": {"message": "Evaluarea generală a serviciului vânzătorului"}, "zong_he_fu_wu_fen__short": {"message": "Evaluare"}, "zong_he_ti_yan_fen": {"message": "Evaluare"}, "zong_he_ti_yan_fen_3": {"message": "Sub 4 Stele"}, "zong_he_ti_yan_fen_4": {"message": "4 - 4,5 stele"}, "zong_he_ti_yan_fen_4_5": {"message": "4,5 - 5,0 stele"}, "zong_he_ti_yan_fen_5": {"message": "5 stele"}, "zong_ku_cun": {"message": "Inventar total"}, "zong_xiao_liang": {"message": "Vanzari totale"}, "zui_jin_30D_3Min_xiang_ying_lv": {"message": "Rată de răspuns de 3 minute în ultimele 30 de zile"}, "zui_jin_30D_48H_lan_shou_lv": {"message": "Rata de recuperare de 48 de ore în ultimele 30 de zile"}, "zui_jin_30D_48H_lv_yue_lv": {"message": "Rata de performanță de 48 de ore în ultimele 30 de zile"}, "zui_jin_30D_jiao_yi_ji_lu": {"message": "Record de tranzacționare (30 de zile)"}, "zui_jin_30D_jiao_yi_ji_lu__desc": {"message": "Record de tranzacționare (30 de zile)"}, "zui_jin_30D_jiu_fen_lv": {"message": "Rata de dispute în ultimele 30 de zile"}, "zui_jin_30D_pin_zhi_tui_kuan_lv": {"message": "Rata de rambursare de calitate în ultimele 30 de zile"}, "zui_jin_30D_zhi_fu_ding_dan_shu": {"message": "Numărul de ordine de plată din ultimele 30 de zile"}}