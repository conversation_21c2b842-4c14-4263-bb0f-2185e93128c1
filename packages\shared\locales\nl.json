{"EXTENSION_NAME": {"message": "TODO: EXTENSION_NAME"}, "EXTENSION_DESCRIPTION": {"message": "TODO: EXTENSION_DESCRIPTION"}, "1688_cross_border_hot_selling": {"message": "1688 Grensoverschrijdende Hot Selling Spot"}, "1688_shi_li_ren_zheng": {"message": "1688 sterktecertificering"}, "1_jian_qi_pi": {"message": "MOQ: 1"}, "1year_yi_shang": {"message": "Ruim 1 jaar"}, "24H": {"message": "24H"}, "24H_fa_huo": {"message": "<PERSON><PERSON> binnen 24 uur"}, "24H_lan_shou_lv": {"message": "24-<PERSON><PERSON> verp<PERSON>"}, "30D_shang_xin": {"message": "Nieuwe maandelijkse aankomsten"}, "30d_sales": {"message": "$amount$ verkocht in 30 dagen", "placeholders": {"amount": {"content": "$1"}}}, "3Min_xiang_ying_lv": {"message": "<PERSON><PERSON><PERSON> binnen 3 minuten."}, "3Min_xiang_ying_lv__desc": {"message": "Het percentage effectieve reacties van Wangwang op berichten met vragen van kopers binnen drie minuten in de afgelopen 30 dagen"}, "48H": {"message": "48 uur"}, "48H_fa_huo": {"message": "<PERSON><PERSON> binnen 48 uur"}, "48H_lan_shou_lv": {"message": "48-<PERSON><PERSON> ve<PERSON>"}, "48H_lan_shou_lv__desc": {"message": "Verhouding van het opgehaalde orderaantal binnen 48 uur ten opzichte van het totaal aantal bestellingen"}, "48H_lv_yue_lv": {"message": "Prestatietarief van 48 uur"}, "48H_lv_yue_lv__desc": {"message": "<PERSON>er<PERSON><PERSON> van het opgehaalde of afgeleverde orderaantal binnen 48 uur ten opzichte van het totaal aantal bestellingen"}, "72H": {"message": "72 uur"}, "7D_shang_xin": {"message": "Nieuwe wekelijkse aankomsten"}, "7D_wu_li_you": {"message": "7 dagen zorgeloos"}, "ABS_title_text": {"message": "Deze aanbieding bevat een merk<PERSON>al"}, "AC_title_text": {"message": "Deze aanbieding heeft het Amazon's Choice-badge"}, "A_title_text": {"message": "Deze aanbieding heeft een A+-inhoudspagina"}, "BS_title_text": {"message": "Deze aanbieding is gerangschikt als de $num$ Best Seller in de categorie $type$", "placeholders": {"type": {"content": "$1"}, "num": {"content": "$2"}}}, "LTD_title_text": {"message": "LTD (Limited Time Deal) betekent dat deze aanbieding deel uitma<PERSON>t van een \"7-daagse promotie\"-evenement"}, "NR_title_text": {"message": "Deze aanbieding is gerangschikt als de $num$ New Release in de categorie $type$", "placeholders": {"type": {"content": "$1"}, "num": {"content": "$2"}}}, "SBV_title_text": {"message": "Deze aanbieding heeft een videoadvertentie, een type PPC-advertentie dat meestal midden in de zoekresultaten verschijnt"}, "SB_title_text": {"message": "Deze aanbieding heeft een merkadvertentie, een type PPC-advertentie dat meestal boven of onder aan de zoekresultaten verschijnt"}, "SP_title_text": {"message": "Deze aanbieding heeft een advertentie voor een gesponsord product"}, "V_title_text": {"message": "De<PERSON> aanbieding heeft een video-introductie"}, "advanced_research": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "agent_ds1688___my_order": {"message": "mijn best<PERSON>"}, "agent_ds1688__add_to_cart": {"message": "Aankoop in het buitenland"}, "agent_ds1688__cart": {"message": "<PERSON><PERSON><PERSON>"}, "agent_ds1688__desc": {"message": "Aangeboden door 1688. Het ondersteunt directe aankopen vanuit het buitenland, betaling in USD en levering aan uw transitmagazijn in China."}, "agent_ds1688__freight": {"message": "Verzendkostencalculator"}, "agent_ds1688__help": {"message": "<PERSON><PERSON><PERSON>"}, "agent_ds1688__packages": {"message": "Vrachtbrief"}, "agent_ds1688__profile": {"message": "Persoonlijk centrum"}, "agent_ds1688__warehouse": {"message": "<PERSON><PERSON>"}, "ai_comment_analysis_advantage": {"message": "Voordelen"}, "ai_comment_analysis_ai": {"message": "AI-beoordelingsanalyse"}, "ai_comment_analysis_available": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "ai_comment_analysis_balance": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON> munten, waardeer op"}, "ai_comment_analysis_behavior": {"message": "Gedrag"}, "ai_comment_analysis_characteristic": {"message": "Klantkenmerken"}, "ai_comment_analysis_comment": {"message": "Het product heeft niet genoeg beoordelingen om nauwkeurige conclusies te trekken, selecteer een product met meer beoordelingen."}, "ai_comment_analysis_consume": {"message": "Geschatte consumptie"}, "ai_comment_analysis_default": {"message": "Standaard beoordelingen"}, "ai_comment_analysis_desire": {"message": "Verwachtingen van klanten"}, "ai_comment_analysis_disadvantage": {"message": "<PERSON><PERSON><PERSON>"}, "ai_comment_analysis_free": {"message": "<PERSON><PERSON><PERSON> pog<PERSON>"}, "ai_comment_analysis_freeNum": {"message": "1 gratis tegoed wordt gebruikt"}, "ai_comment_analysis_go_recharge": {"message": "<PERSON>a naar op<PERSON>n"}, "ai_comment_analysis_intelligence": {"message": "<PERSON><PERSON><PERSON> be<PERSON>del<PERSON>analyse"}, "ai_comment_analysis_location": {"message": "Locatie"}, "ai_comment_analysis_motive": {"message": "Aankoopmotivatie"}, "ai_comment_analysis_network_error": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>, probeer het opnieuw"}, "ai_comment_analysis_normal": {"message": "Foto beoordelingen"}, "ai_comment_analysis_number_reviews": {"message": "Aantal beoordelingen: $num$, Geschatte consumptie: $consume$", "placeholders": {"num": {"content": "$1"}, "consume": {"content": "$2"}}}, "ai_comment_analysis_ordinary": {"message": "Algemene opmerkingen"}, "ai_comment_analysis_percentage": {"message": "Percentage"}, "ai_comment_analysis_problem": {"message": "<PERSON><PERSON> met betaling"}, "ai_comment_analysis_reanalysis": {"message": "Opnieuw analyseren"}, "ai_comment_analysis_reason": {"message": "Reden"}, "ai_comment_analysis_recharge": {"message": "Opwaarderen"}, "ai_comment_analysis_recharged": {"message": "<PERSON>k heb op<PERSON>d"}, "ai_comment_analysis_retry": {"message": "Opnieuw proberen"}, "ai_comment_analysis_scene": {"message": "Gebruiksscenario"}, "ai_comment_analysis_start": {"message": "<PERSON><PERSON> met analyseren"}, "ai_comment_analysis_subject": {"message": "Onderwerpen"}, "ai_comment_analysis_time": {"message": "<PERSON><PERSON><PERSON><PERSON> van <PERSON>"}, "ai_comment_analysis_tool": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "ai_comment_analysis_user_portrait": {"message": "Gebruikersprofiel"}, "ai_comment_analysis_welcome": {"message": "Welkom bij AI-beoordelingsanalyse"}, "ai_comment_analysis_year": {"message": "Opmerkingen van het afgelopen jaar"}, "ai_listing_Exclude_keywords": {"message": "Exclusief trefwoorden"}, "ai_listing_Login_the_feature": {"message": "Voor de functie is inloggen vereist"}, "ai_listing_aI_generation": {"message": "AI-generatie"}, "ai_listing_add_automatic": {"message": "Automatisch"}, "ai_listing_add_dictionary_new": {"message": "Maak een nieuwe bibliotheek"}, "ai_listing_add_enter_keywords": {"message": "<PERSON><PERSON><PERSON> trefwo<PERSON> in"}, "ai_listing_add_inputkey_selling": {"message": "Voer een verkoopargument in en druk op $key$ om het toevoegen te voltooien", "placeholders": {"key": {"content": "$1"}}}, "ai_listing_add_inputkey_warning": {"message": "<PERSON><PERSON> overschreden, maximaal $amount$ verkooppunten", "placeholders": {"amount": {"content": "$1"}}}, "ai_listing_add_keywords": {"message": "<PERSON><PERSON><PERSON> trefwoorden toe"}, "ai_listing_add_manually": {"message": "Handmatig toevoegen"}, "ai_listing_add_selling": {"message": "Voeg verkoopargumenten toe"}, "ai_listing_added_keywords": {"message": "Trefwoorden toegevoegd"}, "ai_listing_added_successfully": {"message": "Succesvol toegevoegd"}, "ai_listing_addexcluded_keywords": {"message": "<PERSON><PERSON><PERSON> de uitgesloten trefwoorden in en druk op Enter om het toevoegen te voltooien."}, "ai_listing_adding_selling": {"message": "Verkoopargumenten toegevoegd"}, "ai_listing_addkeyword_enter": {"message": "Typ de belangrijkste attribuutwoorden in en druk op Enter om het toevoegen te voltooien"}, "ai_listing_ai_description": {"message": "AI-beschrijvingswoordenbibliotheek"}, "ai_listing_ai_dictionary": {"message": "AI-titelwoordenbibliotheek"}, "ai_listing_ai_title": {"message": "AI-titel"}, "ai_listing_aidescription_repeated": {"message": "AI-beschrijvingswoordbibliotheeknaam kan niet worden herhaald"}, "ai_listing_aititle_repeated": {"message": "AI-titelwoordbibliotheeknaam kan niet worden herhaald"}, "ai_listing_data_comes_from": {"message": "De<PERSON> gegevens zijn afkomstig van:"}, "ai_listing_deleted_successfully": {"message": "Met succes verwijderd"}, "ai_listing_dictionary_name": {"message": "Naam bibliotheek"}, "ai_listing_edit_dictionary": {"message": "Bibliotheek aanpassen..."}, "ai_listing_edit_word_library": {"message": "Bewerk de woordenbibliotheek"}, "ai_listing_enter_keywords": {"message": "<PERSON><PERSON><PERSON> trefwoorden in en druk op $key$ om het toevoegen te voltooien", "placeholders": {"key": {"content": "$1"}}}, "ai_listing_exceeded_maximum": {"message": "De limiet is overschreden, maximaal $amount$ zoekwoorden", "placeholders": {"amount": {"content": "$1"}}}, "ai_listing_excluded_word_library": {"message": "Uitgesloten woordenbibliotheek"}, "ai_listing_generate_characters": {"message": "Karakters genereren"}, "ai_listing_generation_platform": {"message": "Generatieplatform"}, "ai_listing_help_optimize": {"message": "Help me de producttitel te optimaliseren, de originele titel is"}, "ai_listing_include_selling": {"message": "Andere verkoopargumenten waren onder meer:"}, "ai_listing_included_keyword": {"message": "Inclusief trefwoorden"}, "ai_listing_included_keywords": {"message": "Inclusief trefwoorden"}, "ai_listing_input_selling": {"message": "Voer een verkoopargument in"}, "ai_listing_input_selling_fit": {"message": "<PERSON><PERSON>r verkoopargumenten in die bij de titel passen"}, "ai_listing_input_selling_please": {"message": "<PERSON><PERSON>r verkooppunten in"}, "ai_listing_intelligently_title": {"message": "<PERSON><PERSON><PERSON> hier<PERSON><PERSON> de vereiste inhoud in om de titel op intelligente wijze te genereren"}, "ai_listing_keyword_product_title": {"message": "Zoekwoord producttitel"}, "ai_listing_keywords_repeated": {"message": "Trefwoorden kunnen niet worden herhaald"}, "ai_listing_listed_selling_points": {"message": "Inclusief verkoopargumenten"}, "ai_listing_long_title_1": {"message": "Bevat basisinformatie zoals merknaam, producttype, productkenmerken, enz."}, "ai_listing_long_title_2": {"message": "Op basis van de standaard producttitel worden SEO-bevorderende trefwoorden toegevoegd."}, "ai_listing_long_title_3": {"message": "Naast de merk<PERSON>, het producttype, de productkenmerken en trefwoorden, worden er ook long-tail trefwoorden opgenomen om een ​​hogere ranking te bereiken in specifieke, gesegmenteerde zoekopdrachten."}, "ai_listing_longtail_keyword_product_title": {"message": "Producttitel met lang zoekwoord"}, "ai_listing_manually_enter": {"message": "Handmatig invoeren..."}, "ai_listing_network_not_working": {"message": "Internet is niet be<PERSON>, VPN is vereist voor toegang tot ChatGPT"}, "ai_listing_new_dictionary": {"message": "Maak een nieuwe woordenbibliotheek..."}, "ai_listing_new_generate": {"message": "<PERSON><PERSON>"}, "ai_listing_optional_words": {"message": "Optionele woorden"}, "ai_listing_original_title": {"message": "<PERSON><PERSON> titel"}, "ai_listing_other_keywords_included": {"message": "Andere trefwoorden waren:"}, "ai_listing_please_again": {"message": "<PERSON><PERSON><PERSON> het opnieuw"}, "ai_listing_please_select": {"message": "De volgende titels zijn voor u gegenereerd, selecteer:"}, "ai_listing_product_category": {"message": "Product categorie"}, "ai_listing_product_category_is": {"message": "De productcategorie is"}, "ai_listing_product_category_to": {"message": "Tot welke categorie behoort het product?"}, "ai_listing_random_keywords": {"message": "Willekeurige $amount$ zoekwoorden", "placeholders": {"amount": {"content": "$1"}}}, "ai_listing_random_selling": {"message": "Willekeurige $amount$ verkoopargumenten", "placeholders": {"amount": {"content": "$1"}}}, "ai_listing_randomize_keywords": {"message": "Willekeurig uit de woordbibliotheek"}, "ai_listing_search_selling": {"message": "Zoek op verkooppunt"}, "ai_listing_select_product_categories": {"message": "Selecteer automatisch productcategorieën."}, "ai_listing_select_product_selling_points": {"message": "Selecteer automatisch productverkooppunten"}, "ai_listing_select_word_library": {"message": "Selecteer woordenbibliotheek"}, "ai_listing_selling": {"message": "Verkooppunten"}, "ai_listing_selling_ask": {"message": "Welke andere verkoopargumentvereisten zijn er voor de titel?"}, "ai_listing_selling_optional": {"message": "Optionele verkooppunten"}, "ai_listing_selling_repeat": {"message": "Punten kunnen niet worden gedupliceerd"}, "ai_listing_set_excluded": {"message": "Instellen als uitgesloten woordenbibliotheek"}, "ai_listing_set_include_selling_points": {"message": "Inclusief verkoopargumenten"}, "ai_listing_set_included": {"message": "Instellen als opgenomen woordbibliotheek"}, "ai_listing_set_selling_dictionary": {"message": "Instellen als verkoopargumentbibliotheek"}, "ai_listing_standard_product_title": {"message": "Standaard producttitel"}, "ai_listing_translated_title": {"message": "Vertaalde titel"}, "ai_listing_visit_chatGPT": {"message": "Bezoek ChatGPT"}, "ai_listing_what_other_keywords": {"message": "Welke andere trefwoorden zijn vereist voor de titel?"}, "aliprice_coupons_apply_again": {"message": "Opnieuw aanvragen"}, "aliprice_coupons_apply_coupons": {"message": "Coupons toepassen"}, "aliprice_coupons_apply_success": {"message": "Gevonden coupon: Bespaar $amount$", "placeholders": {"amount": {"content": "$1"}}}, "aliprice_coupons_applying": {"message": "Codes testen voor de beste deals..."}, "aliprice_coupons_applying_desc": {"message": "Uitchecken: $code$", "placeholders": {"code": {"content": "$1"}}}, "aliprice_coupons_back_to_checkout": {"message": "Ga door naar afrekenen"}, "aliprice_coupons_found_coupons": {"message": "We hebben waardebonnen van $amount$ gevonden", "placeholders": {"amount": {"content": "$1"}}}, "aliprice_coupons_found_coupons_desc": {"message": "Klaar om af te rekenen? Laten we ervoor zorgen dat u de beste prijs krijgt!"}, "aliprice_coupons_no_coupon_aviable": {"message": "Die codes werkten niet. Geen biggie - u krijgt al de beste prijs."}, "aliprice_coupons_toolbar_btn": {"message": "Waardebonnen ontvangen"}, "aliww_translate": {"message": "Aliwangwang Chatvertaler"}, "aliww_translate_supports": {"message": "Ondersteuning: 1688 en Taobao"}, "amazon_extended_keywords_Keywords": {"message": "Zoekwoorden"}, "amazon_extended_keywords_copy_all": {"message": "<PERSON>es <PERSON>"}, "amazon_extended_keywords_more": {"message": "<PERSON><PERSON>"}, "an_lei_ji_xiao_liang_pai_xu": {"message": "Sorteer op cumulatieve omzet"}, "an_lei_xing_cha_kan": {"message": "Type"}, "an_yue_dai_xiao_pai_xu": {"message": "Rangschikking op basis van dropshipping-verkopen"}, "apra_btn__cat_name": {"message": "Beoordelingen analyse"}, "apra_chart__name": {"message": "Percentage productverkopen per land"}, "apra_chart__update_at": {"message": "Updatetijd $time$", "placeholders": {"time": {"content": "$1"}}}, "apra_name": {"message": "Verkoopstatistieken van landen"}, "auto_opening": {"message": "Wordt automatisch geopend in $num$ seconden", "placeholders": {"num": {"content": "$1"}}}, "auto_page_next_x_pages": {"message": "Volgende $autoPaging$ pages", "placeholders": {"autoPaging": {"content": "$1"}}}, "average_days_listed": {"message": "Gemiddelde dagen op de plank"}, "average_hui_fu_lv": {"message": "Gemiddeld antwoordpercentage"}, "average_ping_gong_ying_shang_deng_ji": {"message": "Gemiddeld leveranciersniveau"}, "average_price": {"message": "gemiddelde prijs"}, "average_qi_ding_liang": {"message": "Gemiddelde MOQ"}, "average_rating": {"message": "gemiddelde score"}, "average_ren_zheng_gong_ying_nian_chang": {"message": "G<PERSON><PERSON>deld aantal jaren certificering"}, "average_revenue": {"message": "gemiddelde omzet"}, "average_revenue_per_product": {"message": "Totale omzet ÷ aantal producten"}, "average_sales": {"message": "gemiddelde omzet"}, "average_sales_per_product": {"message": "Totale omzet ÷ aantal producten"}, "bao_han": {"message": "<PERSON><PERSON>"}, "bao_zheng_jin": {"message": "Marge"}, "bian_ti_shu": {"message": "Variaties"}, "biao_ti": {"message": "Titel"}, "blacklist_add_blacklist": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON> deze winkel"}, "blacklist_address_incorrect": {"message": "<PERSON><PERSON> <PERSON><PERSON> is onjuist. Controleer het alstublieft."}, "blacklist_blacked_out": {"message": "<PERSON><PERSON> is geblokkeerd"}, "blacklist_blacklist": {"message": "Zwarte lijst"}, "blacklist_no_records_yet": {"message": "Nog geen record!"}, "blue_rocket": {"message": "로켓배송"}, "brand_name": {"message": "Merk"}, "btn_aliprice_agent__daigou": {"message": "<PERSON><PERSON><PERSON><PERSON> tussen<PERSON>oon"}, "btn_aliprice_agent__dropshipping": {"message": "Dropshipping"}, "btn_have_a_try": {"message": "<PERSON><PERSON><PERSON> het eens"}, "btn_refresh": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "btn_try_it_now": {"message": "<PERSON><PERSON>r het nu"}, "btn_txt_view_on_aliprice": {"message": "Bekijk op AliPrice"}, "bu_bao_han": {"message": "<PERSON><PERSON> geen"}, "bulk_copy_links": {"message": "Bulkkopielinks"}, "bulk_copy_products": {"message": "Bulkkopieproducten"}, "cai_gou_zi_xun": {"message": "Klantenservice"}, "cai_gou_zi_xun__desc": {"message": "Het responspercent<PERSON> van drie minuten van de ve<PERSON>oper"}, "can_ping_lei_xing": {"message": "Type"}, "cao_zuo": {"message": "<PERSON><PERSON>"}, "chan_pin_ID": {"message": "Product-ID"}, "chan_pin_e_wai_xin_xi": {"message": "Product extra info"}, "chan_pin_lian_jie": {"message": "Productlink"}, "cheng_li_shi_jian": {"message": "Oprichtingstijd"}, "city__aba": {"message": "Aba"}, "city__aksu": {"message": "<PERSON><PERSON><PERSON>"}, "city__alaer": {"message": "<PERSON><PERSON><PERSON>"}, "city__altai": {"message": "Altai"}, "city__alxaleague": {"message": "Alxa League"}, "city__ankang": {"message": "Ankan<PERSON>"}, "city__anqing": {"message": "Anqing"}, "city__anshan": {"message": "<PERSON><PERSON>"}, "city__anshun": {"message": "<PERSON><PERSON><PERSON>"}, "city__anyang": {"message": "Anyang"}, "city__baicheng": {"message": "Baicheng"}, "city__baise": {"message": "Baise"}, "city__baisha": {"message": "Baisha"}, "city__baishan": {"message": "Baishan"}, "city__baiyin": {"message": "<PERSON><PERSON><PERSON>"}, "city__baoding": {"message": "<PERSON>od<PERSON>"}, "city__baoji": {"message": "<PERSON><PERSON><PERSON>"}, "city__baoshan": {"message": "<PERSON><PERSON><PERSON>"}, "city__baoting": {"message": "Baoting"}, "city__baotou": {"message": "<PERSON><PERSON><PERSON>"}, "city__bayannur": {"message": "Bayannur"}, "city__bayinguoleng": {"message": "Bayinguoleng"}, "city__bazhong": {"message": "Bazhong"}, "city__beihai": {"message": "Beihai"}, "city__bengbu": {"message": "<PERSON><PERSON><PERSON>"}, "city__benxi": {"message": "Benxi"}, "city__bijie": {"message": "Bijie"}, "city__binzhou": {"message": "Binzhou"}, "city__bortala": {"message": "<PERSON><PERSON><PERSON>"}, "city__bozhou": {"message": "Bozhou"}, "city__cangzhou": {"message": "Cangzhou"}, "city__chamdo": {"message": "<PERSON><PERSON><PERSON>"}, "city__changchun": {"message": "<PERSON><PERSON><PERSON>"}, "city__changde": {"message": "<PERSON><PERSON>"}, "city__changhua": {"message": "Changhua"}, "city__changji": {"message": "Changji"}, "city__changjiang": {"message": "Changjiang"}, "city__changsha": {"message": "Changsha"}, "city__changzhi": {"message": "Changzhi"}, "city__changzhou": {"message": "Changzhou"}, "city__chaohu": {"message": "<PERSON><PERSON>"}, "city__chaoyang": {"message": "Chaoyang"}, "city__chaozhou": {"message": "Chaozhou"}, "city__chengde": {"message": "Chengde"}, "city__chengdu": {"message": "Chengdu"}, "city__chengmai": {"message": "<PERSON><PERSON><PERSON>"}, "city__chenzhou": {"message": "Chenzhou"}, "city__chiayi": {"message": "<PERSON><PERSON><PERSON>"}, "city__chifeng": {"message": "<PERSON><PERSON>"}, "city__chizhou": {"message": "Chizhou"}, "city__chongzuo": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__chuxiong": {"message": "Chuxiong"}, "city__chuzhou": {"message": "Chuzhou"}, "city__dali": {"message": "<PERSON><PERSON>"}, "city__dalian": {"message": "<PERSON><PERSON>"}, "city__dandong": {"message": "Dandong"}, "city__danzhou": {"message": "Danzhou"}, "city__daqing": {"message": "Daqing"}, "city__datong": {"message": "<PERSON><PERSON><PERSON>"}, "city__daxinganling": {"message": "<PERSON><PERSON>'<PERSON>ling"}, "city__dazhou": {"message": "Dazhou"}, "city__dehong": {"message": "<PERSON><PERSON>"}, "city__deyang": {"message": "Deyang"}, "city__dezhou": {"message": "Dezhou"}, "city__dingan": {"message": "Ding'an"}, "city__dingxi": {"message": "Dingxi"}, "city__diqing": {"message": "Diqing"}, "city__dongfang": {"message": "<PERSON><PERSON>g"}, "city__dongguan": {"message": "Dongguan"}, "city__dongying": {"message": "<PERSON><PERSON>"}, "city__enshi": {"message": "<PERSON><PERSON>"}, "city__ezhou": {"message": "Ezhou"}, "city__fangchenggang": {"message": "Fangchenggang"}, "city__foshan": {"message": "<PERSON><PERSON><PERSON>"}, "city__fushun": {"message": "<PERSON><PERSON><PERSON>"}, "city__fuxin": {"message": "Fuxin"}, "city__fuyang": {"message": "Fuyang"}, "city__fuzhou": {"message": "Fuzhou"}, "city__gannan": {"message": "<PERSON><PERSON><PERSON>"}, "city__ganzhou": {"message": "Ganzhou"}, "city__ganzi": {"message": "Ganzi"}, "city__guangan": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__guangyuan": {"message": "Guangyuan"}, "city__guangzhou": {"message": "Guangzhou"}, "city__guigang": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__guilin": {"message": "<PERSON><PERSON><PERSON>"}, "city__guiyang": {"message": "Guiyang"}, "city__guolok": {"message": "Guolok"}, "city__guyuan": {"message": "<PERSON><PERSON>"}, "city__haibei": {"message": "Haibei"}, "city__haidong": {"message": "Haidong"}, "city__haikou": {"message": "Hai<PERSON><PERSON>"}, "city__hainan": {"message": "Hainan"}, "city__haixi": {"message": "Haixi"}, "city__hami": {"message": "<PERSON><PERSON>"}, "city__handan": {"message": "<PERSON><PERSON>"}, "city__hangzhou": {"message": "Hangzhou"}, "city__hanzhong": {"message": "Hanzhong"}, "city__harbin": {"message": "<PERSON><PERSON><PERSON>"}, "city__hebi": {"message": "<PERSON><PERSON>"}, "city__hechi": {"message": "<PERSON><PERSON>"}, "city__hefei": {"message": "<PERSON><PERSON><PERSON>"}, "city__hegang": {"message": "<PERSON><PERSON><PERSON>"}, "city__heihe": {"message": "<PERSON><PERSON><PERSON>"}, "city__hengshui": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__hengyang": {"message": "Hengyang"}, "city__heyuan": {"message": "<PERSON><PERSON>"}, "city__heze": {"message": "<PERSON><PERSON>"}, "city__hezhou": {"message": "Hezhou"}, "city__hohhot": {"message": "Hohhot"}, "city__honghe": {"message": "<PERSON><PERSON>"}, "city__hongkongisland": {"message": "Hong Kong Island"}, "city__hotan": {"message": "<PERSON><PERSON>"}, "city__hsinchu": {"message": "<PERSON><PERSON><PERSON>"}, "city__huaian": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__huaibei": {"message": "<PERSON><PERSON><PERSON>"}, "city__huaihua": {"message": "Huaihua"}, "city__huaisouth": {"message": "Huai South"}, "city__hualien": {"message": "<PERSON><PERSON><PERSON>"}, "city__huanggang": {"message": "<PERSON><PERSON><PERSON>"}, "city__huangnan": {"message": "Huangnan"}, "city__huangshan": {"message": "<PERSON><PERSON>"}, "city__huangshi": {"message": "<PERSON><PERSON>"}, "city__huizhou": {"message": "Huizhou"}, "city__huludao": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__hulunbuir": {"message": "Hulunbuir"}, "city__huzhou": {"message": "Huzhou"}, "city__jiamusi": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__jian": {"message": "<PERSON><PERSON><PERSON>"}, "city__jiangmen": {"message": "Jiangmen"}, "city__jiaozuo": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__jiaxing": {"message": "Jiaxing"}, "city__jiayuguan": {"message": "Jiayuguan"}, "city__jieyang": {"message": "<PERSON><PERSON><PERSON>"}, "city__jilin": {"message": "<PERSON><PERSON>"}, "city__jinan": {"message": "<PERSON><PERSON>"}, "city__jinchang": {"message": "Jinchang"}, "city__jincheng": {"message": "Jincheng"}, "city__jingdezhen": {"message": "Jingdez<PERSON>"}, "city__jingmen": {"message": "Jingmen"}, "city__jingzhou": {"message": "Jingzhou"}, "city__jinhua": {"message": "Jinhua"}, "city__jining": {"message": "Jining"}, "city__jinzhong": {"message": "Jinzhong"}, "city__jinzhou": {"message": "Jinzhou"}, "city__jiujiang": {"message": "Jiujiang"}, "city__jiuquan": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__jixi": {"message": "Ji<PERSON>"}, "city__kaifeng": {"message": "Kaifeng"}, "city__kaohsiung": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__karamay": {"message": "<PERSON><PERSON><PERSON>"}, "city__kashgar": {"message": "Ka<PERSON><PERSON>"}, "city__keelung": {"message": "Keelung"}, "city__kinmen": {"message": "Kinmen"}, "city__kizilsu": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__kowloon": {"message": "Kowloon"}, "city__kunming": {"message": "Ku<PERSON><PERSON>"}, "city__laibin": {"message": "<PERSON><PERSON>"}, "city__laiwu": {"message": "<PERSON><PERSON>"}, "city__langfang": {"message": "<PERSON><PERSON><PERSON>"}, "city__lanzhou": {"message": "Lanzhou"}, "city__ledong": {"message": "<PERSON><PERSON>"}, "city__leshan": {"message": "<PERSON><PERSON>"}, "city__lhasa": {"message": "Lhasa"}, "city__liangshan": {"message": "Liangshan"}, "city__lianyungang": {"message": "Lianyungang"}, "city__liaocheng": {"message": "<PERSON><PERSON><PERSON>"}, "city__liaoyang": {"message": "<PERSON><PERSON><PERSON>"}, "city__liaoyuan": {"message": "<PERSON><PERSON><PERSON>"}, "city__lienchiang": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__lijiang": {"message": "Lijiang"}, "city__lincang": {"message": "Lincang"}, "city__linfen": {"message": "Linfen"}, "city__lingao": {"message": "Lingao"}, "city__lingshui": {"message": "<PERSON><PERSON><PERSON>"}, "city__linxia": {"message": "Lin<PERSON>"}, "city__linyi": {"message": "<PERSON><PERSON>"}, "city__lishui": {"message": "Li<PERSON><PERSON>"}, "city__liupanshui": {"message": "Liupanshu<PERSON>"}, "city__liuzhou": {"message": "Liuzhou"}, "city__longnan": {"message": "<PERSON><PERSON>"}, "city__longyan": {"message": "<PERSON><PERSON>"}, "city__loudi": {"message": "<PERSON><PERSON>"}, "city__luan": {"message": "<PERSON><PERSON><PERSON>"}, "city__luohe": {"message": "<PERSON><PERSON><PERSON>"}, "city__luoyang": {"message": "<PERSON><PERSON><PERSON>"}, "city__luzhou": {"message": "Luzhou"}, "city__lüliang": {"message": "<PERSON>眉liang"}, "city__maanshan": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__macauoutlyingislands": {"message": "Macau Outlying Islands"}, "city__macaupeninsula": {"message": "Macau Peninsula"}, "city__maoming": {"message": "<PERSON><PERSON>"}, "city__meishan": {"message": "<PERSON><PERSON>"}, "city__meizhou": {"message": "Meizhou"}, "city__mianyang": {"message": "Mianyang"}, "city__miaoli": {"message": "<PERSON><PERSON>"}, "city__mudanjiang": {"message": "Mudanjiang"}, "city__nagqu": {"message": "<PERSON>g<PERSON>u"}, "city__nanchang": {"message": "Nanchang"}, "city__nanchong": {"message": "<PERSON><PERSON><PERSON>"}, "city__nanjing": {"message": "Nanjing"}, "city__nanning": {"message": "Nanning"}, "city__nanping": {"message": "<PERSON><PERSON>"}, "city__nantong": {"message": "Nantong"}, "city__nantou": {"message": "<PERSON><PERSON><PERSON>"}, "city__nanyang": {"message": "Nanyang"}, "city__neijiang": {"message": "Neijiang"}, "city__newterritories": {"message": "New Territories"}, "city__ngari": {"message": "<PERSON><PERSON>"}, "city__ningbo": {"message": "Ningbo"}, "city__ningde": {"message": "<PERSON><PERSON><PERSON>"}, "city__nujiang": {"message": "Nujiang"}, "city__nyingchi": {"message": "Nyingchi"}, "city__ordos": {"message": "Ordos"}, "city__panjin": {"message": "Panjin"}, "city__panzhihua": {"message": "Pan Zhihua"}, "city__penghu": {"message": "<PERSON><PERSON><PERSON>"}, "city__pingdingshan": {"message": "Pingdingshan"}, "city__pingliang": {"message": "<PERSON><PERSON><PERSON>"}, "city__pingtung": {"message": "<PERSON><PERSON><PERSON>"}, "city__pingxiang": {"message": "<PERSON><PERSON><PERSON>"}, "city__puer": {"message": "<PERSON>u'er"}, "city__putian": {"message": "<PERSON><PERSON>"}, "city__puyang": {"message": "P<PERSON><PERSON>"}, "city__qiandongnan": {"message": "Qiandongnan"}, "city__qianjiang": {"message": "Qianjiang"}, "city__qiannan": {"message": "<PERSON><PERSON><PERSON>"}, "city__qianxinan": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__qingdao": {"message": "Qingdao"}, "city__qingyang": {"message": "Qingyang"}, "city__qingyuan": {"message": "Qingyuan"}, "city__qinhuangdao": {"message": "Qinhuangdao"}, "city__qinzhou": {"message": "Qinzhou"}, "city__qionghai": {"message": "Qionghai"}, "city__qiongzhong": {"message": "Qiongzhong"}, "city__qiqihar": {"message": "Qiqihar"}, "city__qitaihe": {"message": "<PERSON><PERSON><PERSON>"}, "city__quanzhou": {"message": "Quanzhou"}, "city__qujing": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__quzhou": {"message": "Quzhou"}, "city__rizhao": {"message": "<PERSON><PERSON><PERSON>"}, "city__sanmenxia": {"message": "Sanmenxia"}, "city__sanming": {"message": "Sanming"}, "city__sanya": {"message": "Sanya"}, "city__shangluo": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__shangqiu": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__shangrao": {"message": "<PERSON><PERSON><PERSON>"}, "city__shannan": {"message": "Shannan"}, "city__shantou": {"message": "<PERSON><PERSON><PERSON>"}, "city__shanwei": {"message": "Shanwei"}, "city__shaoguan": {"message": "Shaoguan"}, "city__shaoxing": {"message": "Shaoxing"}, "city__shaoyang": {"message": "Shaoyang"}, "city__shennongjiaforestarea": {"message": "Shennongjia Forest Area"}, "city__shenyang": {"message": "Shenyang"}, "city__shenzhen": {"message": "Shenzhen"}, "city__shigatse": {"message": "Shigat<PERSON>"}, "city__shihezi": {"message": "<PERSON><PERSON><PERSON>"}, "city__shijiazhuang": {"message": "Shijiazhuang"}, "city__shiyan": {"message": "<PERSON><PERSON>"}, "city__shizuishan": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__shuangyashan": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__shuozhou": {"message": "Shuozhou"}, "city__siping": {"message": "<PERSON><PERSON>"}, "city__songyuan": {"message": "Songyuan"}, "city__suihua": {"message": "Su<PERSON>ua"}, "city__suining": {"message": "Suining"}, "city__suizhou": {"message": "<PERSON><PERSON><PERSON>"}, "city__suqian": {"message": "<PERSON><PERSON><PERSON>"}, "city__suzhou": {"message": "Suzhou"}, "city__tacheng": {"message": "Tacheng"}, "city__taian": {"message": "Tai'an"}, "city__taichung": {"message": "Taichung"}, "city__tainan": {"message": "Tainan"}, "city__taipei": {"message": "Taipei"}, "city__taitung": {"message": "<PERSON><PERSON><PERSON>"}, "city__taiyuan": {"message": "Taiyuan"}, "city__taizhou": {"message": "Taizhou"}, "city__tangshan": {"message": "Tangshan"}, "city__taoyuan": {"message": "Taoyuan"}, "city__tianmen": {"message": "Tianmen"}, "city__tianshui": {"message": "<PERSON><PERSON><PERSON>"}, "city__tieling": {"message": "Tieling"}, "city__tongchuan": {"message": "<PERSON><PERSON><PERSON>"}, "city__tonghua": {"message": "Tonghua"}, "city__tongliao": {"message": "<PERSON><PERSON><PERSON>"}, "city__tongling": {"message": "Tongling"}, "city__tongren": {"message": "<PERSON><PERSON>"}, "city__tumushuke": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__tunchang": {"message": "Tunchang"}, "city__turpan": {"message": "<PERSON><PERSON><PERSON>"}, "city__ulanqab": {"message": "Ulanqab"}, "city__urumqi": {"message": "Urumqi"}, "city__wanning": {"message": "Wanning"}, "city__weifang": {"message": "<PERSON><PERSON><PERSON>"}, "city__weihai": {"message": "Weihai"}, "city__weinan": {"message": "Weinan"}, "city__wenchang": {"message": "Wenchang"}, "city__wenshan": {"message": "<PERSON><PERSON>"}, "city__wenzhou": {"message": "Wenzhou"}, "city__wuhai": {"message": "Wu<PERSON>"}, "city__wuhan": {"message": "<PERSON><PERSON>"}, "city__wuhu": {"message": "<PERSON><PERSON>"}, "city__wujiaqu": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__wuwei": {"message": "<PERSON><PERSON>"}, "city__wuxi": {"message": "Wuxi"}, "city__wuzhishan": {"message": "Wuzhishan"}, "city__wuzhong": {"message": "Wuzhong"}, "city__wuzhou": {"message": "Wuzhou"}, "city__xiamen": {"message": "Xiamen"}, "city__xian": {"message": "Xi'<PERSON>"}, "city__xiangfan": {"message": "<PERSON><PERSON><PERSON>"}, "city__xiangtan": {"message": "Xiangtan"}, "city__xiangxi": {"message": "Xiangxi"}, "city__xianning": {"message": "Xianning"}, "city__xiantao": {"message": "Xiantao"}, "city__xianyang": {"message": "<PERSON><PERSON><PERSON>"}, "city__xiaogan": {"message": "<PERSON><PERSON>"}, "city__xilingol": {"message": "Xilingol"}, "city__xinganleague": {"message": "Xing'an League"}, "city__xingtai": {"message": "Xingtai"}, "city__xining": {"message": "Xining"}, "city__xinxiang": {"message": "Xinxiang"}, "city__xinyang": {"message": "Xinyang"}, "city__xinyu": {"message": "Xinyu"}, "city__xinzhou": {"message": "Xinzhou"}, "city__xishuangbanna": {"message": "Xishuangbanna"}, "city__xuancheng": {"message": "Xuancheng"}, "city__xuchang": {"message": "Xuchang"}, "city__xuzhou": {"message": "Xuzhou"}, "city__yaan": {"message": "Ya'an"}, "city__yanan": {"message": "Yan'<PERSON>"}, "city__yanbian": {"message": "Yanbian"}, "city__yancheng": {"message": "Yancheng"}, "city__yangjiang": {"message": "Yangjiang"}, "city__yangquan": {"message": "<PERSON><PERSON><PERSON>"}, "city__yangzhou": {"message": "Yangzhou"}, "city__yantai": {"message": "Yantai"}, "city__yibin": {"message": "<PERSON><PERSON>"}, "city__yichang": {"message": "Yichang"}, "city__yichun": {"message": "<PERSON><PERSON><PERSON>"}, "city__yilan": {"message": "Yilan"}, "city__yili": {"message": "<PERSON><PERSON>"}, "city__yinchuan": {"message": "<PERSON><PERSON><PERSON>"}, "city__yingkou": {"message": "<PERSON><PERSON><PERSON>"}, "city__yingtan": {"message": "Yingtan"}, "city__yiwu": {"message": "<PERSON><PERSON>"}, "city__yiyang": {"message": "Yiyang"}, "city__yongzhou": {"message": "Yongzhou"}, "city__yueyang": {"message": "<PERSON><PERSON><PERSON>"}, "city__yulin": {"message": "Yulin"}, "city__yuncheng": {"message": "Yuncheng"}, "city__yunfu": {"message": "<PERSON><PERSON>"}, "city__yunlin": {"message": "<PERSON><PERSON>"}, "city__yushu": {"message": "Yushu"}, "city__yuxi": {"message": "Yuxi"}, "city__zaozhuang": {"message": "Zaozhuang"}, "city__zhangjiajie": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__zhangjiakou": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__zhangye": {"message": "<PERSON><PERSON>"}, "city__zhangzhou": {"message": "Zhangzhou"}, "city__zhanjiang": {"message": "Zhanjiang"}, "city__zhaoqing": {"message": "Zhaoqing"}, "city__zhaotong": {"message": "Zhaotong"}, "city__zhengzhou": {"message": "Zhengzhou"}, "city__zhenjiang": {"message": "Zhenjiang"}, "city__zhongshan": {"message": "Zhongshan"}, "city__zhongwei": {"message": "Zhongwei"}, "city__zhoukou": {"message": "<PERSON><PERSON><PERSON>"}, "city__zhoushan": {"message": "Zhoushan"}, "city__zhuhai": {"message": "Zhu<PERSON>"}, "city__zhumadian": {"message": "Zhumadian"}, "city__zhuzhou": {"message": "Zhuzhou"}, "city__zibo": {"message": "Zibo"}, "city__zigong": {"message": "Zigong"}, "city__ziyang": {"message": "<PERSON><PERSON><PERSON>"}, "city__zunyi": {"message": "<PERSON><PERSON><PERSON>"}, "click_copy_product_info": {"message": "Klik op Productinformatie kopiëren"}, "commmon_txt_expired": {"message": "<PERSON><PERSON> meer geldig"}, "common__date_range_12m": {"message": "1 jaar"}, "common__date_range_1m": {"message": "1 maand"}, "common__date_range_1w": {"message": "1 week"}, "common__date_range_2w": {"message": "2 weken"}, "common__date_range_3m": {"message": "3 maanden"}, "common__date_range_3w": {"message": "3 weken"}, "common__date_range_6m": {"message": "6 maanden"}, "common_btn_cancel": {"message": "annuleren"}, "common_btn_close": {"message": "Dichtbij"}, "common_btn_save": {"message": "Opsla<PERSON>"}, "common_btn_setting": {"message": "Opstelling"}, "common_email": {"message": "E-mail"}, "common_error_msg_no_data": {"message": "<PERSON><PERSON>"}, "common_error_msg_no_result": {"message": "Sorry, geen resultaat gevonden."}, "common_favorites": {"message": "Favorieten"}, "common_feedback": {"message": "<PERSON><PERSON><PERSON>"}, "common_help": {"message": "<PERSON><PERSON>"}, "common_loading": {"message": "Bezig met laden"}, "common_login": {"message": "Log in"}, "common_logout": {"message": "Uitloggen"}, "common_no": {"message": "<PERSON><PERSON>"}, "common_powered_by_aliprice": {"message": "Aangedreven door AliPrice.com"}, "common_setting": {"message": "Instelling"}, "common_sign_up": {"message": "Aanmelden"}, "common_system_upgrading_title": {"message": "Systeem upgraden"}, "common_system_upgrading_txt": {"message": "<PERSON><PERSON><PERSON> het later alstublieft"}, "common_txt__currency": {"message": "Valuta"}, "common_txt__video_tutorial": {"message": "Video uitleg"}, "common_txt_ago_time": {"message": "$time$ dagen geleden", "placeholders": {"time": {"content": "$1"}}}, "common_txt_all_product": {"message": "alle"}, "common_txt_analysis": {"message": "Analyse"}, "common_txt_basically_used": {"message": "<PERSON><PERSON><PERSON><PERSON> no<PERSON> gebru<PERSON>t"}, "common_txt_biaoti_link": {"message": "Titel+Link"}, "common_txt_biaoti_link_dian_pu": {"message": "Titel+Link+Winkelnaam"}, "common_txt_blacklist": {"message": "Blokkeerlijst"}, "common_txt_cancel": {"message": "<PERSON><PERSON><PERSON>"}, "common_txt_category": {"message": "Categorie"}, "common_txt_chakan": {"message": "Rekening"}, "common_txt_colors": {"message": "kle<PERSON>"}, "common_txt_confirm": {"message": "Bevestigen"}, "common_txt_copied": {"message": "Gekopieerd"}, "common_txt_copy": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "common_txt_copy_link": {"message": "Kopieer link"}, "common_txt_copy_title": {"message": "Titel kopiëren"}, "common_txt_copy_title__link": {"message": "<PERSON><PERSON><PERSON> titel en link"}, "common_txt_day": {"message": "lucht"}, "common_txt_day__short": {"message": "D"}, "common_txt_delete": {"message": "Verwijderen"}, "common_txt_dian_pu_link": {"message": "<PERSON><PERSON><PERSON> wink<PERSON> + link"}, "common_txt_download": {"message": "downloaden"}, "common_txt_downloaded": {"message": "Downloaden"}, "common_txt_export_as_csv": {"message": "Exporteren Excel"}, "common_txt_export_as_txt": {"message": "Exporteren Txt"}, "common_txt_fail": {"message": "Mislukking"}, "common_txt_format": {"message": "Form<PERSON><PERSON>"}, "common_txt_get": {"message": "krijgen"}, "common_txt_incert_selection": {"message": "Omgekeerde selectie"}, "common_txt_install": {"message": "Installeren"}, "common_txt_load_failed": {"message": "<PERSON><PERSON> mislukt"}, "common_txt_month": {"message": "maand"}, "common_txt_more": {"message": "<PERSON><PERSON>"}, "common_txt_new_unused": {"message": "Gloednieuw, ongebruikt"}, "common_txt_next": {"message": "De volgende"}, "common_txt_no_limit": {"message": "Onbeperkt"}, "common_txt_no_noticeable": {"message": "<PERSON><PERSON> z<PERSON>t<PERSON>e krassen of vuil"}, "common_txt_on_sale": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "common_txt_opt_in_out": {"message": "<PERSON><PERSON> uit"}, "common_txt_order": {"message": "<PERSON><PERSON><PERSON>"}, "common_txt_others": {"message": "<PERSON><PERSON><PERSON>"}, "common_txt_overall_poor_condition": {"message": "Over het algemeen slechte staat"}, "common_txt_patterns": {"message": "<PERSON><PERSON>"}, "common_txt_platform": {"message": "Platformen"}, "common_txt_please_select": {"message": "Selecteer alstublieft"}, "common_txt_prev": {"message": "Vorige"}, "common_txt_price": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "common_txt_privacy_policy": {"message": "Privacybeleid"}, "common_txt_product_condition": {"message": "Productstatus"}, "common_txt_rating": {"message": "Beoordeling"}, "common_txt_ratings": {"message": "Waarderingen"}, "common_txt_reload": {"message": "<PERSON><PERSON><PERSON>"}, "common_txt_reset": {"message": "Resetten"}, "common_txt_retail": {"message": "Klein<PERSON><PERSON>"}, "common_txt_review": {"message": "Recensie"}, "common_txt_sale": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "common_txt_same": {"message": "Dezelfde"}, "common_txt_scratches_and_dirt": {"message": "Met krassen en vuil"}, "common_txt_search_title": {"message": "<PERSON><PERSON> titel"}, "common_txt_select_all": {"message": "Selecteer alles"}, "common_txt_selected": {"message": "Geselekt"}, "common_txt_share": {"message": "<PERSON><PERSON>"}, "common_txt_sold": {"message": "verkocht"}, "common_txt_sold_out": {"message": "uitverkocht"}, "common_txt_some_scratches": {"message": "Enkele krassen en vuil"}, "common_txt_sort_by": {"message": "Sorteer op"}, "common_txt_state": {"message": "Staa<PERSON>"}, "common_txt_success": {"message": "Succes"}, "common_txt_sys_err": {"message": "systeemfout"}, "common_txt_today": {"message": "Vandaag"}, "common_txt_total": {"message": "alle"}, "common_txt_unselect_all": {"message": "Omgekeerde selectie"}, "common_txt_upload_image": {"message": "Afbeelding uploaden"}, "common_txt_visit": {"message": "Be<PERSON>ek"}, "common_txt_whitelist": {"message": "<PERSON><PERSON> lijst"}, "common_txt_wholesale": {"message": "Groothandel"}, "common_txt_wildberries": {"message": "Wildberries"}, "common_txt_year": {"message": "Jaar"}, "common_yes": {"message": "<PERSON>a"}, "compare_tool_btn_clear_all": {"message": "Wis alles"}, "compare_tool_btn_compare": {"message": "Vergelijken"}, "compare_tool_btn_contact": {"message": "Contact"}, "compare_tool_tips_max_compared": {"message": "Dodaj maksymalnie $maxComparedCount$", "placeholders": {"maxComparedCount": {"content": "$1"}}}, "configure_notifiactions": {"message": "Confi<PERSON><PERSON> meldingen"}, "contact_us": {"message": "Neem contact op"}, "context_menu_screenshot_search": {"message": "Screenshot zoeken voor dezelfde stijl"}, "context_menus_aliprice_search_by_image": {"message": "Zoek afbeelding op AliPrice"}, "context_menus_goote_trans": {"message": "<PERSON><PERSON><PERSON> verta<PERSON>/<PERSON><PERSON> weer<PERSON>ven"}, "context_menus_search_by_image": {"message": "Zoek op afbeelding op $storeName$", "placeholders": {"storeName": {"content": "$1"}}}, "context_menus_search_by_image_capture": {"message": "Leg vast in $storeName$", "placeholders": {"storeName": {"content": "$1"}}}, "context_menus_translator": {"message": "Vastleggen om te vertalen"}, "converter_modal_amount_placeholder": {"message": "Vul hier het bedrag in"}, "converter_modal_btn_convert": {"message": "<PERSON><PERSON><PERSON>"}, "converter_modal_exchange_rate_source": {"message": "De gegevens zijn afkomstig van $boc$ wisselkoers Updatetijd: $updatedAt$", "placeholders": {"boc": {"content": "$1"}, "updatedAt": {"content": "$2"}}}, "converter_modal_name": {"message": "wisselkoers"}, "converter_modal_search_placeholder": {"message": "zoekvaluta"}, "copy_all_contact_us_notice": {"message": "Deze site wordt op dit moment niet on<PERSON>te<PERSON>, neem contact met ons op"}, "copy_product_info": {"message": "Kopieer productinformatie"}, "copy_suggest_search_kw": {"message": "<PERSON><PERSON><PERSON> drop<PERSON><PERSON>"}, "country__han_gou": {"message": "Zuid-Korea"}, "country__ri_ben": {"message": "Japan"}, "country__yue_nan": {"message": "Vietnam"}, "currency_convert__custom": {"message": "Aangepaste wisselkoers"}, "currency_convert__sync_server": {"message": "Synchroniseer de server"}, "dang_ri_fa_huo": {"message": "Verzending op dezelfde dag"}, "dao_chu_quan_dian_shang_pin": {"message": "Alle winkelproducten exporteren"}, "dao_chu_wei_CSV": {"message": "Exporteren"}, "dao_chu_zi_duan": {"message": "Velden exporteren"}, "delivery_address": {"message": "Afleveradres"}, "delivery_company": {"message": "Be<PERSON>g<PERSON><PERSON><PERSON><PERSON>"}, "di_zhi": {"message": "adres"}, "dian_ji_cha_xun": {"message": "Klik om te bevragen"}, "dian_pu_ID": {"message": "Winkel-<PERSON>"}, "dian_pu_di_zhi": {"message": "Winkeladres"}, "dian_pu_lian_jie": {"message": "Winkellink"}, "dian_pu_ming": {"message": "<PERSON><PERSON> naam"}, "dian_pu_ming_cheng": {"message": "<PERSON><PERSON> naam"}, "dian_pu_shang_pin_zong_hsu": {"message": "Totaal aantal producten in de winkel: $num$", "placeholders": {"num": {"content": "$1"}}}, "dian_pu_xin_xi": {"message": "Informatie opslaan"}, "ding_zai_zuo_ce": {"message": "links genageld"}, "disable_old_version_tips_disable_btn_title": {"message": "<PERSON><PERSON><PERSON> de oude versie uit"}, "download_image__SKU_variant_images": {"message": "SKU-variantafbeeldingen"}, "download_image__assume": {"message": "We hebben bijvoorbeeld 2 afbeeldingen, product1.jpg en product2.gif.\nimg_{$no$} wordt hernoemd naar img_01.jpg, img_02.gif;\n{$group$}_{$no$} wordt hernoemd naar main_image_01.jpg, main_image_02.gif;", "placeholders": {"no": {"content": "$3"}, "group": {"content": "$2"}}}, "download_image__batch_download": {"message": "Batch downloaden"}, "download_image__combined_image": {"message": "Gecombineerde productdetailafbeelding"}, "download_image__continue_downloading": {"message": "Doorgaan met <PERSON>en"}, "download_image__description_images": {"message": "Beschrijving"}, "download_image__download_combined_image": {"message": "Download de gecombineerde productdetailafbeelding"}, "download_image__download_zip": {"message": "ZIP-bestand downloaden"}, "download_image__enlarge_check": {"message": "Ondersteunt alleen JPEG-, JPG-, GIF- en PNG-af<PERSON>ldingen, maximale grootte van een enkele afbeelding: 1600 * 1600"}, "download_image__enlarge_image": {"message": "Afbeelding vergroten"}, "download_image__export": {"message": "Eksport"}, "download_image__height": {"message": "<PERSON><PERSON><PERSON>"}, "download_image__ignore_videos": {"message": "De video is genegeerd, omdat deze niet kan worden geëxporteerd"}, "download_image__img_translate": {"message": "Afbeelding vertalen"}, "download_image__main_image": {"message": "hoofdafbeelding"}, "download_image__multi_folder": {"message": "Meerdere mappen"}, "download_image__name": {"message": "afbeelding downloaden"}, "download_image__notice_content": {"message": "Vink 'Vraag waar u elk bestand wilt opslaan voordat u het downloadt' niet aan in de downloadinstellingen van uw browser!!! <PERSON> zullen er veel dialoogvensters zijn."}, "download_image__notice_ignore": {"message": "<PERSON>et op<PERSON>uw om dit bericht vragen"}, "download_image__order_number": {"message": "{$no$} serienummer; {$group$} groepsnaam; {$date$} tijdstempel", "placeholders": {"no": {"content": "$1"}, "group": {"content": "$2"}, "date": {"content": "$3"}}}, "download_image__overview": {"message": "Overzicht"}, "download_image__prompt_download_zip": {"message": "Er zijn te veel afbeeldingen, u kunt ze beter downloaden als een zip-map."}, "download_image__rename": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "download_image__rule": {"message": "Naamgevingsregels"}, "download_image__single_folder": {"message": "Enkele map"}, "download_image__sku_image": {"message": "SKU-afbeeldingen"}, "download_image__video": {"message": "video-"}, "download_image__width": {"message": "<PERSON><PERSON><PERSON>"}, "download_reviews__download_images": {"message": "Review-afbeelding downloaden"}, "download_reviews__dropdown_title": {"message": "Review-afbeelding downloaden"}, "download_reviews__export_csv": {"message": "CSV exporteren"}, "download_reviews__no_images": {"message": "0 foto's besch<PERSON><PERSON>ar om te downloaden"}, "download_reviews__no_reviews": {"message": "Er zijn geen opmerkingen beschikbaar om te <PERSON>en!"}, "download_reviews__notice": {"message": "Tip:"}, "download_reviews__notice__chrome_settings": {"message": "Stel de Chrome-browser in om te vragen waar elk bestand moet worden opgeslagen voordat het wordt gedownload, stel in op \"Uit\""}, "download_reviews__notice__wait": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> van het aantal beoordelingen kan de wachttijd langer zijn"}, "download_reviews__pages_list__all": {"message": "Alle"}, "download_reviews__pages_list__page": {"message": "Vorige $page$ pagina's", "placeholders": {"page": {"content": "$1"}}}, "download_reviews__pages_list_title": {"message": "<PERSON><PERSON> bereik"}, "export_shopping_cart__csv_filed__details_url": {"message": "Productlink"}, "export_shopping_cart__csv_filed__details_url_with_sku": {"message": "Echo SKU-link"}, "export_shopping_cart__csv_filed__images": {"message": "Afbeeldingslink"}, "export_shopping_cart__csv_filed__quantity": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "export_shopping_cart__csv_filed__sale_price": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "export_shopping_cart__csv_filed__specs": {"message": "Specificaties"}, "export_shopping_cart__csv_filed__store_name": {"message": "<PERSON><PERSON> naam"}, "export_shopping_cart__csv_filed__store_url": {"message": "Winkel koppeling"}, "export_shopping_cart__csv_filed__title": {"message": "Productnaam"}, "export_shopping_cart__export_btn": {"message": "Exporteren"}, "export_shopping_cart__export_empty": {"message": "Selecteer een product!"}, "fa_huo_shi_jian": {"message": "Verzenden"}, "favorite_add_email": {"message": "E-mail toevoegen"}, "favorite_add_favorites": {"message": "Toevoegen aan favorieten"}, "favorite_added": {"message": "Toegevoegd"}, "favorite_btn_add": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>."}, "favorite_btn_notify": {"message": "Prijs volgen"}, "favorite_cate_name_all": {"message": "Alle producten"}, "favorite_current_price": {"message": "<PERSON><PERSON><PERSON> prijs"}, "favorite_due_date": {"message": "Vervaldatum"}, "favorite_enable_notification": {"message": "Schakel e-mailmeldingen in"}, "favorite_expired": {"message": "Verlopen"}, "favorite_go_to_enable": {"message": "Ga naar inschakelen"}, "favorite_msg_add_success": {"message": "Toegevoegd aan favor<PERSON>en"}, "favorite_msg_del_success": {"message": "Verwijderd uit favorieten"}, "favorite_msg_failure": {"message": "Mislukt! Vernieuw de pagina en probeer het opnieuw."}, "favorite_please_add_email": {"message": "Voeg e-mail toe"}, "favorite_price_drop": {"message": "Omlaag"}, "favorite_price_rise": {"message": "Omhoog"}, "favorite_price_untracked": {"message": "<PERSON><PERSON><PERSON><PERSON> niet g<PERSON>d"}, "favorite_saved_price": {"message": "Opgeslagen prijs"}, "favorite_stop_tracking": {"message": "Stop met volgen"}, "favorite_sub_email_address": {"message": "Abonnements-e-mailadres"}, "favorite_tracking_period": {"message": "Trackingperiode"}, "favorite_tracking_prices": {"message": "Prijzen volgen"}, "favorite_verify_email": {"message": "E-mailadres verifiëren"}, "favorites_list_remove_prompt_msg": {"message": "Weet u zeker dat u deze wilt verwijderen?"}, "favorites_update_button": {"message": "Update de prijzen nu"}, "fen_lei": {"message": "Categorie"}, "fen_xia_yan_xuan": {"message": "<PERSON><PERSON>"}, "find_similar": {"message": "<PERSON><PERSON>"}, "first_ali_price_date": {"message": "De datum waarop het voor het eerst werd gecrawld door de AliPrice-crawler"}, "fooview_coupons_modal_no_data": {"message": "<PERSON><PERSON>"}, "fooview_coupons_modal_title": {"message": "Waardebonnen"}, "fooview_favorites__product_sub__tooltip_l1": {"message": "Prijs < $lowerPrice$", "placeholders": {"lowerPrice": {"content": "$1"}}}, "fooview_favorites__product_sub__tooltip_l2": {"message": "of prijs > $higherPrice$", "placeholders": {"higherPrice": {"content": "$1"}}}, "fooview_favorites__product_sub__tooltip_l3": {"message": "Deadline"}, "fooview_favorites_error_msg_no_favorites": {"message": "Voeg hier favoriete producten toe om een ​​prijsverlagingswaarschuwing te ontvangen."}, "fooview_favorites_filter_latest": {"message": "Laatste"}, "fooview_favorites_filter_price_drop": {"message": "korting"}, "fooview_favorites_filter_price_up": {"message": "prijs ve<PERSON><PERSON><PERSON>"}, "fooview_favorites_modal_title": {"message": "<PERSON><PERSON>"}, "fooview_favorites_modal_title_title": {"message": "Ga naar AliPrice Fav<PERSON>"}, "fooview_favorites_track_price": {"message": "Om de prijs te volgen"}, "fooview_price_history_app_price": {"message": "APP prijs:"}, "fooview_price_history_title": {"message": "Prijsgeschiedenis"}, "fooview_product_list_feedback": {"message": "<PERSON><PERSON><PERSON>"}, "fooview_product_list_orders": {"message": "Bestellingen"}, "fooview_product_list_price": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "fooview_reviews_error_msg_no_review": {"message": "We hebben geen recensies gevonden voor dit product."}, "fooview_reviews_filter_buyer_reviews": {"message": "<PERSON><PERSON>'s van kopers"}, "fooview_reviews_modal_title": {"message": "Beoordelingen"}, "fooview_same_product_choose_category": {"message": "Kies categorie"}, "fooview_same_product_filter_feedback": {"message": "<PERSON><PERSON><PERSON>"}, "fooview_same_product_filter_orders": {"message": "Bestellingen"}, "fooview_same_product_filter_price": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "fooview_same_product_filter_rating": {"message": "Beoordeling"}, "fooview_same_product_modal_title": {"message": "Vind hetzelfde product"}, "fooview_same_product_search_by_image": {"message": "Zoek op afbeelding"}, "fooview_seller_analysis_modal_title": {"message": "Verkopersanalyse"}, "for_12_months": {"message": "Voor 1 jaar"}, "for_12_months_list_pro": {"message": "12 maanden"}, "for_12_months_nei": {"message": "Binnen 12 maanden"}, "for_1_months": {"message": "1 maand"}, "for_1_months_nei": {"message": "Binnen 1 maand"}, "for_3_months": {"message": "Voor 3 maanden"}, "for_3_months_nei": {"message": "Binnen 3 maanden"}, "for_6_months": {"message": "Voor 6 maanden"}, "for_6_months_nei": {"message": "Binnen 6 ma<PERSON>en"}, "for_9_months": {"message": "9 maanden"}, "for_9_months_nei": {"message": "Binnen 9 maanden"}, "fu_gou_lv": {"message": "Terugkooptarief"}, "gao_liang_bu_tong_dian": {"message": "verschillen benadrukken"}, "gao_liang_guang_gao_chan_pin": {"message": "Markeer advertentieproducten"}, "geng_duo_xin_xi": {"message": "Meer informatie"}, "geng_xin_shi_jian": {"message": "Updatetijd"}, "get_store_products_fail_tip": {"message": "Klik op OK om naar verificatie te gaan en normale toegang te garanderen"}, "gong_x_kuan_shang_pin": {"message": "In totaal $amount$ producten", "placeholders": {"amount": {"content": "$1"}}}, "gong_ying_shang": {"message": "Leverancier"}, "gong_ying_shang_ID": {"message": "Leverancier ID"}, "gong_ying_shang_deng_ji": {"message": "Leveranciersbeoordeling"}, "gong_ying_shang_nian_zhan": {"message": "Leverancier is ouder"}, "gong_ying_shang_xin_xi": {"message": "leverancier informatie"}, "gong_ying_shang_zhu_ye_lian_jie": {"message": "Link naar homepage van leverancier"}, "green_rocket": {"message": "로켓프레시"}, "grey_rocket": {"message": "로켓설치"}, "gu_ji_shou_jia": {"message": "Geschatte verkoopprijs"}, "guan_jian_zi": {"message": "Trefwoord"}, "guang_gao_chan_pin": {"message": "Ad. producten"}, "guang_gao_zhan_bi": {"message": "<PERSON><PERSON>"}, "guo_ji_wu_liu_yun_fei": {"message": "Internationale verzendkosten"}, "guo_lv_tiao_jian": {"message": "Filters"}, "hao_ping_lv": {"message": "<PERSON><PERSON><PERSON><PERSON> be<PERSON>deling"}, "highest_price": {"message": "<PERSON><PERSON>"}, "historical_trend": {"message": "historische trends"}, "how_to_screenshot": {"message": "<PERSON><PERSON> de linkermuisknop ingedrukt om het gebied te selecteren, tik op de rechtermuisknop of op de Esc-toets om de schermafbeelding te verlaten"}, "howt_it_works": {"message": "Hoe het werkt"}, "hui_fu_lv": {"message": "Responspercentage"}, "hui_tou_lv": {"message": "retourpercentage"}, "inquire_freightFee": {"message": "Vrachtaanvraag"}, "inquire_freightFee_Yuan": {"message": "Vracht/Yuan"}, "inquire_freightFee_province": {"message": "<PERSON><PERSON><PERSON>"}, "inquire_freightFee_the": {"message": "De vracht is $num$, wat betekent dat de regio gratis verzending heeft.", "placeholders": {"num": {"content": "$1"}}}, "is_ad": {"message": "Advertentie."}, "jia_ge": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "jia_ge_dan_wei": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "jia_ge_qu_shi": {"message": "Trend"}, "jia_zai_n_ge_shang_pin": {"message": "$num$ producten laden", "placeholders": {"num": {"content": "$1"}}}, "jin_30_tian_xiao_liang_zhan_bi": {"message": "Percentage van verkoopvolume in de laatste 30 dagen"}, "jin_30_tian_xiao_shou_e_zhan_bi": {"message": "Percentage van omzet in de laatste 30 dagen"}, "jin_30d_xiao_liang": {"message": "verkoop"}, "jin_30d_xiao_liang__desc": {"message": "Totale omzet in de afgelopen 30 dagen"}, "jin_30d_xiao_shou_e": {"message": "A<PERSON>zet"}, "jin_30d_xiao_shou_e__desc": {"message": "Totale omzet in de afgelopen 30 dagen"}, "jin_90_tian_mai_jia_shu": {"message": "Kopers in de afgelopen 90 dagen"}, "jin_90_tian_xiao_shou_liang": {"message": "Verkoop in de afgelopen 90 dagen"}, "jing_xuan_huo_yuan": {"message": "Gese<PERSON><PERSON><PERSON> bronnen"}, "jing_ying_mo_shi": {"message": "Bedrijfsmodel"}, "jing_ying_mo_shi__gong_chang": {"message": "<PERSON>abrikant"}, "jiu_fen_jie_jue": {"message": "Geschillenbeslechting"}, "jiu_fen_jie_jue__desc": {"message": "<PERSON><PERSON><PERSON><PERSON> van geschillen over <PERSON><PERSON><PERSON><PERSON> van verkopers"}, "jiu_fen_lv": {"message": "Geschillenpercentage"}, "jiu_fen_lv__desc": {"message": "Percentage bestellingen met klachten die in de afgelopen 30 dagen zijn voltooid en die onder de verantwoordelijkheid van de verkoper of beide partijen vallen"}, "kai_dian_ri_qi": {"message": "Openingsdatum"}, "keywords": {"message": "Trefwoorden"}, "kua_jin_Select_pan_huo": {"message": "Grensoverschrijdende selectie"}, "last15_days": {"message": "Laatste 15 dagen"}, "last180_days": {"message": "Laatste 180 dagen"}, "last30_days": {"message": "Laatste 30 dagen"}, "last360_days": {"message": "Laatste 360 ​​dagen"}, "last45_days": {"message": "Laatste 45 dagen"}, "last60_days": {"message": "Laatste 60 dagen"}, "last7_days": {"message": "Laatste 7 dagen"}, "last90_days": {"message": "Laatste 90 dagen"}, "last_30d_sales": {"message": "Uitverkoop van de laatste 30 dagen"}, "lei_ji": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "lei_ji_xiao_liang": {"message": "Totaal"}, "lei_ji_xiao_liang__desc": {"message": "Alle verkopen na product op de plank"}, "lei_ji_xiao_liang_pai_xu__desc": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON> verkoopvolume in de afgelopen 30 dagen, g<PERSON><PERSON><PERSON><PERSON> van hoog naar laag"}, "lian_xi_fang_shi": {"message": "Contactgegevens"}, "list_time": {"message": "Op houdbaarheidsdatum"}, "load_more": {"message": "<PERSON><PERSON> <PERSON>"}, "login_to_aliprice": {"message": "Log in op AliPrice"}, "long_link": {"message": "Lange link"}, "lowest_price": {"message": "Laag"}, "mai_jia_shu": {"message": "Verkoper"}, "mao_li_lv": {"message": "Marge"}, "mobile_view__dkxbqy": {"message": "Open een nieuw tabblad"}, "mobile_view__sjdxq": {"message": "Details in app"}, "mobile_view__sjdxqy": {"message": "Detailpagina in app"}, "mobile_view__smck": {"message": "Scannen om te bekijken"}, "mobile_view__smckms": {"message": "Gebruik de camera of app om te scannen en te bekijken"}, "modified_failed": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON> mislukt"}, "modified_successfully": {"message": "Succesvol gewijzigd"}, "nav_btn_favorites": {"message": "Mijn collecties"}, "nav_btn_package": {"message": "Pakket"}, "nav_btn_product_info": {"message": "Over het product"}, "nav_btn_viewed": {"message": "Bekeken"}, "no_suggest_search_kw": {"message": "Loading..."}, "none": {"message": "<PERSON><PERSON>"}, "normal_link": {"message": "Normale link"}, "notice": {"message": "hint"}, "number_reviews": {"message": "Recensies"}, "only_show_num": {"message": "Totaal producten: $allnum$, Verborgen: $hidenum2$", "placeholders": {"allnum": {"content": "$1"}, "hidenum2": {"content": "$2"}}}, "only_show_selected": {"message": "Verwijderen Ni<PERSON>"}, "open": {"message": "Open"}, "open_links": {"message": "Open links"}, "options_page_tab_check_links": {"message": "Controleer links"}, "options_page_tab_gernal": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "options_page_tab_notifications": {"message": "Meldingen"}, "options_page_tab_others": {"message": "<PERSON><PERSON><PERSON>"}, "options_page_tab_sbi": {"message": "Zoek op afbeelding"}, "options_page_tab_shortcuts": {"message": "Snelkoppelingen"}, "options_page_tab_shortcuts_title": {"message": "Lettergrootte voor snelkoppelingen"}, "options_page_tab_similar_products": {"message": "Dezelfde producten"}, "orange_rocket": {"message": "판매자로켓"}, "order_list_open_links_tip": {"message": "Meerdere productlinks staan ​​op het punt te openen"}, "order_list_sku_show_title": {"message": "Toon geselecteerde varianten in de gedeelde links"}, "orders_last30_days": {"message": "Aantal bestellingen in de afgelopen 30 dagen"}, "pTutorial_favorites_block1_desc1": {"message": "De producten die u heeft gevolgd, worden hier vermeld"}, "pTutorial_favorites_block1_title": {"message": "Favorieten"}, "pTutorial_popup_block1_desc1": {"message": "Een groen label betekent dat er producten in prijs zijn verlaagd"}, "pTutorial_popup_block1_title": {"message": "Snelkoppelingen en favorieten"}, "pTutorial_price_history_block1_desc1": {"message": "<PERSON>lik op \"Prijs volgen\" en voeg producten toe aan favorieten. Zodra hun prijzen dalen, ontvangt u meldingen"}, "pTutorial_price_history_block1_title": {"message": "Prijs volgen"}, "pTutorial_reviews_block1_desc1": {"message": "Kopersbeoordelingen van Itao en echte foto's van AliExpress-feedback"}, "pTutorial_reviews_block1_title": {"message": "Beoordelingen"}, "pTutorial_reviews_block2_desc1": {"message": "Het is altijd handig om beoordelingen van andere kopers te controleren"}, "pTutorial_same_products_block1_desc1": {"message": "U kunt ze vergelijken om de beste keuze te maken"}, "pTutorial_same_products_block1_desc2": {"message": "Klik op 'Meer' voor 'Zoeken op afbeelding'"}, "pTutorial_same_products_block1_title": {"message": "Dezelfde producten"}, "pTutorial_same_products_by_image_search_block1_desc1": {"message": "Zet de productafbeelding daar neer en kies een categorie"}, "pTutorial_same_products_by_image_search_block1_title": {"message": "Zoek op afbeelding"}, "pTutorial_seller_analysis_block1_desc1": {"message": "Positieve feedback van de verkoper, feedbackscores en hoe lang de verkoper op de markt is"}, "pTutorial_seller_analysis_block1_title": {"message": "Verkoperscore"}, "pTutorial_seller_analysis_block2_desc2": {"message": "Verkopersbeoordeling is gebaseerd op 3 indexen: item zoals beschreven, communicatiesnelheid"}, "pTutorial_seller_analysis_block3_desc3": {"message": "We gebruiken 3 kleuren en pictogrammen om het vertrouwensniveau van verkopers aan te geven"}, "page_count": {"message": "Aantal pagina's"}, "pai_chu": {"message": "Uitgesloten"}, "pai_chu_HK_bu_yi_xia_xiaoshou": {"message": "Hongkong-beperkt uitsluiten"}, "pai_chu_JP_bu_yi_xia_xiaoshou": {"message": "Japan-beperkt uitsluiten"}, "pai_chu_KR_bu_yi_xia_xiaoshou": {"message": "Korea-beperkt uitsluiten"}, "pai_chu_KZ_bu_yi_xia_xiaoshou": {"message": "Kazachstan-beperkt uitsluiten"}, "pai_chu_MO_bu_yi_xia_xiaoshou": {"message": "Macau-beperkt uitsluiten"}, "pai_chu_RU_bu_yi_xia_xiaoshou": {"message": "Oost-Europa-beperkt uitsluiten"}, "pai_chu_SA_bu_yi_xia_xiaoshou": {"message": "Saoedi-Arabië-beperkt uitsluiten"}, "pai_chu_TW_bu_yi_xia_xiaoshou": {"message": "Taiwan-beperkt uitsluiten"}, "pai_chu_USA_bu_yi_xia_xiaoshou": {"message": "VS-beperkt uitsluiten"}, "pai_chu_VN_bu_yi_xia_xiaoshou": {"message": "Vietnam-beperkt uitsluiten"}, "pai_chu_bu_yi_xia_xiaoshou": {"message": "Beperkte artikelen uitsluiten"}, "payable_price_formula": {"message": "Prijs + Verzending + Korting"}, "pdd_check_retail_btn_txt": {"message": "<PERSON><PERSON>"}, "pdd_pifa_to_retail_btn_txt": {"message": "<PERSON><PERSON> in de winkel"}, "pdp_copy_fail": {"message": "Kopiëren mislukt!"}, "pdp_copy_success": {"message": "Kopiëren geslaagd!"}, "pdp_share_modal_subtitle": {"message": "<PERSON>l screenshot, hij/zij zal jouw keuze zien."}, "pdp_share_modal_title": {"message": "<PERSON>l je keuze"}, "pdp_share_screenshot": {"message": "Schermafbeelding delen"}, "pei_song": {"message": "Verzending"}, "pin_lei": {"message": "Categorie"}, "pin_zhi_ti_yan": {"message": "Productkwaliteit"}, "pin_zhi_ti_yan__desc": {"message": "Kwaliteitsrestitutiepercentage van de winkel van de verkoper"}, "pin_zhi_tui_kuan_lv": {"message": "Terugbetalingspercentage"}, "pin_zhi_tui_kuan_lv__desc": {"message": "Het percentage bestellingen dat alleen in de afgelopen 30 dagen is terugbetaald en geretourneerd"}, "ping_fen": {"message": "Beoordeling"}, "ping_jun_fa_huo_su_du": {"message": "Gemiddelde verzendsnelheid"}, "pkgInfo_hide": {"message": "Logistieke info: aan/uit"}, "pkgInfo_no_trace": {"message": "<PERSON>n logistieke informatie"}, "platform_name__1688": {"message": "1688"}, "platform_name__17zwd": {"message": "17zwd.com"}, "platform_name__51taoyang": {"message": "51taoyang.com"}, "platform_name__5ts": {"message": "5ts.com"}, "platform_name__91jf": {"message": "91jf.com"}, "platform_name__alibaba": {"message": "Alibaba.com"}, "platform_name__aliexpress": {"message": "AliExpress"}, "platform_name__amazon": {"message": "Amazon"}, "platform_name__bao66": {"message": "bao66.cn"}, "platform_name__chinagoods": {"message": "chinagoods.com"}, "platform_name__dhgate": {"message": "DHgate"}, "platform_name__e3hui": {"message": "e3hui.com"}, "platform_name__ebay": {"message": "Ebay"}, "platform_name__hznzcn": {"message": "hznzcn.com"}, "platform_name__jd": {"message": "JD"}, "platform_name__juyi5": {"message": "juyi5.cn"}, "platform_name__naver_shopping": {"message": "Naver Shopping"}, "platform_name__pinduoduo": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "platform_name__pinduoduo_pifa": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON> g<PERSON><PERSON><PERSON>"}, "platform_name__shopee": {"message": "<PERSON>ee"}, "platform_name__sjxzw": {"message": "571xz.com"}, "platform_name__sooxie": {"message": "sooxie.com"}, "platform_name__taobao": {"message": "Taobao"}, "platform_name__taobao_lite": {"message": "Taobao Lite"}, "platform_name__vvic": {"message": "vvic.com"}, "platform_name__walmart": {"message": "Walmart"}, "platform_name__wsy": {"message": "wsy.com"}, "platform_name__xingfujie": {"message": "xingfujie.cn"}, "platform_name__yiwugo": {"message": "Yiwugo.com"}, "platform_name__yunchepin": {"message": "yunchepin.cn"}, "platform_name__zhaojiafang": {"message": "zhaojiafang.com"}, "popup_go_to_home": {"message": "<PERSON><PERSON>"}, "popup_go_to_platform": {"message": "Ga naar $name$", "placeholders": {"name": {"content": "$1"}}}, "popup_search_placeholder": {"message": "<PERSON>k ben aan het winkelen voor ..."}, "popup_track_package_btn_track": {"message": "TRACK"}, "popup_track_package_desc": {"message": "ALLES-IN-<PERSON><PERSON><PERSON> PAKKETTRACKING"}, "popup_track_package_search_placeholder": {"message": "Volg nummer"}, "popup_translate_search_placeholder": {"message": "Vertaal en zoek op $searchOn$", "placeholders": {"searchOn": {"content": "$1"}}}, "price_history": {"message": "Prijsgeschiedenis"}, "price_history_chart_tip_ae": {"message": "Tip: Het aantal bestellingen is het cumulatieve aantal bestellingen sinds de lancering"}, "price_history_chart_tip_coupang": {"message": "Tip: Coupang verwijdert het orderaantal van frauduleuze orders"}, "price_history_inm_1688_l1": {"message": "Installeer alstublieft"}, "price_history_inm_1688_l2": {"message": "AliPrice-winkelassistent voor 1688"}, "price_history_panel_lowest_price": {"message": "Laagste prijs:"}, "price_history_panel_tab_price_tracking": {"message": "Prijsgeschiedenis"}, "price_history_panel_tab_seller_analysis": {"message": "Verkopersanalyse"}, "price_history_pro_modal_title": {"message": "Prijsgeschiedenis & Bestelgeschiedenis"}, "privacy_consent__btn_agree": {"message": "<PERSON><PERSON><PERSON> toes<PERSON> voor gegevensverzameling"}, "privacy_consent__btn_disable_all": {"message": "<PERSON><PERSON>"}, "privacy_consent__btn_enable_all": {"message": "<PERSON><PERSON><PERSON> alles in"}, "privacy_consent__btn_uninstall": {"message": "Verwijderen"}, "privacy_consent__desc_privacy": {"message": "Merk op dat, zonder data of cookies, sommige functies uitgeschakeld zijn omdat die functies de uitleg van data of cookies nodig hebben, maar je kunt nog steeds de andere functies gebruiken."}, "privacy_consent__desc_privacy_L1": {"message": "Zonder data of cookies werkt het helaas niet omdat we de uitleg van data of cookies nodig hebben."}, "privacy_consent__desc_privacy_L2": {"message": "Als u ons niet toestaat deze informatie te verzamelen, verwijder deze dan."}, "privacy_consent__item_cookies_desc": {"message": "<PERSON><PERSON>, we krijgen alleen uw valutagegevens in cookies tijdens het online winkelen om de prijsgeschiedenis te tonen."}, "privacy_consent__item_cookies_title": {"message": "Vereiste cookies"}, "privacy_consent__item_functional_desc_L1": {"message": "1. Voeg cookies toe aan de browser om uw computer of apparaat anoniem te identificeren."}, "privacy_consent__item_functional_desc_L2": {"message": "2. <PERSON><PERSON><PERSON> <PERSON><PERSON> g<PERSON><PERSON>s toe in add-on om met functie te werken."}, "privacy_consent__item_functional_title": {"message": "Functionele en analytische cookies"}, "privacy_consent__more_desc": {"message": "Houd er rekening mee dat we uw persoonlijke gegevens niet delen met andere bedrijven en dat er geen advertentiebedrijven gegevens verzamelen via onze service."}, "privacy_consent__options__btn__desc": {"message": "Om alle functies te gebruiken, moet u deze inschakelen."}, "privacy_consent__options__btn__label": {"message": "Zet het aan"}, "privacy_consent__options__desc_L1": {"message": "We zullen de volgende gegevens verzamelen waarmee u persoonlijk geïdentificeerd kunt worden:"}, "privacy_consent__options__desc_L2": {"message": "- cookies, we krijgen alleen uw valutagegevens in cookies wanneer u online winkelt om de prijsgeschiedenis te tonen."}, "privacy_consent__options__desc_L3": {"message": "- en voeg cookies toe in de browser om uw computer of apparaat anoniem te identificeren."}, "privacy_consent__options__desc_L4": {"message": "- andere anonieme gegevens maken deze extensie handiger."}, "privacy_consent__options__desc_L5": {"message": "Houd er rekening mee dat we uw persoonlijke gegevens niet delen met andere bedrijven en dat er geen advertentiebedrijven gegevens verzamelen via onze service."}, "privacy_consent__privacy_preferences": {"message": "Privacy-voorkeuren"}, "privacy_consent__read_more": {"message": "<PERSON><PERSON> meer >>"}, "privacy_consent__title_privacy": {"message": "Privacy"}, "product_info": {"message": "Productinfo"}, "product_recommend__name": {"message": "Dezelfde producten"}, "product_research": {"message": "Productonderzoek"}, "product_sub__email_desc": {"message": "E-mail voor prijswaarschuwing"}, "product_sub__email_edit": {"message": "bewerking"}, "product_sub__email_not_verified": {"message": "Verifieer e-mail a.u.b."}, "product_sub__email_required": {"message": "Geef e-mail op"}, "product_sub__form_countdown": {"message": "Automatisch sluiten na $seconds$ seconden", "placeholders": {"seconds": {"content": "$1"}}}, "product_sub__form_fail": {"message": "Kan herinnering niet toevoegen!"}, "product_sub__form_input_price": {"message": "invoerprijs"}, "product_sub__form_item_country": {"message": "natie"}, "product_sub__form_item_current_price": {"message": "<PERSON><PERSON><PERSON> prijs"}, "product_sub__form_item_duration": {"message": "spoor"}, "product_sub__form_item_higher_price": {"message": "Of prijs>"}, "product_sub__form_item_invalid_higher_price": {"message": "Prijs moet hoger zijn dan $price$", "placeholders": {"price": {"content": "$1"}}}, "product_sub__form_item_invalid_lower_price": {"message": "Prijs moet lager zijn dan $price$", "placeholders": {"price": {"content": "$1"}}}, "product_sub__form_item_lower_price": {"message": "<PERSON><PERSON> <"}, "product_sub__form_submit": {"message": "<PERSON><PERSON>"}, "product_sub__form_success": {"message": "Het is gelukt om een ​​herinnering toe te voegen!"}, "product_sub__high_price_notify": {"message": "Houd mij op de hoogte van prijsstijgingen"}, "product_sub__low_price_notify": {"message": "Houd mij op de hoogte van prijsverlagingen"}, "product_sub__modal_title": {"message": "Herinnering voor wijziging abonnementsprijs"}, "province__an_hui": {"message": "<PERSON><PERSON>"}, "province__ao_men": {"message": "Macau"}, "province__bei_jing": {"message": "Beijing"}, "province__chong_qing": {"message": "Chongqing"}, "province__fu_jian": {"message": "Fujian"}, "province__gan_su": {"message": "Gansu"}, "province__guang_dong": {"message": "Guangdong"}, "province__guang_xi": {"message": "Guangxi"}, "province__gui_zhou": {"message": "Guizhou"}, "province__hai_nan": {"message": "Hainan"}, "province__he_bei": {"message": "Hebei"}, "province__he_nan": {"message": "<PERSON><PERSON>"}, "province__hei_long_jiang": {"message": "Heilongjiang"}, "province__hu_bei": {"message": "Hubei"}, "province__hu_nan": {"message": "Hunan"}, "province__ji_lin": {"message": "<PERSON><PERSON>"}, "province__jiang_su": {"message": "Jiangsu"}, "province__jiang_xi": {"message": "Jiangxi"}, "province__liao_ning": {"message": "Liaoning"}, "province__nei_meng_gu": {"message": "Inner Mongolia"}, "province__ning_xia": {"message": "Ningxia"}, "province__qing_hai": {"message": "Qinghai"}, "province__shan_dong": {"message": "Shandong"}, "province__shan_xi": {"message": "Shaanxi"}, "province__shang_hai": {"message": "Shanghai"}, "province__si_chuan": {"message": "Sichuan"}, "province__tai_wan": {"message": "Taiwan"}, "province__tian_jin": {"message": "Tianjin"}, "province__xi_zhang": {"message": "Tibet"}, "province__xiang_gang": {"message": "Hong Kong"}, "province__xin_jiang": {"message": "Xinjiang"}, "province__yun_nan": {"message": "Yunnan"}, "province__zhe_jiang": {"message": "Zhejiang"}, "purple_rocket": {"message": "로켓직구"}, "qi_ding_liang": {"message": "MOQ"}, "qi_ding_liang_qi_ding_jia": {"message": "MOQ & MOP"}, "qi_ye_mian_ji": {"message": "Enterprise-gebied"}, "qing_zhi_shao_xuan_ze_yi_ge_chan_pin": {"message": "Selecteer minimaal één product"}, "qing_zhi_shao_xuan_ze_yi_ge_zi_duan": {"message": "Selecteer minimaal <PERSON><PERSON> veld"}, "qu_deng_lu": {"message": "Inloggen"}, "quan_guo_yan_xuan": {"message": "Wereldwijde k<PERSON>ze"}, "recommendation_popup_banner_btn_install": {"message": "Installeer het"}, "recommendation_popup_banner_desc": {"message": "Toon prijsgeschiedenis binnen 3/6 maanden en prijsverlaging"}, "region__all": {"message": "Alle regio's"}, "region__hai_wai": {"message": "Overseas"}, "region__hua_bei_qu": {"message": "North China"}, "region__hua_dong_qu": {"message": "East China"}, "region__hua_nan_qu": {"message": "South China"}, "region__hua_zhong_qu": {"message": "Central China"}, "region__jiang_zhe_lu": {"message": "Jiangsu, Zhejiang and Shanghai"}, "remove_web_limits": {"message": "Rechtsklikken inschakelen"}, "ren_zheng_gong_chang": {"message": "G<PERSON><PERSON><PERSON><PERSON><PERSON> fabriek"}, "ren_zheng_gong_ying_shang_nian_zhan": {"message": "J<PERSON><PERSON><PERSON> gecertificeerd leverancier"}, "required_to_aliprice_login": {"message": "Moet inloggen op AliPrice"}, "revenue_last30_days": {"message": "30 dagen verkoop"}, "review_counts": {"message": "Aantal verzamelaars"}, "rocket": {"message": "로켓"}, "ru_zhu_nian_xian": {"message": "Ingangsperiode"}, "sales_amount_last30_days": {"message": "Verkoop in de afgelopen 30 dagen"}, "sales_last30_days": {"message": "Verkoopvolume in de afgelopen 30 dagen"}, "sbi_alibaba_cate__accessories": {"message": "Accessoires"}, "sbi_alibaba_cate__aqfk": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "sbi_alibaba_cate__bags_cases": {"message": "Tassen en koffers"}, "sbi_alibaba_cate__beauty": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "sbi_alibaba_cate__beverage": {"message": "Drank"}, "sbi_alibaba_cate__bgwh": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "sbi_alibaba_cate__bz": {"message": "Pakket"}, "sbi_alibaba_cate__ccyj": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "sbi_alibaba_cate__clothes": {"message": "<PERSON><PERSON><PERSON>"}, "sbi_alibaba_cate__cmgd": {"message": "Media-uitzendingen"}, "sbi_alibaba_cate__coat_jacket": {"message": "Jas en jas"}, "sbi_alibaba_cate__consumer_electronics": {"message": "Consumentenelektronica"}, "sbi_alibaba_cate__cryp": {"message": "Producten voor volwassenen"}, "sbi_alibaba_cate__csyp": {"message": "Beddengoed"}, "sbi_alibaba_cate__cwyy": {"message": "<PERSON><PERSON>"}, "sbi_alibaba_cate__cysx": {"message": "Horeca vers"}, "sbi_alibaba_cate__dgdq": {"message": "Elektricien"}, "sbi_alibaba_cate__dl": {"message": "<PERSON><PERSON><PERSON> spelen"}, "sbi_alibaba_cate__dress_suits": {"message": "Jurk en pakken"}, "sbi_alibaba_cate__dszm": {"message": "<PERSON><PERSON><PERSON>ting"}, "sbi_alibaba_cate__dzqj": {"message": "Elektronisch apparaat"}, "sbi_alibaba_cate__essb": {"message": "Gebruikte uitrusting"}, "sbi_alibaba_cate__food": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "sbi_alibaba_cate__fspj": {"message": "kleding"}, "sbi_alibaba_cate__furniture": {"message": "<PERSON><PERSON><PERSON>"}, "sbi_alibaba_cate__fzpg": {"message": "<PERSON><PERSON> leer"}, "sbi_alibaba_cate__ghjq": {"message": "Persoonlijke verzorging"}, "sbi_alibaba_cate__gt": {"message": "Staal"}, "sbi_alibaba_cate__gyp": {"message": "Ambachten"}, "sbi_alibaba_cate__hb": {"message": "Milieuvriendelijk"}, "sbi_alibaba_cate__hfcz": {"message": "Huidverzorging make-up"}, "sbi_alibaba_cate__hg": {"message": "Chemische industrie"}, "sbi_alibaba_cate__jg": {"message": "Verwerken"}, "sbi_alibaba_cate__jianccai": {"message": "Bouwmaterialen"}, "sbi_alibaba_cate__jichuang": {"message": "Werktuigmachine"}, "sbi_alibaba_cate__jjry": {"message": "Huishoudelijk dagelijks gebruik"}, "sbi_alibaba_cate__jtys": {"message": "Vervoer"}, "sbi_alibaba_cate__jxsb": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "sbi_alibaba_cate__jxwj": {"message": "Mechanische hardware"}, "sbi_alibaba_cate__jydq": {"message": "Huishoudelijke apparaten"}, "sbi_alibaba_cate__jzjc": {"message": "Bouwmaterialen voor woningverbetering"}, "sbi_alibaba_cate__jzjf": {"message": "Huishoudelijke textiel"}, "sbi_alibaba_cate__mj": {"message": "<PERSON><PERSON><PERSON>"}, "sbi_alibaba_cate__myyp": {"message": "Baby producten"}, "sbi_alibaba_cate__nanz": {"message": "<PERSON><PERSON>"}, "sbi_alibaba_cate__nvz": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "sbi_alibaba_cate__ny": {"message": "Energie"}, "sbi_alibaba_cate__others": {"message": "<PERSON><PERSON><PERSON>"}, "sbi_alibaba_cate__qcyp": {"message": "Auto accessoires"}, "sbi_alibaba_cate__qmpj": {"message": "Auto-onderdelen"}, "sbi_alibaba_cate__shoes": {"message": "<PERSON><PERSON><PERSON>"}, "sbi_alibaba_cate__smdn": {"message": "Digitale computer"}, "sbi_alibaba_cate__snqj": {"message": "Opslag en reiniging"}, "sbi_alibaba_cate__spjs": {"message": "<PERSON><PERSON> drinken"}, "sbi_alibaba_cate__swfw": {"message": "Bedrijfsdiensten"}, "sbi_alibaba_cate__toys_hobbies": {"message": "Speelgoed"}, "sbi_alibaba_cate__trousers_skirt": {"message": "Broeken en rok"}, "sbi_alibaba_cate__txcp": {"message": "Communicatie producten"}, "sbi_alibaba_cate__tz": {"message": "Kinderkleding"}, "sbi_alibaba_cate__underwear": {"message": "Ondergoed"}, "sbi_alibaba_cate__wjgj": {"message": "Hardware tools"}, "sbi_alibaba_cate__xgpi": {"message": "<PERSON><PERSON>"}, "sbi_alibaba_cate__xmhz": {"message": "projectmatige samenwerking"}, "sbi_alibaba_cate__xs": {"message": "<PERSON><PERSON><PERSON>"}, "sbi_alibaba_cate__ydfs": {"message": "Sportkleding"}, "sbi_alibaba_cate__ydhw": {"message": "Buitensport"}, "sbi_alibaba_cate__yjkc": {"message": "Metallurgische mineralen"}, "sbi_alibaba_cate__yqyb": {"message": "Instrumentatie"}, "sbi_alibaba_cate__ys": {"message": "Afdrukken"}, "sbi_alibaba_cate__yyby": {"message": "Medische zorg"}, "sbi_alibaba_cn_kj_90mjs": {"message": "Aantal kopers in de afgelopen 90 dagen"}, "sbi_alibaba_cn_kj_90xsl": {"message": "Verkoopvolume in de afgelopen 90 dagen"}, "sbi_alibaba_cn_kj_gjsj": {"message": "Geschatte prijs"}, "sbi_alibaba_cn_kj_gjwlyf": {"message": "Internationale verzendkosten"}, "sbi_alibaba_cn_kj_gjyf": {"message": "Verzendkosten"}, "sbi_alibaba_cn_kj_gssj": {"message": "Geschatte prijs"}, "sbi_alibaba_cn_kj_lr": {"message": "Winst"}, "sbi_alibaba_cn_kj_lrgs": {"message": "Winst = geschatte prijs x winstmarge"}, "sbi_alibaba_cn_kj_pjfhsd": {"message": "Gemiddelde bezorgsnelheid"}, "sbi_alibaba_cn_kj_qtfy": {"message": "andere vergo<PERSON>"}, "sbi_alibaba_cn_kj_qtfygs": {"message": "Overige kosten = geschatte prijs x overige kostenverhouding"}, "sbi_alibaba_cn_kj_spjg": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "sbi_alibaba_cn_kj_spzl": {"message": "Gewicht"}, "sbi_alibaba_cn_kj_szd": {"message": "Plaats"}, "sbi_alibaba_cn_kj_unit_ge": {"message": "Stukken"}, "sbi_alibaba_cn_kj_unit_jian": {"message": "Stukken"}, "sbi_alibaba_cn_kj_unit_ke": {"message": "Gram"}, "sbi_alibaba_cn_kj_unit_ren": {"message": "Kopers:"}, "sbi_alibaba_cn_kj_unit_tai": {"message": "Stukken"}, "sbi_alibaba_cn_kj_unit_tao": {"message": "Sets"}, "sbi_alibaba_cn_kj_unit_tian": {"message": "dagen"}, "sbi_alibaba_cn_kj_zwbj": {"message": "<PERSON><PERSON> prijs"}, "sbi_aliprice_alibaba_cn__jiage": {"message": "prijs"}, "sbi_aliprice_alibaba_cn__keshou": {"message": "Beschikbaar voor verkoop"}, "sbi_aliprice_alibaba_cn__moren": {"message": "standaard"}, "sbi_aliprice_alibaba_cn__queding": {"message": "<PERSON><PERSON> wel"}, "sbi_aliprice_alibaba_cn__xiaoliang": {"message": "verkoop"}, "sbi_aliprice_alibaba_cn_cate__jiaju": {"message": "<PERSON><PERSON><PERSON>"}, "sbi_aliprice_alibaba_cn_cate__linghsi": {"message": "snack"}, "sbi_aliprice_alibaba_cn_cate__meizhuang": {"message": "make-up"}, "sbi_aliprice_alibaba_cn_cate__neiyi": {"message": "Ondergoed"}, "sbi_aliprice_alibaba_cn_cate__peishi": {"message": "Accessoires"}, "sbi_aliprice_alibaba_cn_cate__pinchui": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "sbi_aliprice_alibaba_cn_cate__qita": {"message": "anderen"}, "sbi_aliprice_alibaba_cn_cate__qunzhuang": {"message": "Rok"}, "sbi_aliprice_alibaba_cn_cate__shangyi": {"message": "Jas<PERSON>"}, "sbi_aliprice_alibaba_cn_cate__shuma": {"message": "Elektronica"}, "sbi_aliprice_alibaba_cn_cate__wanju": {"message": "Speelgoed"}, "sbi_aliprice_alibaba_cn_cate__xiangbao": {"message": "Bagage"}, "sbi_aliprice_alibaba_cn_cate__xiazhuang": {"message": "Bodems"}, "sbi_aliprice_alibaba_cn_cate__xiezi": {"message": "schoen"}, "sbi_aliprice_cate__apparel": {"message": "<PERSON><PERSON><PERSON>"}, "sbi_aliprice_cate__automobiles_motorcycles": {"message": "Auto's en motoren"}, "sbi_aliprice_cate__beauty_health": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "sbi_aliprice_cate__cellphones_telecommunications": {"message": "Mobiele telefoons en telecommunicatie"}, "sbi_aliprice_cate__computer_office": {"message": "Computer en kantoor"}, "sbi_aliprice_cate__consumer_electronics": {"message": "Consumentenelektronica"}, "sbi_aliprice_cate__education_office_supplies": {"message": "Onderwijs en kantoorbenodigdheden"}, "sbi_aliprice_cate__electronic_components_supplies": {"message": "Elektronische componenten en benodigdheden"}, "sbi_aliprice_cate__furniture": {"message": "<PERSON><PERSON><PERSON>"}, "sbi_aliprice_cate__hair_extensions_wigs": {"message": "Haarextensies en p<PERSON>iken"}, "sbi_aliprice_cate__home_garden": {"message": "Huis & Tuin"}, "sbi_aliprice_cate__home_improvement": {"message": "Verbouwing"}, "sbi_aliprice_cate__jewelry_accessories": {"message": "Sieraden en accessoires"}, "sbi_aliprice_cate__luggage_bags": {"message": "Bagage en tassen"}, "sbi_aliprice_cate__mother_kids": {"message": "Moeder en kinderen"}, "sbi_aliprice_cate__novelty_special_use": {"message": "Nieuwheid en speciaal gebruik"}, "sbi_aliprice_cate__security_protection": {"message": "Beveiliging"}, "sbi_aliprice_cate__shoes": {"message": "<PERSON><PERSON><PERSON>"}, "sbi_aliprice_cate__sports_entertainment": {"message": "Sport en entertainment"}, "sbi_aliprice_cate__toys_hobbies": {"message": "Speelgoed en hobby's"}, "sbi_aliprice_cate__watches": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "sbi_aliprice_cate__weddings_events": {"message": "Bruiloften en evenementen"}, "sbi_btn_capture_txt": {"message": "Gevangen nemen"}, "sbi_btn_source_now_txt": {"message": "<PERSON>u bron"}, "sbi_button__chat_with_me": {"message": "<PERSON><PERSON><PERSON> met me"}, "sbi_button__contact_supplier": {"message": "Contact"}, "sbi_button__hide_on_this_site": {"message": "Niet laten zien op deze site"}, "sbi_button__open_settings": {"message": "Configureer zoeken op afbeelding"}, "sbi_capture_shortcut_tip": {"message": "of druk op de \"Enter\" -toets op het toetsenbord"}, "sbi_capturing_tip": {"message": "Vastleggen"}, "sbi_composed_rating_45": {"message": "4,5 - 5,0 sterren"}, "sbi_crop_and_search": {"message": "<PERSON><PERSON>"}, "sbi_crop_start": {"message": "Gebruik Screenshot"}, "sbi_err_captcha_action": {"message": "Verifiëren"}, "sbi_err_captcha_for_alibaba_cn": {"message": "Verificatie nodig. Upload een foto om te verifiëren. (Bekijk $video_tutorial$ of probeer cookies te wissen)", "placeholders": {"feedback": {"content": "$1"}, "video_tutorial": {"content": "$1"}}}, "sbi_err_captcha_for_aliprice": {"message": "Ongebruikelijk verkeer, verifieer a.u.b."}, "sbi_err_captcha_for_taobao": {"message": "Taobao verzoekt u om te verifiëren, upload een foto handmatig en zoek om deze te verifiëren. Deze fout is te wijten aan het nieuwe verificatiebeleid van 'TaoBao zoeken op afbeelding'. We raden u aan de klacht te vaak te verifiëren op Taobao $feedback$.", "placeholders": {"feedback": {"content": "$1"}}}, "sbi_err_captcha_for_taobao_feedback": {"message": "feedback"}, "sbi_err_captcha_msg": {"message": "$platform$ vereist dat u een afbeelding uploadt om te zoeken of de beveiligingsverificatie voltooit om zoekbeperkingen te verwijderen", "placeholders": {"platform": {"content": "$1"}}}, "sbi_err_checking_if_low_version": {"message": "Controleer of het de nieuwste versie is"}, "sbi_err_cookie_btn_clear": {"message": "Cookies wissen"}, "sbi_err_cookie_for_alibaba_cn": {"message": "Wis 1688 cookies? (Moet opnieuw inloggen)"}, "sbi_err_desperate_feature_pdd": {"message": "De beeldzoekfunctie is verplaatst naar Pinduoduo Zoeken op Beeldextensie."}, "sbi_err_hidden_skill_of_improve_search_rate": {"message": "Hoe het slagingspercentage van het zoeken naar afbeeldingen te verbeteren?"}, "sbi_err_img_undersize": {"message": "Afbeelding > $imgMinimumSize$", "placeholders": {"imgMinimumSize": {"content": "$1"}}}, "sbi_err_login_required": {"message": "Inloggen $loginSite$", "placeholders": {"loginSite": {"content": "$1"}}}, "sbi_err_login_required_action": {"message": "Log in"}, "sbi_err_low_version": {"message": "Installeer de nieuwste versie ($latestVersion$)", "placeholders": {"latestVersion": {"content": "$1"}}}, "sbi_err_low_version_action": {"message": "Downloaden"}, "sbi_err_need_help": {"message": "<PERSON><PERSON><PERSON>ig"}, "sbi_err_network": {"message": "Netwerkfout, zorg ervoor dat u de website kunt bezoeken"}, "sbi_err_not_low_version": {"message": "De nieuwste versie is geïnstalleerd ($latestVersion$)", "placeholders": {"latestVersion": {"content": "$1"}}}, "sbi_err_try_again": {"message": "<PERSON><PERSON><PERSON> opnieuw"}, "sbi_err_try_again_action": {"message": "<PERSON><PERSON><PERSON> opnieuw"}, "sbi_err_visit_and_try": {"message": "Probeer het opnieuw of ga naar $website$ om het opnieuw te proberen", "placeholders": {"website": {"content": "$1"}}}, "sbi_err_visit_site": {"message": "Ga naar de startpagina van $siteName$", "placeholders": {"siteName": {"content": "$1"}}}, "sbi_fail_tip": {"message": "Het laden is mislukt. <PERSON><PERSON><PERSON><PERSON> de pagina en probeer het opnieuw."}, "sbi_kuajing_filter_area": {"message": "<PERSON><PERSON><PERSON>"}, "sbi_kuajing_filter_au": {"message": "Australië"}, "sbi_kuajing_filter_btn_confirm": {"message": "Bevestigen"}, "sbi_kuajing_filter_de": {"message": "Duitsland"}, "sbi_kuajing_filter_destination_country": {"message": "Land van bestemming"}, "sbi_kuajing_filter_es": {"message": "Spanje"}, "sbi_kuajing_filter_estimate": {"message": "Sc<PERSON><PERSON>"}, "sbi_kuajing_filter_estimate_price": {"message": "Geschatte prijs"}, "sbi_kuajing_filter_estimate_price_formula": {"message": "Geschatte prijsformule = (grondstofprijs + internationale logistieke vracht)/(1 - winstmarge - overige kostenratio)"}, "sbi_kuajing_filter_fr": {"message": "<PERSON><PERSON><PERSON>"}, "sbi_kuajing_filter_kw_placeholder": {"message": "<PERSON><PERSON><PERSON> trefwo<PERSON> in die overeenkomen met de titel"}, "sbi_kuajing_filter_logistics": {"message": "Logistiek sjabloon"}, "sbi_kuajing_filter_logistics_china_post": {"message": "China Post Luchtpost:"}, "sbi_kuajing_filter_logistics_discount": {"message": "Logistieke korting"}, "sbi_kuajing_filter_logistics_epacket": {"message": "e-pakket"}, "sbi_kuajing_filter_logistics_price_formula": {"message": "Internationale logistieke vracht = (gewicht x verzendkosten + registratiekosten) x (1 - korting)"}, "sbi_kuajing_filter_others_fee": {"message": "andere vergo<PERSON>"}, "sbi_kuajing_filter_profit_percent": {"message": "Winstmarge"}, "sbi_kuajing_filter_prop": {"message": "attributen"}, "sbi_kuajing_filter_ru": {"message": "Rusland"}, "sbi_kuajing_filter_total": {"message": "Overeenkomen met $count$ vergelijkbare items", "placeholders": {"count": {"content": "$1"}}}, "sbi_kuajing_filter_uk": {"message": "VK"}, "sbi_kuajing_filter_usa": {"message": "Amerika"}, "sbi_login_punish_title__pdd_pifa": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON> g<PERSON><PERSON><PERSON>"}, "sbi_msg_no_result": {"message": "<PERSON><PERSON> g<PERSON>,log in op $loginSite$ of probeer een andere foto", "placeholders": {"loginSite": {"content": "$1"}}}, "sbi_msg_no_result_check_support_page_l1": {"message": "Tijdelijk niet beschikbaar voor Safari, gebruik $supportPage$.", "placeholders": {"supportPage": {"content": "$1"}}}, "sbi_msg_no_result_check_support_page_l2": {"message": "Chrome-browser en zijn extensies"}, "sbi_msg_no_result_reinstall_l1": {"message": "<PERSON><PERSON> resultaten gevo<PERSON>, log in op $loginSite$ of probeer een andere foto, of installeer de laatste versie $latestExtUrl$ opnieuw", "placeholders": {"loginSite": {"content": "$1"}, "latestExtUrl": {"content": "$2"}}}, "sbi_msg_no_result_reinstall_l2": {"message": "Laatste versie", "placeholders": {}}, "sbi_search_by_selected_area": {"message": "G<PERSON><PERSON><PERSON>d gebied"}, "sbi_shipping_": {"message": "Verzending op dezelfde dag"}, "sbi_specify_category": {"message": "Specificeer categorie:"}, "sbi_start_crop": {"message": "Selecteer gebied"}, "sbi_store_name_alibabaCNKuaJing": {"message": "1688 overzee"}, "sbi_tutorial_btn_more": {"message": "<PERSON><PERSON> man<PERSON>en"}, "sbi_tutorial_find_coupons_on_taobao": {"message": "Taobao-coupons vinden"}, "sbi_txt__empty_retry": {"message": "Sorry, geen resultaten gevonden, probeer het opnieuw."}, "sbi_txt__min_order": {"message": "<PERSON><PERSON> bestellen"}, "sbi_visiting": {"message": "B<PERSON>en"}, "sbi_yiwugo__jiagexiangtan": {"message": "Neem contact op met de verkoper voor de prijs"}, "sbi_yiwugo__qigou": {"message": "$num$ Stuks (MOQ)", "placeholders": {"num": {"content": "$1"}}}, "sbi_yiwugo__xing": {"message": "<PERSON><PERSON><PERSON>"}, "searchByImage_screenshot": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> met <PERSON><PERSON>"}, "searchByImage_search": {"message": "<PERSON><PERSON> met <PERSON><PERSON> klik naar de<PERSON> items"}, "searchByImage_size_type": {"message": "De bestandsgrootte mag niet groter zijn dan $num$ MB, alleen $type$", "placeholders": {"num": {"content": "$1"}, "type": {"content": "$2"}}}, "search_by_image_progress_analysing": {"message": "Afbeelding analyseren"}, "search_by_image_progress_searching": {"message": "Zoek naar producten"}, "search_by_image_progress_sending": {"message": "Afbeelding verzenden"}, "search_by_image_response_rate": {"message": "Reactieratio: $responseRate$ van kopers die contact hebben opgenomen met deze leverancier, hebben binnen $responseInHour$ uur een reactie ontvangen.", "placeholders": {"responseRate": {"content": "$1"}, "responseInHour": {"content": "$2"}}}, "search_by_keyword": {"message": "Zoeken op trefwoord:"}, "select_country_language_modal_title_country": {"message": "Land"}, "select_country_language_modal_title_language": {"message": "Taal"}, "select_country_region_modal_title": {"message": "Selecteer een land / regio"}, "select_language_modal_title": {"message": "Selecteer een taal:"}, "select_shop": {"message": "Selecteer winkel"}, "sellers_count": {"message": "Aantal verkopers op de huidige pagina"}, "sellers_count_per_page": {"message": "Aantal verkopers op de huidige pagina"}, "service_score": {"message": "Uitgebreide servicebeoordeling"}, "set_shortcut_keys": {"message": "Sneltoetsen instellen"}, "setting_logo_title": {"message": "Winkelassistent"}, "setting_modal_options_position_title": {"message": "Plug-in positie"}, "setting_modal_options_position_value_left": {"message": "Linkerhoek"}, "setting_modal_options_position_value_right": {"message": "<PERSON><PERSON><PERSON> hoek"}, "setting_modal_options_theme_title": {"message": "<PERSON><PERSON> kleur"}, "setting_modal_options_theme_value_dark": {"message": "<PERSON><PERSON>"}, "setting_modal_options_theme_value_light": {"message": "Licht"}, "setting_modal_title": {"message": "Instellingen"}, "setting_options_country_title": {"message": "Land / regio"}, "setting_options_hover_zoom_desc": {"message": "Beweeg uw muis om in te zoomen"}, "setting_options_hover_zoom_title": {"message": "Be<PERSON>eg de zoom"}, "setting_options_jd_coupon_desc": {"message": "Coupon gevonden op JD.com"}, "setting_options_jd_coupon_title": {"message": "JD.com coupon"}, "setting_options_language_title": {"message": "Taal"}, "setting_options_price_drop_alert_desc": {"message": "<PERSON><PERSON> de prij<PERSON> van producten in My Favorite zakt, ontvang je een pushmelding."}, "setting_options_price_drop_alert_title": {"message": "Prijsdaling alert"}, "setting_options_price_history_on_list_page_desc": {"message": "Toon prijsgeschiedenis op productzoekpagina"}, "setting_options_price_history_on_list_page_title": {"message": "Prijsgeschiedenis (lijstpagina)"}, "setting_options_price_history_on_produt_page_desc": {"message": "Geef de productgeschiedenis weer op de productdetailpagina"}, "setting_options_price_history_on_produt_page_title": {"message": "P<PERSON>js<PERSON><PERSON><PERSON><PERSON> (detailpagina)"}, "setting_options_sales_analysis_desc": {"message": "Ondersteuningsstatistieken van prijs, verkoopvolume, aantal verkopers en winkelverkoopratio op de $platforms$ productlijstpagina", "placeholders": {"platforms": {"content": "$1"}}}, "setting_options_sales_analysis_title": {"message": "Verkoopanalyse"}, "setting_options_save_success_msg": {"message": "Succes"}, "setting_options_tacking_price_title": {"message": "Prijswijzigingswaarschuwing"}, "setting_options_value_off": {"message": "Uit"}, "setting_options_value_on": {"message": "<PERSON><PERSON>"}, "setting_pkg_quick_view_desc": {"message": "Ondersteuning: 1688 en Taobao"}, "setting_saved_message": {"message": "Veranderingen succesvol opgeslagen"}, "setting_section_enable_platform_title": {"message": "<PERSON><PERSON> uit"}, "setting_section_setting_title": {"message": "Instellingen"}, "setting_section_shortcuts_title": {"message": "Snelkoppelingen"}, "settings_aliprice_agent__desc": {"message": "Weergegeven op de productdetailpagina van $platforms$", "placeholders": {"platforms": {"content": "$1"}}}, "settings_aliprice_agent__title": {"message": "<PERSON><PERSON> voor mij"}, "settings_copy_link__desc": {"message": "Weergeven op productdetailpagina"}, "settings_copy_link__title": {"message": "Knop Kopiëren en Titel zoeken"}, "settings_currency_desc__for_detail": {"message": "Ondersteuning 1688 productdetailpagina"}, "settings_currency_desc__for_list": {"message": "Zoeken op afbeelding (inclusief 1688/1688 in het buitenland/Taobao)"}, "settings_currency_desc__for_sbi": {"message": "Selecteer de prijs"}, "settings_currency_desc_display_for_list": {"message": "Getoond in zoeken naar afbeeldingen (inclusief 1688/1688 in het buitenland/Taobao)"}, "settings_currency_rate_desc": {"message": "Wisselkoers bijwerken van \"$currencyRateFrom$\"", "placeholders": {"currencyRateFrom": {"content": "$1"}}}, "settings_currency_rate_from_boc": {"message": "Bank van China"}, "settings_download_images__desc": {"message": "Ondersteuning voor het downloaden van afbeeldingen van $platforms$", "placeholders": {"platforms": {"content": "$1"}}}, "settings_download_images__title": {"message": "knop afbeelding downloaden"}, "settings_download_reviews__desc": {"message": "Weergegeven op de productdetailpagina van $platforms$", "placeholders": {"platforms": {"content": "$1"}}}, "settings_download_reviews__title": {"message": "Download recensie-af<PERSON>ldingen"}, "settings_google_translate_desc": {"message": "<PERSON><PERSON> met de rechtermuisknop om de Google Translate-balk te krijgen"}, "settings_google_translate_title": {"message": "vertaling van web<PERSON>'s"}, "settings_historical_trend_desc": {"message": "Weergeven in de rechterbenedenhoek van de afbeelding op de productlijstpagina"}, "settings_modal_btn_more": {"message": "<PERSON><PERSON> instellingen"}, "settings_productInfo_desc": {"message": "<PERSON><PERSON> meer gedetailleerde productinformatie weer op de productlijstpagina. Als u dit inschakelt, kan de belasting van de computer toenemen en kan er paginavertraging optreden. Als dit de prestaties beïnvlo<PERSON>t, wordt aanbevolen om het uit te schakelen."}, "settings_product_recommend__desc": {"message": "Wordt weergegeven onder de hoofdafbeelding op de pagina met productgegevens van $platforms$", "placeholders": {"platforms": {"content": "$1"}}}, "settings_product_recommend__name": {"message": "Producten aanbevolen"}, "settings_research_desc": {"message": "Vraag meer gedetailleerde informatie op de productlijstpagina"}, "settings_sbi_add_to_list": {"message": "Toevoegen aan $listType$", "placeholders": {"listType": {"content": "$1"}}}, "settings_sbi_detail_page_icon_title_desc": {"message": "Afbeelding zoekresultaat miniatuur"}, "settings_sbi_remove_from_list": {"message": "Verwijderen uit de $listType$", "placeholders": {"listType": {"content": "$1"}}}, "settings_search_by_image_add_to_blacklist": {"message": "Toevoegen aan blo<PERSON>keerlijst"}, "settings_search_by_image_adjust_entrance_entrance": {"message": "Ingangspositie aanpassen"}, "settings_search_by_image_blacklist_desc": {"message": "Toon geen pictogram op websites in de zwarte lijst."}, "settings_search_by_image_blacklist_title": {"message": "Blokkeerlijst"}, "settings_search_by_image_bottom_left": {"message": "Linksonder"}, "settings_search_by_image_bottom_right": {"message": "Rechtsonder"}, "settings_search_by_image_clear_blacklist": {"message": "Blokkeerlijst wissen"}, "settings_search_by_image_detail_page_icon_title": {"message": "Miniatuur"}, "settings_search_by_image_detail_page_icon_val_large": {"message": "groter"}, "settings_search_by_image_detail_page_icon_val_small": {"message": "<PERSON><PERSON>"}, "settings_search_by_image_display_button_desc": {"message": "<PERSON><PERSON> klik op het pictogram om op afbeelding te zoeken"}, "settings_search_by_image_display_button_title": {"message": "Pictogram op afbeeldingen"}, "settings_search_by_image_sourece_websites_desc": {"message": "Vind het bronproduct op deze websites"}, "settings_search_by_image_sourece_websites_title": {"message": "Zoeken op afbeeldingsresultaat"}, "settings_search_by_image_top_left": {"message": "Linksboven"}, "settings_search_by_image_top_right": {"message": "Rechtsboven"}, "settings_search_keyword_on_x__desc": {"message": "Zoek woorden op $platform$", "placeholders": {"platform": {"content": "$1"}}}, "settings_search_keyword_on_x__title": {"message": "Toon $platform$ pictogram bij geselecteerde woorden", "placeholders": {"platform": {"content": "$1"}}}, "settings_similar_products_desc": {"message": "Probeer hetzelfde product op die websites te vinden (max. 5)"}, "settings_similar_products_title": {"message": "Vind hetzelfde product"}, "settings_toolbar_expand_title": {"message": "Plug-in minimaliseert"}, "settings_top_toolbar_desc": {"message": "Zoekbalk bovenaan de pagina"}, "settings_top_toolbar_title": {"message": "Zoekbalk"}, "settings_translate_search_desc": {"message": "Vertaal in het Chinees en zoek"}, "settings_translate_search_title": {"message": "Meertalig zoeken"}, "settings_translator_contextmenu_title": {"message": "Vastleggen om te vertalen"}, "settings_translator_title": {"message": "Vertalen"}, "shai_xuan_dao_chu": {"message": "Filter om te exporteren"}, "shai_xuan_zi_duan": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "shang_jia_shi_jian": {"message": "Op planktijd"}, "shang_pin_biao_ti": {"message": "product titel"}, "shang_pin_dui_bi": {"message": "productvergelijking"}, "shang_pin_lian_jie": {"message": "productlink"}, "shang_pin_xin_xi": {"message": "Product informatie"}, "share_modal__content": {"message": "deel het met je vrienden"}, "share_modal__disable_for_while": {"message": "Ik wil niets delen"}, "share_modal__title": {"message": "Hou je van $extensionName$?", "placeholders": {"extensionName": {"content": "$1"}}}, "sheng_yu_ku_cun": {"message": "Resterend"}, "shi_fou_ke_ding_zhi": {"message": "Is het a<PERSON><PERSON><PERSON><PERSON>?"}, "shi_fou_ren_zheng_gong_ying_sha": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "shi_fou_ren_zheng_gong_ying_shang_zong_shu": {"message": "Gecertificeerde leveranciers"}, "shi_fou_you_mao_yi_dan_bao": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "shi_fou_you_mao_yi_dan_bao_zong_shu": {"message": "Handelsgara<PERSON><PERSON>"}, "shipping_fee": {"message": "Verzendkosten"}, "shop_followers": {"message": "Winkelvolgers"}, "shou_qi": {"message": "<PERSON><PERSON>"}, "similar_products_warn_max_platforms": {"message": "<PERSON> tot 5"}, "sku_calc_price": {"message": "Berekende prijs"}, "sku_calc_price_settings": {"message": "Instellingen voor berekende prijs"}, "sku_formula": {"message": "Formule"}, "sku_formula_desc": {"message": "Formulebeschrijving"}, "sku_formula_desc_text": {"message": "Ondersteunt complexe wiskundige formules, waarbij de oorspronkelijke prijs wordt weergegeven door A en de vracht door B.\n\n<br/>\n\nOndersteunt haakjes (), plus +, min -, vermenigvuldiging * en deling /\n\n<br/>\n\nVoorbeeld:\n\n<br/>\n\n1. Om de oorspronkelijke prijs 1,2 keer te berekenen en vervolgens de vrachtkosten toe te voegen, is de formule: A*1,2+B\n\n<br/>\n\n2. Om de oorspronkelijke prijs plus 1 yuan te berekenen en vervolgens met 1,2 te vermenigvuldigen, is de formule: (A+1)*1,2\n\n<br/>\n\n3. Om de oorspronkelijke prijs plus 10 yuan te berekenen, vervolgens met 1,2 te vermenigvuldigen en vervolgens 3 yuan af te trekken, is de formule: (A+10)*1,2-3"}, "sku_in_stock": {"message": "<PERSON> voorra<PERSON>"}, "sku_invalid_formula_format": {"message": "Ongeldige formule-indeling"}, "sku_inventory": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "sku_link_copy_fail": {"message": "Succesvol gekopieerd, SKU-specificaties en attributen zijn niet geselecteerd"}, "sku_link_copy_success": {"message": "Succesvol gekop<PERSON>erd, SKU-specificaties en attributen geselecteerd"}, "sku_list": {"message": "SKU-lijst"}, "sku_min_qrder_qty": {"message": "Minimale bestelhoe<PERSON><PERSON><PERSON><PERSON>"}, "sku_name": {"message": "SKU-naam"}, "sku_no": {"message": "Nr."}, "sku_original_price": {"message": "Oorspronkelijke prijs"}, "sku_price": {"message": "SKU-prijs"}, "stop_track_time_label": {"message": "Tracking deadline:"}, "suo_zai_di_qu": {"message": "plaats"}, "tab_pkg_quick_view": {"message": "Logistieke monitor"}, "tab_product_details_price_history": {"message": "Prijsgeschiedenis"}, "tab_product_details_reviews": {"message": "Fotorecensies"}, "tab_product_details_seller_analysis": {"message": "Verkopersanalyse"}, "tab_product_details_similar_products": {"message": "Dezelfde producten"}, "total_days_listed_per_product": {"message": "So<PERSON> van dagen op de plank ÷ aantal producten"}, "total_items": {"message": "Totaal aantal artikelen"}, "total_price_per_product": {"message": "So<PERSON> van prijzen ÷ aantal producten"}, "total_rating_per_product": {"message": "Totale beoordelingen ÷ aantal producten"}, "total_revenue": {"message": "Totale verkoop"}, "total_revenue40_items": {"message": "Totale verkoop van $amount$ producten op de huidige pagina", "placeholders": {"amount": {"content": "$1"}}}, "total_sales": {"message": "Totale verkoop"}, "total_sales40_items": {"message": "Totale verkoop van $amount$ producten op de huidige pagina", "placeholders": {"amount": {"content": "$1"}}}, "track_for_12_months": {"message": "Track voor: 1 jaar"}, "track_for_3_months": {"message": "Track voor: 3 maanden"}, "track_for_6_months": {"message": "Track voor: 6 maanden"}, "tracking_price_email_add_btn": {"message": "E-mailadres toe<PERSON>n"}, "tracking_price_email_edit_btn": {"message": "E-mail bewerken"}, "tracking_price_email_intro": {"message": "We zullen u via e-mail op de hoogte stellen."}, "tracking_price_email_invalid": {"message": "Geef een geldig e-mailadres op"}, "tracking_price_email_verified_desc": {"message": "U kunt nu onze prijsverlagingswaarschuwing ontvangen."}, "tracking_price_email_verified_title": {"message": "Succesvol geverifieerd"}, "tracking_price_email_verify_desc_line1": {"message": "We hebben een verificatielink naar uw e-mailadres gestuurd,"}, "tracking_price_email_verify_desc_line2": {"message": "controleer uw e-mailinbox."}, "tracking_price_email_verify_title": {"message": "Verifieer Email"}, "tracking_price_web_push_notification_intro": {"message": "Op desktop: AliPrice kan elk product voor u volgen en u een webpush-melding sturen zodra de prijs verandert."}, "tracking_price_web_push_notification_title": {"message": "Web push-meldingen"}, "translate_im__login_required": {"message": "Vertaald door AliPrice, log in op $loginUrl$", "placeholders": {"loginUrl": {"content": "$1"}}}, "translate_im__paste_fail": {"message": "Vertaald en gekopieerd naar het klembord, maar vanwege de beperking van Aliwangwang moet je het handmatig plakken!"}, "translate_im__send": {"message": "Vertalen en verzenden"}, "translate_search": {"message": "Vertalen en zoeken"}, "translation_originals_translated": {"message": "Origineel en Chinees"}, "translation_translated": {"message": "Chinese"}, "translator_btn_capture_txt": {"message": "Vertalen"}, "translator_language_auto_detect": {"message": "Automatische detectie"}, "translator_language_detected": {"message": "gedetecteerd"}, "translator_language_search_placeholder": {"message": "Zoektaal"}, "try_again": {"message": "<PERSON><PERSON><PERSON> het opnieuw"}, "tu_pian_chi_cun": {"message": "Afbeeldingsgrootte:"}, "tu_pian_lian_jie": {"message": "Afbeeldingslink"}, "tui_huan_ti_yan": {"message": "<PERSON><PERSON> ervar<PERSON>"}, "tui_huan_ti_yan__desc": {"message": "Beoordeel de after-salesindicatoren van verkopers"}, "tutorial__show_all": {"message": "Alle functies"}, "tutorial_ae_popup_title": {"message": "Pin de extensie, open Aliexpress"}, "tutorial_aliexpress_reviews_analysis": {"message": "AliExpress Review-analyse"}, "tutorial_aliprice_agent_with1688_desc_l1": {"message": "Ondersteuning USD"}, "tutorial_aliprice_agent_with1688_desc_l2": {"message": "Verzending naar Korea/Japan/vasteland China"}, "tutorial_aliprice_agent_with1688_title": {"message": "1688 Ondersteunt aankopen in het buitenland"}, "tutorial_auto_apply_coupon_title": {"message": "Coupon automatisch toepassen"}, "tutorial_btn_end": {"message": "Einde"}, "tutorial_btn_example": {"message": "Voorbeeld"}, "tutorial_btn_have_a_try": {"message": "<PERSON><PERSON>, probeer het eens"}, "tutorial_btn_next": {"message": "<PERSON><PERSON>"}, "tutorial_btn_see_more": {"message": "<PERSON><PERSON>"}, "tutorial_compare_products": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON> met <PERSON><PERSON><PERSON><PERSON> s<PERSON>l"}, "tutorial_currency_convert_title": {"message": "wisselkoers conversie"}, "tutorial_export_shopping_cart": {"message": "Exporteer CSV, ondersteun Taobao en 1688"}, "tutorial_export_shopping_cart_title": {"message": "winkelwagen exporteren"}, "tutorial_price_history_pro": {"message": "Weergegeven op de productdetailpagina.\nOndersteuning Shopee, Lazada, Amazon en Ebay"}, "tutorial_price_history_pro_title": {"message": "Prijsgeschiedenis en bestelgeschiedenis voor het hele jaar"}, "tutorial_sbi_context_menu_screenshot_title": {"message": "Screenshot zoeken voor dezelfde stijl"}, "tutorial_translate_search": {"message": "Vertalen om te zoeken"}, "tutorial_translate_search_and_package_tracking": {"message": "Zoeken naar vertalingen en volgen van pakketten"}, "unit_bao": {"message": "stuks"}, "unit_ben": {"message": "stuks"}, "unit_bi": {"message": "best<PERSON><PERSON>"}, "unit_chuang": {"message": "stuks"}, "unit_dai": {"message": "stuks"}, "unit_dui": {"message": "paar"}, "unit_fen": {"message": "stuks"}, "unit_ge": {"message": "stuks"}, "unit_he": {"message": "stuks"}, "unit_jian": {"message": "stuks"}, "unit_li_fang_mi": {"message": "m³"}, "unit_ping": {"message": "stuks"}, "unit_ping_fang_mi": {"message": "㎡"}, "unit_shuang": {"message": "paar"}, "unit_tai": {"message": "stuks"}, "unit_ti": {"message": "stuks"}, "unit_tiao": {"message": "stuks"}, "unit_xiang": {"message": "stuks"}, "unit_zhang": {"message": "stuks"}, "unit_zhi": {"message": "stuks"}, "verify_contact_support": {"message": "Contact opnemen met ondersteuning"}, "verify_human_verification": {"message": "Menselijke verificatie"}, "verify_unusual_access": {"message": "Ongebruikelijke toegang gedetecteerd"}, "view_history_clean_all": {"message": "Maak alles schoon"}, "view_history_clean_all_warring": {"message": "Alle bekeken records opschonen?"}, "view_history_clean_all_warring_title": {"message": "Waarschuwing"}, "view_history_viewd": {"message": "Bekeken"}, "website": {"message": "website"}, "weight": {"message": "gewicht"}, "wu_fa_huo_qu_dao_gai_shu_ju": {"message": "<PERSON>n de gegevens niet verkrijgen"}, "wu_liu_shi_xiao": {"message": "Op tijd verzending"}, "wu_liu_shi_xiao__desc": {"message": "Het 48-uurs ophaalpercentage en het uitvoeringspercentage van de wink<PERSON> van de ve<PERSON>oper"}, "xia_dan_jia": {"message": "Eindprijs"}, "xian_xuan_ze_product_attributes": {"message": "Selecteer de productkenmerken"}, "xiao_liang": {"message": "Omzet"}, "xiao_liang_zhan_bi": {"message": "Percentage van verkoopvolume"}, "xiao_shi": {"message": "$num$ uren", "placeholders": {"num": {"content": "$1"}}}, "xiao_shou_e": {"message": "Omzet"}, "xiao_shou_e_zhan_bi": {"message": "Per<PERSON><PERSON> van omzet"}, "xuan_zhong_x_tiao_ji_lu": {"message": "Selecteer $amount$ records", "placeholders": {"amoun": {"content": "$1"}, "amount": {"content": "$1"}}}, "yan_xuan": {"message": "<PERSON><PERSON>"}, "yi_ding_zai_zuo_ce": {"message": "Vastgezet"}, "yi_jia_zai_wan_suo_you_shang_pin": {"message": "Alle producten geladen"}, "yi_nian_xiao_liang": {"message": "Jaarlijkse omzet"}, "yi_nian_xiao_liang_zhan_bi": {"message": "Jaarlijkse omzetaandeel"}, "yi_nian_xiao_shou_e": {"message": "Jaarlijkse omzet"}, "yi_nian_xiao_shou_e_zhan_bi": {"message": "Jaarlijkse omzetaandeel"}, "yi_shua_xin": {"message": "Vernieuwd"}, "yin_cang_xiang_tong_dian": {"message": "overeenkomsten verbergen"}, "you_xiao_liang": {"message": "Met verkoopvolume"}, "yu_ji_dao_da_shi_jian": {"message": "Geschatte <PERSON>"}, "yuan_gong_ren_shu": {"message": "Aantal werknemers"}, "yue_cheng_jiao": {"message": "Maandelijks volume"}, "yue_dai_xiao": {"message": "Dropshipping"}, "yue_dai_xiao__desc": {"message": "Dropshipping-verkopen in de afgelopen 30 dagen"}, "yue_dai_xiao_pai_xu__desc": {"message": "Dropshipping-verkopen in de afgelopen 30 dagen, g<PERSON><PERSON><PERSON><PERSON> van hoog naar laag"}, "yue_xiao_liang__desc": {"message": "Verkoopvolume in de afgelopen 30 dagen"}, "zhan_kai": {"message": "<PERSON><PERSON>"}, "zhe_kou": {"message": "Korting"}, "zhi_chi_yi_jian_dai_fa": {"message": "Dropshipping"}, "zhi_chi_yi_jian_dai_fa_bao_you": {"message": "<PERSON><PERSON>"}, "zhi_fu_ding_dan_shu": {"message": "<PERSON><PERSON><PERSON>"}, "zhi_fu_ding_dan_shu__desc": {"message": "Aantal bestellingen voor dit product (30 dagen)"}, "zhu_ce_xing_zhi": {"message": "Registratie aard"}, "zi_ding_yi_tiao_jian": {"message": "Aangepaste voorwaarden"}, "zi_duan": {"message": "<PERSON><PERSON><PERSON>"}, "zi_ti_xiao_liang": {"message": "Variatie verkocht"}, "zong_he_fu_wu_fen": {"message": "<PERSON><PERSON><PERSON><PERSON> be<PERSON>"}, "zong_he_fu_wu_fen__desc": {"message": "Algemen<PERSON> be<PERSON> van de service van de verkoper"}, "zong_he_fu_wu_fen__short": {"message": "Beoordeling"}, "zong_he_ti_yan_fen": {"message": "Beoordeling"}, "zong_he_ti_yan_fen_3": {"message": "Onder de 4 sterren"}, "zong_he_ti_yan_fen_4": {"message": "4 - 4,5 sterren"}, "zong_he_ti_yan_fen_4_5": {"message": "4,5 - 5,0 sterren"}, "zong_he_ti_yan_fen_5": {"message": "5 sterren"}, "zong_ku_cun": {"message": "Totale inventaris"}, "zong_xiao_liang": {"message": "Totale verkoop"}, "zui_jin_30D_3Min_xiang_ying_lv": {"message": "Responspercentage van 3 minuten in de afgelopen 30 dagen"}, "zui_jin_30D_48H_lan_shou_lv": {"message": "Herstelpercentage van 48 uur in de afgelopen 30 dagen"}, "zui_jin_30D_48H_lv_yue_lv": {"message": "Prestatiepercentage van 48 uur in de afgelopen 30 dagen"}, "zui_jin_30D_jiao_yi_ji_lu": {"message": "Handelsrecord (30 dagen)"}, "zui_jin_30D_jiao_yi_ji_lu__desc": {"message": "Handelsrecord (30 dagen)"}, "zui_jin_30D_jiu_fen_lv": {"message": "Geschilpercentage in de afgelopen 30 dagen"}, "zui_jin_30D_pin_zhi_tui_kuan_lv": {"message": "Kwaliteitsterugbetalingspercentage in de afgelopen 30 dagen"}, "zui_jin_30D_zhi_fu_ding_dan_shu": {"message": "Het aantal betalingsopdrachten in de afgelopen 30 dagen"}}