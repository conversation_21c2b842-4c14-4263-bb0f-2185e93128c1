/**
 * @fileoverview (v2)
 * 全模块的类型定义中心。
 * 整合了原有的 extension-config-manager, i18n-manager, 和 helpers 的相关类型。
 */

import type { Browser } from 'wxt/browser';

// #region --- 基础枚举与字面量类型 ---

export type WebstoreType =
  | 'chrome'
  | 'firefox'
  | 'browser360'
  | 'safari'
  | 'adspower'
  | 'opera'
  | 'edge';

export type WebstoreCNType =
  | 'e-c'
  | 'e-f'
  | 'e-o'
  | 'e-360'
  | 'e-s'
  | 'e-ads'
  | 'e-edge'
  | WebstoreType;

export type VariantType = 'master' | 'tm' | 'tmBeta' | 'dba' | 'offline';

export type VariantChannel = WebstoreType | `${WebstoreType}_offline`;

export type ManifestVersionType = 2 | 3;

// #endregion

// #region --- 原始配置相关类型 (extension.config.ts) ---

/**
 * @description i18n 配置
 */
export interface I18nConfig {
  locales?: string[];
  includeKeys?: string[];
  excludeKeys?: string[];
  chromeOnlyLocales?: string[];
  chromeOnlyKeys?: string[];
}

/**
 * @description Manifest.json 的基础配置
 */
export type BaseManifestConfig = Omit<Browser.runtime.Manifest, 'manifest_version'> & {
  manifest_version: ManifestVersionType;
  default_locale?: string;
};

/**
 * @description 用户在 extension.config.ts 中提供的 Manifest 配置
 */
export type ManifestConfig = Partial<BaseManifestConfig>;

/**
 * @description 单个渠道包（变体）的配置
 */
export interface VariantConfig {
  variantId: string;
  variantName: string;
  variantType: VariantType;
  webstore: WebstoreType;
  webstoreId?: string;
  webstoreUrl?: string;
  measurementId?: string;
  manifestVersion?: ManifestVersionType;
  defaultLocale?: string;
  manifest?: Partial<BaseManifestConfig>;
  i18n?: Partial<I18nConfig>;
}

/**
 * @description 原始的扩展配置 (来自 extension.config.ts)
 */
export interface ExtensionConfig {
  name: string;
  version: string;
  manifestVersion?: ManifestVersionType;
  defaultLocale?: string;
  i18n?: I18nConfig;
  manifest?: ManifestConfig;
  variants: VariantConfig[];
  measurementId?: string;
}

// #endregion

// #region --- 处理后的配置类型 ---

/**
 * @description 经过处理后的 i18n 配置
 */
export interface ProcessedI18nConfig {
  locales: string[];
  includeKeys?: string[];
  excludeKeys?: string[];
  chromeOnlyLocales?: string[];
  chromeOnlyKeys?: string[];
  vueMessages: Record<string, Record<string, string>>;
  chromeMessages: Record<string, Record<string, { message: string }>>;
}

/**
 * @description 经过处理后的所有项目相关路径
 */
export interface ProcessedPaths {
  // 静态路径
  workspace: string;
  packages: string;
  extensions: string;
  shared: string;
  sharedLocales: string;
  dist: string;
  release: string;
  scripts: string;
  changelog: string;

  // '{workspace}/extensions/{extensionName}/'
  extensionRoot: string;

  // '{extensionRoot}/extension.config.ts'
  extensionRawConfig: string;

  // '{extensionRoot}/locales/'
  extensionRawLocales: string;

  // '{extensionRoot}/.variants/'
  extensionVariantsRoot: string;

  // '{extensionVariantsRoot}/{variantTarget}/'
  extensionVariantTargetRoot: string;

  // '{extensionVariantTargetRoot}/extension.config.json'
  extensionVariantTargetJSON: string;

  // '{extensionVariantTargetRoot}/i18n.json'
  extensionVariantTargetI18n: string;

  // '{extensionVariantTargetRoot}/public/_locales/'
  extensionVariantTargetLocales: string;

  // '{extensionRoot}/.manifests/'
  extensionManifestsRoot: string;

  // '{extensionManifestsRoot}/manifest.{variantTarget}.json'
  extensionVariantTargetManifest: string;

  // dev: '{workspace}/dist/{extensionName}/{version}/{variantTarget}/'
  // release: '{workspace}/release/{extensionName}/{version}/{variantTarget}/'
  extensionVariantTargetOutput: string;
}

/**
 * @description 最终处理完成的、用于输出的单个变体配置
 */
export interface ProcessedVariantConfig {
  name: string; // 扩展名称
  version: string; // 扩展版本
  manifestVersion: ManifestVersionType; // 最终确定的 manifest 版本
  defaultLocale: string; // 最终确定的默认语言
  variantID: string; // 渠道包ID，例如 'cookies_manager_master'
  variantName: string; // 渠道包名称，例如 'master'
  variantType: VariantType; // 渠道包类型，例如 'master'
  variantChannel: VariantChannel; // 渠道包类型标识，等同于 webstore。如果是 'offline' 则为 '{webstore}_offline'
  webstoreCN: WebstoreCNType; // 渠道包的浏览器标识，例如 'e-c' 或 'e-f'，没有就留空
  webstore: WebstoreType; // 目标浏览器，例如 'chrome' 或 'firefox'
  webstoreId?: string; // 浏览器商店 ID，例如 'fgfdgrifnekkkcbapdfandpixbdfhh'
  webstoreUrl?: string; // 浏览器商店 URL，留空就用 webstoreId 生成
  measurementId?: string; // GA 测量 ID，例如 'G-PJYG13CSDE'
  variantTarget: string; // 完整的渠道包标识，例如 'chrome-mv3-master'，用于文件名和路径
  i18n: ProcessedI18nConfig; // 合并后的 i18n 配置，不包含最终语言包内容（vueMessages和chromeMessages）
  manifest: BaseManifestConfig; // 合并后的 manifest 配置，不包含提权字段（manifest_version 和 default_locale）
  paths: ProcessedPaths; // 处理后的路径信息，包含所有相关路径
}

// #endregion

// #region --- 内部与辅助类型 ---

/**
 * @description 验证结果
 */
export interface ValidationResult {
  isValid: boolean;
  errors: string[];
  warnings: string[];
}

/**
 * @description 插件渠道包的结构化信息
 */
export interface VariantInfo {
  webstore: WebstoreType;
  mv: string;
  variant: VariantType;
  target: string;
  'webstore-variant': string;
}

/**
 * @description i18n 语言包消息
 */
export interface LocaleMessages {
  [key: string]: string;
}

/**
 * @description Chrome manifest 中使用的消息格式
 */
export interface ChromeMessage {
  message: string;
  description?: string;
  placeholders?: Record<string, { content: string; example?: string }>;
}

// #endregion
