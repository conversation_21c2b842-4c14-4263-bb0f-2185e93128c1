{"name": "wxt-cookies", "description": "wxt cookies manager", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "cross-env NODE_ENV=development tsx scripts/build/cli.ts dev", "build": "cross-env NODE_ENV=production tsx scripts/build/cli.ts build", "release": "cross-env NODE_ENV=production tsx scripts/changelog-release/cli.ts release", "commit": "cz", "compile": "vue-tsc --noEmit", "format": "prettier --write packages/**/*.{ts,vue,html,css}", "lint": "eslint packages/**/*.{ts,vue} --fix", "oxlint": "oxlint packages/**/*.{ts,vue} --fix", "prepare": "husky"}, "dependencies": {"@tailwindcss/vite": "^4.1.6", "@vueuse/core": "^13.1.0", "@webext-core/messaging": "^2.2.0", "@wxt-dev/i18n": "^0.2.4", "browserslist": "^4.24.5", "consola": "^3.4.2", "exceljs": "^4.4.0", "execa": "^9.6.0", "iconify-icon": "^3.0.0", "lodash-es": "^4.17.21", "loglevel": "^1.9.2", "loglevel-plugin-prefix": "^0.8.4", "pinia": "^3.0.2", "tailwindcss": "^4.1.6", "vue": "^3.5.13", "vue-i18n": "^11.1.6", "vue-router": "^4.5.1"}, "devDependencies": {"@commitlint/cli": "^19.8.1", "@commitlint/config-conventional": "^19.8.1", "@eslint/js": "^9.26.0", "@intlify/unplugin-vue-i18n": "^6.0.8", "@types/fs-extra": "^11.0.4", "@types/glob": "^8.1.0", "@types/lodash-es": "^4.17.12", "@types/node": "^24.0.4", "@types/semver": "^7.7.0", "@types/yargs": "^17.0.33", "@wxt-dev/auto-icons": "^1.0.2", "@wxt-dev/module-vue": "^1.0.2", "commitizen": "^4.3.1", "conventional-changelog-cli": "^5.0.0", "cross-env": "^7.0.3", "cz-conventional-changelog": "^3.3.0", "eslint": "^9.26.0", "eslint-plugin-vue": "^10.1.0", "fs-extra": "^11.3.0", "glob": "^11.0.3", "globals": "^16.1.0", "husky": "^9.1.7", "lightningcss": "^1.30.0", "lint-staged": "^16.0.0", "oxlint": "^0.16.10", "prettier": "3.5.3", "prettier-plugin-organize-imports": "^4.1.0", "prettier-plugin-tailwindcss": "^0.6.11", "semver": "^7.7.2", "standard-version": "^9.5.0", "tsx": "^4.20.3", "typescript": "5.6.3", "typescript-eslint": "^8.32.0", "vue-tsc": "^2.2.10", "wxt": "^0.20.7", "yargs": "^18.0.0"}, "engines": {"node": ">=22.16.0"}, "packageManager": "pnpm@10.12.4", "lint-staged": {"*.{ts,vue,html,css}": ["prettier --write", "oxlint --fix"]}}