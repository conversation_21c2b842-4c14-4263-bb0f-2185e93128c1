{"$schema": "./node_modules/oxlint/configuration_schema.json", "plugins": ["typescript"], "categories": {"correctness": "off"}, "env": {"builtin": true}, "rules": {"@typescript-eslint/ban-ts-comment": "error", "no-array-constructor": "error", "@typescript-eslint/no-duplicate-enum-values": "error", "@typescript-eslint/no-empty-object-type": "error", "@typescript-eslint/no-explicit-any": "warn", "@typescript-eslint/no-extra-non-null-assertion": "error", "@typescript-eslint/no-misused-new": "error", "@typescript-eslint/no-namespace": "error", "@typescript-eslint/no-non-null-asserted-optional-chain": "error", "@typescript-eslint/no-require-imports": "error", "@typescript-eslint/no-this-alias": "error", "@typescript-eslint/no-unnecessary-type-constraint": "error", "@typescript-eslint/no-unsafe-declaration-merging": "error", "@typescript-eslint/no-unsafe-function-type": "error", "no-unused-expressions": "error", "no-unused-vars": "error", "@typescript-eslint/no-wrapper-object-types": "error", "@typescript-eslint/prefer-as-const": "error", "@typescript-eslint/prefer-namespace-keyword": "error", "@typescript-eslint/triple-slash-reference": "error"}, "overrides": [{"files": ["**/*.{js,mjs,cjs,ts,vue}"], "rules": {"for-direction": "error", "no-async-promise-executor": "error", "no-case-declarations": "error", "no-class-assign": "error", "no-compare-neg-zero": "error", "no-cond-assign": "error", "no-const-assign": "error", "no-constant-binary-expression": "error", "no-constant-condition": "error", "no-control-regex": "error", "no-debugger": "error", "no-delete-var": "error", "no-dupe-class-members": "error", "no-dupe-else-if": "error", "no-dupe-keys": "error", "no-duplicate-case": "error", "no-empty": "error", "no-empty-character-class": "error", "no-empty-pattern": "error", "no-empty-static-block": "error", "no-ex-assign": "error", "no-extra-boolean-cast": "error", "no-fallthrough": "error", "no-func-assign": "error", "no-global-assign": "error", "no-import-assign": "error", "no-invalid-regexp": "error", "no-irregular-whitespace": "error", "no-loss-of-precision": "error", "no-new-native-nonconstructor": "error", "no-nonoctal-decimal-escape": "error", "no-obj-calls": "error", "no-prototype-builtins": "error", "no-redeclare": "error", "no-regex-spaces": "error", "no-self-assign": "error", "no-setter-return": "error", "no-shadow-restricted-names": "error", "no-sparse-arrays": "error", "no-this-before-super": "error", "no-unexpected-multiline": "error", "no-unsafe-finally": "error", "no-unsafe-negation": "error", "no-unsafe-optional-chaining": "error", "no-unused-labels": "error", "no-unused-private-class-members": "error", "no-useless-backreference": "error", "no-useless-catch": "error", "no-useless-escape": "error", "no-with": "error", "require-yield": "error", "use-isnan": "error", "valid-typeof": "error"}}, {"files": ["**/*.{js,mjs,cjs,ts,vue}"], "env": {"browser": true, "shared-node-browser": true}}, {"files": ["**/*.ts", "**/*.tsx", "**/*.mts", "**/*.cts"], "rules": {"no-class-assign": "off", "no-const-assign": "off", "no-dupe-class-members": "off", "no-dupe-keys": "off", "no-func-assign": "off", "no-import-assign": "off", "no-new-native-nonconstructor": "off", "no-obj-calls": "off", "no-redeclare": "off", "no-setter-return": "off", "no-this-before-super": "off", "no-unsafe-negation": "off", "no-var": "error", "no-with": "off", "prefer-rest-params": "error", "prefer-spread": "error"}}, {"files": ["**/*.{ts,tsx,js,jsx,vue}"], "rules": {"no-unused-vars": "off"}}]}