---
description: 
globs: 
alwaysApply: false
---
### 技术设计文档 (TDD)

#### 1. 系统架构

本系统采用 **“配置即代码 (Configuration as Code)”** 和 **“构建时生成 (Build-time Generation)”** 的核心思想。

**流程图**:
`用户执行 pnpm 命令` -> `自定义构建脚本 (scripts/build.ts)` -> `读取 extension.config.ts` -> `生成运行时文件 (.variants/...)` -> `调用 WXT 框架` -> `WXT 读取生成的文件并打包`

**核心组件**:

1.  **配置层 (`extension.config.ts`)**: 每个插件的声明式定义。
2.  **脚本层 (`scripts/`)**: 负责解析配置、处理业务逻辑（i18n, manifest合并）、生成运行时文件的自动化工具集。这是整个系统的“大脑”。
3.  **框架层 (`wxt`)**: 利用 WXT 的能力进行最后的编译、打包、热重载等工作。我们的脚本层为其准备好了所有输入。

#### 2. 关键实现细节

##### 2.1. 路径管理 (`scripts/helpers.ts`)

*   提供一个全局 `paths` 对象，包含所有重要的项目目录（根目录、`packages`, `scripts` 等）的绝对路径。
*   使用 `import.meta.url` 和 `fileURLToPath` 以兼容 Node.js ESM 模块规范。
*   所有其他脚本应通过此 `paths` 对象引用文件路径，以保证统一性。

##### 2.2. 类型定义 (`scripts/types.ts`)

*   定义项目中所有核心实体（`ExtensionConfig`, `VariantConfig`, `I18nConfig` 等）的 TypeScript 类型。
*   这些类型为 `extension.config.ts` 提供智能提示和类型检查，并作为脚本层处理数据的契约。

##### 2.3. 主构建脚本 (`scripts/build.ts`)

这是由 `pnpm` 命令直接调用的主入口脚本。

*   **职责**:
    1.  **解析输入**: 使用 `process.argv` 或 `minimist` 等库解析命令行参数，确定 `extensionName` 和 `target` (如 `chrome-mv3-master`)。
    2.  **加载配置**: 根据 `extensionName` 动态 `import()` 对应的 `packages/extensions/{extensionName}/extension.config.ts` 文件。
    3.  **生成运行时上下文**:
        *   从 `extensionConfig.variants` 数组中找到与 `target` 匹配的变体配置。
        *   深度合并 `extensionConfig.manifest` 和 `variant.manifest`，生成最终的 `manifest` 对象。注意处理 MV2 (`browser_action`) 和 MV3 (`action`) 的互斥关系。
        *   创建 `RuntimeExtensionInfo` 对象 (`extension.json` 的内容)。
    4.  **调用 I18n 脚本**: 执行 `scripts/i18n/build-i18n-files.ts`，并传入必要的上下文（插件路径、目标、i18n配置）。
    5.  **写入文件**:
        *   在插件目录下创建 `.variants/{target}` 目录。
        *   将生成的 `manifest` 对象序列化为 `manifest.json` 并写入。
        *   将 `RuntimeExtensionInfo` 对象序列化为 `extension.json` 并写入。
    6.  **执行 WXT**:
        *   设置环境变量 `WXT_WEBSTORE`, `WXT_MV`, `WXT_VARIANT`。
        *   使用 `execa` 或 `child_process.spawn` 调用 `wxt dev` 或 `wxt build` 命令，并将其 `cwd` (当前工作目录) 设置为目标插件的目录 (`packages/extensions/{extensionName}/`)。

##### 2.4. I18n 构建脚本 (`scripts/i18n/build-i18n-files.ts`)

*   **职责**: 负责所有语言包的转换和生成。
*   **输入**: 插件的 `i18n` 配置、公共语言包路径、插件语言包路径、目标输出路径 (`.variants/{target}/`)。
*   **逻辑**:
    1.  读取并合并公共语言包和插件语言包。
    2.  遍历每一个 key/value 对。
    3.  **处理条件消息**: 如果 `message` 是一个对象，则根据当前 `target` (e.g., `chrome-mv3-master`) 匹配最精确的文案。
    4.  **生成 Vue-i18n 文件 (`i18n.json`)**:
        *   将 `$placeholder$` 语法替换为 `{placeholder}`。
        *   将 `{ "key": { "message": "value" } }` 结构扁平化为 `{ "key": "value" }`。
        *   写入到 `.variants/{target}/i18n.json`。
    5.  **生成 Chrome Locales (`_locales/`)**:
        *   根据 `i18n.chromeOnlyKeys` 过滤 keys。
        *   对于每个保留的 message，解析其占位符 (e.g., `$msg$ $count$`)，并自动生成 `placeholders` 字段，如 `{ "msg": { "content": "$1" }, "count": { "content": "$2" } }`。
        *   写入到 `.variants/{target}/_locales/{lang}/messages.json`。

##### 2.5. WXT 集成 (`scripts/wxt-helper.ts` & `wxt.config.ts`)

*   **`scripts/wxt-helper.ts`**:
    *   导出一个 `defineProjectWxtConfig` 工厂函数。
    *   此函数内部读取 `process.env` (由 `build.ts` 设置) 来确定 `target`。
    *   **核心作用**: 动态计算出当前构建目标 `manifest.json` 的绝对路径 (`.../.variants/{target}/manifest.json`)，并将其传给 WXT 的 `manifest` 配置项。
    *   同时，它定义了所有插件共享的配置，如 `vite` 别名 (`@shared`)、输出目录 (`outDir`) 等。
*   **`packages/extensions/{name}/wxt.config.ts`**:
    *   每个插件的此文件都极其简洁。
    *   它只需 `import { defineProjectWxtConfig } from '...'` 并直接 `export default defineProjectWxtConfig()`。
    *   如果插件有特殊的 WXT 配置需求（如自定义 Vite 插件），可以作为参数传入 `defineProjectWxtConfig({})`。

#### 3. 最终目录结构

以下是整个 `my-extension-matrix` 项目的完整目标目录结构。此结构旨在实现关注点分离，最大化代码复用，并支持自动化构建流程。

```plaintext
my-extension-matrix/
├── .gitignore
├── package.json
├── pnpm-workspace.yaml
├── tsconfig.json
│
├── packages/
│   ├── extensions/ # 存放所有独立的插件项目
│   │   └── cookies_manager/ # 示例：一个名为 "cookies_manager" 的插件
│   │       ├── .manifest/ # (自动生成) 归档每一次发布的 manifest.json，用于版本对比
│   │       │   └── manifest.chrome-mv3-master.json
│   │       │
│   │       ├── .output/ # (自动生成 by WXT) 存放打包后的产物
│   │       │   └── chrome-mv3-master/
│   │       │       ├── manifest.json
│   │       │       ├── background.js
│   │       │       └── ... (其他打包产物)
│   │       │
│   │       ├── .variants/ # (自动生成) 存放为每个渠道插件生成的运行时文件
│   │       │   └── chrome-mv3-master/ # 示例：一个渠道插件的运行时目录
│   │       │       ├── _locales/ # (自动生成) Chrome Extension 格式的语言包
│   │       │       │   └── en/
│   │       │       │       └── messages.json
│   │       │       ├── i18n.json # (自动生成) Vue-i18n 格式的语言包
│   │       │       ├── extension.json # (自动生成) 渠道插件的最终元数据
│   │       │       └── manifest.json # (自动生成) 渠道插件最终的清单文件
│   │       │
│   │       ├── public/ # (可选) 存放需要原样复制到插件根目录的静态资源
│   │       │   └── some-asset.txt
│   │       │
│   │       ├── src/ # 插件的源代码
│   │       │   ├── assets/ # 图片、字体、CSS等资源
│   │       │   ├── components/ # Vue 组件
│   │       │   ├── composables/ # Vue Composables
│   │       │   ├── entrypoints/ # WXT 入口点 (background, content-script, popup, etc.)
│   │       │   │   ├── background.ts
│   │       │   │   ├── popup.html
│   │       │   │   └── popup.ts
│   │       │   ├── icons/ # 插件图标
│   │       │   │   ├── icon-16.png
│   │       │   │   ├── icon-48.png
│   │       │   │   └── icon-128.png
│   │       │   ├── locales/ # 插件专属的语言包 (源文件)
│   │       │   │   ├── en.json
│   │       │   │   └── zh_CN.json
│   │       │   └── utils/ # 工具函数
│   │       │
│   │       ├── changelog.ts # (可选) 插件的更新日志
│   │       ├── extension.config.ts # 核心！插件的配置文件
│   │       ├── tsconfig.json # 插件的 TypeScript 配置
│   │       └── wxt.config.ts # 插件的 WXT 配置文件
│   │
│   └── shared/ # 存放跨插件共享的模块
│       ├── components/ # 共享 Vue 组件
│       ├── composables/ # 共享 Vue Composables
│       ├── locales/ # 共享的公共语言包
│       │   ├── en.json
│       │   └── zh_CN.json
│       └── utils/ # 共享的工具函数
│
└── scripts/ # 存放项目构建和自动化脚本
    ├── i18n/ # 专门处理国际化的脚本
    │   ├── build-i18n-files.ts # 合并和转换语言包的主脚本
    │   ├── excel-json.ts # Excel 和 JSON 互转的工具
    │   ├── helpers.ts # i18n 相关的辅助函数
    │   ├── types.ts # i18n 相关的类型定义
    │   ├── messages.template.xlsx # 翻译工作流的 Excel 模板
    │   └── messages.xlsx # 翻译工作流的 Excel 文件
    │
    ├── build.ts # 核心！项目主构建脚本
    ├── constants.ts # 项目范围内的常量 (如支持的浏览器列表)
    ├── helpers.ts # 通用的辅助函数 (如路径管理)
    ├── release-extensions.json # 用于 `pnpm release` 命令的配置文件
    ├── types.ts # 项目范围内的核心类型定义
    └── wxt-helper.ts # WXT 公共配置工厂函数
```
