import fs from 'fs-extra';
import { execaCommand } from 'execa';
import readline from 'readline';
import { createLogger } from '../helpers/logger.js';
import { getCommitsSinceTag, getLatestTagForPackage, getExtensionPackages } from './git.js';
import { parseCommit } from './commit-parser.js';
import type { ParsedCommit } from './types.js';
import { checkIssues, loadIssueStatuses } from './issue-checker.js';
import { projectPaths } from '../helpers/utils.js';
import path from 'path';
import { determineNextVersion, formatChangelog } from './generator.js';

const logger = createLogger('发布流程');

/**
 * 显示交互式确认提示
 *
 * 该函数创建一个交互式提示，要求用户确认是否继续执行发布操作：
 * - 显示完整的发布计划信息
 * - 等待用户输入 y/Y 确认或 n/N 取消
 * - 默认为取消操作以确保安全
 * - 使用 readline 接口进行用户交互
 *
 * @param {string} message - 要显示给用户的确认消息
 * @returns {Promise<boolean>} 用户确认结果，true 表示确认，false 表示取消
 *
 * @example
 * // 显示发布确认提示
 * const confirmed = await askForConfirmation('是否继续执行发布操作？');
 * if (confirmed) {
 *   // 执行发布
 * } else {
 *   // 取消发布
 * }
 */
async function askForConfirmation(message: string): Promise<boolean> {
  return new Promise((resolve) => {
    const rl = readline.createInterface({
      input: process.stdin,
      output: process.stdout,
    });

    rl.question(`${message} (y/N): `, (answer) => {
      rl.close();
      const confirmed = answer.toLowerCase() === 'y' || answer.toLowerCase() === 'yes';
      resolve(confirmed);
    });
  });
}

export interface ReleasePlan {
  extensionName: string;
  lastVersion: string;
  nextVersion: string;
  commits: ParsedCommit[];
}

/**
 * 生成发布计划
 *
 * 该函数分析所有插件包的提交历史，生成发布计划：
 * - 获取所有插件包的信息和提交历史
 * - 为每个插件包查找最新标签和相关提交
 * - 根据提交内容确定是否需要发布新版本
 * - 支持强制包含指定插件（即使没有新提交）
 * - 支持手动指定版本号覆盖自动计算的版本
 * - 返回包含所有需要发布插件的计划列表
 *
 * @param {string[]} includeExtensions - 强制包含的插件列表，即使没有新提交也会包含在发布计划中
 * @param {Record<string, string>} versionOverrides - 手动指定的版本覆盖映射
 *   键为插件名，值为指定的版本号
 * @returns {Promise<ReleasePlan[]>} 发布计划数组，每个计划包含：
 *   - extensionName: 插件名称
 *   - lastVersion: 上一个版本号
 *   - nextVersion: 下一个版本号
 *   - commits: 相关的提交记录
 *
 * @example
 * // 生成标准发布计划
 * const plans = await generateReleasePlan();
 * // 返回: [{ extensionName: 'cookies_manager', lastVersion: '1.0.0', nextVersion: '1.1.0', commits: [...] }]
 *
 * @example
 * // 强制包含特定插件并指定版本
 * const plans = await generateReleasePlan(
 *   ['stable_extension'],
 *   { 'cookies_manager': '2.0.0' }
 * );
 */
export async function generateReleasePlan(
  includeExtensions: string[] = [],
  versionOverrides: Record<string, string> = {},
): Promise<ReleasePlan[]> {
  const plans: ReleasePlan[] = [];
  const packages = await getExtensionPackages();
  const allCommitsRaw = await getCommitsSinceTag(null); // 获取所有提交
  const allCommits = allCommitsRaw.map(parseCommit).filter((c): c is ParsedCommit => c !== null);

  for (const pkg of packages) {
    const lastTag = await getLatestTagForPackage(pkg.name);
    const lastVersion = lastTag ? lastTag.split('-v')[1] : '0.0.0';

    const commitsForPackage = allCommits.filter((c) => c.appliesTo.includes(pkg.name));

    if (commitsForPackage.length > 0 || includeExtensions.includes(pkg.name)) {
      const autoVersion = determineNextVersion(lastVersion, commitsForPackage);
      const nextVersion = versionOverrides[pkg.name] || autoVersion;

      plans.push({
        extensionName: pkg.name,
        lastVersion,
        nextVersion,
        commits: commitsForPackage,
      });
    }
  }

  return plans;
}

/**
 * 执行发布流程
 *
 * 该函数是发布系统的主入口，执行完整的发布流程：
 * - 解析命令行选项，处理版本覆盖设置
 * - 生成发布计划并检查是否有需要发布的变更
 * - 加载 issue 状态文件并检查未完成的 issue
 * - 根据选项决定是否强制发布或仅预演
 * - 执行实际的发布操作（更新文件、创建提交和标签）
 *
 * @param {Object} options - CLI 选项对象
 * @param {boolean} options.dryRun - 是否为预演模式，true 时不会实际修改文件
 * @param {boolean} options.force - 是否强制发布，忽略 issue 状态警告
 * @param {string} [options.include] - 强制包含的插件名称
 * @param {string | string[]} [options.setVersion] - 手动指定版本，格式为 "extension@version"
 * @returns {Promise<void>} 无返回值，通过日志输出执行结果
 *
 * @example
 * // 标准发布流程
 * await runRelease({ dryRun: false, force: false });
 *
 * @example
 * // 预演模式
 * await runRelease({ dryRun: true, force: false });
 *
 * @example
 * // 强制发布特定插件并指定版本
 * await runRelease({
 *   dryRun: false,
 *   force: true,
 *   include: 'cookies_manager',
 *   setVersion: 'cookies_manager@2.0.0'
 * });
 */
export async function runRelease(options: {
  dryRun: boolean;
  force: boolean;
  include?: string;
  setVersion?: string | string[];
}) {
  const versionOverrides: Record<string, string> = {};
  if (options.setVersion) {
    const versions = Array.isArray(options.setVersion) ? options.setVersion : [options.setVersion];
    for (const ver of versions) {
      const [name, version] = ver.split('@');
      if (name && version) {
        versionOverrides[name] = version;
      }
    }
  }

  logger.info('正在生成发布计划...');
  const includeExtensions = options.include ? [options.include] : [];
  const releasePlan = await generateReleasePlan(includeExtensions, versionOverrides);

  if (releasePlan.length === 0) {
    logger.info('没有检测到需要发布的变更。');
    return;
  }

  logger.info('发布计划:');
  for (const plan of releasePlan) {
    const versionSource = versionOverrides[plan.extensionName] ? '手动指定' : '自动计算';
    logger.info(
      `  - ${plan.extensionName}: ${plan.lastVersion} -> ${plan.nextVersion} (${versionSource})`,
    );
  }

  const issueStatuses = await loadIssueStatuses(
    path.join(projectPaths.workspace, 'issue_status.json'),
  );
  const warnings = checkIssues(
    releasePlan.flatMap((p) => p.commits),
    issueStatuses,
  );

  if (warnings.length > 0) {
    logger.warn('警告：检测到未完成的 Issue:');
    warnings.forEach((w) => logger.warn(`  - ${w}`));
    if (!options.force) {
      logger.error('因警告而中止发布。可使用 --force 标志忽略。');
      return;
    }
  }

  if (options.dryRun) {
    logger.warn('预演完成。未作任何修改。');
    return;
  }

  // 显示发布确认提示
  logger.info('\n=== 发布确认 ===');
  logger.info('即将执行以下发布操作：');
  for (const plan of releasePlan) {
    const versionSource = versionOverrides[plan.extensionName] ? '手动指定' : '自动计算';
    logger.info(
      `  - ${plan.extensionName}: ${plan.lastVersion} -> ${plan.nextVersion} (${versionSource})`,
    );
    logger.info(`    提交数量: ${plan.commits.length}`);
  }
  logger.info('\n操作内容：');
  logger.info('  1. 更新各插件的 changelog.json 文件（添加新版本记录）');
  logger.info('  2. 创建 git 提交记录');
  logger.info('  3. 创建对应的 git 标签');
  logger.info('\n注意：此操作将修改文件并创建 git 提交，无法轻易撤销！');

  const confirmed = await askForConfirmation('\n确认执行发布操作吗？');
  if (!confirmed) {
    logger.info('发布操作已取消。');
    return;
  }

  logger.info('开始执行发布操作...');
  await executeReleasePlan(releasePlan);
}

/**
 * 执行发布计划，修改文件并创建 git 提交和标签
 *
 * 该函数执行实际的发布操作：
 * 1. 循环所有计划，更新每个插件的 changelog.json 文件。
 * 2. 将所有被修改的 changelog.json 文件一次性添加到 git。
 * 3. 创建一个包含所有变更的原子 git 提交。
 * 4. 为每个发布的插件创建对应的 git 标签。
 *
 * @param {ReleasePlan[]} releasePlan - 发布计划数组，包含要发布的插件信息
 * @returns {Promise<void>} 无返回值，通过日志输出执行结果
 */
async function executeReleasePlan(releasePlan: ReleasePlan[]) {
  const now = new Date();
  const changelogPaths: string[] = [];
  const releaseSummary = releasePlan.map((p) => `${p.extensionName} v${p.nextVersion}`).join(', ');

  // 1. 更新所有 changelog.json 文件
  for (const plan of releasePlan) {
    logger.info(`正在更新 ${plan.extensionName} 的 changelog.json...`);
    const changelogJsonPath = path.join(
      projectPaths.extensions,
      plan.extensionName,
      'changelog.json',
    );
    changelogPaths.push(changelogJsonPath);

    const jsonEntry = {
      version: plan.nextVersion,
      releaseDate: now.toISOString().split('T')[0], // YYYY-MM-DD
      releaseAt: now.getTime(), // 时间戳
      notes: formatChangelog(plan.commits),
    };

    let existingChangelog: unknown[] = [];
    try {
      if (await fs.pathExists(changelogJsonPath)) {
        existingChangelog = await fs.readJson(changelogJsonPath);
        if (!Array.isArray(existingChangelog)) {
          existingChangelog = [];
        }
      }
    } catch (error) {
      logger.warn(`读取现有 changelog.json 失败: ${error}`);
      existingChangelog = [];
    }

    const updatedChangelogJson = [jsonEntry, ...existingChangelog];
    await fs.writeJson(changelogJsonPath, updatedChangelogJson, { spaces: 2 });
  }

  // 2. 提交所有变更
  logger.info('正在创建 git 提交...');
  await execaCommand(`git add ${changelogPaths.map((p) => `"${p}"`).join(' ')}`);
  await execaCommand(`git commit -m "chore(release): publish ${releaseSummary}"`);

  // 3. 创建所有标签
  for (const plan of releasePlan) {
    logger.info(`正在为 ${plan.extensionName} 创建 git 标签...`);
    await execaCommand(`git tag ${plan.extensionName}-v${plan.nextVersion}`);
  }

  logger.success(`发布完成！请运行 'git push --follow-tags' 来推送变更。`);
}
