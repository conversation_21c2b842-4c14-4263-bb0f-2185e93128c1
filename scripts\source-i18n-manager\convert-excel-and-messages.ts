import ExcelJS from 'exceljs';
import fs from 'fs-extra';
import { isEmpty } from 'lodash-es';
import path from 'path';
import { Logger } from '../helpers/logger.js';
import { projectPaths } from '../helpers/utils.js';
import {
  collectKeys,
  convertExcelToJsonPlaceholders,
  convertJsonToExcelPlaceholders,
  filterMessageKeys,
  getBaseKeys,
  getExistingLocales,
  getNestedValue,
  jsonFileExists,
  readJson,
  validateTranslationEntries,
  writeJson,
} from './helpers.js';
import type { Excel2I18nOptions, ExcelData, FlatMessageEntry, I18n2ExcelOptions } from './types.js';

Logger.setVerbose(true);
const logger = Logger.create('ExcelI18nManager');

export const TRANSLATE_EXCEL_FILE_PATH = path.resolve(
  projectPaths.scripts,
  './source-i18n-manager/messages.xlsx',
);
export const TEMPLATE_TRANSLATE_EXCEL_FILE_PATH = path.resolve(
  projectPaths.scripts,
  './source-i18n-manager/messages.template.xlsx',
);
export const TRANSLATE_SHEET_NAME = 'translate';
export const TEMPLATE_COLUMNS = [
  'message key',
  'Group Notes',
  'Notes',
  'Requirement',
  'am',
  'ar',
  'bg',
  'bn',
  'ca',
  'cs',
  'da',
  'de',
  'el',
  'en',
  'en_GB',
  'en_US',
  'es',
  'es_419',
  'et',
  'fa',
  'fi',
  'fil',
  'fr',
  'gu',
  'he',
  'hi',
  'hr',
  'hu',
  'id',
  'it',
  'ja',
  'kn',
  'ko',
  'lt',
  'lv',
  'ml',
  'mr',
  'ms',
  'my',
  'ne',
  'nl',
  'no',
  'pl',
  'pt_BR',
  'pt_PT',
  'ro',
  'ru',
  'si',
  'sk',
  'sl',
  'sr',
  'sv',
  'sw',
  'ta',
  'te',
  'th',
  'tr',
  'uk',
  'vi',
  'zh_CN',
  'zh_TW',
];

/**
 * 将 Excel 翻译文件转换为多个 i18n JSON 文件
 *
 * 功能说明：
 * 1. 读取 Excel 文件中的翻译数据
 * 2. 校验翻译条目的有效性和占位符一致性
 * 3. 将有效的翻译数据转换为嵌套的 JSON 格式
 * 4. 合并到现有的语言包文件中
 *
 * @param localesDir - 输出 JSON 文件的目标目录
 * @param options - 转换选项配置
 * @param options.autoAddNewLocale - 是否自动添加新语言文件，默认为 false
 * @param options.sheetName - 指定 Excel 工作表名称，默认为 'translate'
 * @throws 如果处理过程中出现错误，会抛出异常
 */
export async function excel2i18n(
  localesDir: string,
  options: Excel2I18nOptions = {},
): Promise<void> {
  const excelPath = TRANSLATE_EXCEL_FILE_PATH;
  const startTime = Date.now();
  let hasErrors = false;

  try {
    logger.info('开始 Excel 转 JSON 处理');

    // 设置默认选项
    const opts = {
      autoAddNewLocale: false,
      sheetName: 'translate',
      ...options,
    };

    // 读取 Excel 文件
    const excelData = await readExcelFile(excelPath, opts.sheetName);
    if (!excelData) {
      throw new Error('无法读取 Excel 文件');
    }

    const { header, rows } = excelData;

    // 获取现有的语言列表
    const existingLocales = await getExistingLocales(localesDir);
    const allLocales = Array.from(header.localeColumns.keys());

    logger.info(`Excel 中发现 ${allLocales.length} 种语言: ${allLocales.join(', ')}`);
    logger.info(`目标目录中现有 ${existingLocales.length} 种语言: ${existingLocales.join(', ')}`);

    // 准备翻译条目进行批量校验
    const translationEntries: Array<{
      messageKey: string;
      requirement: string;
      translation: string;
      locale: string;
      rowNumber: number;
    }> = [];

    // 收集所有翻译条目
    for (const row of rows) {
      for (const [locale, translation] of Object.entries(row.translations)) {
        if (typeof translation === 'string' && translation.trim()) {
          translationEntries.push({
            messageKey: row.messageKey,
            requirement: row.requirement,
            translation,
            locale,
            rowNumber: row.rowNumber,
          });
        }
      }
    }

    // 批量校验
    logger.info(`开始校验 ${translationEntries.length} 个翻译条目`);
    const validationResult = validateTranslationEntries(translationEntries, rows);

    // 输出校验错误和警告
    for (const invalidEntry of validationResult.invalidEntries) {
      if (invalidEntry.validationResult.error) {
        logger.error(invalidEntry.validationResult.error);
        hasErrors = true;
      } else if (invalidEntry.validationResult.warning) {
        logger.warn(invalidEntry.validationResult.warning);
      }
    }

    // 按语言组织有效的翻译数据
    const localeData: Record<string, Record<string, string>> = {};

    for (const entry of validationResult.validEntries) {
      const { locale, messageKey, translation } = entry;

      // 检查是否需要处理这个语言
      const shouldProcess =
        opts.autoAddNewLocale ||
        existingLocales.includes(locale) ||
        jsonFileExists(localesDir, locale);

      if (!shouldProcess) {
        logger.warn(`跳过语言 ${locale}：文件不存在且 autoAddNewLocale 为 false`);
        continue;
      }

      if (!localeData[locale]) {
        localeData[locale] = {};
      }

      // 转换占位符格式：Excel -> JSON
      const convertedTranslation = convertExcelToJsonPlaceholders(translation);
      localeData[locale][messageKey] = convertedTranslation;
    }

    // 转换为嵌套格式并写入文件
    const nestedLocaleData: Record<string, Record<string, unknown>> = {};
    for (const [locale, flatData] of Object.entries(localeData)) {
      /**
       * {
  EXTENSION_NAME: 'AliPrice Search by Image for 1688',
  'EXTENSION_NAME.chrome-mv3-master': '1688 search by image',
  'EXTENSION_NAME.chrome-tm': '8s94df89sf498',
  context_menu_screenshot_search: 'test $placeholer1$  $placeholer2$'
} => 

    {
  EXTENSION_NAME: {
  message: 'AliPrice Search by Image for 1688',
  'chrome-mv3-master': '1688 search by image',
  'chrome-tm': '8s94df89sf498',
  }
  }
       */
      nestedLocaleData[locale] = {};
      for (const [key, value] of Object.entries(flatData)) {
        const [messageKey, messageKeyFlag] = key.split('.');

        if (isEmpty(messageKeyFlag)) {
          nestedLocaleData[locale][messageKey] = {
            ...(nestedLocaleData[locale][messageKey] || {}),
            message: value,
          };
        } else {
          nestedLocaleData[locale][messageKey] = {
            ...(nestedLocaleData[locale][messageKey] || {}),
            [messageKeyFlag]: value,
          };
        }
      }
    }

    const writtenCount = await writeAllJsonFiles(localesDir, nestedLocaleData);

    const duration = Date.now() - startTime;

    // 输出处理结果
    logger.success(`处理完成：`);
    logger.info(`- 处理语言: ${Object.keys(localeData).length}`);
    logger.info(`- 写入文件: ${writtenCount}`);
    logger.info(`- 有效翻译: ${validationResult.summary.valid}`);
    logger.info(`- 跳过条目: ${validationResult.summary.invalid}`);
    logger.info(`- 耗时: ${duration}ms`);
  } catch (error) {
    logger.error(
      `Excel 转 JSON 处理失败: ${error instanceof Error ? error.message : String(error)}`,
    );
    hasErrors = true;
  }

  // 如果有错误，抛出异常
  if (hasErrors) {
    throw new Error('Excel 转 JSON 处理失败');
  }
}

/**
 * 将多个 i18n JSON 文件聚合到一个 Excel 文件中
 *
 * 功能说明：
 * 1. 读取指定目录下的所有 JSON 语言包文件
 * 2. 将嵌套的 JSON 结构转换为扁平化的消息条目
 * 3. 根据过滤规则筛选要导出的消息键
 * 4. 生成 Excel 文件，包含所有语言的翻译数据
 * 5. 支持基于模板的 Excel 文件生成
 *
 * @param localesDir - 包含源 .json 文件的目录
 * @param options - 转换选项配置
 * @param options.sourceLocale - 源语言代码，用于 Requirement 列，默认为 'en'
 * @param options.sheetName - 指定 Excel 工作表名称，默认为 'translate-{YYYYMMDD}'
 * @param options.includeKeys - 包含的消息键模式列表，为空表示包含所有
 * @param options.excludeKeys - 排除的消息键模式列表
 * @param options.duplicateSheetAction - 工作表重名时的处理方式：'overwrite' 或 'append'
 * @throws 如果处理过程中出现错误，会抛出异常
 */
export async function i18n2excel(
  localesDir: string,
  options: I18n2ExcelOptions = {},
): Promise<void> {
  const startTime = Date.now();
  let hasErrors = false;

  try {
    logger.info('开始 JSON 转 Excel 处理');

    // 检查目标 Excel 文件是否存在，如果不存在则从模板复制
    if (!(await fs.pathExists(TRANSLATE_EXCEL_FILE_PATH))) {
      logger.info(
        `目标 Excel 文件不存在，从模板复制: ${TEMPLATE_TRANSLATE_EXCEL_FILE_PATH} -> ${TRANSLATE_EXCEL_FILE_PATH}`,
      );

      if (!(await fs.pathExists(TEMPLATE_TRANSLATE_EXCEL_FILE_PATH))) {
        throw new Error(`模板 Excel 文件不存在: ${TEMPLATE_TRANSLATE_EXCEL_FILE_PATH}`);
      }

      // 确保目标目录存在
      await fs.ensureDir(path.dirname(TRANSLATE_EXCEL_FILE_PATH));

      // 复制模板文件
      await fs.copy(TEMPLATE_TRANSLATE_EXCEL_FILE_PATH, TRANSLATE_EXCEL_FILE_PATH);
      logger.success(`已从模板复制 Excel 文件`);
    }

    const excelPath = TRANSLATE_EXCEL_FILE_PATH;

    // 设置默认选项
    const opts = {
      sourceLocale: 'en',
      sheetName: `translate-${new Date().toISOString().slice(0, 10).replace(/-/g, '')}`,
      includeKeys: [],
      excludeKeys: [],
      duplicateSheetAction: 'overwrite' as const,
      ...options,
    };

    // 读取所有 JSON 文件
    const localeData = await readAllJsonFiles(localesDir);
    const locales = Object.keys(localeData).sort();

    if (locales.length === 0) {
      throw new Error('没有找到有效的 JSON 语言包文件');
    }

    logger.info(`发现 ${locales.length} 种语言: ${locales.join(', ')}`);

    // 转换为扁平化条目
    const flatEntries = convertToFlatEntries(localeData);
    const allBaseKeys = getBaseKeys(flatEntries);

    // 应用 message key 过滤
    const filteredBaseKeys = filterMessageKeys(allBaseKeys, opts.includeKeys, opts.excludeKeys);

    logger.info(`提取到 ${flatEntries.length} 个文案，${allBaseKeys.length} 个基础 key`);
    if (opts.includeKeys.length > 0 || opts.excludeKeys.length > 0) {
      logger.info(`过滤后保留 ${filteredBaseKeys.length} 个基础 key`);
    }

    // 构建数据行
    const rows: (string | number)[][] = [];

    // 按基础 key 分组处理（只处理过滤后的 keys）
    for (const baseKey of filteredBaseKeys) {
      const relatedEntries = flatEntries.filter((entry) => entry.baseKey === baseKey);

      // 按条件路径排序：基础消息在前，然后按字母顺序
      relatedEntries.sort((a, b) => {
        if (!a.conditionPath && !b.conditionPath) return 0;
        if (!a.conditionPath) return -1;
        if (!b.conditionPath) return 1;
        return a.conditionPath.localeCompare(b.conditionPath);
      });

      for (const entry of relatedEntries) {
        const row = buildRowFromTemplate(entry, opts.sourceLocale, localeData);
        rows.push(row);
      }
    }

    // 使用模板文件写入 Excel 文件
    const success = await writeExcelFileFromTemplate(
      excelPath,
      'template',
      excelPath,
      { headers: [], rows }, // headers 会从模板复制，这里传空数组
      opts.sheetName,
      opts.duplicateSheetAction,
    );

    if (!success) {
      throw new Error('写入 Excel 文件失败');
    }

    const duration = Date.now() - startTime;

    // 输出处理结果
    logger.success(`处理完成：`);
    logger.info(`- 处理语言: ${locales.length}`);
    logger.info(`- 文案: ${flatEntries.length}`);
    logger.info(`- Excel 行数: ${rows.length}`);
    logger.info(`- 输出文件: ${excelPath}`);
    logger.info(`- 耗时: ${duration}ms`);
  } catch (error) {
    logger.error(
      `JSON 转 Excel 处理失败: ${error instanceof Error ? error.message : String(error)}`,
    );
    hasErrors = true;
  }

  // 如果有错误，抛出异常
  if (hasErrors) {
    throw new Error('JSON 转 Excel 处理失败');
  }
}

/**
 * 读取 Excel 文件并解析为结构化数据
 * @param excelPath - Excel 文件路径
 * @param sheetName - 工作表名称
 * @returns 解析后的 Excel 数据对象，失败时返回 null
 */
async function readExcelFile(excelPath: string, sheetName: string): Promise<ExcelData | null> {
  try {
    if (!(await fs.pathExists(excelPath))) {
      logger.error(`Excel 文件不存在: ${excelPath}`);
      return null;
    }

    const workbook = new ExcelJS.Workbook();
    await workbook.xlsx.readFile(excelPath);

    const worksheet = workbook.getWorksheet(sheetName);
    if (!worksheet) {
      logger.error(`工作表 "${sheetName}" 不存在`);
      return null;
    }

    const rows: ExcelData['rows'] = [];
    const header: ExcelData['header'] = {
      messageKeyColumn: -1,
      requirementColumn: -1,
      localeColumns: new Map(),
    };

    // 解析表头（严格按照 TEMPLATE_COLUMNS 顺序）
    const headerRow = worksheet.getRow(1);
    headerRow.eachCell((_cell, colNumber) => {
      const templateCol = TEMPLATE_COLUMNS[colNumber - 1];
      if (!templateCol) return;
      if (templateCol === 'message key') {
        header.messageKeyColumn = colNumber;
      } else if (templateCol === 'Requirement') {
        header.requirementColumn = colNumber;
      } else if (
        templateCol !== 'Group Notes' &&
        templateCol !== 'Notes' &&
        templateCol.length >= 2
      ) {
        // 只将语言列加入
        header.localeColumns.set(templateCol, colNumber);
      }
    });

    if (header.messageKeyColumn === -1) {
      logger.error('未找到 "message key" 列');
      return null;
    }

    // 解析数据行
    worksheet.eachRow((row, rowNumber) => {
      if (rowNumber === 1) return; // 跳过表头

      const messageKey = String(row.getCell(header.messageKeyColumn).value || '').trim();
      if (!messageKey) return; // 跳过空行

      const requirement = String(row.getCell(header.requirementColumn).value || '').trim();

      const translations: Record<string, string> = {};
      for (const [locale, colIndex] of header.localeColumns) {
        const translation = String(row.getCell(colIndex).value || '').trim();
        translations[locale] = translation;
      }

      rows.push({
        messageKey,
        requirement,
        translations,
        rowNumber,
      });
    });

    return { header, rows };
  } catch (error) {
    logger.error(`读取 Excel 文件失败: ${error instanceof Error ? error.message : String(error)}`);
    return null;
  }
}

/**
 * 基于模板写入 Excel 文件
 * @param templatePath - 模板文件路径
 * @param templateSheetName - 模板工作表名称
 * @param outputPath - 输出文件路径
 * @param data - 要写入的数据
 * @param sheetName - 新工作表名称
 * @param duplicateSheetAction - 重名工作表的处理方式
 * @returns 是否写入成功
 */
async function writeExcelFileFromTemplate(
  templatePath: string,
  templateSheetName: string,
  outputPath: string,
  data: { headers: string[]; rows: (string | number)[][] },
  sheetName: string,
  duplicateSheetAction: 'overwrite' | 'append',
): Promise<boolean> {
  try {
    // 读取模板文件
    const workbook = new ExcelJS.Workbook();
    await workbook.xlsx.readFile(templatePath);

    const templateSheet = workbook.getWorksheet(templateSheetName);
    if (!templateSheet) {
      logger.error(`模板工作表 "${templateSheetName}" 不存在`);
      return false;
    }

    // 检查目标工作表是否存在
    let targetSheet = workbook.getWorksheet(sheetName);
    if (targetSheet) {
      if (duplicateSheetAction === 'overwrite') {
        workbook.removeWorksheet(targetSheet.id);
        targetSheet = undefined;
      } else {
        // append 模式，使用唯一名称
        let counter = 1;
        let newSheetName = `${sheetName}_${counter}`;
        while (workbook.getWorksheet(newSheetName)) {
          counter++;
          newSheetName = `${sheetName}_${counter}`;
        }
        sheetName = newSheetName;
      }
    }

    // 复制模板工作表
    if (!targetSheet) {
      targetSheet = workbook.addWorksheet(sheetName);

      // 复制模板的表头和格式
      templateSheet.eachRow((row, rowNumber) => {
        if (rowNumber === 1) {
          // 复制表头
          const newRow = targetSheet!.getRow(rowNumber);
          row.eachCell((cell, colNumber) => {
            newRow.getCell(colNumber).value = cell.value;
            // 复制样式
            newRow.getCell(colNumber).style = { ...cell.style };
          });
          return;
        }
      });
    }

    // 写入数据行
    data.rows.forEach((rowData, index) => {
      const rowNumber = index + 2; // 从第二行开始（第一行是表头）
      const row = targetSheet!.getRow(rowNumber);

      rowData.forEach((cellValue, colIndex) => {
        row.getCell(colIndex + 1).value = cellValue;
      });
    });

    // 保存文件
    await workbook.xlsx.writeFile(outputPath);
    return true;
  } catch (error) {
    logger.error(`写入 Excel 文件失败: ${error instanceof Error ? error.message : String(error)}`);
    return false;
  }
}

/**
 * 写入所有 JSON 文件，合并现有数据
 *
 * 特殊处理：
 * - 写入时将 EXTENSION_NAME 和 EXTENSION_DESCRIPTION 字段排序到最前面
 *
 * @param localesDir - 语言包目录
 * @param nestedLocaleData - 要写入的嵌套语言数据
 * @returns 成功写入的文件数量
 */
async function writeAllJsonFiles(
  localesDir: string,
  nestedLocaleData: Record<string, Record<string, unknown>>,
): Promise<number> {
  let writtenCount = 0;

  try {
    await fs.ensureDir(localesDir);

    for (const [locale, data] of Object.entries(nestedLocaleData)) {
      const filePath = path.join(localesDir, `${locale}.json`);
      const oldData = (await readJson(filePath)) || {};
      const mergedData = { ...oldData, ...data };

      // 对合并后的数据进行排序，EXTENSION_NAME 和 EXTENSION_DESCRIPTION 置顶
      const sortedData = sortJsonData(mergedData);

      if (await writeJson(filePath, sortedData)) {
        writtenCount++;
        logger.verbose(`写入文件: ${path.relative(projectPaths.workspace, filePath)}`);
      }
    }
  } catch (error) {
    logger.error(`写入 JSON 文件失败: ${error instanceof Error ? error.message : String(error)}`);
  }

  return writtenCount;
}

/**
 * 读取指定目录下的所有 JSON 语言包文件
 * @param localesDir - 语言包目录路径
 * @returns 语言包数据对象，键为语言代码，值为语言包内容
 */
async function readAllJsonFiles(
  localesDir: string,
): Promise<Record<string, Record<string, unknown>>> {
  const localeData: Record<string, Record<string, unknown>> = {};

  try {
    if (!(await fs.pathExists(localesDir))) {
      logger.warn(`语言包目录不存在: ${localesDir}`);
      return localeData;
    }

    const locales = await getExistingLocales(localesDir);

    for (const locale of locales) {
      const filePath = path.join(localesDir, `${locale}.json`);
      const data = await readJson(filePath);

      if (data) {
        localeData[locale] = data;
        logger.verbose(`读取文件: ${path.relative(projectPaths.workspace, filePath)}`);
      } else {
        logger.warn(`读取文件失败: ${path.relative(projectPaths.workspace, filePath)}`);
      }
    }
  } catch (error) {
    logger.error(`读取语言包目录失败: ${error instanceof Error ? error.message : String(error)}`);
  }

  return localeData;
}

/**
 * 将嵌套的语言包对象转换为扁平化的消息条目数组
 * @param localeData - 语言包数据对象，键为语言代码，值为嵌套的消息对象
 * @returns 扁平化的消息条目数组
 */
function convertToFlatEntries(
  localeData: Record<string, Record<string, unknown>>,
): FlatMessageEntry[] {
  const flatEntries: FlatMessageEntry[] = [];
  const allKeys = new Set<string>();

  // 收集所有可能的 key
  for (const data of Object.values(localeData)) {
    collectKeys(data, '', allKeys);
  }

  // 为每个 key 创建条目
  for (const key of allKeys) {
    const translations: Record<string, string> = {};

    for (const [locale, data] of Object.entries(localeData)) {
      const value = getNestedValue(data, key);
      if (typeof value === 'string') {
        translations[locale] = value;
      }
    }

    flatEntries.push({
      flatKey: /\.message$/.test(key) ? key.replace(/\.message$/, '') : key,
      baseKey: key.split('.')[0], // 简化的基础 key 提取
      translations,
    });
  }

  return flatEntries;
}

/**
 * 根据 Excel 模板结构构建数据行
 * @param entry - 扁平化的消息条目
 * @param sourceLocale - 源语言代码，用于 Requirement 列
 * @param localeData - 完整的语言包数据
 * @returns Excel 行数据数组，按 TEMPLATE_COLUMNS 顺序排列
 */
function buildRowFromTemplate(
  entry: FlatMessageEntry,
  sourceLocale: string,
  localeData: Record<string, Record<string, unknown>>,
): (string | number)[] {
  const row: (string | number)[] = [];

  for (const column of TEMPLATE_COLUMNS) {
    switch (column) {
      case 'message key':
        row.push(entry.flatKey);
        break;
      case 'Group Notes':
      case 'Notes':
        row.push(''); // 空值
        break;
      case 'Requirement': {
        const requirementText = getRequirementText(entry, sourceLocale, localeData);
        row.push(convertJsonToExcelPlaceholders(requirementText));
        break;
      }
      default: {
        // 语言列
        const translation = entry.translations[column] || '';
        const convertedTranslation = translation ? convertJsonToExcelPlaceholders(translation) : '';
        row.push(convertedTranslation);
        break;
      }
    }
  }

  return row;
}

/**
 * 获取 Excel Requirement 列的文本内容
 *
 * 优先级顺序：
 * 1. 当前条目的源语言翻译
 * 2. 基础条目的源语言翻译（如果当前是条件变体）
 * 3. 任何可用的翻译文本
 *
 * @param entry - 扁平化的消息条目
 * @param sourceLocale - 源语言代码
 * @param localeData - 完整的语言包数据
 * @returns Requirement 列的文本内容
 */
function getRequirementText(
  entry: FlatMessageEntry,
  sourceLocale: string,
  localeData: Record<string, Record<string, unknown>>,
): string {
  // 首先尝试从当前条目获取源语言翻译
  if (entry.translations[sourceLocale]) {
    return entry.translations[sourceLocale];
  }

  // 如果当前条目没有源语言翻译，尝试从基础条目获取
  if (entry.conditionPath) {
    const baseEntry = Object.values(localeData[sourceLocale] || {}).find((value: unknown) => {
      if (typeof value === 'object' && value !== null && 'message' in value) {
        return true;
      }
      return false;
    });

    if (
      baseEntry &&
      typeof baseEntry === 'object' &&
      baseEntry !== null &&
      'message' in baseEntry
    ) {
      return (baseEntry as { message?: string }).message || '';
    }
  }

  // 最后尝试使用任何可用的翻译
  const availableTranslations = Object.values(entry.translations).filter(
    (v): v is string => typeof v === 'string' && Boolean(v),
  );
  if (availableTranslations.length > 0) {
    return availableTranslations[0];
  }

  return '';
}

/**
 * 对 JSON 数据进行排序，将 EXTENSION_NAME 和 EXTENSION_DESCRIPTION 置顶
 * @param data - 要排序的数据
 * @returns 排序后的数据
 */
function sortJsonData(data: Record<string, unknown>): Record<string, unknown> {
  const sortedData: Record<string, unknown> = {};

  // 优先添加 EXTENSION_NAME 和 EXTENSION_DESCRIPTION
  if (data.EXTENSION_NAME !== undefined) {
    sortedData.EXTENSION_NAME = data.EXTENSION_NAME;
  }
  if (data.EXTENSION_DESCRIPTION !== undefined) {
    sortedData.EXTENSION_DESCRIPTION = data.EXTENSION_DESCRIPTION;
  }

  // 添加其他字段，按字母顺序排序
  const otherKeys = Object.keys(data)
    .filter((key) => key !== 'EXTENSION_NAME' && key !== 'EXTENSION_DESCRIPTION')
    .sort();

  for (const key of otherKeys) {
    sortedData[key] = data[key];
  }

  return sortedData;
}
