{"EXTENSION_NAME": {"message": "TODO: EXTENSION_NAME"}, "EXTENSION_DESCRIPTION": {"message": "TODO: EXTENSION_DESCRIPTION"}, "1688_cross_border_hot_selling": {"message": "1688 Sınır Ötesi Sıcak Satış Noktası"}, "1688_shi_li_ren_zheng": {"message": "1688 güç sertifikası"}, "1_jian_qi_pi": {"message": "MOQ: 1"}, "1year_yi_shang": {"message": "1 yıldan fazla"}, "24H": {"message": "24H"}, "24H_fa_huo": {"message": "24 saat içinde teslimat"}, "24H_lan_shou_lv": {"message": "24 saatlik Paketleme oranı"}, "30D_shang_xin": {"message": "Aylık Yeni <PERSON>ler"}, "30d_sales": {"message": "$amount$ 30 günde satıldı", "placeholders": {"amount": {"content": "$1"}}}, "3Min_xiang_ying_lv": {"message": "3 dakika içinde yanıt."}, "3Min_xiang_ying_lv__desc": {"message": "Wangwang'ın son 30 gün içinde alıcı sorgulama mesajlarına 3 dakika içinde verdiği etkili yanıtların oranı"}, "48H": {"message": "48 saat"}, "48H_fa_huo": {"message": "48 saat içinde teslimat"}, "48H_lan_shou_lv": {"message": "48 saatlik Paketleme hızı"}, "48H_lan_shou_lv__desc": {"message": "48 saat içinde alınan sipariş sayısının toplam sipariş sayısına oranı"}, "48H_lv_yue_lv": {"message": "48 saatlik performans oranı"}, "48H_lv_yue_lv__desc": {"message": "48 saat içinde teslim alınan veya teslim edilen sipariş sayısının toplam sipariş sayısına oranı"}, "72H": {"message": "72 saat"}, "7D_shang_xin": {"message": "Haftalık Yeni Gelenler"}, "7D_wu_li_you": {"message": "7 gün bakımsız"}, "ABS_title_text": {"message": "Bu listede bir marka hikayesi var"}, "AC_title_text": {"message": "Bu listede Amazon'un Seçimi rozeti var"}, "A_title_text": {"message": "Bu listede A+ içerik sayfası var"}, "BS_title_text": {"message": "Bu listede $type$ kategorisinde $num$ En Çok Satan Ürün olarak sıralanmıştır", "placeholders": {"type": {"content": "$1"}, "num": {"content": "$2"}}}, "LTD_title_text": {"message": "LTD (Sınırlı Süreli Fırsat), bu <PERSON><PERSON> \"7 günlük promosyon\" etkinliğinin bir parçası olduğu anlamına gelir"}, "NR_title_text": {"message": "Bu listede $type$ kategorisinde $num$ Yeni Çıkan Ürün olarak sıralanmıştır", "placeholders": {"type": {"content": "$1"}, "num": {"content": "$2"}}}, "SBV_title_text": {"message": "Bu listede genellikle arama sonuçlarının ortasında görünen bir PPC reklamı türü olan bir video reklamı var"}, "SB_title_text": {"message": "Bu listede genellikle arama sonuçlarının en üstünde veya en altında görünen bir PPC reklamı türü olan bir marka reklamı var"}, "SP_title_text": {"message": "Bu listede Sponsorlu Ürün reklamı var"}, "V_title_text": {"message": "Bu listede bir video tanıtımı var"}, "advanced_research": {"message": "Gelişmiş <PERSON>ştırma"}, "agent_ds1688___my_order": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "agent_ds1688__add_to_cart": {"message": "Yurtdışı satın alma"}, "agent_ds1688__cart": {"message": "Alışveriş Sepeti"}, "agent_ds1688__desc": {"message": "1688 tarafından sağlanmıştır. Yurt dışından doğrudan satın almayı, USD cinsinden ödemeyi ve Çin'deki transit deponuza teslimatı destekler."}, "agent_ds1688__freight": {"message": "Nakliye Maliyeti Hesaplayıcı"}, "agent_ds1688__help": {"message": "Yardım"}, "agent_ds1688__packages": {"message": "İrsaliye"}, "agent_ds1688__profile": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "agent_ds1688__warehouse": {"message": "<PERSON><PERSON><PERSON>"}, "ai_comment_analysis_advantage": {"message": "Artıları"}, "ai_comment_analysis_ai": {"message": "AI inceleme analizi"}, "ai_comment_analysis_available": {"message": "Mevcut"}, "ai_comment_analysis_balance": {"message": "<PERSON><PERSON><PERSON>, lüt<PERSON> yükleyin"}, "ai_comment_analysis_behavior": {"message": "Davranış"}, "ai_comment_analysis_characteristic": {"message": "Kalabalık özellikleri"}, "ai_comment_analysis_comment": {"message": "Ürünün doğru sonuçlara varmak için yeterli yorumu yok, lütfen daha fazla yorumu olan bir ürün seçin."}, "ai_comment_analysis_consume": {"message": "<PERSON><PERSON><PERSON>"}, "ai_comment_analysis_default": {"message": "Varsayılan incelemeler"}, "ai_comment_analysis_desire": {"message": "Müşteri beklentileri"}, "ai_comment_analysis_disadvantage": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "ai_comment_analysis_free": {"message": "Ücretsiz denemeler"}, "ai_comment_analysis_freeNum": {"message": "1 ücretsiz kredi kullanılacaktır"}, "ai_comment_analysis_go_recharge": {"message": "Ücret yüklemeye git"}, "ai_comment_analysis_intelligence": {"message": "Akıllı inceleme analizi"}, "ai_comment_analysis_location": {"message": "<PERSON><PERSON>"}, "ai_comment_analysis_motive": {"message": "Satın alma motivasyonu"}, "ai_comment_analysis_network_error": {"message": "<PERSON><PERSON>, lütfen tekrar deneyin"}, "ai_comment_analysis_normal": {"message": "Fotoğ<PERSON><PERSON>"}, "ai_comment_analysis_number_reviews": {"message": "İnceleme Sayısı: $num$, <PERSON><PERSON><PERSON>: $consume$", "placeholders": {"num": {"content": "$1"}, "consume": {"content": "$2"}}}, "ai_comment_analysis_ordinary": {"message": "<PERSON><PERSON>"}, "ai_comment_analysis_percentage": {"message": "<PERSON><PERSON>z<PERSON>"}, "ai_comment_analysis_problem": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "ai_comment_analysis_reanalysis": {"message": "Yeniden analiz et"}, "ai_comment_analysis_reason": {"message": "<PERSON><PERSON>"}, "ai_comment_analysis_recharge": {"message": "Ücret yükleme"}, "ai_comment_analysis_recharged": {"message": "Ücret y<PERSON>"}, "ai_comment_analysis_retry": {"message": "<PERSON><PERSON><PERSON> dene"}, "ai_comment_analysis_scene": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "ai_comment_analysis_start": {"message": "<PERSON><PERSON><PERSON> ba<PERSON>"}, "ai_comment_analysis_subject": {"message": "<PERSON><PERSON><PERSON>"}, "ai_comment_analysis_time": {"message": "<PERSON><PERSON><PERSON><PERSON> sü<PERSON>i"}, "ai_comment_analysis_tool": {"message": "Yapay zeka aracı"}, "ai_comment_analysis_user_portrait": {"message": "Kullanıcı profili"}, "ai_comment_analysis_welcome": {"message": "AI inceleme analizine hoş geldiniz"}, "ai_comment_analysis_year": {"message": "Geçtiğimiz yıldan yo<PERSON>"}, "ai_listing_Exclude_keywords": {"message": "<PERSON><PERSON><PERSON> k<PERSON> hari<PERSON> tut"}, "ai_listing_Login_the_feature": {"message": "Özellik için oturum açmanız gerekiyor"}, "ai_listing_aI_generation": {"message": "Yapay zeka üretimi"}, "ai_listing_add_automatic": {"message": "Otomatik"}, "ai_listing_add_dictionary_new": {"message": "Yeni bir kitaplık oluştur"}, "ai_listing_add_enter_keywords": {"message": "<PERSON><PERSON><PERSON> girin"}, "ai_listing_add_inputkey_selling": {"message": "Bir satış noktası girin ve eklemeyi tamamlamak için $key$ tuşuna basın", "placeholders": {"key": {"content": "$1"}}}, "ai_listing_add_inputkey_warning": {"message": "Sınır aşıldı; $amount$ satış noktasına kadar", "placeholders": {"amount": {"content": "$1"}}}, "ai_listing_add_keywords": {"message": "<PERSON><PERSON><PERSON>"}, "ai_listing_add_manually": {"message": "<PERSON>"}, "ai_listing_add_selling": {"message": "Satış noktaları ekleyin"}, "ai_listing_added_keywords": {"message": "<PERSON><PERSON><PERSON>"}, "ai_listing_added_successfully": {"message": "Başarıyla eklendi"}, "ai_listing_addexcluded_keywords": {"message": "<PERSON><PERSON> tutulan anahtar k<PERSON> giri<PERSON>, <PERSON><PERSON><PERSON><PERSON> ta<PERSON><PERSON><PERSON> için enter tuşuna basın."}, "ai_listing_adding_selling": {"message": "Satış noktaları eklendi"}, "ai_listing_addkeyword_enter": {"message": "<PERSON><PERSON><PERSON> nitelik sözcüklerini yazın ve eklemeyi tamamlamak için enter tuşuna basın"}, "ai_listing_ai_description": {"message": "AI açıklama kelime kitaplığı"}, "ai_listing_ai_dictionary": {"message": "AI başlık kelime kitaplığı"}, "ai_listing_ai_title": {"message": "AI başlığı"}, "ai_listing_aidescription_repeated": {"message": "AI açıklama sözcüğü kitaplığı adı tekrarlanamıyor"}, "ai_listing_aititle_repeated": {"message": "AI başlık kelime kitaplığı adı tekrarlanamıyor"}, "ai_listing_data_comes_from": {"message": "<PERSON><PERSON><PERSON> geliyor:"}, "ai_listing_deleted_successfully": {"message": "Başar<PERSON><PERSON>"}, "ai_listing_dictionary_name": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> adı"}, "ai_listing_edit_dictionary": {"message": "Kitaplığı değiştir..."}, "ai_listing_edit_word_library": {"message": "<PERSON><PERSON><PERSON> kitaplığını düzenleyin"}, "ai_listing_enter_keywords": {"message": "<PERSON><PERSON><PERSON> keli<PERSON> girin ve eklemeyi tamamlamak için $key$ tuşuna basın", "placeholders": {"key": {"content": "$1"}}}, "ai_listing_exceeded_maximum": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON> aşıldı, maksimum $amount$ anahtar kelime", "placeholders": {"amount": {"content": "$1"}}}, "ai_listing_excluded_word_library": {"message": "<PERSON><PERSON> tutulan kelime <PERSON>ığı"}, "ai_listing_generate_characters": {"message": "Karakterler oluştur"}, "ai_listing_generation_platform": {"message": "Nesil platformu"}, "ai_listing_help_optimize": {"message": "Ürün başlığını optimize etmeme yardımcı olun; orijinal başlık"}, "ai_listing_include_selling": {"message": "<PERSON><PERSON><PERSON> satış noktaları şunları içeriyordu:"}, "ai_listing_included_keyword": {"message": "<PERSON><PERSON> edilen anah<PERSON> keli<PERSON>er"}, "ai_listing_included_keywords": {"message": "<PERSON><PERSON> edilen anah<PERSON> keli<PERSON>er"}, "ai_listing_input_selling": {"message": "Bir satış noktası girin"}, "ai_listing_input_selling_fit": {"message": "Başlığa uygun satış noktalarını girin"}, "ai_listing_input_selling_please": {"message": "Lütfen satış noktalarını girin"}, "ai_listing_intelligently_title": {"message": "Başlığı akıllıca oluşturmak için gerekli içeriği yukarıya girin"}, "ai_listing_keyword_product_title": {"message": "<PERSON><PERSON><PERSON> k<PERSON> b<PERSON>"}, "ai_listing_keywords_repeated": {"message": "<PERSON><PERSON><PERSON> te<PERSON>"}, "ai_listing_listed_selling_points": {"message": "<PERSON><PERSON> edilen satış noktaları"}, "ai_listing_long_title_1": {"message": "<PERSON><PERSON>, ür<PERSON><PERSON>ü<PERSON>, ürün özellikleri vb. gibi temel bilgileri içerir."}, "ai_listing_long_title_2": {"message": "Standart ürün başlığının temeline SEO’ya yardımcı olacak anahtar kelimeler eklenir."}, "ai_listing_long_title_3": {"message": "Uzun kuyruklu anahtar keli<PERSON>, mark<PERSON>, <PERSON><PERSON><PERSON><PERSON> tü<PERSON>, ürün özellikleri ve anahtar kelimeleri içermenin yanı sıra, <PERSON><PERSON><PERSON><PERSON>, b<PERSON><PERSON>ümlere ayrılmış arama sorgularında daha yüksek sıralamalar elde etmek için de dahil edilir."}, "ai_listing_longtail_keyword_product_title": {"message": "Uzun kuyruklu anahtar kelime ürün başlığı"}, "ai_listing_manually_enter": {"message": "<PERSON> o<PERSON>ak girin..."}, "ai_listing_network_not_working": {"message": "İnternet mevcut de<PERSON>, ChatGPT'ye erişmek için VPN gerekiyor"}, "ai_listing_new_dictionary": {"message": "<PERSON><PERSON> bir keli<PERSON>i oluşturun..."}, "ai_listing_new_generate": {"message": "Oluştur"}, "ai_listing_optional_words": {"message": "İsteğe bağlı kelimeler"}, "ai_listing_original_title": {"message": "Orjinal başlık"}, "ai_listing_other_keywords_included": {"message": "Diğer anahtar kelimeler şunları içeriyordu:"}, "ai_listing_please_again": {"message": "Lütfen tekrar deneyin"}, "ai_listing_please_select": {"message": "Aşağıdaki başlıklar sizin için oluşturuldu, lütfen seçin:"}, "ai_listing_product_category": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "ai_listing_product_category_is": {"message": "<PERSON><PERSON><PERSON><PERSON> ka<PERSON>:"}, "ai_listing_product_category_to": {"message": "Ürün hangi kategoriye ait?"}, "ai_listing_random_keywords": {"message": "Rastgele $amount$ anahtar kelime", "placeholders": {"amount": {"content": "$1"}}}, "ai_listing_random_selling": {"message": "Rastgele $amount$ satış noktası", "placeholders": {"amount": {"content": "$1"}}}, "ai_listing_randomize_keywords": {"message": "<PERSON><PERSON><PERSON> kitaplığından rastgele seç"}, "ai_listing_search_selling": {"message": "Satış noktasına göre ara"}, "ai_listing_select_product_categories": {"message": "<PERSON><PERSON><PERSON><PERSON> ka<PERSON>gor<PERSON>ini otomatik olarak seçin."}, "ai_listing_select_product_selling_points": {"message": "Ürün satış noktalarını otomatik olarak seç"}, "ai_listing_select_word_library": {"message": "<PERSON><PERSON><PERSON> kitaplığını seçin"}, "ai_listing_selling": {"message": "Satış noktaları"}, "ai_listing_selling_ask": {"message": "Başlık için başka hangi satış noktası gereksinimleri var?"}, "ai_listing_selling_optional": {"message": "İsteğe bağlı satış noktaları"}, "ai_listing_selling_repeat": {"message": "<PERSON><PERSON><PERSON>"}, "ai_listing_set_excluded": {"message": "<PERSON><PERSON> tutulan kelime <PERSON>aplığı olarak ayarla"}, "ai_listing_set_include_selling_points": {"message": "Satış noktalarını dahil edin"}, "ai_listing_set_included": {"message": "<PERSON><PERSON> edilen kelime <PERSON>ı olarak ayarla"}, "ai_listing_set_selling_dictionary": {"message": "Satış noktası kitaplığı olarak ayarla"}, "ai_listing_standard_product_title": {"message": "Standart ürün ba<PERSON>ı<PERSON>ı"}, "ai_listing_translated_title": {"message": "Çevrilmiş Başlık"}, "ai_listing_visit_chatGPT": {"message": "ChatGPT'yi ziyaret edin"}, "ai_listing_what_other_keywords": {"message": "Başlık için başka hangi anahtar kelimeler gereklidir?"}, "aliprice_coupons_apply_again": {"message": "Tekrar Başvur"}, "aliprice_coupons_apply_coupons": {"message": "Kuponları uygula"}, "aliprice_coupons_apply_success": {"message": "Kupon bulundu: $amount$ kazanın", "placeholders": {"amount": {"content": "$1"}}}, "aliprice_coupons_applying": {"message": "En iyi fırsatlar için kodlar test ediliyor..."}, "aliprice_coupons_applying_desc": {"message": "Kontrol ediliyor: $code$", "placeholders": {"code": {"content": "$1"}}}, "aliprice_coupons_back_to_checkout": {"message": "<PERSON><PERSON><PERSON>"}, "aliprice_coupons_found_coupons": {"message": "$amount$ kupon bulduk", "placeholders": {"amount": {"content": "$1"}}}, "aliprice_coupons_found_coupons_desc": {"message": "Çıkış yapmaya hazır mısınız? En iyi fiyatı aldığınızdan emin olalım!"}, "aliprice_coupons_no_coupon_aviable": {"message": "O kodlar işe yaramadı. Önemli değil - zaten en iyi fiyatı alıyorsunuz."}, "aliprice_coupons_toolbar_btn": {"message": "<PERSON><PERSON><PERSON>"}, "aliww_translate": {"message": "Aliwangwang Sohbet Tercümanı"}, "aliww_translate_supports": {"message": "Destek: 1688 ve Taobao"}, "amazon_extended_keywords_Keywords": {"message": "<PERSON><PERSON><PERSON>"}, "amazon_extended_keywords_copy_all": {"message": "<PERSON><PERSON><PERSON> k<PERSON>"}, "amazon_extended_keywords_more": {"message": "<PERSON><PERSON>"}, "an_lei_ji_xiao_liang_pai_xu": {"message": "Kümülatif <PERSON> göre sırala"}, "an_lei_xing_cha_kan": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON> g<PERSON>"}, "an_yue_dai_xiao_pai_xu": {"message": "Dropshipping <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> gö<PERSON> s<PERSON>"}, "apra_btn__cat_name": {"message": "İnceleme analizi"}, "apra_chart__name": {"message": "Ülkeye göre ürün <PERSON>ın yüzdesi"}, "apra_chart__update_at": {"message": "Güncelleme z<PERSON>ı $time$", "placeholders": {"time": {"content": "$1"}}}, "apra_name": {"message": "Ülkelerin satış istatistikleri"}, "auto_opening": {"message": "$num$ saniye içinde otomatik olarak açılıyor", "placeholders": {"num": {"content": "$1"}}}, "auto_page_next_x_pages": {"message": "Sonraki $autoPaging$ sayfa", "placeholders": {"autoPaging": {"content": "$1"}}}, "average_days_listed": {"message": "Ortalama raf g<PERSON>i"}, "average_hui_fu_lv": {"message": "Ortalama yanıt oranı"}, "average_ping_gong_ying_shang_deng_ji": {"message": "Ortalama tedarikçi seviyesi"}, "average_price": {"message": "Ortal<PERSON>t"}, "average_qi_ding_liang": {"message": "<PERSON><PERSON><PERSON>"}, "average_rating": {"message": "Ortalama puanı"}, "average_ren_zheng_gong_ying_nian_chang": {"message": "Ortalama sertifikasyon yılı"}, "average_revenue": {"message": "<PERSON><PERSON><PERSON>"}, "average_revenue_per_product": {"message": "Toplam gelir ÷ Ürün sayısı"}, "average_sales": {"message": "Ortalama <PERSON>"}, "average_sales_per_product": {"message": "Toplam satışlar ÷ Ürün sayısı"}, "bao_han": {"message": "İçerir"}, "bao_zheng_jin": {"message": "kena<PERSON> b<PERSON>"}, "bian_ti_shu": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "biao_ti": {"message": "Başlık"}, "blacklist_add_blacklist": {"message": "Bu mağazayı engelle"}, "blacklist_address_incorrect": {"message": "Adres yanlış. Lütfen kontrol et."}, "blacklist_blacked_out": {"message": "Mağaza engellendi"}, "blacklist_blacklist": {"message": "<PERSON>e"}, "blacklist_no_records_yet": {"message": "He<PERSON><PERSON>z kayıt yok!"}, "blue_rocket": {"message": "로켓배송"}, "brand_name": {"message": "<PERSON><PERSON>"}, "btn_aliprice_agent__daigou": {"message": "Satınalma aracısı"}, "btn_aliprice_agent__dropshipping": {"message": "<PERSON><PERSON>"}, "btn_have_a_try": {"message": "<PERSON><PERSON><PERSON> dene"}, "btn_refresh": {"message": "<PERSON><PERSON><PERSON>"}, "btn_try_it_now": {"message": "<PERSON><PERSON><PERSON> dene"}, "btn_txt_view_on_aliprice": {"message": "AliPrice üzerinde görüntüle"}, "bu_bao_han": {"message": "İçermiyor"}, "bulk_copy_links": {"message": "Toplu Kopyalama Bağlantıları"}, "bulk_copy_products": {"message": "Toplu Kopyalama Ürünleri"}, "cai_gou_zi_xun": {"message": "Müşteri servisi"}, "cai_gou_zi_xun__desc": {"message": "Satıcının üç dakikalık yanıt oranı"}, "can_ping_lei_xing": {"message": "<PERSON><PERSON><PERSON>"}, "cao_zuo": {"message": "Operasyon"}, "chan_pin_ID": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "chan_pin_e_wai_xin_xi": {"message": "Ürün Ekstra Bilgi"}, "chan_pin_lian_jie": {"message": "Ürün Bağlantısı"}, "cheng_li_shi_jian": {"message": "Kuruluş zamanı"}, "city__aba": {"message": "Aba"}, "city__aksu": {"message": "<PERSON><PERSON><PERSON>"}, "city__alaer": {"message": "<PERSON><PERSON><PERSON>"}, "city__altai": {"message": "Altai"}, "city__alxaleague": {"message": "Alxa League"}, "city__ankang": {"message": "Ankan<PERSON>"}, "city__anqing": {"message": "Anqing"}, "city__anshan": {"message": "<PERSON><PERSON>"}, "city__anshun": {"message": "<PERSON><PERSON><PERSON>"}, "city__anyang": {"message": "Anyang"}, "city__baicheng": {"message": "Baicheng"}, "city__baise": {"message": "Baise"}, "city__baisha": {"message": "Baisha"}, "city__baishan": {"message": "Baishan"}, "city__baiyin": {"message": "<PERSON><PERSON><PERSON>"}, "city__baoding": {"message": "<PERSON>od<PERSON>"}, "city__baoji": {"message": "<PERSON><PERSON><PERSON>"}, "city__baoshan": {"message": "<PERSON><PERSON><PERSON>"}, "city__baoting": {"message": "Baoting"}, "city__baotou": {"message": "<PERSON><PERSON><PERSON>"}, "city__bayannur": {"message": "Bayannur"}, "city__bayinguoleng": {"message": "Bayinguoleng"}, "city__bazhong": {"message": "Bazhong"}, "city__beihai": {"message": "Beihai"}, "city__bengbu": {"message": "<PERSON><PERSON><PERSON>"}, "city__benxi": {"message": "Benxi"}, "city__bijie": {"message": "Bijie"}, "city__binzhou": {"message": "Binzhou"}, "city__bortala": {"message": "<PERSON><PERSON><PERSON>"}, "city__bozhou": {"message": "Bozhou"}, "city__cangzhou": {"message": "Cangzhou"}, "city__chamdo": {"message": "<PERSON><PERSON><PERSON>"}, "city__changchun": {"message": "<PERSON><PERSON><PERSON>"}, "city__changde": {"message": "<PERSON><PERSON>"}, "city__changhua": {"message": "Changhua"}, "city__changji": {"message": "Changji"}, "city__changjiang": {"message": "Changjiang"}, "city__changsha": {"message": "Changsha"}, "city__changzhi": {"message": "Changzhi"}, "city__changzhou": {"message": "Changzhou"}, "city__chaohu": {"message": "<PERSON><PERSON>"}, "city__chaoyang": {"message": "Chaoyang"}, "city__chaozhou": {"message": "Chaozhou"}, "city__chengde": {"message": "Chengde"}, "city__chengdu": {"message": "Chengdu"}, "city__chengmai": {"message": "<PERSON><PERSON><PERSON>"}, "city__chenzhou": {"message": "Chenzhou"}, "city__chiayi": {"message": "<PERSON><PERSON><PERSON>"}, "city__chifeng": {"message": "<PERSON><PERSON>"}, "city__chizhou": {"message": "Chizhou"}, "city__chongzuo": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__chuxiong": {"message": "Chuxiong"}, "city__chuzhou": {"message": "Chuzhou"}, "city__dali": {"message": "<PERSON><PERSON>"}, "city__dalian": {"message": "<PERSON><PERSON>"}, "city__dandong": {"message": "Dandong"}, "city__danzhou": {"message": "Danzhou"}, "city__daqing": {"message": "Daqing"}, "city__datong": {"message": "<PERSON><PERSON><PERSON>"}, "city__daxinganling": {"message": "<PERSON><PERSON>'<PERSON>ling"}, "city__dazhou": {"message": "Dazhou"}, "city__dehong": {"message": "<PERSON><PERSON>"}, "city__deyang": {"message": "Deyang"}, "city__dezhou": {"message": "Dezhou"}, "city__dingan": {"message": "Ding'an"}, "city__dingxi": {"message": "Dingxi"}, "city__diqing": {"message": "Diqing"}, "city__dongfang": {"message": "<PERSON><PERSON>g"}, "city__dongguan": {"message": "Dongguan"}, "city__dongying": {"message": "<PERSON><PERSON>"}, "city__enshi": {"message": "<PERSON><PERSON>"}, "city__ezhou": {"message": "Ezhou"}, "city__fangchenggang": {"message": "Fangchenggang"}, "city__foshan": {"message": "<PERSON><PERSON><PERSON>"}, "city__fushun": {"message": "<PERSON><PERSON><PERSON>"}, "city__fuxin": {"message": "Fuxin"}, "city__fuyang": {"message": "Fuyang"}, "city__fuzhou": {"message": "Fuzhou"}, "city__gannan": {"message": "<PERSON><PERSON><PERSON>"}, "city__ganzhou": {"message": "Ganzhou"}, "city__ganzi": {"message": "Ganzi"}, "city__guangan": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__guangyuan": {"message": "Guangyuan"}, "city__guangzhou": {"message": "Guangzhou"}, "city__guigang": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__guilin": {"message": "<PERSON><PERSON><PERSON>"}, "city__guiyang": {"message": "Guiyang"}, "city__guolok": {"message": "Guolok"}, "city__guyuan": {"message": "<PERSON><PERSON>"}, "city__haibei": {"message": "Haibei"}, "city__haidong": {"message": "Haidong"}, "city__haikou": {"message": "Hai<PERSON><PERSON>"}, "city__hainan": {"message": "Hainan"}, "city__haixi": {"message": "Haixi"}, "city__hami": {"message": "<PERSON><PERSON>"}, "city__handan": {"message": "<PERSON><PERSON>"}, "city__hangzhou": {"message": "Hangzhou"}, "city__hanzhong": {"message": "Hanzhong"}, "city__harbin": {"message": "<PERSON><PERSON><PERSON>"}, "city__hebi": {"message": "<PERSON><PERSON>"}, "city__hechi": {"message": "<PERSON><PERSON>"}, "city__hefei": {"message": "<PERSON><PERSON><PERSON>"}, "city__hegang": {"message": "<PERSON><PERSON><PERSON>"}, "city__heihe": {"message": "<PERSON><PERSON><PERSON>"}, "city__hengshui": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__hengyang": {"message": "Hengyang"}, "city__heyuan": {"message": "<PERSON><PERSON>"}, "city__heze": {"message": "<PERSON><PERSON>"}, "city__hezhou": {"message": "Hezhou"}, "city__hohhot": {"message": "Hohhot"}, "city__honghe": {"message": "<PERSON><PERSON>"}, "city__hongkongisland": {"message": "Hong Kong Island"}, "city__hotan": {"message": "<PERSON><PERSON>"}, "city__hsinchu": {"message": "<PERSON><PERSON><PERSON>"}, "city__huaian": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__huaibei": {"message": "<PERSON><PERSON><PERSON>"}, "city__huaihua": {"message": "Huaihua"}, "city__huaisouth": {"message": "Huai South"}, "city__hualien": {"message": "<PERSON><PERSON><PERSON>"}, "city__huanggang": {"message": "<PERSON><PERSON><PERSON>"}, "city__huangnan": {"message": "Huangnan"}, "city__huangshan": {"message": "<PERSON><PERSON>"}, "city__huangshi": {"message": "<PERSON><PERSON>"}, "city__huizhou": {"message": "Huizhou"}, "city__huludao": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__hulunbuir": {"message": "Hulunbuir"}, "city__huzhou": {"message": "Huzhou"}, "city__jiamusi": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__jian": {"message": "<PERSON><PERSON><PERSON>"}, "city__jiangmen": {"message": "Jiangmen"}, "city__jiaozuo": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__jiaxing": {"message": "Jiaxing"}, "city__jiayuguan": {"message": "Jiayuguan"}, "city__jieyang": {"message": "<PERSON><PERSON><PERSON>"}, "city__jilin": {"message": "<PERSON><PERSON>"}, "city__jinan": {"message": "<PERSON><PERSON>"}, "city__jinchang": {"message": "Jinchang"}, "city__jincheng": {"message": "Jincheng"}, "city__jingdezhen": {"message": "Jingdez<PERSON>"}, "city__jingmen": {"message": "Jingmen"}, "city__jingzhou": {"message": "Jingzhou"}, "city__jinhua": {"message": "Jinhua"}, "city__jining": {"message": "Jining"}, "city__jinzhong": {"message": "Jinzhong"}, "city__jinzhou": {"message": "Jinzhou"}, "city__jiujiang": {"message": "Jiujiang"}, "city__jiuquan": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__jixi": {"message": "Ji<PERSON>"}, "city__kaifeng": {"message": "Kaifeng"}, "city__kaohsiung": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__karamay": {"message": "<PERSON><PERSON><PERSON>"}, "city__kashgar": {"message": "Ka<PERSON><PERSON>"}, "city__keelung": {"message": "Keelung"}, "city__kinmen": {"message": "Kinmen"}, "city__kizilsu": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__kowloon": {"message": "Kowloon"}, "city__kunming": {"message": "Ku<PERSON><PERSON>"}, "city__laibin": {"message": "<PERSON><PERSON>"}, "city__laiwu": {"message": "<PERSON><PERSON>"}, "city__langfang": {"message": "<PERSON><PERSON><PERSON>"}, "city__lanzhou": {"message": "Lanzhou"}, "city__ledong": {"message": "<PERSON><PERSON>"}, "city__leshan": {"message": "<PERSON><PERSON>"}, "city__lhasa": {"message": "Lhasa"}, "city__liangshan": {"message": "Liangshan"}, "city__lianyungang": {"message": "Lianyungang"}, "city__liaocheng": {"message": "<PERSON><PERSON><PERSON>"}, "city__liaoyang": {"message": "<PERSON><PERSON><PERSON>"}, "city__liaoyuan": {"message": "<PERSON><PERSON><PERSON>"}, "city__lienchiang": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__lijiang": {"message": "Lijiang"}, "city__lincang": {"message": "Lincang"}, "city__linfen": {"message": "Linfen"}, "city__lingao": {"message": "Lingao"}, "city__lingshui": {"message": "<PERSON><PERSON><PERSON>"}, "city__linxia": {"message": "Lin<PERSON>"}, "city__linyi": {"message": "<PERSON><PERSON>"}, "city__lishui": {"message": "Li<PERSON><PERSON>"}, "city__liupanshui": {"message": "Liupanshu<PERSON>"}, "city__liuzhou": {"message": "Liuzhou"}, "city__longnan": {"message": "<PERSON><PERSON>"}, "city__longyan": {"message": "<PERSON><PERSON>"}, "city__loudi": {"message": "<PERSON><PERSON>"}, "city__luan": {"message": "<PERSON><PERSON><PERSON>"}, "city__luohe": {"message": "<PERSON><PERSON><PERSON>"}, "city__luoyang": {"message": "<PERSON><PERSON><PERSON>"}, "city__luzhou": {"message": "Luzhou"}, "city__lüliang": {"message": "<PERSON>眉liang"}, "city__maanshan": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__macauoutlyingislands": {"message": "Macau Outlying Islands"}, "city__macaupeninsula": {"message": "Macau Peninsula"}, "city__maoming": {"message": "<PERSON><PERSON>"}, "city__meishan": {"message": "<PERSON><PERSON>"}, "city__meizhou": {"message": "Meizhou"}, "city__mianyang": {"message": "Mianyang"}, "city__miaoli": {"message": "<PERSON><PERSON>"}, "city__mudanjiang": {"message": "Mudanjiang"}, "city__nagqu": {"message": "<PERSON>g<PERSON>u"}, "city__nanchang": {"message": "Nanchang"}, "city__nanchong": {"message": "<PERSON><PERSON><PERSON>"}, "city__nanjing": {"message": "Nanjing"}, "city__nanning": {"message": "Nanning"}, "city__nanping": {"message": "<PERSON><PERSON>"}, "city__nantong": {"message": "Nantong"}, "city__nantou": {"message": "<PERSON><PERSON><PERSON>"}, "city__nanyang": {"message": "Nanyang"}, "city__neijiang": {"message": "Neijiang"}, "city__newterritories": {"message": "New Territories"}, "city__ngari": {"message": "<PERSON><PERSON>"}, "city__ningbo": {"message": "Ningbo"}, "city__ningde": {"message": "<PERSON><PERSON><PERSON>"}, "city__nujiang": {"message": "Nujiang"}, "city__nyingchi": {"message": "Nyingchi"}, "city__ordos": {"message": "Ordos"}, "city__panjin": {"message": "Panjin"}, "city__panzhihua": {"message": "Pan Zhihua"}, "city__penghu": {"message": "<PERSON><PERSON><PERSON>"}, "city__pingdingshan": {"message": "Pingdingshan"}, "city__pingliang": {"message": "<PERSON><PERSON><PERSON>"}, "city__pingtung": {"message": "<PERSON><PERSON><PERSON>"}, "city__pingxiang": {"message": "<PERSON><PERSON><PERSON>"}, "city__puer": {"message": "<PERSON>u'er"}, "city__putian": {"message": "<PERSON><PERSON>"}, "city__puyang": {"message": "P<PERSON><PERSON>"}, "city__qiandongnan": {"message": "Qiandongnan"}, "city__qianjiang": {"message": "Qianjiang"}, "city__qiannan": {"message": "<PERSON><PERSON><PERSON>"}, "city__qianxinan": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__qingdao": {"message": "Qingdao"}, "city__qingyang": {"message": "Qingyang"}, "city__qingyuan": {"message": "Qingyuan"}, "city__qinhuangdao": {"message": "Qinhuangdao"}, "city__qinzhou": {"message": "Qinzhou"}, "city__qionghai": {"message": "Qionghai"}, "city__qiongzhong": {"message": "Qiongzhong"}, "city__qiqihar": {"message": "Qiqihar"}, "city__qitaihe": {"message": "<PERSON><PERSON><PERSON>"}, "city__quanzhou": {"message": "Quanzhou"}, "city__qujing": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__quzhou": {"message": "Quzhou"}, "city__rizhao": {"message": "<PERSON><PERSON><PERSON>"}, "city__sanmenxia": {"message": "Sanmenxia"}, "city__sanming": {"message": "Sanming"}, "city__sanya": {"message": "Sanya"}, "city__shangluo": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__shangqiu": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__shangrao": {"message": "<PERSON><PERSON><PERSON>"}, "city__shannan": {"message": "Shannan"}, "city__shantou": {"message": "<PERSON><PERSON><PERSON>"}, "city__shanwei": {"message": "Shanwei"}, "city__shaoguan": {"message": "Shaoguan"}, "city__shaoxing": {"message": "Shaoxing"}, "city__shaoyang": {"message": "Shaoyang"}, "city__shennongjiaforestarea": {"message": "Shennongjia Forest Area"}, "city__shenyang": {"message": "Shenyang"}, "city__shenzhen": {"message": "Shenzhen"}, "city__shigatse": {"message": "Shigat<PERSON>"}, "city__shihezi": {"message": "<PERSON><PERSON><PERSON>"}, "city__shijiazhuang": {"message": "Shijiazhuang"}, "city__shiyan": {"message": "<PERSON><PERSON>"}, "city__shizuishan": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__shuangyashan": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__shuozhou": {"message": "Shuozhou"}, "city__siping": {"message": "<PERSON><PERSON>"}, "city__songyuan": {"message": "Songyuan"}, "city__suihua": {"message": "Su<PERSON>ua"}, "city__suining": {"message": "Suining"}, "city__suizhou": {"message": "<PERSON><PERSON><PERSON>"}, "city__suqian": {"message": "<PERSON><PERSON><PERSON>"}, "city__suzhou": {"message": "Suzhou"}, "city__tacheng": {"message": "Tacheng"}, "city__taian": {"message": "Tai'an"}, "city__taichung": {"message": "Taichung"}, "city__tainan": {"message": "Tainan"}, "city__taipei": {"message": "Taipei"}, "city__taitung": {"message": "<PERSON><PERSON><PERSON>"}, "city__taiyuan": {"message": "Taiyuan"}, "city__taizhou": {"message": "Taizhou"}, "city__tangshan": {"message": "Tangshan"}, "city__taoyuan": {"message": "Taoyuan"}, "city__tianmen": {"message": "Tianmen"}, "city__tianshui": {"message": "<PERSON><PERSON><PERSON>"}, "city__tieling": {"message": "Tieling"}, "city__tongchuan": {"message": "<PERSON><PERSON><PERSON>"}, "city__tonghua": {"message": "Tonghua"}, "city__tongliao": {"message": "<PERSON><PERSON><PERSON>"}, "city__tongling": {"message": "Tongling"}, "city__tongren": {"message": "<PERSON><PERSON>"}, "city__tumushuke": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__tunchang": {"message": "Tunchang"}, "city__turpan": {"message": "<PERSON><PERSON><PERSON>"}, "city__ulanqab": {"message": "Ulanqab"}, "city__urumqi": {"message": "Urumqi"}, "city__wanning": {"message": "Wanning"}, "city__weifang": {"message": "<PERSON><PERSON><PERSON>"}, "city__weihai": {"message": "Weihai"}, "city__weinan": {"message": "Weinan"}, "city__wenchang": {"message": "Wenchang"}, "city__wenshan": {"message": "<PERSON><PERSON>"}, "city__wenzhou": {"message": "Wenzhou"}, "city__wuhai": {"message": "Wu<PERSON>"}, "city__wuhan": {"message": "<PERSON><PERSON>"}, "city__wuhu": {"message": "<PERSON><PERSON>"}, "city__wujiaqu": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__wuwei": {"message": "<PERSON><PERSON>"}, "city__wuxi": {"message": "Wuxi"}, "city__wuzhishan": {"message": "Wuzhishan"}, "city__wuzhong": {"message": "Wuzhong"}, "city__wuzhou": {"message": "Wuzhou"}, "city__xiamen": {"message": "Xiamen"}, "city__xian": {"message": "Xi'<PERSON>"}, "city__xiangfan": {"message": "<PERSON><PERSON><PERSON>"}, "city__xiangtan": {"message": "Xiangtan"}, "city__xiangxi": {"message": "Xiangxi"}, "city__xianning": {"message": "Xianning"}, "city__xiantao": {"message": "Xiantao"}, "city__xianyang": {"message": "<PERSON><PERSON><PERSON>"}, "city__xiaogan": {"message": "<PERSON><PERSON>"}, "city__xilingol": {"message": "Xilingol"}, "city__xinganleague": {"message": "Xing'an League"}, "city__xingtai": {"message": "Xingtai"}, "city__xining": {"message": "Xining"}, "city__xinxiang": {"message": "Xinxiang"}, "city__xinyang": {"message": "Xinyang"}, "city__xinyu": {"message": "Xinyu"}, "city__xinzhou": {"message": "Xinzhou"}, "city__xishuangbanna": {"message": "Xishuangbanna"}, "city__xuancheng": {"message": "Xuancheng"}, "city__xuchang": {"message": "Xuchang"}, "city__xuzhou": {"message": "Xuzhou"}, "city__yaan": {"message": "Ya'an"}, "city__yanan": {"message": "Yan'<PERSON>"}, "city__yanbian": {"message": "Yanbian"}, "city__yancheng": {"message": "Yancheng"}, "city__yangjiang": {"message": "Yangjiang"}, "city__yangquan": {"message": "<PERSON><PERSON><PERSON>"}, "city__yangzhou": {"message": "Yangzhou"}, "city__yantai": {"message": "Yantai"}, "city__yibin": {"message": "<PERSON><PERSON>"}, "city__yichang": {"message": "Yichang"}, "city__yichun": {"message": "<PERSON><PERSON><PERSON>"}, "city__yilan": {"message": "Yilan"}, "city__yili": {"message": "<PERSON><PERSON>"}, "city__yinchuan": {"message": "<PERSON><PERSON><PERSON>"}, "city__yingkou": {"message": "<PERSON><PERSON><PERSON>"}, "city__yingtan": {"message": "Yingtan"}, "city__yiwu": {"message": "<PERSON><PERSON>"}, "city__yiyang": {"message": "Yiyang"}, "city__yongzhou": {"message": "Yongzhou"}, "city__yueyang": {"message": "<PERSON><PERSON><PERSON>"}, "city__yulin": {"message": "Yulin"}, "city__yuncheng": {"message": "Yuncheng"}, "city__yunfu": {"message": "<PERSON><PERSON>"}, "city__yunlin": {"message": "<PERSON><PERSON>"}, "city__yushu": {"message": "Yushu"}, "city__yuxi": {"message": "Yuxi"}, "city__zaozhuang": {"message": "Zaozhuang"}, "city__zhangjiajie": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__zhangjiakou": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__zhangye": {"message": "<PERSON><PERSON>"}, "city__zhangzhou": {"message": "Zhangzhou"}, "city__zhanjiang": {"message": "Zhanjiang"}, "city__zhaoqing": {"message": "Zhaoqing"}, "city__zhaotong": {"message": "Zhaotong"}, "city__zhengzhou": {"message": "Zhengzhou"}, "city__zhenjiang": {"message": "Zhenjiang"}, "city__zhongshan": {"message": "Zhongshan"}, "city__zhongwei": {"message": "Zhongwei"}, "city__zhoukou": {"message": "<PERSON><PERSON><PERSON>"}, "city__zhoushan": {"message": "Zhoushan"}, "city__zhuhai": {"message": "Zhu<PERSON>"}, "city__zhumadian": {"message": "Zhumadian"}, "city__zhuzhou": {"message": "Zhuzhou"}, "city__zibo": {"message": "Zibo"}, "city__zigong": {"message": "Zigong"}, "city__ziyang": {"message": "<PERSON><PERSON><PERSON>"}, "city__zunyi": {"message": "<PERSON><PERSON><PERSON>"}, "click_copy_product_info": {"message": "Ürün bilgilerini kopyala'ya tıklayın"}, "commmon_txt_expired": {"message": "S<PERSON><PERSON>i doldu"}, "common__date_range_12m": {"message": "1 yıl"}, "common__date_range_1m": {"message": "1 ay"}, "common__date_range_1w": {"message": "1 hafta"}, "common__date_range_2w": {"message": "2 hafta"}, "common__date_range_3m": {"message": "3 ay"}, "common__date_range_3w": {"message": "3 hafta"}, "common__date_range_6m": {"message": "6 ay"}, "common_btn_cancel": {"message": "İptal"}, "common_btn_close": {"message": "Ka<PERSON><PERSON>"}, "common_btn_save": {"message": "<PERSON><PERSON>"}, "common_btn_setting": {"message": "<PERSON><PERSON><PERSON>"}, "common_email": {"message": "E-posta"}, "common_error_msg_no_data": {"message": "Veri yok"}, "common_error_msg_no_result": {"message": "Ma<PERSON>ef hi<PERSON> sonuç bulunamadı."}, "common_favorites": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "common_feedback": {"message": "geri bildirim"}, "common_help": {"message": "Yardım"}, "common_loading": {"message": "Yükleniyor"}, "common_login": {"message": "<PERSON><PERSON><PERSON>"}, "common_logout": {"message": "Çıkış"}, "common_no": {"message": "Hay<PERSON><PERSON>"}, "common_powered_by_aliprice": {"message": "AliPrice.com tarafından desteklenmektedir"}, "common_setting": {"message": "<PERSON><PERSON>"}, "common_sign_up": {"message": "<PERSON><PERSON><PERSON> ol"}, "common_system_upgrading_title": {"message": "Sistem yükseltme"}, "common_system_upgrading_txt": {"message": "<PERSON><PERSON><PERSON><PERSON> daha sonra deneyin"}, "common_txt__currency": {"message": "para birimi"}, "common_txt__video_tutorial": {"message": "Video öğretici"}, "common_txt_ago_time": {"message": "$time$ gün önce", "placeholders": {"time": {"content": "$1"}}}, "common_txt_all_product": {"message": "Tümü"}, "common_txt_analysis": {"message": "analiz"}, "common_txt_basically_used": {"message": "Neredeyse hiç kullanılmadı"}, "common_txt_biaoti_link": {"message": "Başlık+Bağlantı"}, "common_txt_biaoti_link_dian_pu": {"message": "Başlık+Bağlantı+Mağaza Adı"}, "common_txt_blacklist": {"message": "en<PERSON><PERSON><PERSON>"}, "common_txt_cancel": {"message": "İptal etmek"}, "common_txt_category": {"message": "<PERSON><PERSON><PERSON>"}, "common_txt_chakan": {"message": "Kontrol etmek"}, "common_txt_colors": {"message": "<PERSON><PERSON><PERSON>"}, "common_txt_confirm": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "common_txt_copied": {"message": "Kopyalandı"}, "common_txt_copy": {"message": "Kopyala"}, "common_txt_copy_link": {"message": "Bağlantıyı kopyala"}, "common_txt_copy_title": {"message": "Başlığı kopyala"}, "common_txt_copy_title__link": {"message": "Başlığı ve bağlantıyı kopyala"}, "common_txt_day": {"message": "gökyüzü"}, "common_txt_day__short": {"message": "D"}, "common_txt_delete": {"message": "Sil"}, "common_txt_dian_pu_link": {"message": "Mağaza adını + bağlantıyı kopyala"}, "common_txt_download": {"message": "indirmek"}, "common_txt_downloaded": {"message": "Indirmek"}, "common_txt_export_as_csv": {"message": "Excel'i dışa aktar"}, "common_txt_export_as_txt": {"message": "Txt'yi dışa aktar"}, "common_txt_fail": {"message": "<PERSON><PERSON>"}, "common_txt_format": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "common_txt_get": {"message": "elde et<PERSON>k"}, "common_txt_incert_selection": {"message": "<PERSON><PERSON><PERSON>"}, "common_txt_install": {"message": "Yüklemek"}, "common_txt_load_failed": {"message": "Yükleme başarısız"}, "common_txt_month": {"message": "ay"}, "common_txt_more": {"message": "Da<PERSON>"}, "common_txt_new_unused": {"message": "Yepyeni, kullanılmamış"}, "common_txt_next": {"message": "<PERSON><PERSON><PERSON>"}, "common_txt_no_limit": {"message": "Sınırsız"}, "common_txt_no_noticeable": {"message": "G<PERSON><PERSON>ü<PERSON>ür çizik veya kir yok"}, "common_txt_on_sale": {"message": "Mevcut"}, "common_txt_opt_in_out": {"message": "<PERSON><PERSON>ık ka<PERSON>ı"}, "common_txt_order": {"message": "Sipariş"}, "common_txt_others": {"message": "Di<PERSON><PERSON><PERSON><PERSON>"}, "common_txt_overall_poor_condition": {"message": "<PERSON><PERSON> k<PERSON> durum"}, "common_txt_patterns": {"message": "desen"}, "common_txt_platform": {"message": "Platformlar"}, "common_txt_please_select": {"message": "Lütfen seçin"}, "common_txt_prev": {"message": "<PERSON><PERSON><PERSON>"}, "common_txt_price": {"message": "<PERSON><PERSON><PERSON>"}, "common_txt_privacy_policy": {"message": "Gizlilik Politikası"}, "common_txt_product_condition": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "common_txt_rating": {"message": "Değerlendirme"}, "common_txt_ratings": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "common_txt_reload": {"message": "<PERSON><PERSON><PERSON>"}, "common_txt_reset": {"message": "Sıfırla"}, "common_txt_retail": {"message": "Perakende"}, "common_txt_review": {"message": "gözden geçirmek"}, "common_txt_sale": {"message": "Mevcut"}, "common_txt_same": {"message": "Aynı"}, "common_txt_scratches_and_dirt": {"message": "Çizikler ve kirlerle"}, "common_txt_search_title": {"message": "<PERSON><PERSON> b<PERSON>ığı"}, "common_txt_select_all": {"message": "<PERSON><PERSON><PERSON>"}, "common_txt_selected": {"message": "seçilmiş"}, "common_txt_share": {"message": "Paylaş"}, "common_txt_sold": {"message": "<PERSON><PERSON>ld<PERSON>"}, "common_txt_sold_out": {"message": "<PERSON><PERSON><PERSON> satı<PERSON>ı"}, "common_txt_some_scratches": {"message": "Bazı çizikler ve kirler"}, "common_txt_sort_by": {"message": "<PERSON><PERSON><PERSON>"}, "common_txt_state": {"message": "durum"}, "common_txt_success": {"message": "Başarı"}, "common_txt_sys_err": {"message": "Sistem hatası"}, "common_txt_today": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "common_txt_total": {"message": "tüm"}, "common_txt_unselect_all": {"message": "<PERSON><PERSON><PERSON>"}, "common_txt_upload_image": {"message": "Fotoğ<PERSON><PERSON>"}, "common_txt_visit": {"message": "<PERSON><PERSON><PERSON>"}, "common_txt_whitelist": {"message": "beyaz liste"}, "common_txt_wholesale": {"message": "Toptan"}, "common_txt_wildberries": {"message": "Wildberries"}, "common_txt_year": {"message": "<PERSON><PERSON><PERSON>"}, "common_yes": {"message": "<PERSON><PERSON>"}, "compare_tool_btn_clear_all": {"message": "<PERSON><PERSON><PERSON> temizle"}, "compare_tool_btn_compare": {"message": "Karşılaştırmak"}, "compare_tool_btn_contact": {"message": "İletişim"}, "compare_tool_tips_max_compared": {"message": "添加最多$maxComparedCount$个", "placeholders": {"maxComparedCount": {"content": "$1"}}}, "configure_notifiactions": {"message": "Bildirimleri Yapılandır"}, "contact_us": {"message": "Bize Ulaşın"}, "context_menu_screenshot_search": {"message": "Aynı stil için ekran görüntüsü araması"}, "context_menus_aliprice_search_by_image": {"message": "AliP<PERSON><PERSON><PERSON>"}, "context_menus_goote_trans": {"message": "Sayfayı çevir/Orijinali gö<PERSON>"}, "context_menus_search_by_image": {"message": "$storeName$'da görselle ara", "placeholders": {"storeName": {"content": "$1"}}}, "context_menus_search_by_image_capture": {"message": "$storeName$'ya Yakala", "placeholders": {"storeName": {"content": "$1"}}}, "context_menus_translator": {"message": "Çevirmek için <PERSON>"}, "converter_modal_amount_placeholder": {"message": "Tu<PERSON><PERSON> buraya girin"}, "converter_modal_btn_convert": {"message": "dönüştürmek"}, "converter_modal_exchange_rate_source": {"message": "Veriler $boc$ döviz kurundan geliyor Güncelleme zamanı: $updatedAt$", "placeholders": {"boc": {"content": "$1"}, "updatedAt": {"content": "$2"}}}, "converter_modal_name": {"message": "Para birimi dönüştürme"}, "converter_modal_search_placeholder": {"message": "arama para birimi"}, "copy_all_contact_us_notice": {"message": "Bu site şu anda des<PERSON>, lütfen bizimle iletişime geçin"}, "copy_product_info": {"message": "<PERSON><PERSON>ün bilgi<PERSON>ini k<PERSON>ala"}, "copy_suggest_search_kw": {"message": "Açılır <PERSON>"}, "country__han_gou": {"message": "<PERSON><PERSON><PERSON>"}, "country__ri_ben": {"message": "Japonya"}, "country__yue_nan": {"message": "Vietnam"}, "currency_convert__custom": {"message": "<PERSON><PERSON> dö<PERSON>z kuru"}, "currency_convert__sync_server": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "dang_ri_fa_huo": {"message": "Aynı gün kargo"}, "dao_chu_quan_dian_shang_pin": {"message": "Mağazadaki Tüm Ürünleri Dışa Aktar"}, "dao_chu_wei_CSV": {"message": "İhracat"}, "dao_chu_zi_duan": {"message": "Alanları Dışa Aktar"}, "delivery_address": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "delivery_company": {"message": "Teslimat şirketi"}, "di_zhi": {"message": "adres"}, "dian_ji_cha_xun": {"message": "Sorgulamak için tı<PERSON>ın"}, "dian_pu_ID": {"message": "Mağaza kimliği"}, "dian_pu_di_zhi": {"message": "Mağaza Adresi"}, "dian_pu_lian_jie": {"message": "Mağaza Bağlantısı"}, "dian_pu_ming": {"message": "<PERSON><PERSON><PERSON>n adı"}, "dian_pu_ming_cheng": {"message": "<PERSON><PERSON><PERSON>n adı"}, "dian_pu_shang_pin_zong_hsu": {"message": "Mağazadaki Toplam Ürün Sayısı: $num$", "placeholders": {"num": {"content": "$1"}}}, "dian_pu_xin_xi": {"message": "<PERSON><PERSON><PERSON>"}, "ding_zai_zuo_ce": {"message": "sola çivilenmiş"}, "download_image__SKU_variant_images": {"message": "SKU varyantı resimleri"}, "download_image__assume": {"message": "<PERSON><PERSON><PERSON><PERSON>, product1.jpg ve product2.gif olmak üzere 2 resmimiz var.\nimg_{$no$} img_01.jpg, img_02.gif olarak yeniden adlandırılacak;\n{$group$}_{$no$} main_image_01.jpg, main_image_02.gif olarak yeniden adlandırılacak;", "placeholders": {"no": {"content": "$3"}, "group": {"content": "$2"}}}, "download_image__batch_download": {"message": "Toplu İndirme"}, "download_image__combined_image": {"message": "Birleşik ürün detay resmi"}, "download_image__continue_downloading": {"message": "İndirmeye devam et"}, "download_image__description_images": {"message": "Açıklama resimleri"}, "download_image__download_combined_image": {"message": "Birleşik ürün detay resmini indirin"}, "download_image__download_zip": {"message": "<PERSON><PERSON> indir"}, "download_image__enlarge_check": {"message": "Yalnızca JPEG, JPG, GIF ve PNG resimlerini destekler, tek bir resmin maksimum boyutu: 1600 * 1600"}, "download_image__enlarge_image": {"message": "<PERSON><PERSON><PERSON>"}, "download_image__export": {"message": "Bağlantıları dışa aktar"}, "download_image__height": {"message": "Yükseklik"}, "download_image__ignore_videos": {"message": "Dışa aktarılamadığı için video yoksayıldı"}, "download_image__img_translate": {"message": "<PERSON><PERSON><PERSON>"}, "download_image__main_image": {"message": "<PERSON> resim"}, "download_image__multi_folder": {"message": "Çoklu klasör"}, "download_image__name": {"message": "resmi indir"}, "download_image__notice_content": {"message": "Lütfen tarayıcınızın indirme ayarlarında \"İndirmeden önce her dosyanın nereye kaydedileceğini sor\" seçeneğini işaretlemeyin!!! Aksi takdirde çok sayıda iletişim kutusu olacaktır."}, "download_image__notice_ignore": {"message": "Bu mesajı bir daha isteme"}, "download_image__order_number": {"message": "{$no$} seri numarası; {$group$} grup adı; {$date$} zaman damgası", "placeholders": {"no": {"content": "$1"}, "group": {"content": "$2"}, "date": {"content": "$3"}}}, "download_image__overview": {"message": "genel bakış"}, "download_image__prompt_download_zip": {"message": "Çok fazla resim var, bunları bir zip klasörü olarak indirmeniz daha iyi olur."}, "download_image__rename": {"message": "<PERSON><PERSON><PERSON> Adlandırma"}, "download_image__rule": {"message": "Adlandırma kuralları"}, "download_image__single_folder": {"message": "Tek klasör"}, "download_image__sku_image": {"message": "SKU görüntüleri"}, "download_image__video": {"message": "video"}, "download_image__width": {"message": "Genişlik"}, "download_reviews__download_images": {"message": "<PERSON>nce<PERSON><PERSON> indir"}, "download_reviews__dropdown_title": {"message": "<PERSON>nce<PERSON><PERSON> indir"}, "download_reviews__export_csv": {"message": "CSV'yi dışa aktar"}, "download_reviews__no_images": {"message": "0 fotoğraf indirilebilir"}, "download_reviews__no_reviews": {"message": "İndirilecek inceleme yok!"}, "download_reviews__notice": {"message": "Uç:"}, "download_reviews__notice__chrome_settings": {"message": "Chrome tarayıcıyı indirmeden önce her dosyanın nereye kaydedileceğini soracak şekilde ayar<PERSON>ın, \"Ka<PERSON><PERSON>\" olarak ayarlayın"}, "download_reviews__notice__wait": {"message": "İnceleme sayı<PERSON>ına bağlı olarak bekleme süresi uzayabilir."}, "download_reviews__pages_list__all": {"message": "<PERSON><PERSON><PERSON>"}, "download_reviews__pages_list__page": {"message": "Önceki $page$ sayfaları", "placeholders": {"page": {"content": "$1"}}}, "download_reviews__pages_list_title": {"message": "<PERSON><PERSON><PERSON>"}, "export_shopping_cart__csv_filed__details_url": {"message": "<PERSON><PERSON><PERSON><PERSON> bağlantısı"}, "export_shopping_cart__csv_filed__details_url_with_sku": {"message": "Yankı SKU bağlantısı"}, "export_shopping_cart__csv_filed__images": {"message": "<PERSON>sim bağlantısı"}, "export_shopping_cart__csv_filed__quantity": {"message": "<PERSON><PERSON><PERSON>"}, "export_shopping_cart__csv_filed__sale_price": {"message": "<PERSON><PERSON><PERSON>"}, "export_shopping_cart__csv_filed__specs": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "export_shopping_cart__csv_filed__store_name": {"message": "<PERSON><PERSON><PERSON>n adı"}, "export_shopping_cart__csv_filed__store_url": {"message": "Mağaza bağlantısı"}, "export_shopping_cart__csv_filed__title": {"message": "<PERSON><PERSON><PERSON><PERSON> adı"}, "export_shopping_cart__export_btn": {"message": "İhracat"}, "export_shopping_cart__export_empty": {"message": "Lütfen bir ürün se<PERSON>!"}, "fa_huo_shi_jian": {"message": "Nakliye"}, "favorite_add_email": {"message": "E-posta Ad<PERSON><PERSON>"}, "favorite_add_favorites": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "favorite_added": {"message": "Eklendi"}, "favorite_btn_add": {"message": "Fiyat Düşüş Uyarısı."}, "favorite_btn_notify": {"message": "<PERSON><PERSON><PERSON>"}, "favorite_cate_name_all": {"message": "<PERSON><PERSON><PERSON>"}, "favorite_current_price": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "favorite_due_date": {"message": "Son tarih"}, "favorite_enable_notification": {"message": "Lütfen e-posta bildirimini etkinleştir"}, "favorite_expired": {"message": "S<PERSON><PERSON>i doldu"}, "favorite_go_to_enable": {"message": "Etkinleştirmeye git"}, "favorite_msg_add_success": {"message": "<PERSON>av<PERSON><PERSON>e ekle"}, "favorite_msg_del_success": {"message": "<PERSON><PERSON>k k<PERSON>anılanlardan sil"}, "favorite_msg_failure": {"message": "Başarısız! Sayfayı Yenile ve tekrar deneyin."}, "favorite_please_add_email": {"message": "Lütfen bir e-posta adresi ekleyin"}, "favorite_price_drop": {"message": "Aşağı"}, "favorite_price_rise": {"message": "Yukarı"}, "favorite_price_untracked": {"message": "<PERSON><PERSON>t takip edilmedi"}, "favorite_saved_price": {"message": "<PERSON><PERSON><PERSON><PERSON>t"}, "favorite_stop_tracking": {"message": "<PERSON><PERSON><PERSON> du<PERSON>"}, "favorite_sub_email_address": {"message": "Abonelik e-posta adresi"}, "favorite_tracking_period": {"message": "<PERSON><PERSON><PERSON>"}, "favorite_tracking_prices": {"message": "Fiyatları takip etme"}, "favorite_verify_email": {"message": "E-posta adresini doğrula"}, "favorites_list_remove_prompt_msg": {"message": "<PERSON><PERSON><PERSON> silmek istediğinizden emin misiniz?"}, "favorites_update_button": {"message": "Fiyatları şimdi güncelleyin"}, "fen_lei": {"message": "<PERSON><PERSON><PERSON>"}, "fen_xia_yan_xuan": {"message": "Distribütö<PERSON><PERSON><PERSON>"}, "find_similar": {"message": "Benzerini <PERSON>"}, "first_ali_price_date": {"message": "AliPrice tarayıcısı tarafından ilk kez yakalandığı tarih"}, "fooview_coupons_modal_no_data": {"message": "Kupon yok"}, "fooview_coupons_modal_title": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "fooview_favorites__product_sub__tooltip_l1": {"message": "Fiyat < $lowerPrice$", "placeholders": {"lowerPrice": {"content": "$1"}}}, "fooview_favorites__product_sub__tooltip_l2": {"message": "veya fiyat > $higherPrice$", "placeholders": {"higherPrice": {"content": "$1"}}}, "fooview_favorites__product_sub__tooltip_l3": {"message": "Son tes<PERSON> tarihi"}, "fooview_favorites_error_msg_no_favorites": {"message": "Fiyat düşüş uyarısı almak için buraya favori ürünler ekleyin."}, "fooview_favorites_filter_latest": {"message": "En son"}, "fooview_favorites_filter_price_drop": {"message": "indirim"}, "fooview_favorites_filter_price_up": {"message": "fiyat artışı"}, "fooview_favorites_modal_title": {"message": "<PERSON>av<PERSON><PERSON><PERSON>"}, "fooview_favorites_modal_title_title": {"message": "Ali<PERSON><PERSON>"}, "fooview_favorites_track_price": {"message": "Fiyatı takip etmek"}, "fooview_price_history_app_price": {"message": "APP Fiyatı :"}, "fooview_price_history_title": {"message": "<PERSON><PERSON><PERSON>"}, "fooview_product_list_feedback": {"message": "<PERSON><PERSON><PERSON>"}, "fooview_product_list_orders": {"message": "Sipariş"}, "fooview_product_list_price": {"message": "<PERSON><PERSON><PERSON>"}, "fooview_reviews_error_msg_no_review": {"message": "<PERSON>u <PERSON><PERSON><PERSON>n hakkında herhangi bir inceleme bulamadık."}, "fooview_reviews_filter_buyer_reviews": {"message": "Alıcıların fotoğrafları"}, "fooview_reviews_modal_title": {"message": "yorum<PERSON>"}, "fooview_same_product_choose_category": {"message": "<PERSON><PERSON><PERSON>"}, "fooview_same_product_filter_feedback": {"message": "<PERSON><PERSON><PERSON>"}, "fooview_same_product_filter_orders": {"message": "Sipariş"}, "fooview_same_product_filter_price": {"message": "<PERSON><PERSON><PERSON>"}, "fooview_same_product_filter_rating": {"message": "Değerlendirme"}, "fooview_same_product_modal_title": {"message": "Aynı ürün bulun"}, "fooview_same_product_search_by_image": {"message": "<PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> ara"}, "fooview_seller_analysis_modal_title": {"message": "Satıcı Analizi"}, "for_12_months": {"message": "1 yıl için"}, "for_12_months_list_pro": {"message": "12 ay"}, "for_12_months_nei": {"message": "12 ay içinde"}, "for_1_months": {"message": "1 ay"}, "for_1_months_nei": {"message": "1 ay içinde"}, "for_3_months": {"message": "3 ay boyunca"}, "for_3_months_nei": {"message": "3 ay içinde"}, "for_6_months": {"message": "6 ay boyunca"}, "for_6_months_nei": {"message": "6 ay içinde"}, "for_9_months": {"message": "9 ay"}, "for_9_months_nei": {"message": "9 ay içinde"}, "fu_gou_lv": {"message": "<PERSON><PERSON> satın alma oranı"}, "gao_liang_bu_tong_dian": {"message": "farklılıkları vurgula"}, "gao_liang_guang_gao_chan_pin": {"message": "Reklam Ürünlerini Vurgula"}, "geng_duo_xin_xi": {"message": "<PERSON><PERSON> fazla bilgi"}, "geng_xin_shi_jian": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "get_store_products_fail_tip": {"message": "Normal erişimi garantilemek için doğrulamaya gitmek üzere Tamam'ı tıklayın"}, "gong_x_kuan_shang_pin": {"message": "Toplam $amount$ ürün", "placeholders": {"amount": {"content": "$1"}}}, "gong_ying_shang": {"message": "Ted<PERSON><PERSON><PERSON><PERSON>"}, "gong_ying_shang_ID": {"message": "tedarikçi kimliği"}, "gong_ying_shang_deng_ji": {"message": "Tedarikçi Derecelendirmesi"}, "gong_ying_shang_nian_zhan": {"message": "Tedarikçi daha yaşlı"}, "gong_ying_shang_xin_xi": {"message": "Tedarikçi bilgileri"}, "gong_ying_shang_zhu_ye_lian_jie": {"message": "Tedarikçi Ana Sayfası Bağlantısı"}, "green_rocket": {"message": "로켓프레시"}, "grey_rocket": {"message": "로켓설치"}, "gu_ji_shou_jia": {"message": "<PERSON><PERSON><PERSON> Fiyatı"}, "guan_jian_zi": {"message": "<PERSON><PERSON><PERSON>"}, "guang_gao_chan_pin": {"message": "Reklam ürünleri"}, "guang_gao_zhan_bi": {"message": "Re<PERSON>m oranı"}, "guo_ji_wu_liu_yun_fei": {"message": "Uluslararası Kargo Ücreti"}, "guo_lv_tiao_jian": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "hao_ping_lv": {"message": "<PERSON><PERSON><PERSON> derecelendirme"}, "highest_price": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "historical_trend": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "how_to_screenshot": {"message": "Alanı seçmek için farenin sol düğmesini basılı tutun, ekran görüntüsünden çıkmak için sağ fare düğmesine veya Esc tuşuna dokunun."}, "howt_it_works": {"message": "Na<PERSON><PERSON>l <PERSON>ışır"}, "hui_fu_lv": {"message": "Yanıt oranı"}, "hui_tou_lv": {"message": "dönüş oranı"}, "inquire_freightFee": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "inquire_freightFee_Yuan": {"message": "Kargo/Yuan"}, "inquire_freightFee_province": {"message": "<PERSON><PERSON>"}, "inquire_freightFee_the": {"message": "Kargo ücreti $num$ olup, bölgede ücretsiz kargo imkanı mevcuttur.", "placeholders": {"num": {"content": "$1"}}}, "is_ad": {"message": "Rekla<PERSON>."}, "jia_ge": {"message": "<PERSON><PERSON><PERSON>"}, "jia_ge_dan_wei": {"message": "<PERSON><PERSON><PERSON>"}, "jia_ge_qu_shi": {"message": "Akım"}, "jia_zai_n_ge_shang_pin": {"message": "$num$ <PERSON><PERSON><PERSON><PERSON><PERSON>", "placeholders": {"num": {"content": "$1"}}}, "jin_30_tian_xiao_liang_zhan_bi": {"message": "Son 30 gündeki satış hacminin yüzdesi"}, "jin_30_tian_xiao_shou_e_zhan_bi": {"message": "Son 30 gündeki gelirin yüzdesi"}, "jin_30d_xiao_liang": {"message": "Satış"}, "jin_30d_xiao_liang__desc": {"message": "Son 30 gündeki toplam satış hacmi"}, "jin_30d_xiao_shou_e": {"message": "<PERSON><PERSON>"}, "jin_30d_xiao_shou_e__desc": {"message": "Son 30 güne ait toplam ciro"}, "jin_90_tian_mai_jia_shu": {"message": "Son 90 Gündeki Alıcılar"}, "jin_90_tian_xiao_shou_liang": {"message": "Son 90 Gündeki Satışlar"}, "jing_xuan_huo_yuan": {"message": "Seçilen kaynaklar"}, "jing_ying_mo_shi": {"message": "İş modeli"}, "jing_ying_mo_shi__gong_chang": {"message": "Üretici firma"}, "jiu_fen_jie_jue": {"message": "Tartışmalı karar"}, "jiu_fen_jie_jue__desc": {"message": "Satıcıların mağaza haklarına ilişkin ihtilafların muhasebeleştirilmesi"}, "jiu_fen_lv": {"message": "Anlaşmazlık oranı"}, "jiu_fen_lv__desc": {"message": "Son 30 gün iç<PERSON>e tama<PERSON>lanan ve satıcının veya her iki tarafın sorumluluğunda olduğu değerlendirilen şikayetli siparişlerin oranı"}, "kai_dian_ri_qi": {"message": "Açılış tarihi"}, "keywords": {"message": "<PERSON><PERSON><PERSON>"}, "kua_jin_Select_pan_huo": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "last15_days": {"message": "Son 15 gün"}, "last180_days": {"message": "Son 180 gün"}, "last30_days": {"message": "Son 30 gün i<PERSON><PERSON>e"}, "last360_days": {"message": "Son 360 gün"}, "last45_days": {"message": "Son 45 gün"}, "last60_days": {"message": "Son 60 gün"}, "last7_days": {"message": "Son 7 gün"}, "last90_days": {"message": "Son 90 gün"}, "last_30d_sales": {"message": "Son 30 günlük satışlar"}, "lei_ji": {"message": "Kümülatif"}, "lei_ji_xiao_liang": {"message": "Toplam"}, "lei_ji_xiao_liang__desc": {"message": "Raftaki üründen sonraki tüm <PERSON>"}, "lei_ji_xiao_liang_pai_xu__desc": {"message": "Son 30 gün içindeki kümülatif satış hacmi, yüksekten düşüğe doğru sıralanmıştır"}, "lian_xi_fang_shi": {"message": "İletişim bilgileri"}, "list_time": {"message": "<PERSON><PERSON><PERSON> tarihi"}, "load_more": {"message": "Daha Fazlasını Yükle"}, "login_to_aliprice": {"message": "AliPrice'ta oturum açın"}, "long_link": {"message": "Uzun Bağlantı"}, "lowest_price": {"message": "Düşük"}, "mai_jia_shu": {"message": "Satıcılar"}, "mao_li_lv": {"message": "<PERSON><PERSON>"}, "mobile_view__dkxbqy": {"message": "<PERSON>ni <PERSON>k<PERSON>"}, "mobile_view__sjdxq": {"message": "Uygulamada Ayrıntılar"}, "mobile_view__sjdxqy": {"message": "Uygulamada Ayrıntı Sayfası"}, "mobile_view__smck": {"message": "G<PERSON>rüntülemek için <PERSON>"}, "mobile_view__smckms": {"message": "Taramak ve görüntülemek için lütfen kamerayı veya uygulamayı kullanın"}, "modified_failed": {"message": "Değişiklik başarısız oldu"}, "modified_successfully": {"message": "Başarıyla değiştirildi"}, "nav_btn_favorites": {"message": "Koleksiyonlarım"}, "nav_btn_package": {"message": "paket"}, "nav_btn_product_info": {"message": "<PERSON><PERSON><PERSON><PERSON> ha<PERSON>"}, "nav_btn_viewed": {"message": "G<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "no_suggest_search_kw": {"message": "Loading..."}, "none": {"message": "Hiç<PERSON>i"}, "normal_link": {"message": "Normal Bağlantı"}, "notice": {"message": "<PERSON><PERSON><PERSON>"}, "number_reviews": {"message": "<PERSON><PERSON><PERSON>"}, "only_show_num": {"message": "Toplam ürünler: $allnum$, Gizli: $hidenum2$", "placeholders": {"allnum": {"content": "$1"}, "hidenum2": {"content": "$2"}}}, "only_show_selected": {"message": "İşaretlenmemişi Kaldır"}, "open": {"message": "Aç"}, "open_links": {"message": "Bağlantıları aç"}, "options_page_tab_check_links": {"message": "Bağlantıları kontrol et"}, "options_page_tab_gernal": {"message": "<PERSON><PERSON>"}, "options_page_tab_notifications": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "options_page_tab_others": {"message": "Di<PERSON><PERSON><PERSON><PERSON>"}, "options_page_tab_sbi": {"message": "<PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> ara"}, "options_page_tab_shortcuts": {"message": "K<PERSON><PERSON>ollar"}, "options_page_tab_shortcuts_title": {"message": "Kısayollar için yazı tipi boyutu"}, "options_page_tab_similar_products": {"message": "Aynı"}, "orange_rocket": {"message": "판매자로켓"}, "order_list_open_links_tip": {"message": "Birden fazla ürün bağlantısı açılmak üzere"}, "order_list_sku_show_title": {"message": "Paylaşılan bağlantılarda seçili varyantları göster"}, "orders_last30_days": {"message": "Son 30 gündeki sipariş sayısı"}, "pTutorial_favorites_block1_desc1": {"message": "İzlediğiniz ürünler burada listelenmiştir."}, "pTutorial_favorites_block1_title": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "pTutorial_popup_block1_desc1": {"message": "<PERSON><PERSON><PERSON>, fi<PERSON>t dü<PERSON><PERSON><PERSON> ürün<PERSON>in olduğu anlamına gelir"}, "pTutorial_popup_block1_title": {"message": "Kısayollar ve Sık <PERSON>"}, "pTutorial_price_history_block1_desc1": {"message": "Fiyatı takip et i tıklayın, ürünleri sık kullanılanlara ekleyin. Fiyatları düştüğünde, bildirim alacaksınız."}, "pTutorial_price_history_block1_title": {"message": "Takip fiyatı"}, "pTutorial_reviews_block1_desc1": {"message": "Alıcıların Itao yorumları ve AliExpress geri bildiriminden gerçek fotoğraflar"}, "pTutorial_reviews_block1_title": {"message": "yorum<PERSON>"}, "pTutorial_reviews_block2_desc1": {"message": "<PERSON><PERSON><PERSON>an gelen yorumları kontrol etmek her zaman yardımcı olur"}, "pTutorial_same_products_block1_desc1": {"message": "En iyi seçimi yapmak için bunları karşılaştırabilirsiniz."}, "pTutorial_same_products_block1_desc2": {"message": "Görselle ara için \"<PERSON><PERSON><PERSON>\" i tıklayın"}, "pTutorial_same_products_block1_title": {"message": "Aynı ürünler"}, "pTutorial_same_products_by_image_search_block1_desc1": {"message": "<PERSON><PERSON><PERSON><PERSON> resmini oraya bırakın ve bir kategori seçin"}, "pTutorial_same_products_by_image_search_block1_title": {"message": "Görselle ara"}, "pTutorial_seller_analysis_block1_desc1": {"message": "Satıcının olumlu geri bildirim oranı, geri bildirim puanları ve satıcının piyasada ne kadar süre kaldığı"}, "pTutorial_seller_analysis_block1_title": {"message": "Satıcı oranı"}, "pTutorial_seller_analysis_block2_desc2": {"message": "Satıcı derecelendirmesi 3 dizine dayanmaktadır: Tanımlanan ürün, Haberleşme Nakliye Hızı"}, "pTutorial_seller_analysis_block3_desc3": {"message": "Satıcıların güven düzeylerini belirtmek için 3 renk ve simge kullanıyoruz"}, "page_count": {"message": "Sayfa sayısı"}, "pai_chu": {"message": "<PERSON><PERSON> t<PERSON>"}, "pai_chu_HK_bu_yi_xia_xiaoshou": {"message": "Hong Kong-Kısıtlıları Hariç <PERSON>"}, "pai_chu_JP_bu_yi_xia_xiaoshou": {"message": "Japonya-Kısıtlıları Hariç Tut"}, "pai_chu_KR_bu_yi_xia_xiaoshou": {"message": "Kore-Kısıtlıları Hariç Tut"}, "pai_chu_KZ_bu_yi_xia_xiaoshou": {"message": "Kazakistan-Kısıtlıları Hariç Tut"}, "pai_chu_MO_bu_yi_xia_xiaoshou": {"message": "Makao-Kısıtlıları Hariç Tut"}, "pai_chu_RU_bu_yi_xia_xiaoshou": {"message": "Doğu Avrupa-Kısıtlıları Hariç Tut"}, "pai_chu_SA_bu_yi_xia_xiaoshou": {"message": "Suudi Arabistan-Kısıtlıları Hariç <PERSON>"}, "pai_chu_TW_bu_yi_xia_xiaoshou": {"message": "Tayvan-Kısıtlıları Hariç Tut"}, "pai_chu_USA_bu_yi_xia_xiaoshou": {"message": "ABD-Kısıtlıları Hariç Tut"}, "pai_chu_VN_bu_yi_xia_xiaoshou": {"message": "Vietnam-Kısıtlıları Hariç Tut"}, "pai_chu_bu_yi_xia_xiaoshou": {"message": "Kısıtlı Ürünleri Hariç Tut"}, "payable_price_formula": {"message": "Fiyat + Kargo + İndirim"}, "pdd_check_retail_btn_txt": {"message": "Perakende kontrol edin"}, "pdd_pifa_to_retail_btn_txt": {"message": "perakende satın al"}, "pdp_copy_fail": {"message": "Kopyalama başar<PERSON>sız oldu!"}, "pdp_copy_success": {"message": "Kopyalama başarılı!"}, "pdp_share_modal_subtitle": {"message": "Ekran görüntü<PERSON><PERSON><PERSON><PERSON>, seçiminizi görecektir."}, "pdp_share_modal_title": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "pdp_share_screenshot": {"message": "Ekran görüntüsünü <PERSON>"}, "pei_song": {"message": "Nakliye"}, "pin_lei": {"message": "<PERSON><PERSON><PERSON>"}, "pin_zhi_ti_yan": {"message": "<PERSON><PERSON><PERSON><PERSON> ka<PERSON>"}, "pin_zhi_ti_yan__desc": {"message": "Satıcının mağazasının kalite iade oranı"}, "pin_zhi_tui_kuan_lv": {"message": "<PERSON><PERSON>me oranı"}, "pin_zhi_tui_kuan_lv__desc": {"message": "Son 30 gün içinde yalnızca iadesi ve iadesi yapılan siparişlerin oranı"}, "ping_fen": {"message": "Değerlendirme"}, "ping_jun_fa_huo_su_du": {"message": "Ortalama Gönderim H<PERSON>"}, "pkgInfo_hide": {"message": "Lojistik bilgisi: açık/kapalı"}, "pkgInfo_no_trace": {"message": "Lojistik bilgisi yok"}, "platform_name__1688": {"message": "1688"}, "platform_name__17zwd": {"message": "17zwd.com"}, "platform_name__51taoyang": {"message": "51taoyang.com"}, "platform_name__5ts": {"message": "5ts.com"}, "platform_name__91jf": {"message": "91jf.com"}, "platform_name__alibaba": {"message": "Alibaba.com"}, "platform_name__aliexpress": {"message": "AliExpress"}, "platform_name__amazon": {"message": "Amazon"}, "platform_name__bao66": {"message": "bao66.cn"}, "platform_name__chinagoods": {"message": "chinagoods.com"}, "platform_name__dhgate": {"message": "DHgate"}, "platform_name__e3hui": {"message": "e3hui.com"}, "platform_name__ebay": {"message": "Ebay"}, "platform_name__hznzcn": {"message": "hznzcn.com"}, "platform_name__jd": {"message": "JD"}, "platform_name__juyi5": {"message": "juyi5.cn"}, "platform_name__naver_shopping": {"message": "Naver Shopping"}, "platform_name__pinduoduo": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "platform_name__pinduoduo_pifa": {"message": "Pinduoduo toptan sat<PERSON>ş"}, "platform_name__shopee": {"message": "<PERSON>ee"}, "platform_name__sjxzw": {"message": "571xz.com"}, "platform_name__sooxie": {"message": "sooxie.com"}, "platform_name__taobao": {"message": "Taobao"}, "platform_name__taobao_lite": {"message": "Taobao Lite"}, "platform_name__vvic": {"message": "vvic.com"}, "platform_name__walmart": {"message": "Walmart"}, "platform_name__wsy": {"message": "wsy.com"}, "platform_name__xingfujie": {"message": "xingfujie.cn"}, "platform_name__yiwugo": {"message": "Yiwugo.com"}, "platform_name__yunchepin": {"message": "yunchepin.cn"}, "platform_name__zhaojiafang": {"message": "zhaojiafang.com"}, "popup_go_to_home": {"message": "ana sayfa"}, "popup_go_to_platform": {"message": "$name$'e gidin", "placeholders": {"name": {"content": "$1"}}}, "popup_search_placeholder": {"message": "Şunlar için al<PERSON>iş yapıyorum..."}, "popup_track_package_btn_track": {"message": "Izlemek"}, "popup_track_package_desc": {"message": "HEPSİ BİR ARADA POSTA TAKİBİ"}, "popup_track_package_search_placeholder": {"message": "Takip numa<PERSON>ı"}, "popup_translate_search_placeholder": {"message": "$searchOn$ üzerinde çevirin ve arama yapın", "placeholders": {"searchOn": {"content": "$1"}}}, "price_history": {"message": "<PERSON><PERSON><PERSON>"}, "price_history_chart_tip_ae": {"message": "İpucu: <PERSON><PERSON><PERSON><PERSON> sayısı, lans<PERSON>an bu yana kümülatif sipariş sayısıdır"}, "price_history_chart_tip_coupang": {"message": "İpucu: <PERSON><PERSON><PERSON>, sahte siparişlerin sipariş sayısını silecektir"}, "price_history_inm_1688_l1": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "price_history_inm_1688_l2": {"message": "1688 için AliPrice Alışveriş Asistanı"}, "price_history_panel_lowest_price": {"message": "en düşük fiyat: "}, "price_history_panel_tab_price_tracking": {"message": "<PERSON><PERSON><PERSON>"}, "price_history_panel_tab_seller_analysis": {"message": "Satıcı Analizi"}, "price_history_pro_modal_title": {"message": "Fiyat geçmişi ve Sipariş geçmişi"}, "privacy_consent__btn_agree": {"message": "Veri toplama onayını tekrar ziyaret edin"}, "privacy_consent__btn_disable_all": {"message": "Kabul etmiyorum"}, "privacy_consent__btn_enable_all": {"message": "<PERSON><PERSON><PERSON>"}, "privacy_consent__btn_uninstall": {"message": "Kaldırmak"}, "privacy_consent__desc_privacy": {"message": "Veriler veya çerezler olmadan bazı işlevlerin kapalı olacağını unutmayın çünkü bu işlevler veri veya çerez açıklamalarına ihtiyaç duyar, ancak yine de diğer işlevleri kullanabilirsiniz."}, "privacy_consent__desc_privacy_L1": {"message": "Ne yazık ki, veri veya çerez olmadan çalışmaz çünkü veri veya çerezlerin açıklanmasına ihtiyacımız vardır."}, "privacy_consent__desc_privacy_L2": {"message": "Bu bilgileri toplamamıza izin vermiyorsanız, l<PERSON><PERSON><PERSON> kaldırın."}, "privacy_consent__item_cookies_desc": {"message": "<PERSON><PERSON><PERSON>, fiyat geçmişini göstermek için çevrimiçi alışveriş yaparken para birimi verilerinizi yalnızca çerezlerde alırız."}, "privacy_consent__item_cookies_title": {"message": "Gerek<PERSON>"}, "privacy_consent__item_functional_desc_L1": {"message": "1. Bilgisayarınızı veya cihazınızı anonim olarak tanımlamak için tarayıcıya tanımlama bilgileri ekleyin."}, "privacy_consent__item_functional_desc_L2": {"message": "2. İşlevle çalışmak için eklentiye işlevsel veriler ekleyin."}, "privacy_consent__item_functional_title": {"message": "Fonksiyonel ve Analitik Çerezler"}, "privacy_consent__more_desc": {"message": "Lütfen kişisel verilerinizi diğer şirketlerle paylaşmadığımızı ve hiçbir reklam şirketinin hizmetimiz aracılığıyla veri toplamadığını bilin."}, "privacy_consent__options__btn__desc": {"message": "Tüm özellikleri kullanmak için onu açmanız gerekir."}, "privacy_consent__options__btn__label": {"message": "<PERSON><PERSON>u"}, "privacy_consent__options__desc_L1": {"message": "<PERSON><PERSON><PERSON>l olarak sizi tanımlayan aşağıdaki verileri toplayacağız:"}, "privacy_consent__options__desc_L2": {"message": "- <PERSON><PERSON><PERSON><PERSON>, para birimi verilerinizi yalnızca çevrimiçi alışveriş yaparken fiyat geçmişini göstermek için çerezlerde alırız."}, "privacy_consent__options__desc_L3": {"message": "- ve bilgisayarınızı veya cihazınızı anonim olarak tanımlamak için tarayıcıya çerezler ekleyin."}, "privacy_consent__options__desc_L4": {"message": "- di<PERSON>er anonim veriler bu uzantıyı daha kullanışlı hale getirir."}, "privacy_consent__options__desc_L5": {"message": "Kişisel verilerinizi başka şirketlerle paylaşmadığımızı ve hiçbir reklam şirketinin hizmetimizle veri toplamadığını lütfen unutmayın."}, "privacy_consent__privacy_preferences": {"message": "Gizlilik tercihleri"}, "privacy_consent__read_more": {"message": "Devamı >>"}, "privacy_consent__title_privacy": {"message": "Gizlilik"}, "product_info": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "product_recommend__name": {"message": "Aynı"}, "product_research": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "product_sub__email_desc": {"message": "Fiyat uyarısı e-postası"}, "product_sub__email_edit": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "product_sub__email_not_verified": {"message": "Lütfen e-postayı doğrulayın"}, "product_sub__email_required": {"message": "Lütfen e-posta sağlayın"}, "product_sub__form_countdown": {"message": "$seconds$ saniye sonra otomatik kapanma", "placeholders": {"seconds": {"content": "$1"}}}, "product_sub__form_fail": {"message": "Hatırlatıcı ekle başarısız!"}, "product_sub__form_input_price": {"message": "<PERSON><PERSON><PERSON>ı"}, "product_sub__form_item_country": {"message": "ulus"}, "product_sub__form_item_current_price": {"message": "Mev<PERSON> fiyat"}, "product_sub__form_item_duration": {"message": "<PERSON><PERSON><PERSON> i<PERSON>in takip edin"}, "product_sub__form_item_higher_price": {"message": "veya fiyat >"}, "product_sub__form_item_invalid_higher_price": {"message": "Fiyat $price$ değerinden büyük olmalıdır", "placeholders": {"price": {"content": "$1"}}}, "product_sub__form_item_invalid_lower_price": {"message": "Fiyat $price$ değerinden düşük olmalıdır", "placeholders": {"price": {"content": "$1"}}}, "product_sub__form_item_lower_price": {"message": "fiyat ne zaman <"}, "product_sub__form_submit": {"message": "Göndermek"}, "product_sub__form_success": {"message": "Hatırlatıcı ekleme başarısı!"}, "product_sub__high_price_notify": {"message": "Fiyat artışlarını bana bildir"}, "product_sub__low_price_notify": {"message": "<PERSON>yat indirim<PERSON>ini bana bildir"}, "product_sub__modal_title": {"message": "Abonelik fiyat değişikliği hatırlatıcısı"}, "province__an_hui": {"message": "<PERSON><PERSON>"}, "province__ao_men": {"message": "Macau"}, "province__bei_jing": {"message": "Beijing"}, "province__chong_qing": {"message": "Chongqing"}, "province__fu_jian": {"message": "Fujian"}, "province__gan_su": {"message": "Gansu"}, "province__guang_dong": {"message": "Guangdong"}, "province__guang_xi": {"message": "Guangxi"}, "province__gui_zhou": {"message": "Guizhou"}, "province__hai_nan": {"message": "Hainan"}, "province__he_bei": {"message": "Hebei"}, "province__he_nan": {"message": "<PERSON><PERSON>"}, "province__hei_long_jiang": {"message": "Heilongjiang"}, "province__hu_bei": {"message": "Hubei"}, "province__hu_nan": {"message": "Hunan"}, "province__ji_lin": {"message": "<PERSON><PERSON>"}, "province__jiang_su": {"message": "Jiangsu"}, "province__jiang_xi": {"message": "Jiangxi"}, "province__liao_ning": {"message": "Liaoning"}, "province__nei_meng_gu": {"message": "Inner Mongolia"}, "province__ning_xia": {"message": "Ningxia"}, "province__qing_hai": {"message": "Qinghai"}, "province__shan_dong": {"message": "Shandong"}, "province__shan_xi": {"message": "Shaanxi"}, "province__shang_hai": {"message": "Shanghai"}, "province__si_chuan": {"message": "Sichuan"}, "province__tai_wan": {"message": "Taiwan"}, "province__tian_jin": {"message": "Tianjin"}, "province__xi_zhang": {"message": "Tibet"}, "province__xiang_gang": {"message": "Hong Kong"}, "province__xin_jiang": {"message": "Xinjiang"}, "province__yun_nan": {"message": "Yunnan"}, "province__zhe_jiang": {"message": "Zhejiang"}, "purple_rocket": {"message": "로켓직구"}, "qi_ding_liang": {"message": "<PERSON><PERSON><PERSON>"}, "qi_ding_liang_qi_ding_jia": {"message": "MOQ ve MOP"}, "qi_ye_mian_ji": {"message": "<PERSON><PERSON><PERSON> alan"}, "qing_zhi_shao_xuan_ze_yi_ge_chan_pin": {"message": "Lütfen en az bir ürün seçiniz"}, "qing_zhi_shao_xuan_ze_yi_ge_zi_duan": {"message": "Lütfen en az bir alan <PERSON>"}, "qu_deng_lu": {"message": "Oturum aç"}, "quan_guo_yan_xuan": {"message": "Küresel Seçim"}, "recommendation_popup_banner_btn_install": {"message": "<PERSON><PERSON><PERSON>"}, "recommendation_popup_banner_desc": {"message": "3/6 ay içinde fiyat geçmişini ve Fiyat düşüş bildirimini görünt<PERSON><PERSON>in"}, "region__all": {"message": "<PERSON><PERSON><PERSON>"}, "region__hai_wai": {"message": "Overseas"}, "region__hua_bei_qu": {"message": "North China"}, "region__hua_dong_qu": {"message": "East China"}, "region__hua_nan_qu": {"message": "South China"}, "region__hua_zhong_qu": {"message": "Central China"}, "region__jiang_zhe_lu": {"message": "Jiangsu, Zhejiang and Shanghai"}, "remove_web_limits": {"message": "Sağ tıklamayı etkinleştir"}, "ren_zheng_gong_chang": {"message": "Sertifikalı Fabrika"}, "ren_zheng_gong_ying_shang_nian_zhan": {"message": "Sertifikalı Tedarikçi Olarak Çalıştığı Yıllar"}, "required_to_aliprice_login": {"message": "AliPrice'ta oturum açmanız gerekiyor"}, "revenue_last30_days": {"message": "Son 30 gündeki satış tutarı"}, "review_counts": {"message": "Koleksiyoncu <PERSON>ı"}, "rocket": {"message": "로켓"}, "ru_zhu_nian_xian": {"message": "<PERSON><PERSON><PERSON>"}, "sales_amount_last30_days": {"message": "Son 30 gündeki toplam satışlar"}, "sales_last30_days": {"message": "Son 30 gündeki satışlar"}, "sbi_alibaba_cate__accessories": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "sbi_alibaba_cate__aqfk": {"message": "Güvenlik"}, "sbi_alibaba_cate__bags_cases": {"message": "Çanta & Kılıflar"}, "sbi_alibaba_cate__beauty": {"message": "Güzellik"}, "sbi_alibaba_cate__beverage": {"message": "İçecek"}, "sbi_alibaba_cate__bgwh": {"message": "<PERSON><PERSON>"}, "sbi_alibaba_cate__bz": {"message": "paket"}, "sbi_alibaba_cate__ccyj": {"message": "mutfak eşyaları"}, "sbi_alibaba_cate__clothes": {"message": "G<PERSON><PERSON><PERSON>"}, "sbi_alibaba_cate__cmgd": {"message": "<PERSON><PERSON><PERSON>"}, "sbi_alibaba_cate__coat_jacket": {"message": "Ceket ve Ceket"}, "sbi_alibaba_cate__consumer_electronics": {"message": "Tüketici Elektroniği"}, "sbi_alibaba_cate__cryp": {"message": "Yetişkin Ürünleri"}, "sbi_alibaba_cate__csyp": {"message": "Yatak <PERSON>"}, "sbi_alibaba_cate__cwyy": {"message": "<PERSON><PERSON><PERSON><PERSON> bahçıvanlığı"}, "sbi_alibaba_cate__cysx": {"message": "taze yemek"}, "sbi_alibaba_cate__dgdq": {"message": "Elektrikçi"}, "sbi_alibaba_cate__dl": {"message": "oyunculuk"}, "sbi_alibaba_cate__dress_suits": {"message": "Elbise ve takım elbise"}, "sbi_alibaba_cate__dszm": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "sbi_alibaba_cate__dzqj": {"message": "Elektronik cihaz"}, "sbi_alibaba_cate__essb": {"message": "Kullanılmış ekipman"}, "sbi_alibaba_cate__food": {"message": "<PERSON><PERSON><PERSON>"}, "sbi_alibaba_cate__fspj": {"message": "G<PERSON><PERSON><PERSON>"}, "sbi_alibaba_cate__furniture": {"message": "Mobilya"}, "sbi_alibaba_cate__fzpg": {"message": "<PERSON><PERSON><PERSON>"}, "sbi_alibaba_cate__ghjq": {"message": "Kişisel Bakım"}, "sbi_alibaba_cate__gt": {"message": "Çelik"}, "sbi_alibaba_cate__gyp": {"message": "El sanatları"}, "sbi_alibaba_cate__hb": {"message": "<PERSON><PERSON><PERSON> <PERSON>"}, "sbi_alibaba_cate__hfcz": {"message": "Cilt bakımı makyajı"}, "sbi_alibaba_cate__hg": {"message": "<PERSON><PERSON><PERSON> endüstri"}, "sbi_alibaba_cate__jg": {"message": "İşleme"}, "sbi_alibaba_cate__jianccai": {"message": "Yapı malzemeleri"}, "sbi_alibaba_cate__jichuang": {"message": "<PERSON><PERSON><PERSON>"}, "sbi_alibaba_cate__jjry": {"message": "Ev günlük kullanım"}, "sbi_alibaba_cate__jtys": {"message": "Toplu taşıma"}, "sbi_alibaba_cate__jxsb": {"message": "Teçhizat"}, "sbi_alibaba_cate__jxwj": {"message": "mekanik <PERSON>m"}, "sbi_alibaba_cate__jydq": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "sbi_alibaba_cate__jzjc": {"message": "<PERSON>v geliştirme yapı malzemeleri"}, "sbi_alibaba_cate__jzjf": {"message": "<PERSON>v teks<PERSON><PERSON>i"}, "sbi_alibaba_cate__mj": {"message": "<PERSON><PERSON><PERSON>"}, "sbi_alibaba_cate__myyp": {"message": "Bebek ürünleri"}, "sbi_alibaba_cate__nanz": {"message": "Erkek<PERSON>in"}, "sbi_alibaba_cate__nvz": {"message": "<PERSON><PERSON>"}, "sbi_alibaba_cate__ny": {"message": "<PERSON><PERSON><PERSON>"}, "sbi_alibaba_cate__others": {"message": "Di<PERSON><PERSON><PERSON><PERSON>"}, "sbi_alibaba_cate__qcyp": {"message": "<PERSON><PERSON>"}, "sbi_alibaba_cate__qmpj": {"message": "<PERSON><PERSON>"}, "sbi_alibaba_cate__shoes": {"message": "Ayakkabı"}, "sbi_alibaba_cate__smdn": {"message": "<PERSON><PERSON><PERSON> bi<PERSON>"}, "sbi_alibaba_cate__snqj": {"message": "<PERSON><PERSON><PERSON> ve temizlik"}, "sbi_alibaba_cate__spjs": {"message": "Yiyecek içecek"}, "sbi_alibaba_cate__swfw": {"message": "İş hizmetleri"}, "sbi_alibaba_cate__toys_hobbies": {"message": "Oyuncak"}, "sbi_alibaba_cate__trousers_skirt": {"message": "Pantolon & Etek"}, "sbi_alibaba_cate__txcp": {"message": "İletişim ürünleri"}, "sbi_alibaba_cate__tz": {"message": "Çocuk giyim"}, "sbi_alibaba_cate__underwear": {"message": "<PERSON><PERSON>ı<PERSON>"}, "sbi_alibaba_cate__wjgj": {"message": "Donanım <PERSON>"}, "sbi_alibaba_cate__xgpi": {"message": "<PERSON><PERSON>"}, "sbi_alibaba_cate__xmhz": {"message": "proje işbirliği"}, "sbi_alibaba_cate__xs": {"message": "<PERSON><PERSON><PERSON>"}, "sbi_alibaba_cate__ydfs": {"message": "Spor giyim"}, "sbi_alibaba_cate__ydhw": {"message": "Açık hava sporu"}, "sbi_alibaba_cate__yjkc": {"message": "Metalurjik Mineraller"}, "sbi_alibaba_cate__yqyb": {"message": "Enstrümantasyon"}, "sbi_alibaba_cate__ys": {"message": "Yazdır"}, "sbi_alibaba_cate__yyby": {"message": "<PERSON><PERSON><PERSON><PERSON> bakım"}, "sbi_alibaba_cn_kj_90mjs": {"message": "Son 90 gündeki alıcı sayısı"}, "sbi_alibaba_cn_kj_90xsl": {"message": "Son 90 gündeki satış hacmi"}, "sbi_alibaba_cn_kj_gjsj": {"message": "<PERSON><PERSON><PERSON> edilen <PERSON>t"}, "sbi_alibaba_cn_kj_gjwlyf": {"message": "Uluslararası nakliye ücreti"}, "sbi_alibaba_cn_kj_gjyf": {"message": "Nakliye ücreti"}, "sbi_alibaba_cn_kj_gssj": {"message": "<PERSON><PERSON><PERSON> edilen <PERSON>t"}, "sbi_alibaba_cn_kj_lr": {"message": "kar"}, "sbi_alibaba_cn_kj_lrgs": {"message": "Kar = tahmini fiyat x kar marjı"}, "sbi_alibaba_cn_kj_pjfhsd": {"message": "Ortalama teslimat hızı"}, "sbi_alibaba_cn_kj_qtfy": {"message": "<PERSON><PERSON><PERSON>"}, "sbi_alibaba_cn_kj_qtfygs": {"message": "Diğer maliyet = tahmini fiyat x diğer maliyet oranı"}, "sbi_alibaba_cn_kj_spjg": {"message": "<PERSON><PERSON><PERSON>"}, "sbi_alibaba_cn_kj_spzl": {"message": "Ağırlık"}, "sbi_alibaba_cn_kj_szd": {"message": "yer"}, "sbi_alibaba_cn_kj_unit_ge": {"message": "adet"}, "sbi_alibaba_cn_kj_unit_jian": {"message": "adet"}, "sbi_alibaba_cn_kj_unit_ke": {"message": "Gram"}, "sbi_alibaba_cn_kj_unit_ren": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "sbi_alibaba_cn_kj_unit_tai": {"message": "adet"}, "sbi_alibaba_cn_kj_unit_tao": {"message": "Setler"}, "sbi_alibaba_cn_kj_unit_tian": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "sbi_alibaba_cn_kj_zwbj": {"message": "fiyat yok"}, "sbi_aliprice_alibaba_cn__jiage": {"message": "fiyat"}, "sbi_aliprice_alibaba_cn__keshou": {"message": "Satılık"}, "sbi_aliprice_alibaba_cn__moren": {"message": "varsay<PERSON>lan"}, "sbi_aliprice_alibaba_cn__queding": {"message": "<PERSON><PERSON>"}, "sbi_aliprice_alibaba_cn__xiaoliang": {"message": "Satış"}, "sbi_aliprice_alibaba_cn_cate__jiaju": {"message": "<PERSON><PERSON><PERSON>"}, "sbi_aliprice_alibaba_cn_cate__linghsi": {"message": "abur cubur"}, "sbi_aliprice_alibaba_cn_cate__meizhuang": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "sbi_aliprice_alibaba_cn_cate__neiyi": {"message": "<PERSON><PERSON>ı<PERSON>"}, "sbi_aliprice_alibaba_cn_cate__peishi": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "sbi_aliprice_alibaba_cn_cate__pinchui": {"message": "şişelenmiş içecek"}, "sbi_aliprice_alibaba_cn_cate__qita": {"message": "Di<PERSON><PERSON><PERSON><PERSON>"}, "sbi_aliprice_alibaba_cn_cate__qunzhuang": {"message": "Etek"}, "sbi_aliprice_alibaba_cn_cate__shangyi": {"message": "Ceket"}, "sbi_aliprice_alibaba_cn_cate__shuma": {"message": "Elektronik"}, "sbi_aliprice_alibaba_cn_cate__wanju": {"message": "Oyuncak"}, "sbi_aliprice_alibaba_cn_cate__xiangbao": {"message": "Valiz"}, "sbi_aliprice_alibaba_cn_cate__xiazhuang": {"message": "<PERSON><PERSON>"}, "sbi_aliprice_alibaba_cn_cate__xiezi": {"message": "ayakkabı"}, "sbi_aliprice_cate__apparel": {"message": "G<PERSON><PERSON><PERSON>"}, "sbi_aliprice_cate__automobiles_motorcycles": {"message": "Otomobiller ve <PERSON>ler"}, "sbi_aliprice_cate__beauty_health": {"message": "güzellik"}, "sbi_aliprice_cate__cellphones_telecommunications": {"message": "Cep Telefonları ve Telekomünikasyon"}, "sbi_aliprice_cate__computer_office": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "sbi_aliprice_cate__consumer_electronics": {"message": "Tüketici Elektroniği"}, "sbi_aliprice_cate__education_office_supplies": {"message": "Eğitim ve Ofis <PERSON>"}, "sbi_aliprice_cate__electronic_components_supplies": {"message": "Elektronik Bileşenler ve Malzemeler"}, "sbi_aliprice_cate__furniture": {"message": "Mobilya"}, "sbi_aliprice_cate__hair_extensions_wigs": {"message": "<PERSON><PERSON>kla<PERSON>"}, "sbi_aliprice_cate__home_garden": {"message": "Ev & Bahçe"}, "sbi_aliprice_cate__home_improvement": {"message": "<PERSON><PERSON>"}, "sbi_aliprice_cate__jewelry_accessories": {"message": "Takı ve Aksesuarlar"}, "sbi_aliprice_cate__luggage_bags": {"message": "Bavul ve Çantalar"}, "sbi_aliprice_cate__mother_kids": {"message": "<PERSON>"}, "sbi_aliprice_cate__novelty_special_use": {"message": "<PERSON><PERSON><PERSON> ve <PERSON>"}, "sbi_aliprice_cate__security_protection": {"message": "Güvenlik koruması"}, "sbi_aliprice_cate__shoes": {"message": "Ayakkabı"}, "sbi_aliprice_cate__sports_entertainment": {"message": "Spor ve Eğlence"}, "sbi_aliprice_cate__toys_hobbies": {"message": "Oyuncaklar ve Hobiler"}, "sbi_aliprice_cate__watches": {"message": "<PERSON><PERSON><PERSON>"}, "sbi_aliprice_cate__weddings_events": {"message": "Düğünler ve Etkinlikler"}, "sbi_btn_capture_txt": {"message": "Ele geçirmek"}, "sbi_btn_source_now_txt": {"message": "Şimdi kaynak"}, "sbi_button__chat_with_me": {"message": "<PERSON><PERSON><PERSON> et"}, "sbi_button__contact_supplier": {"message": "İletişim"}, "sbi_button__hide_on_this_site": {"message": "Bu sitede gösterme"}, "sbi_button__open_settings": {"message": "Görselle aramayı yapılandır"}, "sbi_capture_shortcut_tip": {"message": "<PERSON><PERSON> k<PERSON><PERSON><PERSON><PERSON> \"<PERSON><PERSON>\" tu<PERSON><PERSON> basın"}, "sbi_capturing_tip": {"message": "<PERSON><PERSON><PERSON>"}, "sbi_composed_rating_45": {"message": "4,5 - 5,0 <PERSON><PERSON><PERSON><PERSON>z"}, "sbi_crop_and_search": {"message": "<PERSON><PERSON>"}, "sbi_crop_start": {"message": "Ekran Görü<PERSON><PERSON><PERSON><PERSON>"}, "sbi_err_captcha_action": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "sbi_err_captcha_for_alibaba_cn": {"message": "Do<PERSON><PERSON><PERSON>a gere<PERSON>r, lütfen doğrulamak için bir resim yükleyin. ($video_tutorial$'ı görüntüleyin veya çerezleri temizlemeyi deneyin)", "placeholders": {"feedback": {"content": "$1"}, "video_tutorial": {"content": "$1"}}}, "sbi_err_captcha_for_aliprice": {"message": "Olağandışı trafik, lütfen doğrulayın"}, "sbi_err_captcha_for_taobao": {"message": "Taobao doğrulamanızı istiyor, lütfen manuel olarak bir resim yükleyin ve doğrulamak için arayın. <PERSON><PERSON> <PERSON><PERSON>, \"Resimle TaoBao arama\" yeni doğrulama politikasından kaynaklanmaktadır, Taobao $feedback$'de şikayet doğrulamasının çok sık yapılmasını öneririz.", "placeholders": {"feedback": {"content": "$1"}}}, "sbi_err_captcha_for_taobao_feedback": {"message": "geri bildirim"}, "sbi_err_captcha_msg": {"message": "$platform$, aramaya bir resim yüklemenizi veya arama kısıtlamalarını kaldırmak için güvenlik doğrulamasını tamamlamanızı gerektiriyor", "placeholders": {"platform": {"content": "$1"}}}, "sbi_err_checking_if_low_version": {"message": "En son s<PERSON><PERSON><PERSON><PERSON> o<PERSON>nı kontrol edin"}, "sbi_err_cookie_btn_clear": {"message": "Çerezleri temizlemek"}, "sbi_err_cookie_for_alibaba_cn": {"message": "1688 çerezleri silinsin mi?(Tekrar giriş yapmanız gerekiyor)"}, "sbi_err_desperate_feature_pdd": {"message": "<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> g<PERSON><PERSON>o Ara<PERSON>'ya taşındı."}, "sbi_err_hidden_skill_of_improve_search_rate": {"message": "<PERSON><PERSON><PERSON>l aramanın başarı oranı nasıl artırılır?"}, "sbi_err_img_undersize": {"message": "Resim > $imgMinimumSize$", "placeholders": {"imgMinimumSize": {"content": "$1"}}}, "sbi_err_login_required": {"message": "Giriş yapın $loginSite$", "placeholders": {"loginSite": {"content": "$1"}}}, "sbi_err_login_required_action": {"message": "Oturum aç"}, "sbi_err_low_version": {"message": "En son sür<PERSON><PERSON><PERSON> ($latestVersion$) yü<PERSON>in", "placeholders": {"latestVersion": {"content": "$1"}}}, "sbi_err_low_version_action": {"message": "<PERSON><PERSON><PERSON>"}, "sbi_err_need_help": {"message": "Yardıma İhtiyacınız Var"}, "sbi_err_network": {"message": "<PERSON><PERSON>, web sitesini ziyaret edebildiğinizden emin olun"}, "sbi_err_not_low_version": {"message": "En son s<PERSON><PERSON><PERSON><PERSON> ($latestVersion$)", "placeholders": {"latestVersion": {"content": "$1"}}}, "sbi_err_try_again": {"message": "<PERSON><PERSON><PERSON>"}, "sbi_err_try_again_action": {"message": "<PERSON><PERSON><PERSON>"}, "sbi_err_visit_and_try": {"message": "<PERSON><PERSON><PERSON> deneyin veya yeniden denemek için $website$ adresini ziyaret edin", "placeholders": {"website": {"content": "$1"}}}, "sbi_err_visit_site": {"message": "$siteName$ ana sayfasını ziyaret edin", "placeholders": {"siteName": {"content": "$1"}}}, "sbi_fail_tip": {"message": "<PERSON><PERSON><PERSON><PERSON> ba<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, lütfen sayfayı yenileyin ve tekrar deneyin."}, "sbi_kuajing_filter_area": {"message": "<PERSON>"}, "sbi_kuajing_filter_au": {"message": "Avustralya"}, "sbi_kuajing_filter_btn_confirm": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "sbi_kuajing_filter_de": {"message": "Almanya"}, "sbi_kuajing_filter_destination_country": {"message": "<PERSON><PERSON><PERSON>"}, "sbi_kuajing_filter_es": {"message": "ispanya"}, "sbi_kuajing_filter_estimate": {"message": "<PERSON><PERSON><PERSON>"}, "sbi_kuajing_filter_estimate_price": {"message": "<PERSON><PERSON><PERSON> edilen <PERSON>t"}, "sbi_kuajing_filter_estimate_price_formula": {"message": "<PERSON><PERSON><PERSON> fiyat formülü = (emtia fiyatı + uluslararası lojistik navlun)/(1 - kar marjı - diğer maliyet oranı)"}, "sbi_kuajing_filter_fr": {"message": "<PERSON><PERSON><PERSON>"}, "sbi_kuajing_filter_kw_placeholder": {"message": "Başlıkla eşleşecek anahtar kelimeleri girin"}, "sbi_kuajing_filter_logistics": {"message": "Lojistik şablonu"}, "sbi_kuajing_filter_logistics_china_post": {"message": "Çin Posta Hava Posta"}, "sbi_kuajing_filter_logistics_discount": {"message": "Lojistik indirimi"}, "sbi_kuajing_filter_logistics_epacket": {"message": "e-paket"}, "sbi_kuajing_filter_logistics_price_formula": {"message": "Uluslararası lojistik navlun = (ağırlık x nakliye fiyatı + kayıt ücreti) x (1 - indirim)"}, "sbi_kuajing_filter_others_fee": {"message": "<PERSON><PERSON><PERSON>"}, "sbi_kuajing_filter_profit_percent": {"message": "<PERSON>r marjı"}, "sbi_kuajing_filter_prop": {"message": "Öznitellikler"}, "sbi_kuajing_filter_ru": {"message": "<PERSON><PERSON><PERSON>"}, "sbi_kuajing_filter_total": {"message": "$count$ benzer öğeleri eşleştir", "placeholders": {"count": {"content": "$1"}}}, "sbi_kuajing_filter_uk": {"message": "Birleşik Krallık"}, "sbi_kuajing_filter_usa": {"message": "Amerika"}, "sbi_login_punish_title__pdd_pifa": {"message": "<PERSON><PERSON>"}, "sbi_msg_no_result": {"message": "<PERSON><PERSON><PERSON> b<PERSON>namadı,lütfen $loginSite$'a giriş yapın veya başka bir resim deneyin", "placeholders": {"loginSite": {"content": "$1"}}}, "sbi_msg_no_result_check_support_page_l1": {"message": "Safari için geçici olarak kullanılamaz, lütfen $supportPage$ kullanın.", "placeholders": {"supportPage": {"content": "$1"}}}, "sbi_msg_no_result_check_support_page_l2": {"message": "Chrome tarayıcısı ve uzantıları"}, "sbi_msg_no_result_reinstall_l1": {"message": "Son<PERSON>ç bulunamadı, lütfen $loginSite$'da oturum açın veya başka bir resim deneyin veya en son $latestExtUrl$ sürümünü yeniden yükleyin", "placeholders": {"loginSite": {"content": "$1"}, "latestExtUrl": {"content": "$2"}}}, "sbi_msg_no_result_reinstall_l2": {"message": "En son s<PERSON><PERSON><PERSON><PERSON>", "placeholders": {}}, "sbi_search_by_selected_area": {"message": "<PERSON><PERSON><PERSON><PERSON> alan"}, "sbi_shipping_": {"message": "Aynı gün kargo"}, "sbi_specify_category": {"message": "<PERSON><PERSON><PERSON><PERSON> beli<PERSON>in:"}, "sbi_start_crop": {"message": "<PERSON>"}, "sbi_store_name_alibabaCNKuaJing": {"message": "1688 denizaşırı"}, "sbi_tutorial_btn_more": {"message": "<PERSON><PERSON> fazla yol"}, "sbi_tutorial_find_coupons_on_taobao": {"message": "Taobao Kuponlarını Bul"}, "sbi_txt__empty_retry": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> b<PERSON>, lütfen tekrar den<PERSON>."}, "sbi_txt__min_order": {"message": "<PERSON><PERSON>"}, "sbi_visiting": {"message": "G<PERSON>z atılıyor"}, "sbi_yiwugo__jiagexiangtan": {"message": "Satıcıyla iletişime geç"}, "sbi_yiwugo__qigou": {"message": "$num$ Adet (ADEDI)", "placeholders": {"num": {"content": "$1"}}}, "sbi_yiwugo__xing": {"message": "Yıldızlar"}, "searchByImage_screenshot": {"message": "Tek tıklamayla ekran görüntüsü"}, "searchByImage_search": {"message": "Aynı öğeler için tek tıklamayla arama"}, "searchByImage_size_type": {"message": "Dosya boyutu $num$ MB'tan, yalnızca $type$'dan büyük olamaz", "placeholders": {"num": {"content": "$1"}, "type": {"content": "$2"}}}, "search_by_image_progress_analysing": {"message": "Görüntüyü analiz etme"}, "search_by_image_progress_searching": {"message": "<PERSON><PERSON><PERSON><PERSON> ara"}, "search_by_image_progress_sending": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "search_by_image_response_rate": {"message": "Yanıt Oranı: $responseRate$ Bu tedarikçi ile iletişim kuran alıcıların $responseInHour$ saat içinde bir yanıt aldı.", "placeholders": {"responseRate": {"content": "$1"}, "responseInHour": {"content": "$2"}}}, "search_by_keyword": {"message": "<PERSON><PERSON><PERSON>:"}, "select_country_language_modal_title_country": {"message": "<PERSON><PERSON><PERSON>"}, "select_country_language_modal_title_language": {"message": "Dil"}, "select_country_region_modal_title": {"message": "<PERSON><PERSON> <PERSON><PERSON><PERSON>/bö<PERSON>"}, "select_language_modal_title": {"message": "Bir dil seç:"}, "select_shop": {"message": "Mağaz<PERSON> seçin"}, "sellers_count": {"message": "Geçerli sayfadaki satıcı sayısı"}, "sellers_count_per_page": {"message": "Geçerli sayfadaki satıcı sayısı"}, "service_score": {"message": "Kapsamlı hizmet derecelendirmesi"}, "set_shortcut_keys": {"message": "Kısayol tuşlarını ayarla"}, "setting_logo_title": {"message": "alışveriş asistanı"}, "setting_modal_options_position_title": {"message": "<PERSON><PERSON>"}, "setting_modal_options_position_value_left": {"message": "Solda"}, "setting_modal_options_position_value_right": {"message": "Sağda"}, "setting_modal_options_theme_title": {"message": "<PERSON><PERSON> re<PERSON>i"}, "setting_modal_options_theme_value_dark": {"message": "Karanlık"}, "setting_modal_options_theme_value_light": {"message": "Işık"}, "setting_modal_title": {"message": "<PERSON><PERSON><PERSON>"}, "setting_options_country_title": {"message": "Ülke/Bölge"}, "setting_options_hover_zoom_desc": {"message": "Yakınlaştırmak için fareyle üzerine gelin"}, "setting_options_hover_zoom_title": {"message": "Git ve yakınlaştır"}, "setting_options_jd_coupon_desc": {"message": "JD.com'da kupon bulundu"}, "setting_options_jd_coupon_title": {"message": "JD.com kuponu"}, "setting_options_language_title": {"message": "Dil"}, "setting_options_price_drop_alert_desc": {"message": "En sevdiğim\" indeki malların fiyatı düştüğünde, bir bildirim alacaksınız. "}, "setting_options_price_drop_alert_title": {"message": "Fiyatı takip et"}, "setting_options_price_history_on_list_page_desc": {"message": "<PERSON><PERSON><PERSON><PERSON> arama sayfasında fiyat geçmişini gör<PERSON><PERSON><PERSON><PERSON>"}, "setting_options_price_history_on_list_page_title": {"message": "<PERSON>yat Geçmişi (Liste Sayfası)"}, "setting_options_price_history_on_produt_page_desc": {"message": "<PERSON><PERSON><PERSON><PERSON> detay sayfasında ürün geçmişini gör<PERSON><PERSON>üle"}, "setting_options_price_history_on_produt_page_title": {"message": "Fiyat geçmişi (ayrıntılar sayfası)"}, "setting_options_sales_analysis_desc": {"message": "$platforms$ ürün listesi say<PERSON><PERSON><PERSON> fiyat, satış hacmi, satıcı sayısı ve mağaza satış oranına ilişkin destek istatistikleri", "placeholders": {"platforms": {"content": "$1"}}}, "setting_options_sales_analysis_title": {"message": "Satış analizi"}, "setting_options_save_success_msg": {"message": "Başarı"}, "setting_options_tacking_price_title": {"message": "Fiyat Değişikliği Uyarısı"}, "setting_options_value_off": {"message": "Yakın"}, "setting_options_value_on": {"message": "açık"}, "setting_pkg_quick_view_desc": {"message": "Destek: 1688 ve Taobao"}, "setting_saved_message": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> baş<PERSON><PERSON><PERSON> kaydedil<PERSON>"}, "setting_section_enable_platform_title": {"message": "<PERSON><PERSON>ık ka<PERSON>ı"}, "setting_section_setting_title": {"message": "<PERSON><PERSON><PERSON>"}, "setting_section_shortcuts_title": {"message": "Kısayollar :"}, "settings_aliprice_agent__desc": {"message": "$platforms$ ürün detay sayfasında gösteriliyor", "placeholders": {"platforms": {"content": "$1"}}}, "settings_aliprice_agent__title": {"message": "<PERSON><PERSON>"}, "settings_copy_link__desc": {"message": "<PERSON><PERSON><PERSON><PERSON> detay <PERSON>"}, "settings_copy_link__title": {"message": "Kopyala düğmesi ve Arama başlığı"}, "settings_currency_desc__for_detail": {"message": "1688 ürün detay sayfasını destekleyin"}, "settings_currency_desc__for_list": {"message": "<PERSON><PERSON><PERSON><PERSON> göre arama (1688/1688 denizaşırı/Taobao dahil)"}, "settings_currency_desc__for_sbi": {"message": "Fiyatı seçin"}, "settings_currency_desc_display_for_list": {"message": "<PERSON><PERSON><PERSON><PERSON> aramada gö<PERSON>ilir (1688/1688 yurt dışı/Taobao dahil)"}, "settings_currency_rate_desc": {"message": "\"$currencyRateFrom$\" olan dö<PERSON>z kurunun g<PERSON>llen<PERSON>i", "placeholders": {"currencyRateFrom": {"content": "$1"}}}, "settings_currency_rate_from_boc": {"message": "Çin Bankası"}, "settings_download_images__desc": {"message": "$platforms$'dan resim indirme desteği", "placeholders": {"platforms": {"content": "$1"}}}, "settings_download_images__title": {"message": "resim <PERSON> indir"}, "settings_download_reviews__desc": {"message": "$platforms$ ürün detay sayfasında gösteriliyor", "placeholders": {"platforms": {"content": "$1"}}}, "settings_download_reviews__title": {"message": "İnceleme re<PERSON>ini indir"}, "settings_google_translate_desc": {"message": "Google çeviri çubuğunu almak için sağ tıklayın"}, "settings_google_translate_title": {"message": "web sayfası çevirisi"}, "settings_historical_trend_desc": {"message": "Ürün listesi sayfasındaki resmin sağ alt köşesinde görüntüle"}, "settings_modal_btn_more": {"message": "<PERSON><PERSON> fazla ayar"}, "settings_productInfo_desc": {"message": "Ürün listesi sayfasında daha ayrıntılı ürün bilgisi görüntüleyin. Bunu etkinleştirmek bilgisayarın yükünü artırabilir ve sayfa gecikmesine neden olabilir. Performansı etkiliyorsa, devre dışı bırakmanız önerilir."}, "settings_product_recommend__desc": {"message": "$platforms$ ürün ayrıntıları sayfasında ana görselin altında görüntülenir", "placeholders": {"platforms": {"content": "$1"}}}, "settings_product_recommend__name": {"message": "<PERSON>nerilen <PERSON>"}, "settings_research_desc": {"message": "Ürün listesi sayfasında daha ayrıntılı bilgi sorgulayın"}, "settings_sbi_add_to_list": {"message": "$listType$'a ekleyin", "placeholders": {"listType": {"content": "$1"}}}, "settings_sbi_detail_page_icon_title_desc": {"message": "Fotoğrafa göre arama sonuçlarının küçük resmi"}, "settings_sbi_remove_from_list": {"message": "$listType$ içinden kaldır", "placeholders": {"listType": {"content": "$1"}}}, "settings_search_by_image_add_to_blacklist": {"message": "<PERSON><PERSON><PERSON>"}, "settings_search_by_image_adjust_entrance_entrance": {"message": "<PERSON><PERSON><PERSON> kon<PERSON>u a<PERSON>ın"}, "settings_search_by_image_blacklist_desc": {"message": "Kara listedeki web sitelerinde simge gösterme."}, "settings_search_by_image_blacklist_title": {"message": "kara liste"}, "settings_search_by_image_bottom_left": {"message": "Sol Alt"}, "settings_search_by_image_bottom_right": {"message": "Sağ Alt"}, "settings_search_by_image_clear_blacklist": {"message": "<PERSON> list<PERSON>i temizle"}, "settings_search_by_image_detail_page_icon_title": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "settings_search_by_image_detail_page_icon_val_large": {"message": "<PERSON><PERSON> <PERSON><PERSON><PERSON>"}, "settings_search_by_image_detail_page_icon_val_small": {"message": "<PERSON><PERSON>"}, "settings_search_by_image_display_button_desc": {"message": "Resme göre aramak için simgeye tek tıklama"}, "settings_search_by_image_display_button_title": {"message": "Resimlerdeki simge"}, "settings_search_by_image_sourece_websites_desc": {"message": "Bu web sitelerinde kaynak ürünü bulun"}, "settings_search_by_image_sourece_websites_title": {"message": "G<PERSON>rsel sonucuna göre ara"}, "settings_search_by_image_top_left": {"message": "<PERSON>"}, "settings_search_by_image_top_right": {"message": "<PERSON>ğ Ü<PERSON>"}, "settings_search_keyword_on_x__desc": {"message": "$platform$ üzerinde kelimeleri arayın", "placeholders": {"platform": {"content": "$1"}}}, "settings_search_keyword_on_x__title": {"message": "Seçili kelimeler olduğunda $platform$ simgesini göster", "placeholders": {"platform": {"content": "$1"}}}, "settings_similar_products_desc": {"message": "bu web siteleri aynı ürünü bulmak i<PERSON> den<PERSON> (5'e maks)"}, "settings_similar_products_title": {"message": "Aynı ürünü bul"}, "settings_toolbar_expand_title": {"message": "Eklentiyi küçült"}, "settings_top_toolbar_desc": {"message": "Sayfanın üst kısmındaki Arama Çubuğu"}, "settings_top_toolbar_title": {"message": "<PERSON><PERSON>"}, "settings_translate_search_desc": {"message": "<PERSON><PERSON><PERSON>'ye çevirin ve arayın"}, "settings_translate_search_title": {"message": "Çok dilli arama"}, "settings_translator_contextmenu_title": {"message": "Çevirmek için <PERSON>"}, "settings_translator_title": {"message": "Çevirmek"}, "shai_xuan_dao_chu": {"message": "Dışa Aktarmak için Filtrele"}, "shai_xuan_zi_duan": {"message": "Alanları filtrele"}, "shang_jia_shi_jian": {"message": "<PERSON><PERSON><PERSON> ka<PERSON>"}, "shang_pin_biao_ti": {"message": "<PERSON><PERSON><PERSON><PERSON> b<PERSON>ı<PERSON>ı"}, "shang_pin_dui_bi": {"message": "<PERSON><PERSON>ün <PERSON>ştırması"}, "shang_pin_lian_jie": {"message": "<PERSON><PERSON><PERSON><PERSON> ba<PERSON>lantısı"}, "shang_pin_xin_xi": {"message": "<PERSON><PERSON><PERSON><PERSON> bil<PERSON>"}, "share_modal__content": {"message": "Arkadaşlarınla <PERSON>"}, "share_modal__disable_for_while": {"message": "Hiçbir şey paylaşmak istemiyorum"}, "share_modal__title": {"message": "$extensionName$ sever misin?", "placeholders": {"extensionName": {"content": "$1"}}}, "sheng_yu_ku_cun": {"message": "<PERSON><PERSON>"}, "shi_fou_ke_ding_zhi": {"message": "Özelleştirilebilir mi?"}, "shi_fou_ren_zheng_gong_ying_sha": {"message": "Sertifikalı Tedarikçi"}, "shi_fou_ren_zheng_gong_ying_shang_zong_shu": {"message": "Sertifikalı tedarikçiler"}, "shi_fou_you_mao_yi_dan_bao": {"message": "<PERSON><PERSON><PERSON>"}, "shi_fou_you_mao_yi_dan_bao_zong_shu": {"message": "<PERSON><PERSON><PERSON>"}, "shipping_fee": {"message": "Nakliye ücreti"}, "shop_followers": {"message": "Takipçi satın al"}, "shou_qi": {"message": "<PERSON><PERSON> az"}, "similar_products_warn_max_platforms": {"message": "En fazla 5"}, "sku_calc_price": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "sku_calc_price_settings": {"message": "Hesaplanan Fi<PERSON>t <PERSON>ı"}, "sku_formula": {"message": "<PERSON><PERSON><PERSON>"}, "sku_formula_desc": {"message": "Formül Açıklaması"}, "sku_formula_desc_text": {"message": "Karmaşık matematiksel formülleri destekler, orijinal fiyat A ile ve navlun B ile gösterilir\n\n<br/>\n\nParantezleri (), artı +, eksi -, çarpma * ve bölme / destekler\n\n<br/>\n\nÖrnek:\n\n<br/>\n\n1. Orijinal fiyatın 1,2 katına ulaşmak ve ardından navlunu eklemek için formül şudur: A*1,2+B\n\n<br/>\n\n2. Orijinal fiyatı 1 yuan artı elde etmek ve ardından 1,2 ile çarpmak için formül şudur: (A+1)*1,2\n\n<br/>\n\n3. Orijinal fiyatı 10 yuan artı elde etmek, ardından 1,2 ile çarpmak ve ardından 3 yuan çıkarmak için formül şudur: (A+10)*1,2-3"}, "sku_in_stock": {"message": "Stok<PERSON>"}, "sku_invalid_formula_format": {"message": "Geçersiz formül biçimi"}, "sku_inventory": {"message": "Stok"}, "sku_link_copy_fail": {"message": "Başarıyla kopyalandı, SKU spesifikasyonları ve nitelikleri seçilmedi"}, "sku_link_copy_success": {"message": "Başarıyla kopyalandı, SKU spesifikasyonları ve nitelikleri seçildi"}, "sku_list": {"message": "SKU Listesi"}, "sku_min_qrder_qty": {"message": "Minimum Sipariş Miktarı"}, "sku_name": {"message": "SKU Adı"}, "sku_no": {"message": "Hayır."}, "sku_original_price": {"message": "Orijinal Fiyat"}, "sku_price": {"message": "SKU Fiyatı"}, "stop_track_time_label": {"message": "<PERSON><PERSON><PERSON> son ta<PERSON><PERSON>:"}, "suo_zai_di_qu": {"message": "konum"}, "tab_pkg_quick_view": {"message": "Lojistik Monitörü"}, "tab_product_details_price_history": {"message": "<PERSON><PERSON><PERSON>"}, "tab_product_details_reviews": {"message": "yorum<PERSON>"}, "tab_product_details_seller_analysis": {"message": "analiz"}, "tab_product_details_similar_products": {"message": "Aynı"}, "total_days_listed_per_product": {"message": "Rafta kalan günlerin toplamı ÷ Ürün sayısı"}, "total_items": {"message": "Toplam ürün say<PERSON>ı"}, "total_price_per_product": {"message": "Fiyat toplamı ÷ Ürün sayısı"}, "total_rating_per_product": {"message": "Derecelendirmelerin toplamı ÷ Ürün sayısı"}, "total_revenue": {"message": "Toplam gelir"}, "total_revenue40_items": {"message": "Mevcut sayfadaki $amount$ ürünün toplam geliri", "placeholders": {"amount": {"content": "$1"}}}, "total_sales": {"message": "Toplam satış"}, "total_sales40_items": {"message": "Mevcut sayfadaki $amount$ ürünün toplam satışı", "placeholders": {"amount": {"content": "$1"}}}, "track_for_12_months": {"message": "Parça: 1 yıl"}, "track_for_3_months": {"message": "Takip süresi: 3 ay"}, "track_for_6_months": {"message": "Takip süresi: 6 ay"}, "tracking_price_email_add_btn": {"message": "E-posta ekle"}, "tracking_price_email_edit_btn": {"message": "E-postayı düzenle"}, "tracking_price_email_intro": {"message": "Size e-posta ile bildireceğiz."}, "tracking_price_email_invalid": {"message": "Lütfen geçerli bir e-posta adresi girin"}, "tracking_price_email_verified_desc": {"message": "Artık fiyat düşüşü uyarımızı alabilirsiniz."}, "tracking_price_email_verified_title": {"message": "Başarıyla doğrulandı"}, "tracking_price_email_verify_desc_line1": {"message": "E-posta adresinize bir doğrulama bağlantısı gönderdik,"}, "tracking_price_email_verify_desc_line2": {"message": "lütfen e-posta gelen kutunuzu kontrol edin."}, "tracking_price_email_verify_title": {"message": "E-mail'i doğrula"}, "tracking_price_web_push_notification_intro": {"message": "Masaüstünde: Ali<PERSON>rice si<PERSON> için herhangi bir ürünü izleyebilir ve fiyat değiştiğinde size bir Web Push Bildirimi gönderebilir."}, "tracking_price_web_push_notification_title": {"message": "Web Push Bildirimleri"}, "translate_im__login_required": {"message": "AliPrice tarafı<PERSON>n çevrilmiştir, lütfen $loginUrl$ adresinde oturum açın", "placeholders": {"loginUrl": {"content": "$1"}}}, "translate_im__paste_fail": {"message": "Çevrildi ve panoya kopyalandı, ancak Aliwangwang'ın sınırlaması nedeniyle bunu manuel olarak yapıştırmanız gerekiyor!"}, "translate_im__send": {"message": "<PERSON><PERSON><PERSON>"}, "translate_search": {"message": "<PERSON><PERSON><PERSON> <PERSON>"}, "translation_originals_translated": {"message": "Orijinal ve Çince"}, "translation_translated": {"message": "<PERSON><PERSON><PERSON>"}, "translator_btn_capture_txt": {"message": "Çevirmek"}, "translator_language_auto_detect": {"message": "Otomatik algılama"}, "translator_language_detected": {"message": "Saptanmış"}, "translator_language_search_placeholder": {"message": "<PERSON><PERSON>"}, "try_again": {"message": "<PERSON><PERSON><PERSON>"}, "tu_pian_chi_cun": {"message": "<PERSON><PERSON><PERSON>:"}, "tu_pian_lian_jie": {"message": "Resim Bağlantısı"}, "tui_huan_ti_yan": {"message": "<PERSON><PERSON>"}, "tui_huan_ti_yan__desc": {"message": "Satıcıların satış sonrası göstergelerini değerlendirin"}, "tutorial__show_all": {"message": "<PERSON><PERSON><PERSON>"}, "tutorial_ae_popup_title": {"message": "Uzan<PERSON><PERSON><PERSON><PERSON>, Aliexpress'i açın"}, "tutorial_aliexpress_reviews_analysis": {"message": "AliExpress İnceleme <PERSON>"}, "tutorial_aliprice_agent_with1688_desc_l1": {"message": "Destek USD"}, "tutorial_aliprice_agent_with1688_desc_l2": {"message": "<PERSON><PERSON>/<PERSON><PERSON>/<PERSON><PERSON>"}, "tutorial_aliprice_agent_with1688_title": {"message": "1688 Yurt Dışı Alımı Destekliyor"}, "tutorial_auto_apply_coupon_title": {"message": "<PERSON><PERSON><PERSON> otomatik uygula"}, "tutorial_btn_end": {"message": "Son"}, "tutorial_btn_example": {"message": "Örnek"}, "tutorial_btn_have_a_try": {"message": "Tamam, bir dene"}, "tutorial_btn_next": {"message": "Ayrıca"}, "tutorial_btn_see_more": {"message": "Da<PERSON>"}, "tutorial_compare_products": {"message": "Aynı stille karşılaştırın"}, "tutorial_currency_convert_title": {"message": "döviz kuru dönüştürme"}, "tutorial_export_shopping_cart": {"message": "CSV'yi dı<PERSON>, Taobao ve 1688'u destekley<PERSON>"}, "tutorial_export_shopping_cart_title": {"message": "ihracat sepeti"}, "tutorial_price_history_pro": {"message": "<PERSON><PERSON><PERSON><PERSON> detay say<PERSON>ı<PERSON> görüntülenir.\n<PERSON><PERSON>, <PERSON><PERSON><PERSON>, Amazon ve Ebay'ı destekleyin"}, "tutorial_price_history_pro_title": {"message": "Tam yıl fiyat geçmişi ve sipariş geçmişi"}, "tutorial_sbi_context_menu_screenshot_title": {"message": "Aynı stil için ekran görüntüsü araması"}, "tutorial_translate_search": {"message": "Ara<PERSON><PERSON>"}, "tutorial_translate_search_and_package_tracking": {"message": "Çeviri arama ve paket takibi"}, "unit_bao": {"message": "adet"}, "unit_ben": {"message": "adet"}, "unit_bi": {"message": "emirler"}, "unit_chuang": {"message": "adet"}, "unit_dai": {"message": "adet"}, "unit_dui": {"message": "çift"}, "unit_fen": {"message": "adet"}, "unit_ge": {"message": "adet"}, "unit_he": {"message": "adet"}, "unit_jian": {"message": "adet"}, "unit_li_fang_mi": {"message": "m³"}, "unit_ping": {"message": "adet"}, "unit_ping_fang_mi": {"message": "㎡"}, "unit_shuang": {"message": "çift"}, "unit_tai": {"message": "adet"}, "unit_ti": {"message": "adet"}, "unit_tiao": {"message": "adet"}, "unit_xiang": {"message": "adet"}, "unit_zhang": {"message": "adet"}, "unit_zhi": {"message": "adet"}, "verify_contact_support": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "verify_human_verification": {"message": "İnsan <PERSON>ı"}, "verify_unusual_access": {"message": "Olağandışı eri<PERSON>"}, "view_history_clean_all": {"message": "<PERSON><PERSON><PERSON> temizle"}, "view_history_clean_all_warring": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>n tüm kayıtlar temizlensin mi?"}, "view_history_clean_all_warring_title": {"message": "Uyarı"}, "view_history_viewd": {"message": "G<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "website": {"message": "web sitesi"}, "weight": {"message": "Ağırlık"}, "wu_fa_huo_qu_dao_gai_shu_ju": {"message": "<PERSON><PERSON><PERSON> alınamıyor"}, "wu_liu_shi_xiao": {"message": "Zamanında sevkiyat"}, "wu_liu_shi_xiao__desc": {"message": "Satıcının mağazasının 48 saatlik tahsilat oranı ve sipariş karşılama oranı"}, "xia_dan_jia": {"message": "<PERSON> <PERSON>yat"}, "xian_xuan_ze_product_attributes": {"message": "<PERSON><PERSON><PERSON><PERSON> ö<PERSON> seçin"}, "xiao_liang": {"message": "Satış hacmi"}, "xiao_liang_zhan_bi": {"message": "Satış hacminin yüzdesi"}, "xiao_shi": {"message": "$num$ Saat", "placeholders": {"num": {"content": "$1"}}}, "xiao_shou_e": {"message": "<PERSON><PERSON><PERSON>"}, "xiao_shou_e_zhan_bi": {"message": "<PERSON><PERSON><PERSON>"}, "xuan_zhong_x_tiao_ji_lu": {"message": "$amount$ kayıt seçin", "placeholders": {"amoun": {"content": "$1"}, "amount": {"content": "$1"}}}, "yan_xuan": {"message": "<PERSON><PERSON><PERSON>"}, "yi_ding_zai_zuo_ce": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "yi_jia_zai_wan_suo_you_shang_pin": {"message": "Yüklenen Tüm <PERSON>"}, "yi_nian_xiao_liang": {"message": "Yıllık Satışlar"}, "yi_nian_xiao_liang_zhan_bi": {"message": "Yıllık Satış Payı"}, "yi_nian_xiao_shou_e": {"message": "Yıllık Ciro"}, "yi_nian_xiao_shou_e_zhan_bi": {"message": "Yıllık Ciro Payı"}, "yi_shua_xin": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "yin_cang_xiang_tong_dian": {"message": "benzerlikleri gizle"}, "you_xiao_liang": {"message": "Satış Hacmi ile"}, "yu_ji_dao_da_shi_jian": {"message": "<PERSON><PERSON><PERSON>"}, "yuan_gong_ren_shu": {"message": "Çalışan Sayısı"}, "yue_cheng_jiao": {"message": "<PERSON><PERSON><PERSON>k hacim"}, "yue_dai_xiao": {"message": "<PERSON><PERSON>"}, "yue_dai_xiao__desc": {"message": "Son 30 günde dropshipping <PERSON><PERSON><PERSON>ları"}, "yue_dai_xiao_pai_xu__desc": {"message": "Son 30 gündeki dropshipping satışları yüksekten düşüğe doğru sıralandı"}, "yue_xiao_liang__desc": {"message": "Son 30 gündeki satış hacmi"}, "zhan_kai": {"message": "<PERSON><PERSON> fazla"}, "zhe_kou": {"message": "İndirim"}, "zhi_chi_yi_jian_dai_fa": {"message": "<PERSON><PERSON>"}, "zhi_chi_yi_jian_dai_fa_bao_you": {"message": "Ücretsiz kargo"}, "zhi_fu_ding_dan_shu": {"message": "<PERSON>cre<PERSON><PERSON>"}, "zhi_fu_ding_dan_shu__desc": {"message": "<PERSON>u ürün için sipariş sayısı (30 gün)"}, "zhu_ce_xing_zhi": {"message": "<PERSON><PERSON><PERSON> niteli<PERSON>"}, "zi_ding_yi_tiao_jian": {"message": "<PERSON><PERSON>"}, "zi_duan": {"message": "<PERSON><PERSON>"}, "zi_ti_xiao_liang": {"message": "Varyasyon Satıldı"}, "zong_he_fu_wu_fen": {"message": "<PERSON><PERSON>"}, "zong_he_fu_wu_fen__desc": {"message": "Satıcı hizmetinin genel değerlendirmesi"}, "zong_he_fu_wu_fen__short": {"message": "Değerlendirme"}, "zong_he_ti_yan_fen": {"message": "Değerlendirme"}, "zong_he_ti_yan_fen_3": {"message": "4 Yıldızın Altında"}, "zong_he_ti_yan_fen_4": {"message": "4 - 4,5 Yıldız"}, "zong_he_ti_yan_fen_4_5": {"message": "4,5 - 5,0 <PERSON><PERSON><PERSON><PERSON>z"}, "zong_he_ti_yan_fen_5": {"message": "5 yıldız"}, "zong_ku_cun": {"message": "<PERSON><PERSON> en<PERSON>"}, "zong_xiao_liang": {"message": "Toplam satış"}, "zui_jin_30D_3Min_xiang_ying_lv": {"message": "Son 30 günde 3 dakikalık yanıt oranı"}, "zui_jin_30D_48H_lan_shou_lv": {"message": "Son 30 günde 48H iyileşme oranı"}, "zui_jin_30D_48H_lv_yue_lv": {"message": "Son 30 gündeki 48H performans oranı"}, "zui_jin_30D_jiao_yi_ji_lu": {"message": "<PERSON><PERSON><PERSON> ka<PERSON> (30 gün)"}, "zui_jin_30D_jiao_yi_ji_lu__desc": {"message": "<PERSON><PERSON><PERSON> ka<PERSON> (30 gün)"}, "zui_jin_30D_jiu_fen_lv": {"message": "Son 30 gündeki itiraz oranı"}, "zui_jin_30D_pin_zhi_tui_kuan_lv": {"message": "Son 30 gündeki kalite geri ödeme oranı"}, "zui_jin_30D_zhi_fu_ding_dan_shu": {"message": "Son 30 gündeki ödeme emri <PERSON>ı"}}