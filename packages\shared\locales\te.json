{"EXTENSION_NAME": {"message": "TODO: EXTENSION_NAME"}, "EXTENSION_DESCRIPTION": {"message": "TODO: EXTENSION_DESCRIPTION"}, "1688_cross_border_hot_selling": {"message": "1688 క్రాస్-బోర్డర్ హాట్ సెల్లింగ్ స్పాట్"}, "1688_shi_li_ren_zheng": {"message": "1688 బలం ధృవీకరణ"}, "1_jian_qi_pi": {"message": "MOQ: 1"}, "1year_yi_shang": {"message": "1 సంవత్సరం కంటే ఎక్కువ"}, "24H": {"message": "24H"}, "24H_fa_huo": {"message": "24 గంటల్లో డెలివరీ"}, "24H_lan_shou_lv": {"message": "24-గంటల ప్యాకేజింగ్ రేటు"}, "30D_shang_xin": {"message": "నెలవారీ కొత్త రాకపోకలు"}, "30d_sales": {"message": "నెలవారీ అమ్మకాలు:$amount$", "placeholders": {"amount": {"content": "$1"}}}, "3Min_xiang_ying_lv": {"message": "3 నిమిషాలలోపు ప్రతిస్పందన."}, "3Min_xiang_ying_lv__desc": {"message": "గత 30 రోజుల్లో 3 నిమిషాల్లో కొనుగోలుదారు విచారణ సందేశాలకు వాంగ్వాంగ్ యొక్క ప్రభావవంతమైన ప్రతిస్పందనల నిష్పత్తి"}, "48H": {"message": "48H"}, "48H_fa_huo": {"message": "48 గంటల్లో డెలివరీ"}, "48H_lan_shou_lv": {"message": "48-గంటల ప్యాకేజింగ్ రేటు"}, "48H_lan_shou_lv__desc": {"message": "మొత్తం ఆర్డర్‌ల సంఖ్యకు 48 గంటలలోపు పికప్ ఆర్డర్ నంబర్ నిష్పత్తి"}, "48H_lv_yue_lv": {"message": "48-గంటల పనితీరు రేటు"}, "48H_lv_yue_lv__desc": {"message": "మొత్తం ఆర్డర్‌ల సంఖ్యకు 48 గంటలలోపు తీసుకున్న లేదా డెలివరీ చేయబడిన ఆర్డర్ సంఖ్య నిష్పత్తి"}, "72H": {"message": "72H"}, "7D_shang_xin": {"message": "వారం వారీ కొత్త రాకపోకలు"}, "7D_wu_li_you": {"message": "7 రోజుల కేర్ ఫ్రీ"}, "ABS_title_text": {"message": "ఈ జాబితాలో బ్రాండ్ కథనం ఉంది"}, "AC_title_text": {"message": "ఈ లిస్టింగ్‌లో Amazon's Choice బ్యాడ్జ్ ఉంది"}, "A_title_text": {"message": "ఈ జాబితాలో A+ కంటెంట్ పేజీ ఉంది"}, "BS_title_text": {"message": "ఈ జాబితా $type$ వర్గంలో $num$ బెస్ట్ సెల్లర్‌గా ర్యాంక్ చేయబడింది", "placeholders": {"type": {"content": "$1"}, "num": {"content": "$2"}}}, "LTD_title_text": {"message": "LTD (పరిమిత కాల ఒప్పందం) అంటే ఈ జాబితా \"7-రోజుల ప్రమోషన్\" ఈవెంట్‌లో భాగం"}, "NR_title_text": {"message": "ఈ జాబితా $type$ వర్గంలో $num$ కొత్త విడుదలగా ర్యాంక్ చేయబడింది", "placeholders": {"type": {"content": "$1"}, "num": {"content": "$2"}}}, "SBV_title_text": {"message": "ఈ జాబితాలో వీడియో ప్రకటన ఉంది, సాధారణంగా శోధన ఫలితాల మధ్యలో కనిపించే PPC ప్రకటన రకం"}, "SB_title_text": {"message": "ఈ జాబితాలో బ్రాండ్ ప్రకటన ఉంది, సాధారణంగా శోధన ఫలితాల ఎగువన లేదా దిగువన కనిపించే PPC ప్రకటన రకం"}, "SP_title_text": {"message": "ఈ జాబితాలో ప్రాయోజిత ఉత్పత్తి ప్రకటన ఉంది"}, "V_title_text": {"message": "ఈ జాబితాలో వీడియో పరిచయం ఉంది"}, "advanced_research": {"message": "అధునాతన పరిశోధన"}, "agent_ds1688___my_order": {"message": "నా ఆదేశాలు"}, "agent_ds1688__add_to_cart": {"message": "విదేశీ కొనుగోలు"}, "agent_ds1688__cart": {"message": "షాపింగ్ కార్ట్"}, "agent_ds1688__desc": {"message": "1688 ద్వారా అందించబడింది. ఇది విదేశాల నుండి నేరుగా కొనుగోలు చేయడానికి, USDలో చెల్లింపు మరియు చైనాలోని మీ రవాణా గిడ్డంగికి డెలివరీ చేయడానికి మద్దతు ఇస్తుంది."}, "agent_ds1688__freight": {"message": "షిప్పింగ్ ఖర్చు కాలిక్యులేటర్"}, "agent_ds1688__help": {"message": "సహాయం"}, "agent_ds1688__packages": {"message": "వేబిల్లు"}, "agent_ds1688__profile": {"message": "వ్యక్తిగత కేంద్రం"}, "agent_ds1688__warehouse": {"message": "నా గిడ్డంగి"}, "ai_comment_analysis_advantage": {"message": "ప్రోస్"}, "ai_comment_analysis_ai": {"message": "AI సమీక్ష విశ్లేషణ"}, "ai_comment_analysis_available": {"message": "అందుబాటులో ఉంది"}, "ai_comment_analysis_balance": {"message": "తగినంత నాణేలు లేవు, దయచేసి టాప్ అప్ చేయండి"}, "ai_comment_analysis_behavior": {"message": "ప్రవర్తన"}, "ai_comment_analysis_characteristic": {"message": "గుంపు లక్షణాలు"}, "ai_comment_analysis_comment": {"message": "ఖచ్చితమైన తీర్మానాలను రూపొందించడానికి ఉత్పత్తికి తగినంత సమీక్షలు లేవు, దయచేసి మరిన్ని సమీక్షలతో ఉత్పత్తిని ఎంచుకోండి."}, "ai_comment_analysis_consume": {"message": "అంచనా వినియోగం"}, "ai_comment_analysis_default": {"message": "డిఫాల్ట్ సమీక్షలు"}, "ai_comment_analysis_desire": {"message": "కస్టమర్ అంచనాలు"}, "ai_comment_analysis_disadvantage": {"message": "ప్రతికూలతలు"}, "ai_comment_analysis_free": {"message": "ఉచిత ప్రయత్నాలు"}, "ai_comment_analysis_freeNum": {"message": "1 ఉచిత క్రెడిట్ ఉపయోగించబడుతుంది"}, "ai_comment_analysis_go_recharge": {"message": "పైకి వెళ్లండి"}, "ai_comment_analysis_intelligence": {"message": "తెలివైన సమీక్ష విశ్లేషణ"}, "ai_comment_analysis_location": {"message": "స్థానం"}, "ai_comment_analysis_motive": {"message": "కొనుగోలు ప్రేరణ"}, "ai_comment_analysis_network_error": {"message": "నెట్‌వర్క్ లోపం, దయచేసి మళ్లీ ప్రయత్నించండి"}, "ai_comment_analysis_normal": {"message": "ఫోటో సమీక్షలు"}, "ai_comment_analysis_number_reviews": {"message": "సమీక్షల సంఖ్య: $num$, అంచనా వేయబడిన వినియోగం: $consume$", "placeholders": {"num": {"content": "$1"}, "consume": {"content": "$2"}}}, "ai_comment_analysis_ordinary": {"message": "సాధారణ వ్యాఖ్యలు"}, "ai_comment_analysis_percentage": {"message": "శాతం"}, "ai_comment_analysis_problem": {"message": "చెల్లింపుతో సమస్యలు"}, "ai_comment_analysis_reanalysis": {"message": "తిరిగి విశ్లేషించండి"}, "ai_comment_analysis_reason": {"message": "కారణం"}, "ai_comment_analysis_recharge": {"message": "అదనం"}, "ai_comment_analysis_recharged": {"message": "నేను టాప్ అప్ చేసాను"}, "ai_comment_analysis_retry": {"message": "మళ్లీ ప్రయత్నించండి"}, "ai_comment_analysis_scene": {"message": "వినియోగ దృశ్యం"}, "ai_comment_analysis_start": {"message": "విశ్లేషించడం ప్రారంభించండి"}, "ai_comment_analysis_subject": {"message": "అంశాలు"}, "ai_comment_analysis_time": {"message": "ఉపయోగం సమయం"}, "ai_comment_analysis_tool": {"message": "AI సాధనం"}, "ai_comment_analysis_user_portrait": {"message": "వినియోగదారు వివరాలు"}, "ai_comment_analysis_welcome": {"message": "AI సమీక్ష విశ్లేషణకు స్వాగతం"}, "ai_comment_analysis_year": {"message": "గత సంవత్సరం నుండి వ్యాఖ్యలు"}, "ai_listing_Exclude_keywords": {"message": "కీలకపదాలను మినహాయించండి"}, "ai_listing_Login_the_feature": {"message": "ఫీచర్ కోసం లాగిన్ అవసరం"}, "ai_listing_aI_generation": {"message": "AI తరం"}, "ai_listing_add_automatic": {"message": "ఆటోమేటిక్"}, "ai_listing_add_dictionary_new": {"message": "కొత్త లైబ్రరీని సృష్టించండి"}, "ai_listing_add_enter_keywords": {"message": "కీలకపదాలను నమోదు చేయండి"}, "ai_listing_add_inputkey_selling": {"message": "ఒక విక్రయ కేంద్రాన్ని నమోదు చేసి, జోడించడం పూర్తి చేయడానికి $key$ని నొక్కండి", "placeholders": {"key": {"content": "$1"}}}, "ai_listing_add_inputkey_warning": {"message": "పరిమితి మించిపోయింది, గరిష్టంగా $amount$ అమ్మకపు పాయింట్‌లు", "placeholders": {"amount": {"content": "$1"}}}, "ai_listing_add_keywords": {"message": "కీలకపదాలను జోడించండి"}, "ai_listing_add_manually": {"message": "మాన్యువల్‌గా జోడించండి"}, "ai_listing_add_selling": {"message": "అమ్మకపు పాయింట్లను జోడించండి"}, "ai_listing_added_keywords": {"message": "కీలకపదాలు జోడించబడ్డాయి"}, "ai_listing_added_successfully": {"message": "విజయవంతంగా జోడించబడింది"}, "ai_listing_addexcluded_keywords": {"message": "మినహాయించిన కీలకపదాలను నమోదు చేయండి, జోడించడం పూర్తి చేయడానికి ఎంటర్ నొక్కండి."}, "ai_listing_adding_selling": {"message": "అమ్మకపు పాయింట్లు జోడించబడ్డాయి"}, "ai_listing_addkeyword_enter": {"message": "జోడించడం పూర్తి చేయడానికి కీ అట్రిబ్యూట్ పదాలను టైప్ చేసి ఎంటర్ నొక్కండి"}, "ai_listing_ai_description": {"message": "AI వివరణ పద లైబ్రరీ"}, "ai_listing_ai_dictionary": {"message": "AI టైటిల్ వర్డ్ లైబ్రరీ"}, "ai_listing_ai_title": {"message": "AI శీర్షిక"}, "ai_listing_aidescription_repeated": {"message": "AI వివరణ పదం లైబ్రరీ పేరు పునరావృతం కాదు"}, "ai_listing_aititle_repeated": {"message": "AI శీర్షిక పదం లైబ్రరీ పేరు పునరావృతం కాదు"}, "ai_listing_data_comes_from": {"message": "డేటా దీని నుండి వస్తుంది:"}, "ai_listing_deleted_successfully": {"message": "విజయవంతంగా తొలగించబడింది"}, "ai_listing_dictionary_name": {"message": "లైబ్రరీ పేరు"}, "ai_listing_edit_dictionary": {"message": "లైబ్రరీని సవరించు..."}, "ai_listing_edit_word_library": {"message": "లైబ్రరీ అనే పదాన్ని సవరించండి"}, "ai_listing_enter_keywords": {"message": "జోడించడాన్ని పూర్తి చేయడానికి కీలకపదాలను నమోదు చేసి, $key$ని నొక్కండి", "placeholders": {"key": {"content": "$1"}}}, "ai_listing_exceeded_maximum": {"message": "పరిమితి మించిపోయింది, గరిష్టంగా $amount$ కీలకపదాలు", "placeholders": {"amount": {"content": "$1"}}}, "ai_listing_excluded_word_library": {"message": "వర్డ్ లైబ్రరీ మినహాయించబడింది"}, "ai_listing_generate_characters": {"message": "పాత్రలను రూపొందించండి"}, "ai_listing_generation_platform": {"message": "జనరేషన్ వేదిక"}, "ai_listing_help_optimize": {"message": "ఉత్పత్తి శీర్షికను ఆప్టిమైజ్ చేయడంలో నాకు సహాయపడండి, అసలు శీర్షిక"}, "ai_listing_include_selling": {"message": "ఇతర అమ్మకపు పాయింట్లు ఉన్నాయి:"}, "ai_listing_included_keyword": {"message": "కీలకపదాలు చేర్చబడ్డాయి"}, "ai_listing_included_keywords": {"message": "కీలకపదాలు చేర్చబడ్డాయి"}, "ai_listing_input_selling": {"message": "విక్రయ కేంద్రాన్ని నమోదు చేయండి"}, "ai_listing_input_selling_fit": {"message": "టైటిల్‌తో సరిపోలడానికి విక్రయ పాయింట్‌లను నమోదు చేయండి"}, "ai_listing_input_selling_please": {"message": "దయచేసి విక్రయ పాయింట్లను నమోదు చేయండి"}, "ai_listing_intelligently_title": {"message": "శీర్షికను తెలివిగా రూపొందించడానికి పైన అవసరమైన కంటెంట్‌ను నమోదు చేయండి"}, "ai_listing_keyword_product_title": {"message": "కీవర్డ్ ఉత్పత్తి శీర్షిక"}, "ai_listing_keywords_repeated": {"message": "కీవర్డ్‌లను పునరావృతం చేయడం సాధ్యం కాదు"}, "ai_listing_listed_selling_points": {"message": "అమ్మకపు పాయింట్లు చేర్చబడ్డాయి"}, "ai_listing_long_title_1": {"message": "బ్రాండ్ పేరు, ఉత్పత్తి రకం, ఉత్పత్తి లక్షణాలు మొదలైన ప్రాథమిక సమాచారాన్ని కలిగి ఉంటుంది."}, "ai_listing_long_title_2": {"message": "ప్రామాణిక ఉత్పత్తి శీర్షిక ఆధారంగా, SEOకి అనుకూలమైన కీలకపదాలు జోడించబడతాయి."}, "ai_listing_long_title_3": {"message": "బ్రాండ్ పేరు, ఉత్పత్తి రకం, ఉత్పత్తి లక్షణాలు మరియు కీలక పదాలను కలిగి ఉండటంతో పాటు, నిర్దిష్ట, విభజించబడిన శోధన ప్రశ్నలలో అధిక ర్యాంకింగ్‌లను సాధించడానికి లాంగ్-టెయిల్ కీలకపదాలు కూడా చేర్చబడ్డాయి."}, "ai_listing_longtail_keyword_product_title": {"message": "లాంగ్-టెయిల్ కీవర్డ్ ఉత్పత్తి శీర్షిక"}, "ai_listing_manually_enter": {"message": "మాన్యువల్‌గా నమోదు చేయండి..."}, "ai_listing_network_not_working": {"message": "ఇంటర్నెట్ అందుబాటులో లేదు, ChatGPTని యాక్సెస్ చేయడానికి VPN అవసరం"}, "ai_listing_new_dictionary": {"message": "కొత్త పద లైబ్రరీని సృష్టించండి..."}, "ai_listing_new_generate": {"message": "సృష్టించు"}, "ai_listing_optional_words": {"message": "ఐచ్ఛిక పదాలు"}, "ai_listing_original_title": {"message": "అసలు శీర్షిక"}, "ai_listing_other_keywords_included": {"message": "ఇతర కీలకపదాలు ఉన్నాయి:"}, "ai_listing_please_again": {"message": "దయచేసి మళ్లీ ప్రయత్నించండి"}, "ai_listing_please_select": {"message": "కింది శీర్షికలు మీ కోసం రూపొందించబడ్డాయి, దయచేసి ఎంచుకోండి:"}, "ai_listing_product_category": {"message": "ఉత్పత్తి వర్గం"}, "ai_listing_product_category_is": {"message": "ఉత్పత్తి వర్గం"}, "ai_listing_product_category_to": {"message": "ఉత్పత్తి ఏ వర్గానికి చెందినది?"}, "ai_listing_random_keywords": {"message": "యాదృచ్ఛిక $amount$ కీలకపదాలు", "placeholders": {"amount": {"content": "$1"}}}, "ai_listing_random_selling": {"message": "యాదృచ్ఛిక $amount$ విక్రయ పాయింట్‌లు", "placeholders": {"amount": {"content": "$1"}}}, "ai_listing_randomize_keywords": {"message": "లైబ్రరీ అనే పదం నుండి రాండమైజ్ చేయండి"}, "ai_listing_search_selling": {"message": "విక్రయ పాయింట్ ద్వారా శోధించండి"}, "ai_listing_select_product_categories": {"message": "ఉత్పత్తి వర్గాలను స్వయంచాలకంగా ఎంచుకోండి."}, "ai_listing_select_product_selling_points": {"message": "ఉత్పత్తి విక్రయ కేంద్రాలను స్వయంచాలకంగా ఎంచుకోండి"}, "ai_listing_select_word_library": {"message": "పద లైబ్రరీని ఎంచుకోండి"}, "ai_listing_selling": {"message": "విక్రయ పాయింట్లు"}, "ai_listing_selling_ask": {"message": "టైటిల్ కోసం ఏ ఇతర అమ్మకపు పాయింట్ అవసరాలు ఉన్నాయి?"}, "ai_listing_selling_optional": {"message": "ఐచ్ఛిక విక్రయ పాయింట్లు"}, "ai_listing_selling_repeat": {"message": "పాయింట్లు డూప్లికేట్ చేయబడవు"}, "ai_listing_set_excluded": {"message": "మినహాయించబడిన పద లైబ్రరీగా సెట్ చేయండి"}, "ai_listing_set_include_selling_points": {"message": "అమ్మకపు పాయింట్లను చేర్చండి"}, "ai_listing_set_included": {"message": "చేర్చబడిన పద లైబ్రరీగా సెట్ చేయండి"}, "ai_listing_set_selling_dictionary": {"message": "సెల్లింగ్ పాయింట్ లైబ్రరీగా సెట్ చేయండి"}, "ai_listing_standard_product_title": {"message": "ప్రామాణిక ఉత్పత్తి శీర్షిక"}, "ai_listing_translated_title": {"message": "అనువదించబడిన శీర్షిక"}, "ai_listing_visit_chatGPT": {"message": "ChatGPTని సందర్శించండి"}, "ai_listing_what_other_keywords": {"message": "టైటిల్ కోసం ఏ ఇతర కీలకపదాలు అవసరం?"}, "aliprice_coupons_apply_again": {"message": "మళ్లీ దరఖాస్తు చేసుకోండి"}, "aliprice_coupons_apply_coupons": {"message": "కూపన్లను వర్తింపజేయండి"}, "aliprice_coupons_apply_success": {"message": "కూపన్ కనుగొనబడింది: $amount$ని సేవ్ చేయండి", "placeholders": {"amount": {"content": "$1"}}}, "aliprice_coupons_applying": {"message": "ఉత్తమ డీల్‌ల కోసం కోడ్‌లను పరీక్షిస్తోంది..."}, "aliprice_coupons_applying_desc": {"message": "తనిఖీ చేస్తోంది: $code$", "placeholders": {"code": {"content": "$1"}}}, "aliprice_coupons_back_to_checkout": {"message": "చెక్అవుట్‌కు కొనసాగండి"}, "aliprice_coupons_found_coupons": {"message": "మేము $amount$ కూపన్‌లను కనుగొన్నాము", "placeholders": {"amount": {"content": "$1"}}}, "aliprice_coupons_found_coupons_desc": {"message": "చెక్అవుట్ చేయడానికి సిద్ధంగా ఉన్నారా? మీరు ఉత్తమ ధరను పొందారని నిర్ధారించుకుందాం!"}, "aliprice_coupons_no_coupon_aviable": {"message": "ఆ కోడ్‌లు పని చేయలేదు. పెద్దగా ఏమీ లేదు-మీరు ఇప్పటికే ఉత్తమ ధరను పొందుతున్నారు."}, "aliprice_coupons_toolbar_btn": {"message": "కూపన్లు పొందండి"}, "aliww_translate": {"message": "అలివాంగ్వాంగ్ చాట్ అనువాదకుడు"}, "aliww_translate_supports": {"message": "మద్దతు: 1688 & టావోబావో"}, "amazon_extended_keywords_Keywords": {"message": "కీలకపదాలు"}, "amazon_extended_keywords_copy_all": {"message": "అన్నింటినీ కాపీ చేయండి"}, "amazon_extended_keywords_more": {"message": "మరిన్ని"}, "an_lei_ji_xiao_liang_pai_xu": {"message": "సంచిత అమ్మకాల ద్వారా క్రమబద్ధీకరించండి"}, "an_lei_xing_cha_kan": {"message": "రకం ద్వారా"}, "an_yue_dai_xiao_pai_xu": {"message": "అమ్మకాలను తగ్గించడం ద్వారా ర్యాంకింగ్"}, "apra_btn__cat_name": {"message": "సమీక్షల విశ్లేషణ"}, "apra_chart__name": {"message": "దేశం వారీగా ఉత్పత్తుల అమ్మకాల శాతం"}, "apra_chart__update_at": {"message": "అప్‌డేట్ సమయం $time$", "placeholders": {"time": {"content": "$1"}}}, "apra_name": {"message": "దేశాల అమ్మకాల గణాంకాలు"}, "auto_opening": {"message": "$num$ సెకన్లలో స్వయంచాలకంగా తెరవబడుతుంది", "placeholders": {"num": {"content": "$1"}}}, "auto_page_next_x_pages": {"message": "తదుపరి $autoPaging$ పేజీలు", "placeholders": {"autoPaging": {"content": "$1"}}}, "average_days_listed": {"message": "షెల్ఫ్ రోజులలో సగటు"}, "average_hui_fu_lv": {"message": "సగటు ప్రత్యుత్తర రేటు"}, "average_ping_gong_ying_shang_deng_ji": {"message": "సగటు సరఫరాదారు స్థాయి"}, "average_price": {"message": "సగటు ధర"}, "average_qi_ding_liang": {"message": "సగటు MOQ"}, "average_rating": {"message": "సగటు రేటింగ్"}, "average_ren_zheng_gong_ying_nian_chang": {"message": "సర్టిఫికేషన్ యొక్క సగటు సంవత్సరాలు"}, "average_revenue": {"message": "సగటు ఆదాయం"}, "average_revenue_per_product": {"message": "మొత్తం రాబడి ÷ ఉత్పత్తుల సంఖ్య"}, "average_sales": {"message": "సగటు అమ్మకాలు"}, "average_sales_per_product": {"message": "మొత్తం అమ్మకాలు ÷ ఉత్పత్తుల సంఖ్య"}, "bao_han": {"message": "కలిగి ఉంది"}, "bao_zheng_jin": {"message": "మార్జిన్"}, "bian_ti_shu": {"message": "వైవిధ్యాలు"}, "biao_ti": {"message": "శీర్షిక"}, "blacklist_add_blacklist": {"message": "ఈ దుకాణాన్ని బ్లాక్ చేయండి"}, "blacklist_address_incorrect": {"message": "చిరునామా తప్పు. దయచేసి దాన్ని తనిఖీ చేయండి."}, "blacklist_blacked_out": {"message": "స్టోర్ బ్లాక్ చేయబడింది"}, "blacklist_blacklist": {"message": "బ్లాక్లిస్ట్"}, "blacklist_no_records_yet": {"message": "ఇంకా రికార్డ్ లేదు!"}, "blue_rocket": {"message": "로켓배송"}, "brand_name": {"message": "బ్రాండ్"}, "btn_aliprice_agent__daigou": {"message": "కొనుగోలు మధ్యవర్తి"}, "btn_aliprice_agent__dropshipping": {"message": "డ్రాప్‌షిప్పింగ్"}, "btn_have_a_try": {"message": "ఓసారి ప్రయత్నించు"}, "btn_refresh": {"message": "రిఫ్రెష్ చేయండి"}, "btn_try_it_now": {"message": "ఇప్పుడే ప్రయత్నించు"}, "btn_txt_view_on_aliprice": {"message": "అలీప్రైస్‌లో చూడండి"}, "bu_bao_han": {"message": "దింట్లో ఉండదు"}, "bulk_copy_links": {"message": "బల్క్ కాపీ లింకులు"}, "bulk_copy_products": {"message": "బల్క్ కాపీ ఉత్పత్తులు"}, "cai_gou_zi_xun": {"message": "వినియోగదారుల సేవ"}, "cai_gou_zi_xun__desc": {"message": "విక్రేత యొక్క మూడు నిమిషాల ప్రతిస్పందన రేటు"}, "can_ping_lei_xing": {"message": "టైప్ చేయండి"}, "cao_zuo": {"message": "ఆపరేషన్"}, "chan_pin_ID": {"message": "ఉత్పత్తి ID"}, "chan_pin_e_wai_xin_xi": {"message": "ఉత్పత్తి అదనపు సమాచారం"}, "chan_pin_lian_jie": {"message": "ఉత్పత్తి లింక్"}, "cheng_li_shi_jian": {"message": "స్థాపన సమయం"}, "city__aba": {"message": "Aba"}, "city__aksu": {"message": "<PERSON><PERSON><PERSON>"}, "city__alaer": {"message": "<PERSON><PERSON><PERSON>"}, "city__altai": {"message": "Altai"}, "city__alxaleague": {"message": "Alxa League"}, "city__ankang": {"message": "Ankan<PERSON>"}, "city__anqing": {"message": "Anqing"}, "city__anshan": {"message": "<PERSON><PERSON>"}, "city__anshun": {"message": "<PERSON><PERSON><PERSON>"}, "city__anyang": {"message": "Anyang"}, "city__baicheng": {"message": "Baicheng"}, "city__baise": {"message": "Baise"}, "city__baisha": {"message": "Baisha"}, "city__baishan": {"message": "Baishan"}, "city__baiyin": {"message": "<PERSON><PERSON><PERSON>"}, "city__baoding": {"message": "<PERSON>od<PERSON>"}, "city__baoji": {"message": "<PERSON><PERSON><PERSON>"}, "city__baoshan": {"message": "<PERSON><PERSON><PERSON>"}, "city__baoting": {"message": "Baoting"}, "city__baotou": {"message": "<PERSON><PERSON><PERSON>"}, "city__bayannur": {"message": "Bayannur"}, "city__bayinguoleng": {"message": "Bayinguoleng"}, "city__bazhong": {"message": "Bazhong"}, "city__beihai": {"message": "Beihai"}, "city__bengbu": {"message": "<PERSON><PERSON><PERSON>"}, "city__benxi": {"message": "Benxi"}, "city__bijie": {"message": "Bijie"}, "city__binzhou": {"message": "Binzhou"}, "city__bortala": {"message": "<PERSON><PERSON><PERSON>"}, "city__bozhou": {"message": "Bozhou"}, "city__cangzhou": {"message": "Cangzhou"}, "city__chamdo": {"message": "<PERSON><PERSON><PERSON>"}, "city__changchun": {"message": "<PERSON><PERSON><PERSON>"}, "city__changde": {"message": "<PERSON><PERSON>"}, "city__changhua": {"message": "Changhua"}, "city__changji": {"message": "Changji"}, "city__changjiang": {"message": "Changjiang"}, "city__changsha": {"message": "Changsha"}, "city__changzhi": {"message": "Changzhi"}, "city__changzhou": {"message": "Changzhou"}, "city__chaohu": {"message": "<PERSON><PERSON>"}, "city__chaoyang": {"message": "Chaoyang"}, "city__chaozhou": {"message": "Chaozhou"}, "city__chengde": {"message": "Chengde"}, "city__chengdu": {"message": "Chengdu"}, "city__chengmai": {"message": "<PERSON><PERSON><PERSON>"}, "city__chenzhou": {"message": "Chenzhou"}, "city__chiayi": {"message": "<PERSON><PERSON><PERSON>"}, "city__chifeng": {"message": "<PERSON><PERSON>"}, "city__chizhou": {"message": "Chizhou"}, "city__chongzuo": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__chuxiong": {"message": "Chuxiong"}, "city__chuzhou": {"message": "Chuzhou"}, "city__dali": {"message": "<PERSON><PERSON>"}, "city__dalian": {"message": "<PERSON><PERSON>"}, "city__dandong": {"message": "Dandong"}, "city__danzhou": {"message": "Danzhou"}, "city__daqing": {"message": "Daqing"}, "city__datong": {"message": "<PERSON><PERSON><PERSON>"}, "city__daxinganling": {"message": "<PERSON><PERSON>'<PERSON>ling"}, "city__dazhou": {"message": "Dazhou"}, "city__dehong": {"message": "<PERSON><PERSON>"}, "city__deyang": {"message": "Deyang"}, "city__dezhou": {"message": "Dezhou"}, "city__dingan": {"message": "Ding'an"}, "city__dingxi": {"message": "Dingxi"}, "city__diqing": {"message": "Diqing"}, "city__dongfang": {"message": "<PERSON><PERSON>g"}, "city__dongguan": {"message": "Dongguan"}, "city__dongying": {"message": "<PERSON><PERSON>"}, "city__enshi": {"message": "<PERSON><PERSON>"}, "city__ezhou": {"message": "Ezhou"}, "city__fangchenggang": {"message": "Fangchenggang"}, "city__foshan": {"message": "<PERSON><PERSON><PERSON>"}, "city__fushun": {"message": "<PERSON><PERSON><PERSON>"}, "city__fuxin": {"message": "Fuxin"}, "city__fuyang": {"message": "Fuyang"}, "city__fuzhou": {"message": "Fuzhou"}, "city__gannan": {"message": "<PERSON><PERSON><PERSON>"}, "city__ganzhou": {"message": "Ganzhou"}, "city__ganzi": {"message": "Ganzi"}, "city__guangan": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__guangyuan": {"message": "Guangyuan"}, "city__guangzhou": {"message": "Guangzhou"}, "city__guigang": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__guilin": {"message": "<PERSON><PERSON><PERSON>"}, "city__guiyang": {"message": "Guiyang"}, "city__guolok": {"message": "Guolok"}, "city__guyuan": {"message": "<PERSON><PERSON>"}, "city__haibei": {"message": "Haibei"}, "city__haidong": {"message": "Haidong"}, "city__haikou": {"message": "Hai<PERSON><PERSON>"}, "city__hainan": {"message": "Hainan"}, "city__haixi": {"message": "Haixi"}, "city__hami": {"message": "<PERSON><PERSON>"}, "city__handan": {"message": "<PERSON><PERSON>"}, "city__hangzhou": {"message": "Hangzhou"}, "city__hanzhong": {"message": "Hanzhong"}, "city__harbin": {"message": "<PERSON><PERSON><PERSON>"}, "city__hebi": {"message": "<PERSON><PERSON>"}, "city__hechi": {"message": "<PERSON><PERSON>"}, "city__hefei": {"message": "<PERSON><PERSON><PERSON>"}, "city__hegang": {"message": "<PERSON><PERSON><PERSON>"}, "city__heihe": {"message": "<PERSON><PERSON><PERSON>"}, "city__hengshui": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__hengyang": {"message": "Hengyang"}, "city__heyuan": {"message": "<PERSON><PERSON>"}, "city__heze": {"message": "<PERSON><PERSON>"}, "city__hezhou": {"message": "Hezhou"}, "city__hohhot": {"message": "Hohhot"}, "city__honghe": {"message": "<PERSON><PERSON>"}, "city__hongkongisland": {"message": "Hong Kong Island"}, "city__hotan": {"message": "<PERSON><PERSON>"}, "city__hsinchu": {"message": "<PERSON><PERSON><PERSON>"}, "city__huaian": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__huaibei": {"message": "<PERSON><PERSON><PERSON>"}, "city__huaihua": {"message": "Huaihua"}, "city__huaisouth": {"message": "Huai South"}, "city__hualien": {"message": "<PERSON><PERSON><PERSON>"}, "city__huanggang": {"message": "<PERSON><PERSON><PERSON>"}, "city__huangnan": {"message": "Huangnan"}, "city__huangshan": {"message": "<PERSON><PERSON>"}, "city__huangshi": {"message": "<PERSON><PERSON>"}, "city__huizhou": {"message": "Huizhou"}, "city__huludao": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__hulunbuir": {"message": "Hulunbuir"}, "city__huzhou": {"message": "Huzhou"}, "city__jiamusi": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__jian": {"message": "<PERSON><PERSON><PERSON>"}, "city__jiangmen": {"message": "Jiangmen"}, "city__jiaozuo": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__jiaxing": {"message": "Jiaxing"}, "city__jiayuguan": {"message": "Jiayuguan"}, "city__jieyang": {"message": "<PERSON><PERSON><PERSON>"}, "city__jilin": {"message": "<PERSON><PERSON>"}, "city__jinan": {"message": "<PERSON><PERSON>"}, "city__jinchang": {"message": "Jinchang"}, "city__jincheng": {"message": "Jincheng"}, "city__jingdezhen": {"message": "Jingdez<PERSON>"}, "city__jingmen": {"message": "Jingmen"}, "city__jingzhou": {"message": "Jingzhou"}, "city__jinhua": {"message": "Jinhua"}, "city__jining": {"message": "Jining"}, "city__jinzhong": {"message": "Jinzhong"}, "city__jinzhou": {"message": "Jinzhou"}, "city__jiujiang": {"message": "Jiujiang"}, "city__jiuquan": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__jixi": {"message": "Ji<PERSON>"}, "city__kaifeng": {"message": "Kaifeng"}, "city__kaohsiung": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__karamay": {"message": "<PERSON><PERSON><PERSON>"}, "city__kashgar": {"message": "Ka<PERSON><PERSON>"}, "city__keelung": {"message": "Keelung"}, "city__kinmen": {"message": "Kinmen"}, "city__kizilsu": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__kowloon": {"message": "Kowloon"}, "city__kunming": {"message": "Ku<PERSON><PERSON>"}, "city__laibin": {"message": "<PERSON><PERSON>"}, "city__laiwu": {"message": "<PERSON><PERSON>"}, "city__langfang": {"message": "<PERSON><PERSON><PERSON>"}, "city__lanzhou": {"message": "Lanzhou"}, "city__ledong": {"message": "<PERSON><PERSON>"}, "city__leshan": {"message": "<PERSON><PERSON>"}, "city__lhasa": {"message": "Lhasa"}, "city__liangshan": {"message": "Liangshan"}, "city__lianyungang": {"message": "Lianyungang"}, "city__liaocheng": {"message": "<PERSON><PERSON><PERSON>"}, "city__liaoyang": {"message": "<PERSON><PERSON><PERSON>"}, "city__liaoyuan": {"message": "<PERSON><PERSON><PERSON>"}, "city__lienchiang": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__lijiang": {"message": "Lijiang"}, "city__lincang": {"message": "Lincang"}, "city__linfen": {"message": "Linfen"}, "city__lingao": {"message": "Lingao"}, "city__lingshui": {"message": "<PERSON><PERSON><PERSON>"}, "city__linxia": {"message": "Lin<PERSON>"}, "city__linyi": {"message": "<PERSON><PERSON>"}, "city__lishui": {"message": "Li<PERSON><PERSON>"}, "city__liupanshui": {"message": "Liupanshu<PERSON>"}, "city__liuzhou": {"message": "Liuzhou"}, "city__longnan": {"message": "<PERSON><PERSON>"}, "city__longyan": {"message": "<PERSON><PERSON>"}, "city__loudi": {"message": "<PERSON><PERSON>"}, "city__luan": {"message": "<PERSON><PERSON><PERSON>"}, "city__luohe": {"message": "<PERSON><PERSON><PERSON>"}, "city__luoyang": {"message": "<PERSON><PERSON><PERSON>"}, "city__luzhou": {"message": "Luzhou"}, "city__lüliang": {"message": "<PERSON>眉liang"}, "city__maanshan": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__macauoutlyingislands": {"message": "Macau Outlying Islands"}, "city__macaupeninsula": {"message": "Macau Peninsula"}, "city__maoming": {"message": "<PERSON><PERSON>"}, "city__meishan": {"message": "<PERSON><PERSON>"}, "city__meizhou": {"message": "Meizhou"}, "city__mianyang": {"message": "Mianyang"}, "city__miaoli": {"message": "<PERSON><PERSON>"}, "city__mudanjiang": {"message": "Mudanjiang"}, "city__nagqu": {"message": "<PERSON>g<PERSON>u"}, "city__nanchang": {"message": "Nanchang"}, "city__nanchong": {"message": "<PERSON><PERSON><PERSON>"}, "city__nanjing": {"message": "Nanjing"}, "city__nanning": {"message": "Nanning"}, "city__nanping": {"message": "<PERSON><PERSON>"}, "city__nantong": {"message": "Nantong"}, "city__nantou": {"message": "<PERSON><PERSON><PERSON>"}, "city__nanyang": {"message": "Nanyang"}, "city__neijiang": {"message": "Neijiang"}, "city__newterritories": {"message": "New Territories"}, "city__ngari": {"message": "<PERSON><PERSON>"}, "city__ningbo": {"message": "Ningbo"}, "city__ningde": {"message": "<PERSON><PERSON><PERSON>"}, "city__nujiang": {"message": "Nujiang"}, "city__nyingchi": {"message": "Nyingchi"}, "city__ordos": {"message": "Ordos"}, "city__panjin": {"message": "Panjin"}, "city__panzhihua": {"message": "Pan Zhihua"}, "city__penghu": {"message": "<PERSON><PERSON><PERSON>"}, "city__pingdingshan": {"message": "Pingdingshan"}, "city__pingliang": {"message": "<PERSON><PERSON><PERSON>"}, "city__pingtung": {"message": "<PERSON><PERSON><PERSON>"}, "city__pingxiang": {"message": "<PERSON><PERSON><PERSON>"}, "city__puer": {"message": "<PERSON>u'er"}, "city__putian": {"message": "<PERSON><PERSON>"}, "city__puyang": {"message": "P<PERSON><PERSON>"}, "city__qiandongnan": {"message": "Qiandongnan"}, "city__qianjiang": {"message": "Qianjiang"}, "city__qiannan": {"message": "<PERSON><PERSON><PERSON>"}, "city__qianxinan": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__qingdao": {"message": "Qingdao"}, "city__qingyang": {"message": "Qingyang"}, "city__qingyuan": {"message": "Qingyuan"}, "city__qinhuangdao": {"message": "Qinhuangdao"}, "city__qinzhou": {"message": "Qinzhou"}, "city__qionghai": {"message": "Qionghai"}, "city__qiongzhong": {"message": "Qiongzhong"}, "city__qiqihar": {"message": "Qiqihar"}, "city__qitaihe": {"message": "<PERSON><PERSON><PERSON>"}, "city__quanzhou": {"message": "Quanzhou"}, "city__qujing": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__quzhou": {"message": "Quzhou"}, "city__rizhao": {"message": "<PERSON><PERSON><PERSON>"}, "city__sanmenxia": {"message": "Sanmenxia"}, "city__sanming": {"message": "Sanming"}, "city__sanya": {"message": "Sanya"}, "city__shangluo": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__shangqiu": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__shangrao": {"message": "<PERSON><PERSON><PERSON>"}, "city__shannan": {"message": "Shannan"}, "city__shantou": {"message": "<PERSON><PERSON><PERSON>"}, "city__shanwei": {"message": "Shanwei"}, "city__shaoguan": {"message": "Shaoguan"}, "city__shaoxing": {"message": "Shaoxing"}, "city__shaoyang": {"message": "Shaoyang"}, "city__shennongjiaforestarea": {"message": "Shennongjia Forest Area"}, "city__shenyang": {"message": "Shenyang"}, "city__shenzhen": {"message": "Shenzhen"}, "city__shigatse": {"message": "Shigat<PERSON>"}, "city__shihezi": {"message": "<PERSON><PERSON><PERSON>"}, "city__shijiazhuang": {"message": "Shijiazhuang"}, "city__shiyan": {"message": "<PERSON><PERSON>"}, "city__shizuishan": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__shuangyashan": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__shuozhou": {"message": "Shuozhou"}, "city__siping": {"message": "<PERSON><PERSON>"}, "city__songyuan": {"message": "Songyuan"}, "city__suihua": {"message": "Su<PERSON>ua"}, "city__suining": {"message": "Suining"}, "city__suizhou": {"message": "<PERSON><PERSON><PERSON>"}, "city__suqian": {"message": "<PERSON><PERSON><PERSON>"}, "city__suzhou": {"message": "Suzhou"}, "city__tacheng": {"message": "Tacheng"}, "city__taian": {"message": "Tai'an"}, "city__taichung": {"message": "Taichung"}, "city__tainan": {"message": "Tainan"}, "city__taipei": {"message": "Taipei"}, "city__taitung": {"message": "<PERSON><PERSON><PERSON>"}, "city__taiyuan": {"message": "Taiyuan"}, "city__taizhou": {"message": "Taizhou"}, "city__tangshan": {"message": "Tangshan"}, "city__taoyuan": {"message": "Taoyuan"}, "city__tianmen": {"message": "Tianmen"}, "city__tianshui": {"message": "<PERSON><PERSON><PERSON>"}, "city__tieling": {"message": "Tieling"}, "city__tongchuan": {"message": "<PERSON><PERSON><PERSON>"}, "city__tonghua": {"message": "Tonghua"}, "city__tongliao": {"message": "<PERSON><PERSON><PERSON>"}, "city__tongling": {"message": "Tongling"}, "city__tongren": {"message": "<PERSON><PERSON>"}, "city__tumushuke": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__tunchang": {"message": "Tunchang"}, "city__turpan": {"message": "<PERSON><PERSON><PERSON>"}, "city__ulanqab": {"message": "Ulanqab"}, "city__urumqi": {"message": "Urumqi"}, "city__wanning": {"message": "Wanning"}, "city__weifang": {"message": "<PERSON><PERSON><PERSON>"}, "city__weihai": {"message": "Weihai"}, "city__weinan": {"message": "Weinan"}, "city__wenchang": {"message": "Wenchang"}, "city__wenshan": {"message": "<PERSON><PERSON>"}, "city__wenzhou": {"message": "Wenzhou"}, "city__wuhai": {"message": "Wu<PERSON>"}, "city__wuhan": {"message": "<PERSON><PERSON>"}, "city__wuhu": {"message": "<PERSON><PERSON>"}, "city__wujiaqu": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__wuwei": {"message": "<PERSON><PERSON>"}, "city__wuxi": {"message": "Wuxi"}, "city__wuzhishan": {"message": "Wuzhishan"}, "city__wuzhong": {"message": "Wuzhong"}, "city__wuzhou": {"message": "Wuzhou"}, "city__xiamen": {"message": "Xiamen"}, "city__xian": {"message": "Xi'<PERSON>"}, "city__xiangfan": {"message": "<PERSON><PERSON><PERSON>"}, "city__xiangtan": {"message": "Xiangtan"}, "city__xiangxi": {"message": "Xiangxi"}, "city__xianning": {"message": "Xianning"}, "city__xiantao": {"message": "Xiantao"}, "city__xianyang": {"message": "<PERSON><PERSON><PERSON>"}, "city__xiaogan": {"message": "<PERSON><PERSON>"}, "city__xilingol": {"message": "Xilingol"}, "city__xinganleague": {"message": "Xing'an League"}, "city__xingtai": {"message": "Xingtai"}, "city__xining": {"message": "Xining"}, "city__xinxiang": {"message": "Xinxiang"}, "city__xinyang": {"message": "Xinyang"}, "city__xinyu": {"message": "Xinyu"}, "city__xinzhou": {"message": "Xinzhou"}, "city__xishuangbanna": {"message": "Xishuangbanna"}, "city__xuancheng": {"message": "Xuancheng"}, "city__xuchang": {"message": "Xuchang"}, "city__xuzhou": {"message": "Xuzhou"}, "city__yaan": {"message": "Ya'an"}, "city__yanan": {"message": "Yan'<PERSON>"}, "city__yanbian": {"message": "Yanbian"}, "city__yancheng": {"message": "Yancheng"}, "city__yangjiang": {"message": "Yangjiang"}, "city__yangquan": {"message": "<PERSON><PERSON><PERSON>"}, "city__yangzhou": {"message": "Yangzhou"}, "city__yantai": {"message": "Yantai"}, "city__yibin": {"message": "<PERSON><PERSON>"}, "city__yichang": {"message": "Yichang"}, "city__yichun": {"message": "<PERSON><PERSON><PERSON>"}, "city__yilan": {"message": "Yilan"}, "city__yili": {"message": "<PERSON><PERSON>"}, "city__yinchuan": {"message": "<PERSON><PERSON><PERSON>"}, "city__yingkou": {"message": "<PERSON><PERSON><PERSON>"}, "city__yingtan": {"message": "Yingtan"}, "city__yiwu": {"message": "<PERSON><PERSON>"}, "city__yiyang": {"message": "Yiyang"}, "city__yongzhou": {"message": "Yongzhou"}, "city__yueyang": {"message": "<PERSON><PERSON><PERSON>"}, "city__yulin": {"message": "Yulin"}, "city__yuncheng": {"message": "Yuncheng"}, "city__yunfu": {"message": "<PERSON><PERSON>"}, "city__yunlin": {"message": "<PERSON><PERSON>"}, "city__yushu": {"message": "Yushu"}, "city__yuxi": {"message": "Yuxi"}, "city__zaozhuang": {"message": "Zaozhuang"}, "city__zhangjiajie": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__zhangjiakou": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__zhangye": {"message": "<PERSON><PERSON>"}, "city__zhangzhou": {"message": "Zhangzhou"}, "city__zhanjiang": {"message": "Zhanjiang"}, "city__zhaoqing": {"message": "Zhaoqing"}, "city__zhaotong": {"message": "Zhaotong"}, "city__zhengzhou": {"message": "Zhengzhou"}, "city__zhenjiang": {"message": "Zhenjiang"}, "city__zhongshan": {"message": "Zhongshan"}, "city__zhongwei": {"message": "Zhongwei"}, "city__zhoukou": {"message": "<PERSON><PERSON><PERSON>"}, "city__zhoushan": {"message": "Zhoushan"}, "city__zhuhai": {"message": "Zhu<PERSON>"}, "city__zhumadian": {"message": "Zhumadian"}, "city__zhuzhou": {"message": "Zhuzhou"}, "city__zibo": {"message": "Zibo"}, "city__zigong": {"message": "Zigong"}, "city__ziyang": {"message": "<PERSON><PERSON><PERSON>"}, "city__zunyi": {"message": "<PERSON><PERSON><PERSON>"}, "click_copy_product_info": {"message": "ఉత్పత్తి సమాచారాన్ని కాపీ చేయి క్లిక్ చేయండి"}, "commmon_txt_expired": {"message": "గడువు ముగిసింది"}, "common__date_range_12m": {"message": "1 సంవత్సరం"}, "common__date_range_1m": {"message": "1 నెల"}, "common__date_range_1w": {"message": "1 వారం"}, "common__date_range_2w": {"message": "2 వారాల"}, "common__date_range_3m": {"message": "3 నెలలు"}, "common__date_range_3w": {"message": "3 వారాలు"}, "common__date_range_6m": {"message": "6 నెలల"}, "common_btn_cancel": {"message": "రద్దు చేయండి"}, "common_btn_close": {"message": "దగ్గరగా"}, "common_btn_save": {"message": "సేవ్ చేయండి"}, "common_btn_setting": {"message": "సెటప్"}, "common_email": {"message": "ఇమెయిల్"}, "common_error_msg_no_data": {"message": "సమాచారం లేదు"}, "common_error_msg_no_result": {"message": "క్షమించండి, ఫలితం కనుగొనబడలేదు."}, "common_favorites": {"message": "ఇష్టమైనవి"}, "common_feedback": {"message": "అభిప్రాయం"}, "common_help": {"message": "సహాయం"}, "common_loading": {"message": "లోడ్"}, "common_login": {"message": "ప్రవేశించండి"}, "common_logout": {"message": "లాగ్ అవుట్"}, "common_no": {"message": "లేదు"}, "common_powered_by_aliprice": {"message": "AliPrice.com చేత ఆధారితం"}, "common_setting": {"message": "అమరిక"}, "common_sign_up": {"message": "చేరడం"}, "common_system_upgrading_title": {"message": "సిస్టమ్ అప్‌గ్రేడ్"}, "common_system_upgrading_txt": {"message": "దయచేసి తరువాత ప్రయత్నించండి"}, "common_txt__currency": {"message": "కరెన్సీ"}, "common_txt__video_tutorial": {"message": "వీడియో ట్యుటోరియల్"}, "common_txt_ago_time": {"message": "$time$సమయం$ రోజుల క్రితం", "placeholders": {"time": {"content": "$1"}}}, "common_txt_all_product": {"message": "అన్నీ"}, "common_txt_analysis": {"message": "విశ్లేషణ"}, "common_txt_basically_used": {"message": "దాదాపు ఎప్పుడూ ఉపయోగించలేదు"}, "common_txt_biaoti_link": {"message": "శీర్షిక+లింక్"}, "common_txt_biaoti_link_dian_pu": {"message": "శీర్షిక+లింక్+స్టోర్ పేరు"}, "common_txt_blacklist": {"message": "బ్లాక్‌లిస్ట్"}, "common_txt_cancel": {"message": "రద్దు చేయి"}, "common_txt_category": {"message": "వర్గం"}, "common_txt_chakan": {"message": "తనిఖీ"}, "common_txt_colors": {"message": "రంగులు"}, "common_txt_confirm": {"message": "నిర్ధారించండి"}, "common_txt_copied": {"message": "కాపీ చేయబడింది"}, "common_txt_copy": {"message": "కాపీ"}, "common_txt_copy_link": {"message": "లింక్ను కాపీ చేయండి"}, "common_txt_copy_title": {"message": "శీర్షికను కాపీ చేయండి"}, "common_txt_day": {"message": "ఆకాశం"}, "common_txt_day__short": {"message": "D"}, "common_txt_delete": {"message": "తొలగించు"}, "common_txt_dian_pu_link": {"message": "స్టోర్ పేరు + లింక్‌ని కాపీ చేయండి"}, "common_txt_download": {"message": "డౌన్‌లోడ్ చేయండి"}, "common_txt_downloaded": {"message": "డౌన్‌లోడ్"}, "common_txt_export_as_csv": {"message": "ఎక్సెల్ ఎగుమతి చేయండి"}, "common_txt_export_as_txt": {"message": "ఎగుమతి టెక్స్ట్"}, "common_txt_fail": {"message": "విఫలం"}, "common_txt_format": {"message": "ఫార్మాట్"}, "common_txt_get": {"message": "పొందండి"}, "common_txt_incert_selection": {"message": "ఎంపికను విలోమం చేయండి"}, "common_txt_install": {"message": "ఇన్‌స్టాల్ చేయండి"}, "common_txt_load_failed": {"message": "లోడ్ చేయడంలో విఫలమైంది"}, "common_txt_month": {"message": "నెల"}, "common_txt_more": {"message": "మరింత"}, "common_txt_new_unused": {"message": "పూర్తిగా కొత్తది, ఉపయోగించనిది"}, "common_txt_next": {"message": "తరువాత"}, "common_txt_no_limit": {"message": "అపరిమిత"}, "common_txt_no_noticeable": {"message": "కనిపించే గీతలు లేదా ధూళి లేదు"}, "common_txt_on_sale": {"message": "అందుబాటులో ఉంది"}, "common_txt_opt_in_out": {"message": "ఆఫ్"}, "common_txt_order": {"message": "ఆర్డర్"}, "common_txt_others": {"message": "ఇతరులు"}, "common_txt_overall_poor_condition": {"message": "మొత్తం మీద పరిస్థితి దారుణంగా ఉంది"}, "common_txt_patterns": {"message": "నమూనాలు"}, "common_txt_platform": {"message": "వేదికలు"}, "common_txt_please_select": {"message": "దయచేసి ఎంచుకోండి"}, "common_txt_prev": {"message": "మునుపటి"}, "common_txt_price": {"message": "ధర"}, "common_txt_privacy_policy": {"message": "గోప్యతా విధానం"}, "common_txt_product_condition": {"message": "ఉత్పత్తి స్థితి"}, "common_txt_rating": {"message": "రేటింగ్"}, "common_txt_ratings": {"message": "రేటింగ్‌లు"}, "common_txt_reload": {"message": "మళ్లీ లోడ్ చేయండి"}, "common_txt_reset": {"message": "రీసెట్ చేయండి"}, "common_txt_review": {"message": "సమీక్ష"}, "common_txt_sale": {"message": "అందుబాటులో ఉంది"}, "common_txt_same": {"message": "అదే"}, "common_txt_scratches_and_dirt": {"message": "గీతలు మరియు ధూళితో"}, "common_txt_search_title": {"message": "శోధన శీర్షిక"}, "common_txt_select_all": {"message": "అన్ని ఎంచుకోండి"}, "common_txt_selected": {"message": "ఎంపిక చేయబడింది"}, "common_txt_share": {"message": "భాగస్వామ్యం చేయండి"}, "common_txt_sold": {"message": "విక్రయించబడింది"}, "common_txt_sold_out": {"message": "అమ్ముడుపోయాయి"}, "common_txt_some_scratches": {"message": "కొన్ని గీతలు మరియు ధూళి"}, "common_txt_sort_by": {"message": "ఆమరిక"}, "common_txt_state": {"message": "రాష్ట్రం"}, "common_txt_success": {"message": "విజయం"}, "common_txt_sys_err": {"message": "సిస్టమ్ లోపం"}, "common_txt_today": {"message": "ఈరోజు"}, "common_txt_total": {"message": "అన్ని"}, "common_txt_unselect_all": {"message": "విలోమ ఎంపిక"}, "common_txt_upload_image": {"message": "చిత్రాన్ని అప్‌లోడ్ చేయండి"}, "common_txt_visit": {"message": "సందర్శించండి"}, "common_txt_whitelist": {"message": "వైట్‌లిస్ట్"}, "common_txt_wildberries": {"message": "Wildberries"}, "common_txt_year": {"message": "సంవత్సరం"}, "common_yes": {"message": "అవును"}, "compare_tool_btn_clear_all": {"message": "అన్నీ క్లియర్ చేయండి"}, "compare_tool_btn_compare": {"message": "సరిపోల్చండి"}, "compare_tool_btn_contact": {"message": "సంప్రదించండి"}, "compare_tool_tips_max_compared": {"message": "Додайте до $maxComparedCount$", "placeholders": {"maxComparedCount": {"content": "$1"}}}, "configure_notifiactions": {"message": "నోటిఫికేషన్‌లను కాన్ఫిగర్ చేయండి"}, "contact_us": {"message": "మమ్మల్ని సంప్రదించండి"}, "context_menu_screenshot_search": {"message": "అదే శైలి కోసం స్క్రీన్‌షాట్ శోధన"}, "context_menus_aliprice_search_by_image": {"message": "అలీప్రైస్‌లో చిత్రాన్ని శోధించండి"}, "context_menus_search_by_image": {"message": "Image $storeName$ on లో చిత్రం ద్వారా శోధించండి", "placeholders": {"storeName": {"content": "$1"}}}, "context_menus_search_by_image_capture": {"message": "$storeName$ కు సంగ్రహించండి", "placeholders": {"storeName": {"content": "$1"}}}, "context_menus_translator": {"message": "అనువదించడానికి క్యాప్చర్ చేయండి"}, "converter_modal_amount_placeholder": {"message": "ఇక్కడ మొత్తాన్ని నమోదు చేయండి"}, "converter_modal_btn_convert": {"message": "మార్చు"}, "converter_modal_exchange_rate_source": {"message": "డేటా $boc$ విదేశీ మారకపు రేటు నుండి అందించబడింది అప్‌డేట్ సమయం: $updatedAt$", "placeholders": {"boc": {"content": "$1"}, "updatedAt": {"content": "$2"}}}, "converter_modal_name": {"message": "కరెన్సీ మార్పిడి"}, "converter_modal_search_placeholder": {"message": "కరెన్సీని శోధించండి"}, "copy_all_contact_us_notice": {"message": "ఈ సమయంలో ఈ సైట్‌కు మద్దతు లేదు, దయచేసి మమ్మల్ని సంప్రదించండి"}, "copy_product_info": {"message": "ఉత్పత్తి సమాచారాన్ని కాపీ చేయండి"}, "copy_suggest_search_kw": {"message": "డ్రాప్‌డౌన్ జాబితాలను కాపీ చేయండి"}, "country__han_gou": {"message": "దక్షిణ కొరియా"}, "country__ri_ben": {"message": "జపాన్"}, "country__yue_nan": {"message": "వియత్నాం"}, "currency_convert__custom": {"message": "అనుకూల మార్పిడి రేటు"}, "currency_convert__sync_server": {"message": "సర్వర్ సమకాలీకరించండి"}, "dang_ri_fa_huo": {"message": "అదే రోజు షిప్పింగ్"}, "dao_chu_quan_dian_shang_pin": {"message": "అన్ని స్టోర్ ఉత్పత్తులను ఎగుమతి చేయండి"}, "dao_chu_wei_CSV": {"message": "ఎగుమతి కార్ట్ జాబితా"}, "dao_chu_zi_duan": {"message": "ఎగుమతి ఫీల్డ్స్"}, "delivery_address": {"message": "షిప్పింగ్ చిరునామా"}, "delivery_company": {"message": "డెలివరీ సంస్థ"}, "di_zhi": {"message": "చిరునామా"}, "dian_ji_cha_xun": {"message": "ప్రశ్నించడానికి క్లిక్ చేయండి"}, "dian_pu_ID": {"message": "స్టోర్ ID"}, "dian_pu_di_zhi": {"message": "స్టోర్ చిరునామా"}, "dian_pu_lian_jie": {"message": "స్టోర్ లింక్"}, "dian_pu_ming": {"message": "స్టోర్ పేరు"}, "dian_pu_ming_cheng": {"message": "స్టోర్ పేరు"}, "dian_pu_shang_pin_zong_hsu": {"message": "స్టోర్‌లోని మొత్తం ఉత్పత్తుల సంఖ్య: $num$", "placeholders": {"num": {"content": "$1"}}}, "dian_pu_xin_xi": {"message": "సమాచారాన్ని నిల్వ చేయండి"}, "ding_zai_zuo_ce": {"message": "ఎడమవైపు వ్రేలాడుదీస్తారు"}, "disable_old_version_tips_disable_btn_title": {"message": "పాత సంస్కరణను ఆపివేయి"}, "download_image__SKU_variant_images": {"message": "SKU వేరియంట్ చిత్రాలు"}, "download_image__assume": {"message": "ఉదాహరణకు, మేము 2 చిత్రాలను కలిగి ఉన్నాము, product1.jpg మరియు product2.gif.\nimg_{$no$} పేరు img_01.jpg, img_02.gifగా మార్చబడుతుంది;\n{$group$}_{$no$} పేరు main_image_01.jpg, main_image_02.gifగా మార్చబడుతుంది;", "placeholders": {"no": {"content": "$3"}, "group": {"content": "$2"}}}, "download_image__batch_download": {"message": "బ్యాచ్ డౌన్‌లోడ్"}, "download_image__combined_image": {"message": "సంయుక్త ఉత్పత్తి వివరాల చిత్రం"}, "download_image__continue_downloading": {"message": "డౌన్‌లోడ్ చేయడాన్ని కొనసాగించండి"}, "download_image__description_images": {"message": "వివరణ చిత్రాలు"}, "download_image__download_combined_image": {"message": "మిశ్రమ ఉత్పత్తి వివరాల చిత్రాన్ని డౌన్‌లోడ్ చేయండి"}, "download_image__download_zip": {"message": "జిప్ డౌన్‌లోడ్ చేయండి"}, "download_image__enlarge_check": {"message": "JPEG, JPG, GIF మరియు PNG చిత్రాలకు మాత్రమే మద్దతు ఇస్తుంది, ఒకే చిత్రం యొక్క గరిష్ట పరిమాణం: 1600 * 1600"}, "download_image__enlarge_image": {"message": "చిత్రాన్ని విస్తరించండి"}, "download_image__export": {"message": "లింక్‌లను ఎగుమతి చేయండి"}, "download_image__height": {"message": "ఎత్తు"}, "download_image__ignore_videos": {"message": "వీడియో ఎగుమతి చేయబడనందున విస్మరించబడింది"}, "download_image__img_translate": {"message": "చిత్రం అనువాదం"}, "download_image__main_image": {"message": "ప్రధాన చిత్రం"}, "download_image__multi_folder": {"message": "బహుళ ఫోల్డర్"}, "download_image__name": {"message": "చిత్రాన్ని డౌన్‌లోడ్ చేయండి"}, "download_image__notice_content": {"message": "దయచేసి మీ బ్రౌజర్ డౌన్‌లోడ్ సెట్టింగ్‌లలో \"డౌన్‌లోడ్ చేయడానికి ముందు ప్రతి ఫైల్‌ను ఎక్కడ సేవ్ చేయాలో అడగండి\" అని తనిఖీ చేయవద్దు!!! లేకపోతే చాలా డైలాగ్ బాక్స్‌లు ఉంటాయి."}, "download_image__notice_ignore": {"message": "ఈ సందేశం కోసం మళ్లీ ప్రాంప్ట్ చేయవద్దు"}, "download_image__order_number": {"message": "{$no$} క్రమ సంఖ్య; {$group$} సమూహం పేరు; {$date$} టైమ్‌స్టాంప్", "placeholders": {"no": {"content": "$1"}, "group": {"content": "$2"}, "date": {"content": "$3"}}}, "download_image__overview": {"message": "అవలోకనం"}, "download_image__prompt_download_zip": {"message": "చాలా చిత్రాలు ఉన్నాయి, మీరు వాటిని జిప్ ఫోల్డర్‌గా డౌన్‌లోడ్ చేసుకోవడం మంచిది."}, "download_image__rename": {"message": "పేరు మార్చండి"}, "download_image__rule": {"message": "నామకరణ నియమాలు"}, "download_image__single_folder": {"message": "ఒకే ఫోల్డర్"}, "download_image__sku_image": {"message": "SKU చిత్రాలు"}, "download_image__video": {"message": "వీడియో"}, "download_image__width": {"message": "వెడల్పు"}, "download_reviews__download_images": {"message": "సమీక్ష చిత్రాన్ని డౌన్‌లోడ్ చేయండి"}, "download_reviews__dropdown_title": {"message": "సమీక్ష చిత్రాన్ని డౌన్‌లోడ్ చేయండి"}, "download_reviews__export_csv": {"message": "CSVని ఎగుమతి చేయండి"}, "download_reviews__no_images": {"message": "డౌన్‌లోడ్ చేయడానికి 0 చిత్రాలు అందుబాటులో ఉన్నాయి"}, "download_reviews__no_reviews": {"message": "డౌన్‌లోడ్ చేయడానికి సమీక్ష లేదు!"}, "download_reviews__notice": {"message": "చిట్కా:"}, "download_reviews__notice__chrome_settings": {"message": "డౌన్‌లోడ్ చేయడానికి ముందు ప్రతి ఫైల్‌ను ఎక్కడ సేవ్ చేయాలో అడగడానికి Chrome బ్రౌజర్‌ని సెట్ చేయండి, \"ఆఫ్\"కి సెట్ చేయండి"}, "download_reviews__notice__wait": {"message": "సమీక్షల సంఖ్యను బట్టి, వేచి ఉండే సమయం ఎక్కువ కావచ్చు"}, "download_reviews__pages_list__all": {"message": "అన్నీ"}, "download_reviews__pages_list__page": {"message": "మునుపటి $page$ పేజీలు", "placeholders": {"page": {"content": "$1"}}}, "download_reviews__pages_list_title": {"message": "ఎంపిక పరిధి"}, "export_shopping_cart__csv_filed__details_url": {"message": "ఉత్పత్తి లింక్"}, "export_shopping_cart__csv_filed__details_url_with_sku": {"message": "ఎకో SKU లింక్"}, "export_shopping_cart__csv_filed__images": {"message": "చిత్రం లింక్"}, "export_shopping_cart__csv_filed__quantity": {"message": "పరిమాణం"}, "export_shopping_cart__csv_filed__sale_price": {"message": "ధర"}, "export_shopping_cart__csv_filed__specs": {"message": "స్పెసిఫికేషన్లు"}, "export_shopping_cart__csv_filed__store_name": {"message": "స్టోర్ పేరు"}, "export_shopping_cart__csv_filed__store_url": {"message": "స్టోర్ లింక్"}, "export_shopping_cart__csv_filed__title": {"message": "ఉత్పత్తి నామం"}, "export_shopping_cart__export_btn": {"message": "ఎగుమతి కార్ట్ జాబితా"}, "export_shopping_cart__export_empty": {"message": "దయచేసి ఒక ఉత్పత్తిని ఎంచుకోండి!"}, "fa_huo_shi_jian": {"message": "షిప్పింగ్"}, "favorite_add_email": {"message": "ఇమెయిల్ చిరునామాను జోడించండి"}, "favorite_add_favorites": {"message": "ఇష్టమైన వాటికి జోడించండి"}, "favorite_added": {"message": "చేర్చబడింది"}, "favorite_btn_add": {"message": "ధర డ్రాప్ హెచ్చరిక."}, "favorite_btn_notify": {"message": "ట్రాక్ ధర"}, "favorite_cate_name_all": {"message": "అన్ని ఉత్పత్తులు"}, "favorite_current_price": {"message": "ప్రస్తుత ధర"}, "favorite_due_date": {"message": "గడువు తేదీ"}, "favorite_enable_notification": {"message": "దయచేసి ఇమెయిల్ నోటిఫికేషన్‌ను ఎనేబుల్ చేయండి"}, "favorite_expired": {"message": "గడువు ముగిసింది"}, "favorite_go_to_enable": {"message": "ఎనేబుల్‌కి వెళ్లండి"}, "favorite_msg_add_success": {"message": "ఇష్టమైన వాటికి జోడించబడింది"}, "favorite_msg_del_success": {"message": "ఇష్టమైనవి నుండి తొలగించబడ్డాయి"}, "favorite_msg_failure": {"message": "విఫలం! పేజీని రిఫ్రెష్ చేసి, మళ్లీ ప్రయత్నించండి."}, "favorite_please_add_email": {"message": "దయచేసి మీ ఇమెయిల్ చిరునామాను జోడించండి"}, "favorite_price_drop": {"message": "క్రిందికి"}, "favorite_price_rise": {"message": "పైకి"}, "favorite_price_untracked": {"message": "ధర ట్రాక్ చేయబడలేదు"}, "favorite_saved_price": {"message": "ఆదా చేసిన ధర"}, "favorite_stop_tracking": {"message": "ట్రాక్ చేయడం ఆపివేయండి"}, "favorite_sub_email_address": {"message": "చందా ఇమెయిల్ చిరునామా"}, "favorite_tracking_period": {"message": "ట్రాకింగ్ వ్యవధి"}, "favorite_tracking_prices": {"message": "ధరలను ట్రాక్ చేస్తోంది"}, "favorite_verify_email": {"message": "ఇమెయిల్ చిరునామాను ధృవీకరించండి"}, "favorites_list_remove_prompt_msg": {"message": "మీరు దీన్ని ఖచ్చితంగా తొలగించగలరా?"}, "favorites_update_button": {"message": "ఇప్పుడే ధరలను నవీకరించండి"}, "fen_lei": {"message": "వర్గం"}, "fen_xia_yan_xuan": {"message": "పంపిణీదారు ఎంపిక"}, "find_similar": {"message": "సారూప్యతను కనుగొనండి"}, "first_ali_price_date": {"message": "AliPrice క్రాలర్ ద్వారా మొదటిసారి క్యాప్చర్ చేయబడిన తేదీ"}, "fooview_coupons_modal_no_data": {"message": "కూపన్లు లేవు"}, "fooview_coupons_modal_title": {"message": "కూపన్లు"}, "fooview_favorites__product_sub__tooltip_l1": {"message": "ధర < $lowerPrice$", "placeholders": {"lowerPrice": {"content": "$1"}}}, "fooview_favorites__product_sub__tooltip_l2": {"message": "లేదా ధర > $higherPrice$", "placeholders": {"higherPrice": {"content": "$1"}}}, "fooview_favorites__product_sub__tooltip_l3": {"message": "గడువు"}, "fooview_favorites_error_msg_no_favorites": {"message": "ధర డ్రాప్ హెచ్చరికను స్వీకరించడానికి ఇష్టమైన ఉత్పత్తులను ఇక్కడ జోడించండి."}, "fooview_favorites_filter_latest": {"message": "తాజాది"}, "fooview_favorites_filter_price_drop": {"message": "ధర తగ్గింపు"}, "fooview_favorites_filter_price_up": {"message": "ధర పెరుగుదల"}, "fooview_favorites_modal_title": {"message": "నాకు ఇష్టమైనవి"}, "fooview_favorites_modal_title_title": {"message": "అలీప్రైస్ ఫేవరెట్‌కు వెళ్లండి"}, "fooview_favorites_track_price": {"message": "ధరను ట్రాక్ చేయడానికి"}, "fooview_price_history_app_price": {"message": "APP ధర:"}, "fooview_price_history_title": {"message": "ధర చరిత్ర"}, "fooview_product_list_feedback": {"message": "అభిప్రాయం"}, "fooview_product_list_orders": {"message": "ఆదేశాలు"}, "fooview_product_list_price": {"message": "ధర"}, "fooview_reviews_error_msg_no_review": {"message": "ఈ ఉత్పత్తి కోసం మేము ఎటువంటి సమీక్షలను కనుగొనలేదు."}, "fooview_reviews_filter_buyer_reviews": {"message": "కొనుగోలుదారుల ఫోటోలు"}, "fooview_reviews_modal_title": {"message": "సమీక్షలు"}, "fooview_same_product_choose_category": {"message": "వర్గాన్ని ఎంచుకోండి"}, "fooview_same_product_filter_feedback": {"message": "అభిప్రాయం"}, "fooview_same_product_filter_orders": {"message": "ఆదేశాలు"}, "fooview_same_product_filter_price": {"message": "ధర"}, "fooview_same_product_filter_rating": {"message": "రేటింగ్"}, "fooview_same_product_modal_title": {"message": "అదే ఉత్పత్తిని కనుగొనండి"}, "fooview_same_product_search_by_image": {"message": "చిత్రం ద్వారా శోధించండి"}, "fooview_seller_analysis_modal_title": {"message": "విక్రేత విశ్లేషణ"}, "for_12_months": {"message": "1 సంవత్సరం"}, "for_12_months_list_pro": {"message": "12 నెలలు"}, "for_12_months_nei": {"message": "12 నెలల్లోపు"}, "for_1_months": {"message": "1 నెల"}, "for_1_months_nei": {"message": "1 నెలలోపు"}, "for_3_months": {"message": "3 నెలలు"}, "for_3_months_nei": {"message": "3 నెలల్లోపు"}, "for_6_months": {"message": "6 నెలలు"}, "for_6_months_nei": {"message": "6 నెలల్లోపు"}, "for_9_months": {"message": "9 నెలలు"}, "for_9_months_nei": {"message": "9 నెలల్లో"}, "fu_gou_lv": {"message": "తిరిగి కొనుగోలు రేటు"}, "gao_liang_bu_tong_dian": {"message": "తేడాలను హైలైట్ చేయండి"}, "gao_liang_guang_gao_chan_pin": {"message": "ప్రకటన ఉత్పత్తులను హైలైట్ చేయండి"}, "geng_duo_xin_xi": {"message": "మరింత సమాచారం"}, "geng_xin_shi_jian": {"message": "నవీకరణ సమయం"}, "get_store_products_fail_tip": {"message": "సాధారణ ప్రాప్యతను నిర్ధారించడానికి ధృవీకరణకు వెళ్లడానికి సరే క్లిక్ చేయండి."}, "gong_x_kuan_shang_pin": {"message": "మొత్తం $amount$ ఉత్పత్తులు", "placeholders": {"amount": {"content": "$1"}}}, "gong_ying_shang": {"message": "సరఫరాదారు"}, "gong_ying_shang_ID": {"message": "సరఫరాదారు ID"}, "gong_ying_shang_deng_ji": {"message": "సరఫరాదారు రేటింగ్"}, "gong_ying_shang_nian_zhan": {"message": "సరఫరాదారు పెద్దవాడు"}, "gong_ying_shang_xin_xi": {"message": "సరఫరాదారు సమాచారం"}, "gong_ying_shang_zhu_ye_lian_jie": {"message": "సరఫరాదారు హోమ్‌పేజీ లింక్"}, "green_rocket": {"message": "로켓프레시"}, "grey_rocket": {"message": "로켓설치"}, "gu_ji_shou_jia": {"message": "అంచనా విక్రయ ధర"}, "guan_jian_zi": {"message": "కీవర్డ్"}, "guang_gao_chan_pin": {"message": "ప్రకటన ఉత్పత్తులు"}, "guang_gao_zhan_bi": {"message": "ప్రకటన నిష్పత్తి"}, "guo_ji_wu_liu_yun_fei": {"message": "అంతర్జాతీయ షిప్పింగ్ రుసుము"}, "guo_lv_tiao_jian": {"message": "ఫిల్టర్లు"}, "hao_ping_lv": {"message": "సానుకూల రేటింగ్"}, "highest_price": {"message": "అధిక"}, "historical_trend": {"message": "చారిత్రక ధోరణి"}, "how_to_screenshot": {"message": "ప్రాంతాన్ని ఎంచుకోవడానికి ఎడమ మౌస్ బటన్‌ను నొక్కి పట్టుకోండి, స్క్రీన్‌షాట్ నుండి నిష్క్రమించడానికి కుడి మౌస్ బటన్ లేదా Esc కీని నొక్కండి"}, "howt_it_works": {"message": "అది ఎలా పని చేస్తుంది"}, "hui_fu_lv": {"message": "ప్రతిస్పందన రేటు"}, "hui_tou_lv": {"message": "తిరిగి రేటు"}, "inquire_freightFee": {"message": "రవాణా కోసం అభ్యర్థన"}, "inquire_freightFee_Yuan": {"message": "సరుకు/యువాన్"}, "inquire_freightFee_province": {"message": "ప్రావిన్సులు"}, "inquire_freightFee_the": {"message": "షిప్పింగ్ ధర $num$, అంటే మీ ప్రాంతంలో ఉచిత షిప్పింగ్ అని అర్థం.", "placeholders": {"num": {"content": "$1"}}}, "is_ad": {"message": "ప్రకటన"}, "jia_ge": {"message": "ధర"}, "jia_ge_dan_wei": {"message": "యూనిట్"}, "jia_ge_qu_shi": {"message": "ట్రెండ్"}, "jia_zai_n_ge_shang_pin": {"message": "$num$ ఉత్పత్తులను లోడ్ చేయండి", "placeholders": {"num": {"content": "$1"}}}, "jin_30_tian_xiao_liang_zhan_bi": {"message": "గత 30 రోజులలో అమ్మకాల పరిమాణంలో శాతం"}, "jin_30_tian_xiao_shou_e_zhan_bi": {"message": "గత 30 రోజుల్లో రాబడి శాతం"}, "jin_30d_xiao_liang": {"message": "అమ్మకాలు"}, "jin_30d_xiao_liang__desc": {"message": "గత 30 రోజులలో మొత్తం అమ్మకాల పరిమాణం"}, "jin_30d_xiao_shou_e": {"message": "టర్నోవర్"}, "jin_30d_xiao_shou_e__desc": {"message": "గత 30 రోజుల మొత్తం టర్నోవర్"}, "jin_90_tian_mai_jia_shu": {"message": "గత 90 రోజుల్లో కొనుగోలుదారులు"}, "jin_90_tian_xiao_shou_liang": {"message": "గత 90 రోజుల్లో అమ్మకాలు"}, "jing_xuan_huo_yuan": {"message": "ఎంచుకున్న మూలాలు"}, "jing_ying_mo_shi": {"message": "వ్యాపార నమూనా"}, "jing_ying_mo_shi__gong_chang": {"message": "తయారీదారు"}, "jiu_fen_jie_jue": {"message": "వివాద పరిష్కారం"}, "jiu_fen_jie_jue__desc": {"message": "విక్రేతల స్టోర్ హక్కుల వివాదాల అకౌంటింగ్"}, "jiu_fen_lv": {"message": "వివాద రేటు"}, "jiu_fen_lv__desc": {"message": "గత 30 రోజుల్లో పూర్తి చేసిన ఫిర్యాదులతో కూడిన ఆర్డర్‌ల నిష్పత్తి మరియు విక్రేత లేదా ఇరుపక్షాల బాధ్యతగా నిర్ధారించబడింది"}, "kai_dian_ri_qi": {"message": "ప్రారంభ తేదీ"}, "keywords": {"message": "కీలకపదాలు"}, "kua_jin_Select_pan_huo": {"message": "క్రాస్-బోర్డర్ ఎంపిక"}, "last15_days": {"message": "గత 15 రోజులు"}, "last180_days": {"message": "గత 180 రోజులు"}, "last30_days": {"message": "గత 30 రోజుల్లో"}, "last360_days": {"message": "గత 360 రోజులు"}, "last45_days": {"message": "గత 45 రోజులు"}, "last60_days": {"message": "గత 60 రోజులు"}, "last7_days": {"message": "గత 7 రోజులు"}, "last90_days": {"message": "గత 90 రోజులు"}, "last_30d_sales": {"message": "గత 30 రోజుల విక్రయాలు"}, "lei_ji": {"message": "సంచిత"}, "lei_ji_xiao_liang": {"message": "మొత్తం"}, "lei_ji_xiao_liang__desc": {"message": "షెల్ఫ్‌లో ఉత్పత్తి తర్వాత అన్ని అమ్మకాలు"}, "lei_ji_xiao_liang_pai_xu__desc": {"message": "గత 30 రోజులలో సంచిత విక్రయాల పరిమాణం ఎక్కువ నుండి తక్కువ వరకు క్రమబద్ధీకరించబడింది"}, "lian_xi_fang_shi": {"message": "సంప్రదింపు సమాచారం"}, "list_time": {"message": "నిల్వ తేదీ"}, "load_more": {"message": "మరింత లోడ్ చేయండి"}, "login_to_aliprice": {"message": "AliPriceకి లాగిన్ చేయండి"}, "long_link": {"message": "పొడవైన లింక్"}, "lowest_price": {"message": "తక్కువ"}, "mai_jia_shu": {"message": "విక్రేతలు"}, "mao_li_lv": {"message": "మార్జిన్"}, "mobile_view__dkxbqy": {"message": "కొత్త ట్యాబ్‌ను తెరవండి"}, "mobile_view__sjdxq": {"message": "యాప్‌లో వివరాలు"}, "mobile_view__sjdxqy": {"message": "యాప్‌లో వివరాల పేజీ"}, "mobile_view__smck": {"message": "వీక్షించడానికి స్కాన్ చేయండి"}, "mobile_view__smckms": {"message": "దయచేసి స్కాన్ చేయడానికి మరియు వీక్షించడానికి కెమెరా లేదా యాప్‌ని ఉపయోగించండి"}, "modified_failed": {"message": "సవరణ విఫలమైంది"}, "modified_successfully": {"message": "విజయవంతంగా సవరించబడింది"}, "nav_btn_favorites": {"message": "నా సేకరణలు"}, "nav_btn_package": {"message": "ప్యాకేజీ"}, "nav_btn_product_info": {"message": "ఉత్పత్తి గురించి"}, "nav_btn_viewed": {"message": "వీక్షించారు"}, "no_suggest_search_kw": {"message": "Loading..."}, "none": {"message": "ఏదీ లేదు"}, "normal_link": {"message": "సాధారణ లింక్"}, "notice": {"message": "సూచన"}, "number_reviews": {"message": "సమీక్షలు"}, "only_show_num": {"message": "మొత్తం ఉత్పత్తులు: $allnum$, దాచబడినవి: $hidenum2$", "placeholders": {"allnum": {"content": "$1"}, "hidenum2": {"content": "$2"}}}, "only_show_selected": {"message": "ఎంపిక చేయని వాటిని తీసివేయండి"}, "open": {"message": "తెరవండి"}, "open_links": {"message": "లింక్‌లను తెరవండి"}, "options_page_tab_check_links": {"message": "లింక్‌లను తనిఖీ చేయండి"}, "options_page_tab_gernal": {"message": "జనరల్"}, "options_page_tab_notifications": {"message": "నోటిఫికేషన్‌లు"}, "options_page_tab_others": {"message": "ఇతరులు"}, "options_page_tab_sbi": {"message": "చిత్రం ద్వారా శోధించండి"}, "options_page_tab_shortcuts": {"message": "సత్వరమార్గాలు"}, "options_page_tab_shortcuts_title": {"message": "సత్వరమార్గాల కోసం ఫాంట్ పరిమాణం"}, "options_page_tab_similar_products": {"message": "అదే ఉత్పత్తులు"}, "orange_rocket": {"message": "판매자로켓"}, "order_list_open_links_tip": {"message": "బహుళ ఉత్పత్తి లింక్‌లు తెరవబోతున్నాయి"}, "order_list_sku_show_title": {"message": "భాగస్వామ్య లింక్‌లలో ఎంచుకున్న వేరియంట్‌లను చూపండి"}, "orders_last30_days": {"message": "గత 30 రోజుల్లో ఆర్డర్‌ల సంఖ్య"}, "pTutorial_favorites_block1_desc1": {"message": "మీరు ట్రాక్ చేసిన ఉత్పత్తులు ఇక్కడ ఇవ్వబడ్డాయి"}, "pTutorial_favorites_block1_title": {"message": "ఇష్టమైనవి"}, "pTutorial_popup_block1_desc1": {"message": "గ్రీన్ లేబుల్ అంటే ధర పడిపోయిన ఉత్పత్తులు ఉన్నాయి"}, "pTutorial_popup_block1_title": {"message": "సత్వరమార్గాలు మరియు ఇష్టమైనవి"}, "pTutorial_price_history_block1_desc1": {"message": "ధరను ట్రాక్ చేయి క్లిక్ చేసి, ఇష్టమైన వాటికి ఉత్పత్తులను జోడించండి. వాటి ధరలు పడిపోయిన తర్వాత, మీకు నోటిఫికేషన్‌లు అందుతాయి"}, "pTutorial_price_history_block1_title": {"message": "ట్రాక్ ధర"}, "pTutorial_reviews_block1_desc1": {"message": "ఇటావో నుండి కొనుగోలుదారుల సమీక్షలు మరియు అలీఎక్స్ప్రెస్ ఫీడ్బ్యాక్ నుండి నిజమైన ఫోటోలు"}, "pTutorial_reviews_block1_title": {"message": "సమీక్షలు"}, "pTutorial_reviews_block2_desc1": {"message": "ఇతర కొనుగోలుదారుల నుండి సమీక్షలను తనిఖీ చేయడానికి ఇది ఎల్లప్పుడూ సహాయపడుతుంది"}, "pTutorial_same_products_block1_desc1": {"message": "ఉత్తమ ఎంపిక చేయడానికి మీరు వాటిని పోల్చవచ్చు"}, "pTutorial_same_products_block1_desc2": {"message": "చిత్రం ద్వారా శోధించండి కు 'మరిన్ని' క్లిక్ చేయండి"}, "pTutorial_same_products_block1_title": {"message": "అదే ఉత్పత్తులు"}, "pTutorial_same_products_by_image_search_block1_desc1": {"message": "ఉత్పత్తి చిత్రాన్ని అక్కడ వదలండి మరియు ఒక వర్గాన్ని ఎంచుకోండి"}, "pTutorial_same_products_by_image_search_block1_title": {"message": "చిత్రం ద్వారా శోధించండి"}, "pTutorial_seller_analysis_block1_desc1": {"message": "విక్రేత యొక్క సానుకూల స్పందన రేటు, చూడు స్కోర్‌లు మరియు విక్రేత మార్కెట్లో ఎంతకాలం ఉన్నారు"}, "pTutorial_seller_analysis_block1_title": {"message": "విక్రేత రేటింగ్"}, "pTutorial_seller_analysis_block2_desc2": {"message": "విక్రేత రేటింగ్ 3 సూచికలపై ఆధారపడి ఉంటుంది: వివరించిన అంశం, కమ్యూనికేషన్ షిప్పింగ్ వేగం"}, "pTutorial_seller_analysis_block3_desc3": {"message": "అమ్మకందారుల విశ్వసనీయ స్థాయిలను సూచించడానికి మేము 3 రంగులు మరియు చిహ్నాలను ఉపయోగిస్తాము"}, "page_count": {"message": "పేజీల సంఖ్య"}, "pai_chu": {"message": "మినహాయించబడింది"}, "pai_chu_HK_bu_yi_xia_xiaoshou": {"message": "హాంకాంగ్-పరిమితం చేయబడిన వాటిని మినహాయించండి"}, "pai_chu_JP_bu_yi_xia_xiaoshou": {"message": "జపాన్-పరిమితం చేయబడిన వాటిని మినహాయించండి"}, "pai_chu_KR_bu_yi_xia_xiaoshou": {"message": "కొరియా-పరిమితం చేయబడిన వాటిని మినహాయించండి"}, "pai_chu_KZ_bu_yi_xia_xiaoshou": {"message": "కజకిస్తాన్-పరిమితం చేయబడిన వాటిని మినహాయించండి"}, "pai_chu_MO_bu_yi_xia_xiaoshou": {"message": "మకావు-పరిమితం చేయబడిన వాటిని మినహాయించండి"}, "pai_chu_RU_bu_yi_xia_xiaoshou": {"message": "తూర్పు యూరప్-పరిమితం చేయబడిన వాటిని మినహాయించండి"}, "pai_chu_SA_bu_yi_xia_xiaoshou": {"message": "సౌదీ అరేబియా-పరిమితం చేయబడిన వాటిని మినహాయించండి"}, "pai_chu_TW_bu_yi_xia_xiaoshou": {"message": "తైవాన్-పరిమితం చేయబడిన వాటిని మినహాయించండి"}, "pai_chu_USA_bu_yi_xia_xiaoshou": {"message": "యు.ఎస్.-పరిమితం చేయబడిన వాటిని మినహాయించండి"}, "pai_chu_VN_bu_yi_xia_xiaoshou": {"message": "వియత్నాం-పరిమితం చేయబడిన వాటిని మినహాయించండి"}, "pai_chu_bu_yi_xia_xiaoshou": {"message": "పరిమితం చేయబడిన వస్తువులను మినహాయించండి"}, "payable_price_formula": {"message": "ధర + షిప్పింగ్ + తగ్గింపు"}, "pdd_check_retail_btn_txt": {"message": "రిటైల్ తనిఖీ చేయండి"}, "pdd_pifa_to_retail_btn_txt": {"message": "రిటైల్ వద్ద కొనండి"}, "pdp_copy_fail": {"message": "కాపీ విఫలమైంది!"}, "pdp_copy_success": {"message": "కాపీ విజయవంతమైంది!"}, "pdp_share_modal_subtitle": {"message": "స్క్రీన్‌షాట్‌ను షేర్ చేయండి, అతను/ఆమె మీ ఎంపికను చూస్తారు."}, "pdp_share_modal_title": {"message": "మీ ఎంపికను పంచుకోండి"}, "pdp_share_screenshot": {"message": "స్క్రీన్‌షాట్‌ను భాగస్వామ్యం చేయండి"}, "pei_song": {"message": "షిప్పింగ్"}, "pin_lei": {"message": "వర్గం"}, "pin_zhi_ti_yan": {"message": "ఉత్పత్తి నాణ్యత"}, "pin_zhi_ti_yan__desc": {"message": "విక్రేత దుకాణం యొక్క నాణ్యత వాపసు రేటు"}, "pin_zhi_tui_kuan_lv": {"message": "వాపసు రేటు"}, "pin_zhi_tui_kuan_lv__desc": {"message": "గత 30 రోజుల్లో మాత్రమే రీఫండ్ చేయబడిన మరియు తిరిగి వచ్చిన ఆర్డర్‌ల నిష్పత్తి"}, "ping_fen": {"message": "రేటింగ్"}, "ping_jun_fa_huo_su_du": {"message": "సగటు షిప్పింగ్ వేగం"}, "pkgInfo_hide": {"message": "లాజిస్టిక్స్ సమాచారం: ఆన్/ఆఫ్"}, "pkgInfo_no_trace": {"message": "లాజిస్టిక్స్ సమాచారం లేదు"}, "platform_name__1688": {"message": "1688"}, "platform_name__17zwd": {"message": "17zwd.com"}, "platform_name__51taoyang": {"message": "51taoyang.com"}, "platform_name__5ts": {"message": "5ts.com"}, "platform_name__91jf": {"message": "91jf.com"}, "platform_name__alibaba": {"message": "Alibaba.com"}, "platform_name__aliexpress": {"message": "AliExpress"}, "platform_name__amazon": {"message": "Amazon"}, "platform_name__bao66": {"message": "bao66.cn"}, "platform_name__chinagoods": {"message": "chinagoods.com"}, "platform_name__dhgate": {"message": "DHగేట్"}, "platform_name__e3hui": {"message": "e3hui.com"}, "platform_name__ebay": {"message": "ఈబే"}, "platform_name__hznzcn": {"message": "hznzcn.com"}, "platform_name__jd": {"message": "JD"}, "platform_name__juyi5": {"message": "juyi5.cn"}, "platform_name__naver_shopping": {"message": "Naver Shopping"}, "platform_name__pinduoduo": {"message": "పిండువోడు"}, "platform_name__pinduoduo_pifa": {"message": "Pinduoduo టోకు"}, "platform_name__shopee": {"message": "షాపీ"}, "platform_name__sjxzw": {"message": "571xz.com"}, "platform_name__sooxie": {"message": "sooxie.com"}, "platform_name__taobao": {"message": "తోఁబావు"}, "platform_name__taobao_lite": {"message": "Taobao Lite"}, "platform_name__vvic": {"message": "vvic.com"}, "platform_name__walmart": {"message": "వాల్‌మార్ట్"}, "platform_name__wsy": {"message": "wsy.com"}, "platform_name__xingfujie": {"message": "xingfujie.cn"}, "platform_name__yiwugo": {"message": "Yiwugo.com"}, "platform_name__yunchepin": {"message": "yunchepin.cn"}, "platform_name__zhaojiafang": {"message": "zhaojiafang.com"}, "popup_go_to_home": {"message": "హోమ్"}, "popup_go_to_platform": {"message": "అలీప్రైస్‌కు వెళ్లండి"}, "popup_search_placeholder": {"message": "నేను షాపింగ్ చేస్తున్నాను ..."}, "popup_track_package_btn_track": {"message": "ట్రాక్"}, "popup_track_package_desc": {"message": "ఆల్-ఇన్-వన్ ప్యాకేజీ ట్రాకింగ్"}, "popup_track_package_search_placeholder": {"message": "ట్రాకింగ్ సంఖ్య"}, "popup_translate_search_placeholder": {"message": "$searchOn$లో అనువదించండి మరియు శోధించండి", "placeholders": {"searchOn": {"content": "$1"}}}, "price_history": {"message": "ధర చరిత్ర"}, "price_history_chart_tip_ae": {"message": "చిట్కా: ఆర్డర్‌ల సంఖ్య అనేది ప్రారంభించినప్పటి నుండి ఆర్డర్‌ల సంచిత సంఖ్య"}, "price_history_chart_tip_coupang": {"message": "చిట్కా: Coupang మోసపూరిత ఆర్డర్‌ల ఆర్డర్ కౌంట్‌ను తొలగిస్తుంది"}, "price_history_inm_1688_l1": {"message": "దయచేసి ఇన్‌స్టాల్ చేయండి"}, "price_history_inm_1688_l2": {"message": "1688 కోసం అలీప్రైస్ షాపింగ్ అసిస్టెంట్"}, "price_history_panel_lowest_price": {"message": "అత్యల్ప ధర:"}, "price_history_panel_tab_price_tracking": {"message": "ధర చరిత్ర"}, "price_history_panel_tab_seller_analysis": {"message": "విక్రేత విశ్లేషణ"}, "price_history_pro_modal_title": {"message": "ధర చరిత్ర & ఆర్డర్ చరిత్ర"}, "privacy_consent__btn_agree": {"message": "డేటా సేకరణ సమ్మతిని తిరిగి సందర్శించండి"}, "privacy_consent__btn_disable_all": {"message": "అంగీకరించడం లేదు"}, "privacy_consent__btn_enable_all": {"message": "అన్నీ ప్రారంభించండి"}, "privacy_consent__btn_uninstall": {"message": "తొలగించండి"}, "privacy_consent__desc_privacy": {"message": "డేటా లేదా కుకీలు లేకుండా కొన్ని ఫంక్షన్లు ఆపివేయబడతాయి ఎందుకంటే ఆ ఫంక్షన్లకు డేటా లేదా కుకీల వివరణ అవసరం, కానీ మీరు ఇంకా ఇతర ఫంక్షన్లను ఉపయోగించవచ్చు."}, "privacy_consent__desc_privacy_L1": {"message": "దురదృష్టవశాత్తు, డేటా లేదా కుకీలు లేకుండా ఇది పనిచేయదు ఎందుకంటే మాకు డేటా లేదా కుకీల వివరణ అవసరం."}, "privacy_consent__desc_privacy_L2": {"message": "ఈ సమాచారాన్ని సేకరించడానికి మీరు మమ్మల్ని అనుమతించకపోతే, దయచేసి దాన్ని తీసివేయండి."}, "privacy_consent__item_cookies_desc": {"message": "కుకీ, ధర చరిత్రను చూపించడానికి ఆన్‌లైన్‌లో షాపింగ్ చేసేటప్పుడు మాత్రమే మేము మీ కరెన్సీ డేటాను కుకీలలో పొందుతాము."}, "privacy_consent__item_cookies_title": {"message": "అవసరమైన కుకీలు"}, "privacy_consent__item_functional_desc_L1": {"message": "1. మీ కంప్యూటర్ లేదా పరికరాన్ని అనామకంగా గుర్తించడానికి బ్రౌజర్‌లో కుకీలను జోడించండి."}, "privacy_consent__item_functional_desc_L2": {"message": "2. ఫంక్షన్‌తో పనిచేయడానికి యాడ్-ఆన్‌లో ఫంక్షనల్ డేటాను జోడించండి."}, "privacy_consent__item_functional_title": {"message": "ఫంక్షనల్ మరియు అనలిటిక్స్ కుకీలు"}, "privacy_consent__more_desc": {"message": "దయచేసి మేము మీ వ్యక్తిగత డేటాను ఇతర కంపెనీలతో పంచుకోలేమని మరియు ఏ ప్రకటన కంపెనీలు మా సేవ ద్వారా డేటాను సేకరించవని తెలుసుకోండి."}, "privacy_consent__options__btn__desc": {"message": "అన్ని లక్షణాలను ఉపయోగించడానికి, మీరు దీన్ని ఆన్ చేయాలి."}, "privacy_consent__options__btn__label": {"message": "దాన్ని ఆన్ చేయండి"}, "privacy_consent__options__desc_L1": {"message": "మిమ్మల్ని వ్యక్తిగతంగా గుర్తించే క్రింది డేటాను మేము సేకరిస్తాము:"}, "privacy_consent__options__desc_L2": {"message": "- కుకీలు, ధర చరిత్రను చూపించడానికి మీరు ఆన్‌లైన్‌లో షాపింగ్ చేస్తున్నప్పుడు మాత్రమే మేము మీ కరెన్సీ డేటాను కుకీలలో పొందుతాము."}, "privacy_consent__options__desc_L3": {"message": "- మరియు మీ కంప్యూటర్ లేదా పరికరాన్ని అనామకంగా గుర్తించడానికి బ్రౌజర్‌లో కుకీలను జోడించండి."}, "privacy_consent__options__desc_L4": {"message": "- ఇతర అనామక డేటా ఈ పొడిగింపును మరింత సౌకర్యవంతంగా చేస్తుంది."}, "privacy_consent__options__desc_L5": {"message": "దయచేసి మేము మీ వ్యక్తిగత డేటాను ఇతర కంపెనీలతో పంచుకోము మరియు ఏ ప్రకటన కంపెనీలూ మా సేవ ద్వారా డేటాను సేకరించవు."}, "privacy_consent__privacy_preferences": {"message": "గోప్యతా ప్రాధాన్యతలు"}, "privacy_consent__read_more": {"message": "మరింత చదవండి >>"}, "privacy_consent__title_privacy": {"message": "గోప్యత"}, "product_info": {"message": "ఉత్పత్తి సమాచారం"}, "product_recommend__name": {"message": "అదే ఉత్పత్తులు"}, "product_research": {"message": "ఉత్పత్తి పరిశోధన"}, "product_sub__email_desc": {"message": "ధర హెచ్చరిక ఇమెయిల్"}, "product_sub__email_edit": {"message": "సవరించు"}, "product_sub__email_not_verified": {"message": "దయచేసి ఇమెయిల్‌ని ధృవీకరించండి"}, "product_sub__email_required": {"message": "దయచేసి ఇమెయిల్ అందించండి"}, "product_sub__form_countdown": {"message": "$seconds$ సెకన్ల తర్వాత స్వయంచాలకంగా మూసివేయబడుతుంది", "placeholders": {"seconds": {"content": "$1"}}}, "product_sub__form_fail": {"message": "రిమైండర్‌ని జోడించడం విఫలమైంది!"}, "product_sub__form_input_price": {"message": "ఇన్పుట్ ధర"}, "product_sub__form_item_country": {"message": "దేశం"}, "product_sub__form_item_current_price": {"message": "ప్రస్తుత ధర"}, "product_sub__form_item_duration": {"message": "దీని కోసం ట్రాక్ చేయండి"}, "product_sub__form_item_higher_price": {"message": "లేదా ధర >"}, "product_sub__form_item_invalid_higher_price": {"message": "ధర తప్పనిసరిగా $price$ కంటే ఎక్కువగా ఉండాలి", "placeholders": {"price": {"content": "$1"}}}, "product_sub__form_item_invalid_lower_price": {"message": "ధర తప్పనిసరిగా $price$ కంటే తక్కువగా ఉండాలి", "placeholders": {"price": {"content": "$1"}}}, "product_sub__form_item_lower_price": {"message": "ధర <"}, "product_sub__form_submit": {"message": "సమర్పించండి"}, "product_sub__form_success": {"message": "రిమైండర్ విజయాన్ని జోడించండి!"}, "product_sub__high_price_notify": {"message": "ధరల పెరుగుదల గురించి నాకు తెలియజేయండి"}, "product_sub__low_price_notify": {"message": "ధర తగ్గింపుల గురించి నాకు తెలియజేయండి"}, "product_sub__modal_title": {"message": "సబ్‌స్క్రిప్షన్ ధర మార్పు రిమైండర్"}, "province__an_hui": {"message": "<PERSON><PERSON>"}, "province__ao_men": {"message": "Macau"}, "province__bei_jing": {"message": "Beijing"}, "province__chong_qing": {"message": "Chongqing"}, "province__fu_jian": {"message": "Fujian"}, "province__gan_su": {"message": "Gansu"}, "province__guang_dong": {"message": "Guangdong"}, "province__guang_xi": {"message": "Guangxi"}, "province__gui_zhou": {"message": "Guizhou"}, "province__hai_nan": {"message": "Hainan"}, "province__he_bei": {"message": "Hebei"}, "province__he_nan": {"message": "<PERSON><PERSON>"}, "province__hei_long_jiang": {"message": "Heilongjiang"}, "province__hu_bei": {"message": "Hubei"}, "province__hu_nan": {"message": "Hunan"}, "province__ji_lin": {"message": "<PERSON><PERSON>"}, "province__jiang_su": {"message": "Jiangsu"}, "province__jiang_xi": {"message": "Jiangxi"}, "province__liao_ning": {"message": "Liaoning"}, "province__nei_meng_gu": {"message": "Inner Mongolia"}, "province__ning_xia": {"message": "Ningxia"}, "province__qing_hai": {"message": "Qinghai"}, "province__shan_dong": {"message": "Shandong"}, "province__shan_xi": {"message": "Shaanxi"}, "province__shang_hai": {"message": "Shanghai"}, "province__si_chuan": {"message": "Sichuan"}, "province__tai_wan": {"message": "Taiwan"}, "province__tian_jin": {"message": "Tianjin"}, "province__xi_zhang": {"message": "Tibet"}, "province__xiang_gang": {"message": "Hong Kong"}, "province__xin_jiang": {"message": "Xinjiang"}, "province__yun_nan": {"message": "Yunnan"}, "province__zhe_jiang": {"message": "Zhejiang"}, "purple_rocket": {"message": "로켓직구"}, "qi_ding_liang": {"message": "MOQ"}, "qi_ding_liang_qi_ding_jia": {"message": "MOQ & MOP"}, "qi_ye_mian_ji": {"message": "ఎంటర్ప్రైజ్ ప్రాంతం"}, "qing_zhi_shao_xuan_ze_yi_ge_chan_pin": {"message": "దయచేసి కనీసం ఒక ఉత్పత్తిని ఎంచుకోండి"}, "qing_zhi_shao_xuan_ze_yi_ge_zi_duan": {"message": "దయచేసి కనీసం ఒక ఫీల్డ్‌ని ఎంచుకోండి"}, "qu_deng_lu": {"message": "లాగిన్ చేయండి"}, "quan_guo_yan_xuan": {"message": "గ్లోబల్ ఎంపిక"}, "recommendation_popup_banner_btn_install": {"message": "దీన్ని ఇన్‌స్టాల్ చేయండి"}, "recommendation_popup_banner_desc": {"message": "3/6 నెలల్లో ధర చరిత్రను ప్రదర్శించండి మరియు ధర డ్రాప్ నోటిఫికేషన్"}, "region__all": {"message": "అన్ని ప్రాంతాలు"}, "region__hai_wai": {"message": "Overseas"}, "region__hua_bei_qu": {"message": "North China"}, "region__hua_dong_qu": {"message": "East China"}, "region__hua_nan_qu": {"message": "South China"}, "region__hua_zhong_qu": {"message": "Central China"}, "region__jiang_zhe_lu": {"message": "Jiangsu, Zhejiang and Shanghai"}, "remove_web_limits": {"message": "కుడి క్లిక్‌ని ప్రారంభించండి"}, "ren_zheng_gong_chang": {"message": "సర్టిఫైడ్ ఫ్యాక్టరీ"}, "ren_zheng_gong_ying_shang_nian_zhan": {"message": "సర్టిఫైడ్ సరఫరాదారుగా సంవత్సరాలు"}, "required_to_aliprice_login": {"message": "AliPriceకి లాగిన్ అవ్వాలి"}, "revenue_last30_days": {"message": "గత 30 రోజులలో అమ్మకాల మొత్తం"}, "review_counts": {"message": "కలెక్టర్ల సంఖ్య"}, "rocket": {"message": "로켓"}, "ru_zhu_nian_xian": {"message": "ప్రవేశ కాలం"}, "sales_amount_last30_days": {"message": "గత 30 రోజుల్లో మొత్తం అమ్మకాలు"}, "sales_last30_days": {"message": "గత 30 రోజుల్లో అమ్మకాలు"}, "sbi_alibaba_cate__accessories": {"message": "ఉపకరణాలు"}, "sbi_alibaba_cate__aqfk": {"message": "భద్రత"}, "sbi_alibaba_cate__bags_cases": {"message": "బ్యాగులు & కేసులు"}, "sbi_alibaba_cate__beauty": {"message": "అందం"}, "sbi_alibaba_cate__beverage": {"message": "పానీయం"}, "sbi_alibaba_cate__bgwh": {"message": "కార్యాలయ సంస్కృతి"}, "sbi_alibaba_cate__bz": {"message": "ప్యాకేజీ"}, "sbi_alibaba_cate__ccyj": {"message": "వంటసామాను"}, "sbi_alibaba_cate__clothes": {"message": "దుస్తులు"}, "sbi_alibaba_cate__cmgd": {"message": "మీడియా బ్రాడ్‌కాస్టింగ్"}, "sbi_alibaba_cate__coat_jacket": {"message": "కోట్ & జాకెట్"}, "sbi_alibaba_cate__consumer_electronics": {"message": "కన్స్యూమర్ ఎలక్ట్రానిక్స్"}, "sbi_alibaba_cate__cryp": {"message": "వయోజన ఉత్పత్తులు"}, "sbi_alibaba_cate__csyp": {"message": "బెడ్ లైనింగ్స్"}, "sbi_alibaba_cate__cwyy": {"message": "పెంపుడు జంతువుల తోటపని"}, "sbi_alibaba_cate__cysx": {"message": "తాజాగా క్యాటరింగ్"}, "sbi_alibaba_cate__dgdq": {"message": "ఎలక్ట్రీషియన్"}, "sbi_alibaba_cate__dl": {"message": "నటన"}, "sbi_alibaba_cate__dress_suits": {"message": "దుస్తుల & సూట్లు"}, "sbi_alibaba_cate__dszm": {"message": "లైటింగ్"}, "sbi_alibaba_cate__dzqj": {"message": "ఎలక్ట్రానిక్ పరికరం"}, "sbi_alibaba_cate__essb": {"message": "ఉపయోగించిన సామగ్రి"}, "sbi_alibaba_cate__food": {"message": "ఆహారం"}, "sbi_alibaba_cate__fspj": {"message": "దుస్తులు & ఉపకరణాలు"}, "sbi_alibaba_cate__furniture": {"message": "ఫర్నిచర్"}, "sbi_alibaba_cate__fzpg": {"message": "వస్త్ర తోలు"}, "sbi_alibaba_cate__ghjq": {"message": "వ్యకిగత జాగ్రత"}, "sbi_alibaba_cate__gt": {"message": "ఉక్కు"}, "sbi_alibaba_cate__gyp": {"message": "క్రాఫ్ట్స్"}, "sbi_alibaba_cate__hb": {"message": "పర్యావరణ అనుకూలమైనది"}, "sbi_alibaba_cate__hfcz": {"message": "చర్మ సంరక్షణ అలంకరణ"}, "sbi_alibaba_cate__hg": {"message": "రసాయన పరిశ్రమ"}, "sbi_alibaba_cate__jg": {"message": "ప్రాసెసింగ్"}, "sbi_alibaba_cate__jianccai": {"message": "భవన సామగ్రి"}, "sbi_alibaba_cate__jichuang": {"message": "యంత్ర పరికరం"}, "sbi_alibaba_cate__jjry": {"message": "గృహ రోజువారీ ఉపయోగం"}, "sbi_alibaba_cate__jtys": {"message": "రవాణా"}, "sbi_alibaba_cate__jxsb": {"message": "పరికరాలు"}, "sbi_alibaba_cate__jxwj": {"message": "యాంత్రిక హార్డ్వేర్"}, "sbi_alibaba_cate__jydq": {"message": "గృహోపకరణాలు"}, "sbi_alibaba_cate__jzjc": {"message": "గృహ మెరుగుదల నిర్మాణ వస్తువులు"}, "sbi_alibaba_cate__jzjf": {"message": "హోమ్ టెక్స్‌టైల్స్"}, "sbi_alibaba_cate__mj": {"message": "టవల్"}, "sbi_alibaba_cate__myyp": {"message": "బేబీ ఉత్పత్తులు"}, "sbi_alibaba_cate__nanz": {"message": "పురుషుల"}, "sbi_alibaba_cate__nvz": {"message": "ఆడవారి వస్త్రాలు"}, "sbi_alibaba_cate__ny": {"message": "శక్తి"}, "sbi_alibaba_cate__others": {"message": "ఇతరులు"}, "sbi_alibaba_cate__qcyp": {"message": "ఆటో ఉపకరణాలు"}, "sbi_alibaba_cate__qmpj": {"message": "ఆటో భాగాలు"}, "sbi_alibaba_cate__shoes": {"message": "బూట్లు"}, "sbi_alibaba_cate__smdn": {"message": "డిజిటల్ కంప్యూటర్"}, "sbi_alibaba_cate__snqj": {"message": "నిల్వ మరియు శుభ్రపరచడం"}, "sbi_alibaba_cate__spjs": {"message": "ఆహార పానీయం"}, "sbi_alibaba_cate__swfw": {"message": "వ్యాపార సేవలు"}, "sbi_alibaba_cate__toys_hobbies": {"message": "బొమ్మ"}, "sbi_alibaba_cate__trousers_skirt": {"message": "ప్యాంటు & లంగా"}, "sbi_alibaba_cate__txcp": {"message": "కమ్యూనికేషన్ ఉత్పత్తులు"}, "sbi_alibaba_cate__tz": {"message": "పిల్లల దుస్తులు"}, "sbi_alibaba_cate__underwear": {"message": "లోదుస్తులు"}, "sbi_alibaba_cate__wjgj": {"message": "హార్డ్‌వేర్ సాధనాలు"}, "sbi_alibaba_cate__xgpi": {"message": "తోలు సంచులు"}, "sbi_alibaba_cate__xmhz": {"message": "ప్రాజెక్ట్ సహకారం"}, "sbi_alibaba_cate__xs": {"message": "రబ్బరు"}, "sbi_alibaba_cate__ydfs": {"message": "క్రీడా దుస్తులు"}, "sbi_alibaba_cate__ydhw": {"message": "బహిరంగ క్రీడ"}, "sbi_alibaba_cate__yjkc": {"message": "మెటలర్జికల్ ఖనిజాలు"}, "sbi_alibaba_cate__yqyb": {"message": "వాయిద్యం"}, "sbi_alibaba_cate__ys": {"message": "ముద్రణ"}, "sbi_alibaba_cate__yyby": {"message": "వైద్య సంరక్షణ"}, "sbi_alibaba_cn_kj_90mjs": {"message": "గత 90 రోజుల్లో కొనుగోలుదారుల సంఖ్య"}, "sbi_alibaba_cn_kj_90xsl": {"message": "గత 90 రోజుల్లో అమ్మకాల పరిమాణం"}, "sbi_alibaba_cn_kj_gjsj": {"message": "అంచనా ధర"}, "sbi_alibaba_cn_kj_gjwlyf": {"message": "అంతర్జాతీయ షిప్పింగ్ ఫీజు"}, "sbi_alibaba_cn_kj_gjyf": {"message": "షిప్పింగ్ ఫీజు"}, "sbi_alibaba_cn_kj_gssj": {"message": "అంచనా ధర"}, "sbi_alibaba_cn_kj_lr": {"message": "లాభం"}, "sbi_alibaba_cn_kj_lrgs": {"message": "లాభం = అంచనా ధర x లాభం"}, "sbi_alibaba_cn_kj_pjfhsd": {"message": "సగటు డెలివరీ వేగం"}, "sbi_alibaba_cn_kj_qtfy": {"message": "ఇతర రుసుము"}, "sbi_alibaba_cn_kj_qtfygs": {"message": "ఇతర ఖర్చు = అంచనా ధర x ఇతర వ్యయ నిష్పత్తి"}, "sbi_alibaba_cn_kj_spjg": {"message": "ధర"}, "sbi_alibaba_cn_kj_spzl": {"message": "బరువు"}, "sbi_alibaba_cn_kj_szd": {"message": "స్థానం"}, "sbi_alibaba_cn_kj_unit_ge": {"message": "ముక్కలు"}, "sbi_alibaba_cn_kj_unit_jian": {"message": "ముక్కలు"}, "sbi_alibaba_cn_kj_unit_ke": {"message": "గ్రాము"}, "sbi_alibaba_cn_kj_unit_ren": {"message": "కొనుగోలుదారులు"}, "sbi_alibaba_cn_kj_unit_tai": {"message": "ముక్కలు"}, "sbi_alibaba_cn_kj_unit_tao": {"message": "సెట్ చేస్తుంది"}, "sbi_alibaba_cn_kj_unit_tian": {"message": "రోజులు"}, "sbi_alibaba_cn_kj_zwbj": {"message": "ధర లేదు"}, "sbi_aliprice_alibaba_cn__jiage": {"message": "ధర"}, "sbi_aliprice_alibaba_cn__keshou": {"message": "అమ్మకానికి అందుబాటులో ఉంది"}, "sbi_aliprice_alibaba_cn__moren": {"message": "డిఫాల్ట్"}, "sbi_aliprice_alibaba_cn__queding": {"message": "ఖచ్చితంగా"}, "sbi_aliprice_alibaba_cn__xiaoliang": {"message": "అమ్మకాలు"}, "sbi_aliprice_alibaba_cn_cate__jiaju": {"message": "ఫర్నిచర్"}, "sbi_aliprice_alibaba_cn_cate__linghsi": {"message": "చిరుతిండి"}, "sbi_aliprice_alibaba_cn_cate__meizhuang": {"message": "అలంకరణలు"}, "sbi_aliprice_alibaba_cn_cate__neiyi": {"message": "లోదుస్తులు"}, "sbi_aliprice_alibaba_cn_cate__peishi": {"message": "ఉపకరణాలు"}, "sbi_aliprice_alibaba_cn_cate__pinchui": {"message": "సీసా పానీయం"}, "sbi_aliprice_alibaba_cn_cate__qita": {"message": "ఇతరులు"}, "sbi_aliprice_alibaba_cn_cate__qunzhuang": {"message": "లంగా"}, "sbi_aliprice_alibaba_cn_cate__shangyi": {"message": "జాకెట్"}, "sbi_aliprice_alibaba_cn_cate__shuma": {"message": "ఎలక్ట్రానిక్స్"}, "sbi_aliprice_alibaba_cn_cate__wanju": {"message": "బొమ్మ"}, "sbi_aliprice_alibaba_cn_cate__xiangbao": {"message": "సామాను"}, "sbi_aliprice_alibaba_cn_cate__xiazhuang": {"message": "బాటమ్స్"}, "sbi_aliprice_alibaba_cn_cate__xiezi": {"message": "షూ"}, "sbi_aliprice_cate__apparel": {"message": "దుస్తులు"}, "sbi_aliprice_cate__automobiles_motorcycles": {"message": "ఆటోమొబైల్స్ & మోటార్ సైకిళ్ళు"}, "sbi_aliprice_cate__beauty_health": {"message": "అందం & ఆరోగ్యం"}, "sbi_aliprice_cate__cellphones_telecommunications": {"message": "సెల్‌ఫోన్లు & టెలికమ్యూనికేషన్స్"}, "sbi_aliprice_cate__computer_office": {"message": "కంప్యూటర్ & ఆఫీస్"}, "sbi_aliprice_cate__consumer_electronics": {"message": "కన్స్యూమర్ ఎలక్ట్రానిక్స్"}, "sbi_aliprice_cate__education_office_supplies": {"message": "విద్య & కార్యాలయ సామాగ్రి"}, "sbi_aliprice_cate__electronic_components_supplies": {"message": "ఎలక్ట్రానిక్ భాగాలు & సామాగ్రి"}, "sbi_aliprice_cate__furniture": {"message": "ఫర్నిచర్"}, "sbi_aliprice_cate__hair_extensions_wigs": {"message": "జుట్టు పొడిగింపులు & విగ్స్"}, "sbi_aliprice_cate__home_garden": {"message": "ఇల్లు"}, "sbi_aliprice_cate__home_improvement": {"message": "గృహ మెరుగుదల"}, "sbi_aliprice_cate__jewelry_accessories": {"message": "ఆభరణాలు & ఉపకరణాలు"}, "sbi_aliprice_cate__luggage_bags": {"message": "సామాను & సంచులు"}, "sbi_aliprice_cate__mother_kids": {"message": "తల్లి & పిల్లలు"}, "sbi_aliprice_cate__novelty_special_use": {"message": "వింత & ప్రత్యేక ఉపయోగం"}, "sbi_aliprice_cate__security_protection": {"message": "భద్రత & రక్షణ"}, "sbi_aliprice_cate__shoes": {"message": "షూస్"}, "sbi_aliprice_cate__sports_entertainment": {"message": "క్రీడలు & వినోదం"}, "sbi_aliprice_cate__toys_hobbies": {"message": "బొమ్మలు & అభిరుచులు"}, "sbi_aliprice_cate__watches": {"message": "గడియారాలు"}, "sbi_aliprice_cate__weddings_events": {"message": "వివాహాలు & సంఘటనలు"}, "sbi_btn_capture_txt": {"message": "క్యాప్చర్"}, "sbi_btn_source_now_txt": {"message": "ఇప్పుడు మూలం"}, "sbi_button__chat_with_me": {"message": "నాతో చాట్ చేయండి"}, "sbi_button__contact_supplier": {"message": "Contact"}, "sbi_button__hide_on_this_site": {"message": "ఈ సైట్‌లో చూపవద్దు"}, "sbi_button__open_settings": {"message": "చిత్రం ద్వారా శోధనను కాన్ఫిగర్ చేయండి"}, "sbi_capture_shortcut_tip": {"message": "లేదా కీబోర్డ్‌లోని \"ఎంటర్\" కీని నొక్కండి"}, "sbi_capturing_tip": {"message": "సంగ్రహిస్తోంది"}, "sbi_composed_rating_45": {"message": "4.5 - 5.0 నక్షత్రాలు"}, "sbi_crop_and_search": {"message": "వెతకండి"}, "sbi_crop_start": {"message": "స్క్రీన్ షాట్ ఉపయోగించండి"}, "sbi_err_captcha_action": {"message": "ధృవీకరించండి"}, "sbi_err_captcha_for_alibaba_cn": {"message": "ధృవీకరణ అవసరం, దయచేసి ధృవీకరించడానికి చిత్రాన్ని అప్‌లోడ్ చేయండి. ($video_tutorial$ని వీక్షించండి లేదా కుక్కీలను క్లియర్ చేయడానికి ప్రయత్నించండి)", "placeholders": {"feedback": {"content": "$1"}, "video_tutorial": {"content": "$1"}}}, "sbi_err_captcha_for_aliprice": {"message": "అసాధారణ ట్రాఫిక్, దయచేసి ధృవీకరించండి"}, "sbi_err_captcha_for_taobao": {"message": "టావోబావో ధృవీకరించమని మిమ్మల్ని అభ్యర్థిస్తుంది, దయచేసి చిత్రాన్ని మాన్యువల్‌గా అప్‌లోడ్ చేయండి మరియు దాన్ని ధృవీకరించడానికి శోధించండి. ఈ లోపం \"టావోబావో సెర్చ్ బై ఇమేజ్\" క్రొత్త ధృవీకరణ విధానం కారణంగా ఉంది, టావోబావో $feedback$ పై ఫిర్యాదు చాలా తరచుగా ధృవీకరించాలని మేము మీకు సూచిస్తున్నాము.", "placeholders": {"feedback": {"content": "$1"}}}, "sbi_err_captcha_for_taobao_feedback": {"message": "అభిప్రాయం"}, "sbi_err_captcha_msg": {"message": "$platform$ శోధన పరిమితులను తీసివేయడానికి మీరు శోధించడానికి లేదా భద్రతా ధృవీకరణను పూర్తి చేయడానికి చిత్రాన్ని అప్‌లోడ్ చేయవలసి ఉంటుంది", "placeholders": {"platform": {"content": "$1"}}}, "sbi_err_checking_if_low_version": {"message": "ఇది తాజా వెర్షన్ కాదా అని తనిఖీ చేయండి"}, "sbi_err_cookie_btn_clear": {"message": "కుకీలను క్లియర్ చేయండి"}, "sbi_err_cookie_for_alibaba_cn": {"message": "1688 కుక్కీలను క్లియర్ చేయాలా?(మళ్లీ లాగిన్ కావాలి)"}, "sbi_err_desperate_feature_pdd": {"message": "ఇమేజ్ సెర్చ్ ఫంక్షన్ ఇమేజ్ ఎక్స్‌టెన్షన్ ద్వారా Pinduoduo శోధనకు తరలించబడింది."}, "sbi_err_hidden_skill_of_improve_search_rate": {"message": "ఇమేజ్ సెర్చ్ సక్సెస్ రేటును ఎలా మెరుగుపరచాలి?"}, "sbi_err_img_undersize": {"message": "చిత్రం > $imgMinimumSize$", "placeholders": {"imgMinimumSize": {"content": "$1"}}}, "sbi_err_login_required": {"message": "లాగిన్ అవ్వండి $loginSite$", "placeholders": {"loginSite": {"content": "$1"}}}, "sbi_err_login_required_action": {"message": "ప్రవేశించండి"}, "sbi_err_low_version": {"message": "తాజా వెర్షన్‌ను ఇన్‌స్టాల్ చేయండి ($latestVersion$)", "placeholders": {"latestVersion": {"content": "$1"}}}, "sbi_err_low_version_action": {"message": "డౌన్‌లోడ్"}, "sbi_err_need_help": {"message": "సహాయం కావాలి"}, "sbi_err_network": {"message": "నెట్‌వర్క్ లోపం, మీరు వెబ్‌సైట్‌ను సందర్శించగలరని నిర్ధారించుకోండి"}, "sbi_err_not_low_version": {"message": "తాజా వెర్షన్ ఇన్‌స్టాల్ చేయబడింది ($latestVersion$)", "placeholders": {"latestVersion": {"content": "$1"}}}, "sbi_err_try_again": {"message": "మళ్లీ ప్రయత్నించండి"}, "sbi_err_try_again_action": {"message": "మళ్లీ ప్రయత్నించండి"}, "sbi_err_visit_and_try": {"message": "మళ్లీ ప్రయత్నించండి లేదా మళ్లీ ప్రయత్నించడానికి $website$ని సందర్శించండి", "placeholders": {"website": {"content": "$1"}}}, "sbi_err_visit_site": {"message": "$siteName$ హోమ్ పేజీని సందర్శించండి", "placeholders": {"siteName": {"content": "$1"}}}, "sbi_fail_tip": {"message": "లోడ్ అవుతోంది విఫలమైంది, దయచేసి పేజీని రిఫ్రెష్ చేసి మళ్ళీ ప్రయత్నించండి."}, "sbi_kuajing_filter_area": {"message": "ప్రాంతం"}, "sbi_kuajing_filter_au": {"message": "ఆస్ట్రేలియా"}, "sbi_kuajing_filter_btn_confirm": {"message": "నిర్ధారించండి"}, "sbi_kuajing_filter_de": {"message": "జర్మనీ"}, "sbi_kuajing_filter_destination_country": {"message": "చేరాల్సిన దేశం"}, "sbi_kuajing_filter_es": {"message": "స్పెయిన్"}, "sbi_kuajing_filter_estimate": {"message": "అంచనా"}, "sbi_kuajing_filter_estimate_price": {"message": "అంచనా ధర"}, "sbi_kuajing_filter_estimate_price_formula": {"message": "అంచనా ధర సూత్రం = (వస్తువుల ధర + అంతర్జాతీయ లాజిస్టిక్స్ సరుకు)/(1 - లాభం మార్జిన్ - ఇతర వ్యయ నిష్పత్తి)"}, "sbi_kuajing_filter_fr": {"message": "ఫ్రాన్స్"}, "sbi_kuajing_filter_kw_placeholder": {"message": "శీర్షికకు సరిపోయేలా కీలకపదాలను నమోదు చేయండి"}, "sbi_kuajing_filter_logistics": {"message": "లాజిస్టిక్స్ టెంప్లేట్"}, "sbi_kuajing_filter_logistics_china_post": {"message": "చైనా పోస్ట్ ఎయిర్ మెయిల్"}, "sbi_kuajing_filter_logistics_discount": {"message": "లాజిస్టిక్స్ డిస్కౌంట్"}, "sbi_kuajing_filter_logistics_epacket": {"message": "ఎపాకెట్"}, "sbi_kuajing_filter_logistics_price_formula": {"message": "అంతర్జాతీయ లాజిస్టిక్స్ సరుకు = (బరువు x షిప్పింగ్ ధర + నమోదు రుసుము) x (1 - తగ్గింపు)"}, "sbi_kuajing_filter_others_fee": {"message": "ఇతర రుసుము"}, "sbi_kuajing_filter_profit_percent": {"message": "లాభం"}, "sbi_kuajing_filter_prop": {"message": "గుణాలు"}, "sbi_kuajing_filter_ru": {"message": "రష్యా"}, "sbi_kuajing_filter_total": {"message": "$count$ సారూప్య వస్తువులను సరిపోల్చండి", "placeholders": {"count": {"content": "$1"}}}, "sbi_kuajing_filter_uk": {"message": "యు.కె"}, "sbi_kuajing_filter_usa": {"message": "అమెరికా"}, "sbi_login_punish_title__pdd_pifa": {"message": "పిండువోడు టోకు"}, "sbi_msg_no_result": {"message": "ఫలితం కనుగొనబడలేదు,దయచేసి h కు లాగిన్ అవ్వండి లేదా మరొక చిత్రాన్ని ప్రయత్నించండి", "placeholders": {}}, "sbi_msg_no_result_check_support_page_l1": {"message": "సఫారి కోసం తాత్కాలికంగా అందుబాటులో లేదు, దయచేసి $supportPage$ ని ఉపయోగించండి.", "placeholders": {"supportPage": {"content": "$1"}}}, "sbi_msg_no_result_check_support_page_l2": {"message": "Chrome బ్రౌజర్ మరియు దాని పొడిగింపులు"}, "sbi_msg_no_result_reinstall_l1": {"message": "ఫలితాలు ఏవీ కనుగొనబడలేదు, దయచేసి $loginSite$ కు లాగిన్ అవ్వండి లేదా మరొక చిత్రాన్ని ప్రయత్నించండి లేదా తాజా వెర్షన్ $latestExtUrl$ ని మళ్లీ ఇన్‌స్టాల్ చేయండి", "placeholders": {"loginSite": {"content": "$1"}, "latestExtUrl": {"content": "$2"}}}, "sbi_msg_no_result_reinstall_l2": {"message": "తాజా వెర్షన్", "placeholders": {}}, "sbi_search_by_selected_area": {"message": "ఎంచుకున్న ప్రాంతం"}, "sbi_shipping_": {"message": "అదే రోజు షిప్పింగ్"}, "sbi_specify_category": {"message": "వర్గాన్ని పేర్కొనండి:"}, "sbi_start_crop": {"message": "ప్రాంతాన్ని ఎంచుకోండి"}, "sbi_store_name_alibabaCNKuaJing": {"message": "1688 విదేశాలలో"}, "sbi_tutorial_btn_more": {"message": "మరిన్ని మార్గాలు"}, "sbi_tutorial_find_coupons_on_taobao": {"message": "Taobao కూపన్‌లను కనుగొనండి"}, "sbi_txt__empty_retry": {"message": "క్షమించండి, ఫలితాలు ఏవీ కనుగొనబడలేదు, దయచేసి మళ్లీ ప్రయత్నించండి."}, "sbi_txt__min_order": {"message": "కనిష్ట. ఆర్డర్"}, "sbi_visiting": {"message": "బ్రౌజింగ్"}, "sbi_yiwugo__jiagexiangtan": {"message": "విక్రేతను సంప్రదించండి"}, "sbi_yiwugo__qigou": {"message": "$num$ ముక్కలు (MOQ)", "placeholders": {"num": {"content": "$1"}}}, "sbi_yiwugo__xing": {"message": "నక్షత్రాలు"}, "searchByImage_screenshot": {"message": "ఒక-క్లిక్ స్క్రీన్‌షాట్"}, "searchByImage_search": {"message": "ఒకే ఐటెమ్‌ల కోసం ఒక క్లిక్‌తో శోధించండి"}, "searchByImage_size_type": {"message": "ఫైల్ పరిమాణం $num$ MB కంటే పెద్దదిగా ఉండకూడదు, $type$ మాత్రమే", "placeholders": {"num": {"content": "$1"}, "type": {"content": "$2"}}}, "search_by_image_progress_analysing": {"message": "చిత్రాన్ని విశ్లేషిస్తోంది"}, "search_by_image_progress_searching": {"message": "ఉత్పత్తుల కోసం శోధించండి"}, "search_by_image_progress_sending": {"message": "చిత్రాన్ని పంపుతోంది"}, "search_by_image_response_rate": {"message": "ప్రతిస్పందన రేటు: ఈ సరఫరాదారుని సంప్రదించిన కొనుగోలుదారుల  గంటల్లో $responseInHour$ గంటల్లో స్పందన వచ్చింది.", "placeholders": {"responseInHour": {"content": "$1"}}}, "search_by_keyword": {"message": "కీవర్డ్ ద్వారా శోధించండి:"}, "select_country_language_modal_title_country": {"message": "దేశం"}, "select_country_language_modal_title_language": {"message": "భాష"}, "select_country_region_modal_title": {"message": "దేశం / ప్రాంతాన్ని ఎంచుకోండి"}, "select_language_modal_title": {"message": "భాషను ఎంచుకోండి:"}, "select_shop": {"message": "స్టోర్ ఎంచుకోండి"}, "sellers_count": {"message": "ప్రస్తుత పేజీలో విక్రేతల సంఖ్య"}, "sellers_count_per_page": {"message": "ప్రస్తుత పేజీలో విక్రేతల సంఖ్య"}, "service_score": {"message": "సమగ్ర సేవా రేటింగ్"}, "set_shortcut_keys": {"message": "షార్ట్‌కట్ కీలను సెట్ చేయండి"}, "setting_logo_title": {"message": "షాపింగ్ అసిస్టెంట్"}, "setting_modal_options_position_title": {"message": "ప్లగ్-ఇన్ స్థానం"}, "setting_modal_options_position_value_left": {"message": "ఎడమ మూలలో"}, "setting_modal_options_position_value_right": {"message": "కుడి మూలలో"}, "setting_modal_options_theme_title": {"message": "థీమ్ రంగు"}, "setting_modal_options_theme_value_dark": {"message": "చీకటి"}, "setting_modal_options_theme_value_light": {"message": "కాంతి"}, "setting_modal_title": {"message": "సెట్టింగులు"}, "setting_options_country_title": {"message": "దేశం / ప్రాంతం"}, "setting_options_hover_zoom_desc": {"message": "జూమ్ చేయడానికి మౌస్ ఓవర్"}, "setting_options_hover_zoom_title": {"message": "హోవర్ జూమ్"}, "setting_options_jd_coupon_desc": {"message": "JD.com లో కూపన్ కనుగొనబడింది"}, "setting_options_jd_coupon_title": {"message": "JD.com కూపన్"}, "setting_options_language_title": {"message": "భాష"}, "setting_options_price_drop_alert_desc": {"message": "నా అభిమాన ఉత్పత్తుల ధర పడిపోయినప్పుడు, మీరు పుష్ నోటిఫికేషన్‌ను అందుకుంటారు."}, "setting_options_price_drop_alert_title": {"message": "ధర డ్రాప్ హెచ్చరిక"}, "setting_options_price_history_on_list_page_desc": {"message": "ఉత్పత్తి శోధన పేజీలో ధర చరిత్రను ప్రదర్శించు"}, "setting_options_price_history_on_list_page_title": {"message": "ధర చరిత్ర (జాబితా పేజీ)"}, "setting_options_price_history_on_produt_page_desc": {"message": "ఉత్పత్తి వివరాలు పేజీలో ఉత్పత్తి చరిత్రను ప్రదర్శించండి"}, "setting_options_price_history_on_produt_page_title": {"message": "ధర చరిత్ర (వివరాల పేజీ)"}, "setting_options_sales_analysis_desc": {"message": "$platforms$ ఉత్పత్తి జాబితా పేజీలో ధర, విక్రయాల పరిమాణం, విక్రేతల సంఖ్య మరియు స్టోర్ విక్రయాల నిష్పత్తి యొక్క మద్దతు గణాంకాలు", "placeholders": {"platforms": {"content": "$1"}}}, "setting_options_sales_analysis_title": {"message": "అమ్మకాల విశ్లేషణ"}, "setting_options_save_success_msg": {"message": "విజయం"}, "setting_options_tacking_price_title": {"message": "ధర మార్పు హెచ్చరిక"}, "setting_options_value_off": {"message": "ఆఫ్"}, "setting_options_value_on": {"message": "పై"}, "setting_pkg_quick_view_desc": {"message": "మద్దతు: 1688 & టావోబావో"}, "setting_saved_message": {"message": "మార్పులు విజయవంతంగా సేవ్ చేయబడ్డాయి"}, "setting_section_enable_platform_title": {"message": "ఆఫ్"}, "setting_section_setting_title": {"message": "సెట్టింగులు"}, "setting_section_shortcuts_title": {"message": "సత్వరమార్గాలు"}, "settings_aliprice_agent__desc": {"message": "$platforms$ ఉత్పత్తి వివరాల పేజీలో ప్రదర్శించబడుతుంది", "placeholders": {"platforms": {"content": "$1"}}}, "settings_aliprice_agent__title": {"message": "నా కోసం కొనండి"}, "settings_copy_link__desc": {"message": "ఉత్పత్తి వివరాల పేజీలో ప్రదర్శించు"}, "settings_copy_link__title": {"message": "లింక్ బటన్‌ను కాపీ చేయండి"}, "settings_currency_desc__for_detail": {"message": "1688 ఉత్పత్తి వివరాల పేజీకి మద్దతు ఇవ్వండి"}, "settings_currency_desc__for_list": {"message": "చిత్రం ద్వారా శోధించండి (1688/1688 విదేశీ / టావోబావో చేర్చండి)"}, "settings_currency_desc__for_sbi": {"message": "ధరను ఎంచుకోండి"}, "settings_currency_desc_display_for_list": {"message": "చిత్ర శోధనలో చూపబడింది (1688/1688 ఓవర్సీస్/టావోబావోతో సహా)"}, "settings_currency_rate_desc": {"message": "మార్పిడి రేటును \"$currencyRateFrom$\" నుండి నవీకరిస్తోంది", "placeholders": {"currencyRateFrom": {"content": "$1"}}}, "settings_currency_rate_from_boc": {"message": "చైనా బ్యాంకు"}, "settings_download_images__desc": {"message": "$platforms$ నుండి చిత్రాలను డౌన్‌లోడ్ చేయడానికి మద్దతు", "placeholders": {"platforms": {"content": "$1"}}}, "settings_download_images__title": {"message": "డౌన్‌లోడ్ ఇమేజ్ బటన్"}, "settings_download_reviews__desc": {"message": "$platforms$ ఉత్పత్తి వివరాల పేజీలో ప్రదర్శించబడుతుంది", "placeholders": {"platforms": {"content": "$1"}}}, "settings_download_reviews__title": {"message": "సమీక్ష చిత్రాలను డౌన్‌లోడ్ చేయండి"}, "settings_google_translate_desc": {"message": "గూగుల్ ట్రాన్స్లేట్ బార్ పొందడానికి కుడి క్లిక్ చేయండి"}, "settings_google_translate_title": {"message": "వెబ్ పేజీ అనువాదం"}, "settings_historical_trend_desc": {"message": "ఉత్పత్తి జాబితా పేజీలో చిత్రం యొక్క కుడి దిగువ మూలలో ప్రదర్శించండి"}, "settings_modal_btn_more": {"message": "మరిన్ని సెట్టింగ్‌లు"}, "settings_productInfo_desc": {"message": "ఉత్పత్తి జాబితా పేజీలో మరింత వివరణాత్మక ఉత్పత్తి సమాచారాన్ని ప్రదర్శించండి. దీన్ని ప్రారంభించడం వల్ల కంప్యూటర్ లోడ్ పెరుగుతుంది మరియు పేజీ లాగ్‌కు కారణం కావచ్చు. ఇది పనితీరును ప్రభావితం చేస్తే, దానిని నిలిపివేయమని సిఫార్సు చేయబడింది."}, "settings_product_recommend__desc": {"message": "$platforms$ ఉత్పత్తి వివరాల పేజీలో ప్రధాన చిత్రం క్రింద ప్రదర్శించబడింది", "placeholders": {"platforms": {"content": "$1"}}}, "settings_product_recommend__name": {"message": "సిఫార్సు చేయబడిన ఉత్పత్తులు"}, "settings_research_desc": {"message": "ఉత్పత్తి జాబితా పేజీలో మరింత వివరణాత్మక సమాచారాన్ని ప్రశ్నించండి"}, "settings_sbi_add_to_list": {"message": "$listType$ లోకి జోడించండి", "placeholders": {"listType": {"content": "$1"}}}, "settings_sbi_detail_page_icon_title_desc": {"message": "ఫోటో ద్వారా శోధన ఫలితాల సూక్ష్మచిత్రం"}, "settings_sbi_remove_from_list": {"message": "$listType$ నుండి తీసివేయండి", "placeholders": {"listType": {"content": "$1"}}}, "settings_search_by_image_add_to_blacklist": {"message": "బ్లాక్లిస్ట్కు జోడించండి"}, "settings_search_by_image_adjust_entrance_entrance": {"message": "ప్రవేశ స్థానాన్ని సర్దుబాటు చేయండి"}, "settings_search_by_image_blacklist_desc": {"message": "బ్లాక్లిస్ట్‌లోని వెబ్‌సైట్లలో చిహ్నాన్ని చూపవద్దు."}, "settings_search_by_image_blacklist_title": {"message": "బ్లాక్లిస్ట్"}, "settings_search_by_image_bottom_left": {"message": "దిగువ ఎడమ"}, "settings_search_by_image_bottom_right": {"message": "దిగువ కుడి"}, "settings_search_by_image_clear_blacklist": {"message": "బ్లాక్లిస్ట్ క్లియర్"}, "settings_search_by_image_detail_page_icon_title": {"message": "సూక్ష్మ"}, "settings_search_by_image_detail_page_icon_val_large": {"message": "పెద్దది"}, "settings_search_by_image_detail_page_icon_val_small": {"message": "చిన్నది"}, "settings_search_by_image_display_button_desc": {"message": "చిత్రం ద్వారా శోధించడానికి చిహ్నంపై ఒక క్లిక్ చేయండి"}, "settings_search_by_image_display_button_title": {"message": "చిత్రాలపై ఐకాన్"}, "settings_search_by_image_sourece_websites_desc": {"message": "ఈ వెబ్‌సైట్లలో మూల ఉత్పత్తిని కనుగొనండి"}, "settings_search_by_image_sourece_websites_title": {"message": "చిత్ర ఫలితం ద్వారా శోధించండి"}, "settings_search_by_image_top_left": {"message": "పై ఎడమ"}, "settings_search_by_image_top_right": {"message": "పై కుడి"}, "settings_search_keyword_on_x__desc": {"message": "$platform$లో పదాలను శోధించండి", "placeholders": {"platform": {"content": "$1"}}}, "settings_search_keyword_on_x__title": {"message": "పదాలను ఎంచుకున్నప్పుడు $platform$ చిహ్నాన్ని చూపండి", "placeholders": {"platform": {"content": "$1"}}}, "settings_similar_products_desc": {"message": "ఆ వెబ్‌సైట్లలో ఒకే ఉత్పత్తిని కనుగొనడానికి ప్రయత్నించండి (గరిష్టంగా 5 వరకు)"}, "settings_similar_products_title": {"message": "అదే ఉత్పత్తిని కనుగొనండి"}, "settings_toolbar_expand_title": {"message": "ప్లగిన్ తక్కువ చేయి"}, "settings_top_toolbar_desc": {"message": "పేజీ ఎగువన ఉన్న శోధన పట్టీ"}, "settings_top_toolbar_title": {"message": "శోధన పట్టీ"}, "settings_translate_search_desc": {"message": "చైనీస్‌లోకి అనువదించండి మరియు శోధించండి"}, "settings_translate_search_title": {"message": "బహుభాషా శోధన"}, "settings_translator_contextmenu_title": {"message": "అనువదించడానికి క్యాప్చర్ చేయండి"}, "settings_translator_title": {"message": "అనువదించు"}, "shai_xuan_dao_chu": {"message": "ఎగుమతి చేయడానికి ఫిల్టర్ చేయండి"}, "shai_xuan_zi_duan": {"message": "ఫీల్డ్‌లను ఫిల్టర్ చేయండి"}, "shang_jia_shi_jian": {"message": "షెల్ఫ్ సమయంలో"}, "shang_pin_biao_ti": {"message": "ఉత్పత్తి శీర్షిక"}, "shang_pin_dui_bi": {"message": "ఉత్పత్తి పోలిక"}, "shang_pin_lian_jie": {"message": "ఉత్పత్తి లింక్"}, "shang_pin_xin_xi": {"message": "ఉత్పత్తి సమాచారం"}, "share_modal__content": {"message": "మీ స్నేహితులతో పంచుకోండి"}, "share_modal__disable_for_while": {"message": "నేను ఏమీ పంచుకోవాలనుకోవడం లేదు"}, "share_modal__title": {"message": "మీకు $extensionName$ నచ్చిందా?", "placeholders": {"extensionName": {"content": "$1"}}}, "sheng_yu_ku_cun": {"message": "మిగిలింది"}, "shi_fou_ke_ding_zhi": {"message": "ఇది అనుకూలీకరించదగినదా?"}, "shi_fou_ren_zheng_gong_ying_sha": {"message": "సర్టిఫైడ్ సరఫరాదారు"}, "shi_fou_ren_zheng_gong_ying_shang_zong_shu": {"message": "సర్టిఫైడ్ సరఫరాదారులు"}, "shi_fou_you_mao_yi_dan_bao": {"message": "వాణిజ్య హామీ"}, "shi_fou_you_mao_yi_dan_bao_zong_shu": {"message": "వాణిజ్య హామీలు"}, "shipping_fee": {"message": "షిప్పింగ్ రుసుము"}, "shop_followers": {"message": "అనుచరులను షాపింగ్ చేయండి"}, "shou_qi": {"message": "తక్కువ"}, "similar_products_warn_max_platforms": {"message": "గరిష్టంగా 5"}, "sku_calc_price": {"message": "లెక్కించిన ధర"}, "sku_calc_price_settings": {"message": "లెక్కించిన ధర సెట్టింగ్‌లు"}, "sku_formula": {"message": "ఫార్ములా"}, "sku_formula_desc": {"message": "ఫార్ములా వివరణ"}, "sku_formula_desc_text": {"message": "సంక్లిష్ట గణిత సూత్రాలకు మద్దతు ఇస్తుంది, అసలు ధరను A ద్వారా మరియు సరుకును B ద్వారా సూచిస్తారు\n\n<br/>\n\nబ్రాకెట్లలో (), ప్లస్ +, మైనస్ -, గుణకారం * మరియు భాగహారం /\n\n<br/>\n\nఉదాహరణ:\n\n<br/>\n\n1. అసలు ధరకు 1.2 రెట్లు సాధించి, ఆపై సరుకును జోడించడానికి, సూత్రం: A*1.2+B\n\n<br/>\n\n2. అసలు ధరను 1 యువాన్‌తో కలిపి సాధించడానికి, ఆపై 1.2 రెట్లు గుణించడానికి, సూత్రం: (A+1)*1.2\n\n<br/>\n\n3. అసలు ధరను 10 యువాన్‌తో కలిపి సాధించడానికి, ఆపై 1.2 రెట్లు గుణించి, ఆపై 3 యువాన్‌లను తీసివేయడానికి, సూత్రం: (A+10)*1.2-3"}, "sku_in_stock": {"message": "స్టాక్‌లో ఉంది"}, "sku_invalid_formula_format": {"message": "చెల్లని ఫార్ములా ఫార్మాట్"}, "sku_inventory": {"message": "ఇన్వెంటరీ"}, "sku_link_copy_fail": {"message": "విజయవంతంగా కాపీ చేయబడింది, sku స్పెసిఫికేషన్‌లు మరియు గుణాలు ఎంచుకోబడలేదు"}, "sku_link_copy_success": {"message": "విజయవంతంగా కాపీ చేయబడింది, sku స్పెసిఫికేషన్‌లు మరియు గుణాలు ఎంచుకోబడ్డాయి"}, "sku_list": {"message": "SKU జాబితా"}, "sku_min_qrder_qty": {"message": "కనీస ఆర్డర్ పరిమాణం"}, "sku_name": {"message": "SKU పేరు"}, "sku_no": {"message": "నం."}, "sku_original_price": {"message": "అసలు ధర"}, "sku_price": {"message": "SKU ధర"}, "stop_track_time_label": {"message": "ట్రాకింగ్ గడువు:"}, "suo_zai_di_qu": {"message": "స్థానం"}, "tab_pkg_quick_view": {"message": "లాజిస్టిక్స్ మానిటర్"}, "tab_product_details_price_history": {"message": "ధర చరిత్ర"}, "tab_product_details_reviews": {"message": "ఫోటో సమీక్షలు"}, "tab_product_details_seller_analysis": {"message": "విక్రేత విశ్లేషణ"}, "tab_product_details_similar_products": {"message": "అదే ఉత్పత్తులు"}, "total_days_listed_per_product": {"message": "షెల్ఫ్ రోజుల మొత్తం ÷ ఉత్పత్తుల సంఖ్య"}, "total_items": {"message": "ఉత్పత్తుల మొత్తం సంఖ్య"}, "total_price_per_product": {"message": "ధరల మొత్తం ÷ ఉత్పత్తుల సంఖ్య"}, "total_rating_per_product": {"message": "రేటింగ్‌ల మొత్తం ÷ ఉత్పత్తుల సంఖ్య"}, "total_revenue": {"message": "మొత్తం రాబడి"}, "total_revenue40_items": {"message": "ప్రస్తుత పేజీలోని $amount$ ఉత్పత్తుల మొత్తం రాబడి", "placeholders": {"amount": {"content": "$1"}}}, "total_sales": {"message": "అమ్మకాల మొత్తం"}, "total_sales40_items": {"message": "ప్రస్తుత పేజీలో $amount$ ఉత్పత్తుల మొత్తం అమ్మకాలు", "placeholders": {"amount": {"content": "$1"}}}, "track_for_12_months": {"message": "దీని కోసం ట్రాక్ చేయండి: 1 సంవత్సరం"}, "track_for_3_months": {"message": "దీని కోసం ట్రాక్ చేయండి: 3 నెలలు"}, "track_for_6_months": {"message": "దీని కోసం ట్రాక్ చేయండి: 6 నెలలు"}, "tracking_price_email_add_btn": {"message": "ఇమెయిల్ జోడించండి"}, "tracking_price_email_edit_btn": {"message": "ఇమెయిల్‌ను సవరించండి"}, "tracking_price_email_intro": {"message": "మేము మీకు ఇమెయిల్ ద్వారా తెలియజేస్తాము."}, "tracking_price_email_invalid": {"message": "దయచేసి చెల్లుబాటు అయ్యే ఇమెయిల్‌ను అందించండి"}, "tracking_price_email_verified_desc": {"message": "మీరు ఇప్పుడు మా ధర డ్రాప్ హెచ్చరికను స్వీకరించవచ్చు."}, "tracking_price_email_verified_title": {"message": "విజయవంతంగా ధృవీకరించబడింది"}, "tracking_price_email_verify_desc_line1": {"message": "మేము మీ ఇమెయిల్ చిరునామాకు ధృవీకరణ లింక్‌ను పంపాము,"}, "tracking_price_email_verify_desc_line2": {"message": "దయచేసి మీ ఇమెయిల్ ఇన్‌బాక్స్‌ను తనిఖీ చేయండి."}, "tracking_price_email_verify_title": {"message": "ఇమెయిల్ నిర్ధారించండి"}, "tracking_price_web_push_notification_intro": {"message": "డెస్క్‌టాప్‌లో: అలీప్రైస్ మీ కోసం ఏదైనా ఉత్పత్తిని పర్యవేక్షించగలదు మరియు ధర మారిన తర్వాత మీకు వెబ్ పుష్ నోటిఫికేషన్ పంపవచ్చు."}, "tracking_price_web_push_notification_title": {"message": "వెబ్ పుష్ నోటిఫికేషన్‌లు"}, "translate_im__login_required": {"message": "AliPrice ద్వారా అనువదించబడింది, దయచేసి $loginUrl$కి లాగిన్ చేయండి", "placeholders": {"loginUrl": {"content": "$1"}}}, "translate_im__paste_fail": {"message": "క్లిప్‌బోర్డ్‌కి అనువదించబడింది మరియు కాపీ చేయబడింది, కానీ అలివాంగ్వాంగ్ యొక్క పరిమితి కారణంగా, మీరు దీన్ని మాన్యువల్‌గా అతికించాలి!"}, "translate_im__send": {"message": "అనువదించండి & పంపండి"}, "translate_search": {"message": "అనువదించండి మరియు శోధించండి"}, "translation_originals_translated": {"message": "ఒరిజినల్ మరియు చైనీస్"}, "translation_translated": {"message": "చైనీస్"}, "translator_btn_capture_txt": {"message": "అనువదించు"}, "translator_language_auto_detect": {"message": "స్వయంచాలక గుర్తింపు"}, "translator_language_detected": {"message": "గుర్తించబడింది"}, "translator_language_search_placeholder": {"message": "శోధన భాష"}, "try_again": {"message": "మళ్ళీ ప్రయత్నించండి"}, "tu_pian_chi_cun": {"message": "చిత్ర పరిమాణం:"}, "tu_pian_lian_jie": {"message": "చిత్రం లింక్"}, "tui_huan_ti_yan": {"message": "రిటర్న్ అనుభవం"}, "tui_huan_ti_yan__desc": {"message": "అమ్మకందారుల అమ్మకాల తర్వాత సూచికలను అంచనా వేయండి"}, "tutorial__show_all": {"message": "అన్ని లక్షణాలు"}, "tutorial_ae_popup_title": {"message": "పొడిగింపును పిన్ చేయండి, Aliexpress తెరవండి"}, "tutorial_aliexpress_reviews_analysis": {"message": "AliExpress సమీక్ష విశ్లేషణ"}, "tutorial_aliprice_agent_with1688_desc_l1": {"message": "మద్దతు USD"}, "tutorial_aliprice_agent_with1688_desc_l2": {"message": "కొరియా/జపాన్/మెయిన్‌ల్యాండ్ చైనాకు షిప్పింగ్"}, "tutorial_aliprice_agent_with1688_title": {"message": "1688 విదేశీ కొనుగోలుకు మద్దతు ఇస్తుంది"}, "tutorial_auto_apply_coupon_title": {"message": "స్వయంచాలకంగా దరఖాస్తు కూపన్"}, "tutorial_btn_example": {"message": "ఉదాహరణ"}, "tutorial_btn_see_more": {"message": "మరిన్ని విధులు"}, "tutorial_compare_products": {"message": "అదే శైలితో పోల్చండి"}, "tutorial_currency_convert_title": {"message": "మార్పిడి రేటు మార్పిడి"}, "tutorial_export_shopping_cart": {"message": "CSVని ఎగుమతి చేయండి, Taobao మరియు 1688కి మద్దతు ఇవ్వండి"}, "tutorial_export_shopping_cart_title": {"message": "ఎగుమతి బండి"}, "tutorial_price_history_pro": {"message": "ఉత్పత్తి వివరాల పేజీలో ప్రదర్శించబడుతుంది.\n<PERSON><PERSON>, <PERSON><PERSON><PERSON>, Amazon మరియు Ebayకి మద్దతు ఇవ్వండి"}, "tutorial_price_history_pro_title": {"message": "పూర్తి సంవత్సరం ధర చరిత్ర మరియు ఆర్డర్ చరిత్ర"}, "tutorial_sbi_context_menu_screenshot_title": {"message": "అదే శైలి కోసం స్క్రీన్‌షాట్ శోధన"}, "tutorial_translate_search": {"message": "శోధించడానికి అనువదించండి"}, "tutorial_translate_search_and_package_tracking": {"message": "అనువాద శోధన మరియు ప్యాకేజీ ట్రాకింగ్"}, "unit_bao": {"message": "pcs"}, "unit_ben": {"message": "pcs"}, "unit_bi": {"message": "ఆదేశాలు"}, "unit_chuang": {"message": "pcs"}, "unit_dai": {"message": "pcs"}, "unit_dui": {"message": "prs"}, "unit_fen": {"message": "pcs"}, "unit_ge": {"message": "pcs"}, "unit_he": {"message": "pcs"}, "unit_jian": {"message": "pcs"}, "unit_li_fang_mi": {"message": "m³"}, "unit_ping": {"message": "pcs"}, "unit_ping_fang_mi": {"message": "㎡"}, "unit_shuang": {"message": "prs"}, "unit_tai": {"message": "pcs"}, "unit_ti": {"message": "pcs"}, "unit_tiao": {"message": "pcs"}, "unit_xiang": {"message": "pcs"}, "unit_zhang": {"message": "pcs"}, "unit_zhi": {"message": "pcs"}, "verify_contact_support": {"message": "మద్దతును సంప్రదించండి"}, "verify_human_verification": {"message": "మానవ ధృవీకరణ"}, "verify_unusual_access": {"message": "అసాధారణ యాక్సెస్ కనుగొనబడింది"}, "view_history_clean_all": {"message": "అన్నీ శుభ్రం చేయండి"}, "view_history_clean_all_warring": {"message": "చూసిన అన్ని రికార్డులను శుభ్రపరచాలా?"}, "view_history_clean_all_warring_title": {"message": "హెచ్చరిక"}, "view_history_viewd": {"message": "వీక్షించారు"}, "website": {"message": "వెబ్సైట్"}, "weight": {"message": "బరువు"}, "wu_fa_huo_qu_dao_gai_shu_ju": {"message": "డేటాను పొందడం సాధ్యం కాలేదు"}, "wu_liu_shi_xiao": {"message": "ఆన్-టైమ్ షిప్‌మెంట్"}, "wu_liu_shi_xiao__desc": {"message": "విక్రేత స్టోర్ యొక్క 48-గంటల సేకరణ రేటు మరియు నెరవేర్పు రేటు"}, "xia_dan_jia": {"message": "తుది ధర"}, "xian_xuan_ze_product_attributes": {"message": "ఉత్పత్తి లక్షణాలను ఎంచుకోండి"}, "xiao_liang": {"message": "సేల్స్ వాల్యూమ్"}, "xiao_liang_zhan_bi": {"message": "అమ్మకాల పరిమాణంలో శాతం"}, "xiao_shi": {"message": "$num$ గంటలు", "placeholders": {"num": {"content": "$1"}}}, "xiao_shou_e": {"message": "రాబడి"}, "xiao_shou_e_zhan_bi": {"message": "రాబడి శాతం"}, "xuan_zhong_x_tiao_ji_lu": {"message": "$amount$ రికార్డులను ఎంచుకోండి", "placeholders": {"amoun": {"content": "$1"}, "amount": {"content": "$1"}}}, "yan_xuan": {"message": "ఎంపిక"}, "yi_ding_zai_zuo_ce": {"message": "పిన్ చేయబడింది"}, "yi_jia_zai_wan_suo_you_shang_pin": {"message": "అన్ని ఉత్పత్తులు లోడ్ చేయబడ్డాయి"}, "yi_nian_xiao_liang": {"message": "వార్షిక అమ్మకాలు"}, "yi_nian_xiao_liang_zhan_bi": {"message": "వార్షిక అమ్మకాల వాటా"}, "yi_nian_xiao_shou_e": {"message": "వార్షిక టర్నోవర్"}, "yi_nian_xiao_shou_e_zhan_bi": {"message": "వార్షిక టర్నోవర్ షేర్"}, "yi_shua_xin": {"message": "రిఫ్రెష్ చేయబడింది"}, "yin_cang_xiang_tong_dian": {"message": "సారూప్యతలను దాచండి"}, "you_xiao_liang": {"message": "సేల్స్ వాల్యూమ్‌తో"}, "yu_ji_dao_da_shi_jian": {"message": "అంచనా వేసిన రాక సమయం"}, "yuan_gong_ren_shu": {"message": "ఉద్యోగుల సంఖ్య"}, "yue_cheng_jiao": {"message": "నెలవారీ వాల్యూమ్"}, "yue_dai_xiao": {"message": "డ్రాప్‌షిప్పింగ్"}, "yue_dai_xiao__desc": {"message": "గత 30 రోజుల్లో డ్రాప్‌షిప్పింగ్ విక్రయాలు"}, "yue_dai_xiao_pai_xu__desc": {"message": "గత 30 రోజుల్లో డ్రాప్‌షిప్పింగ్ విక్రయాలు, ఎక్కువ నుండి తక్కువకు క్రమబద్ధీకరించబడ్డాయి"}, "yue_xiao_liang__desc": {"message": "గత 30 రోజులలో అమ్మకాల పరిమాణం"}, "zhan_kai": {"message": "మరిన్ని"}, "zhe_kou": {"message": "తగ్గింపు"}, "zhi_chi_yi_jian_dai_fa": {"message": "డ్రాప్‌షిప్పింగ్"}, "zhi_chi_yi_jian_dai_fa_bao_you": {"message": "ఉచిత షిప్పింగ్"}, "zhi_fu_ding_dan_shu": {"message": "చెల్లింపు ఆర్డర్లు"}, "zhi_fu_ding_dan_shu__desc": {"message": "ఈ ఉత్పత్తి కోసం ఆర్డర్‌ల సంఖ్య (30 రోజులు)"}, "zhu_ce_xing_zhi": {"message": "నమోదు స్వభావం"}, "zi_ding_yi_tiao_jian": {"message": "అనుకూల పరిస్థితులు"}, "zi_duan": {"message": "ఫీల్డ్స్"}, "zi_ti_xiao_liang": {"message": "వైవిధ్యం విక్రయించబడింది"}, "zong_he_fu_wu_fen": {"message": "మొత్తం రేటింగ్"}, "zong_he_fu_wu_fen__desc": {"message": "విక్రేత సేవ యొక్క మొత్తం రేటింగ్"}, "zong_he_fu_wu_fen__short": {"message": "రేటింగ్"}, "zong_he_ti_yan_fen": {"message": "రేటింగ్"}, "zong_he_ti_yan_fen_3": {"message": "4 నక్షత్రాల క్రింద"}, "zong_he_ti_yan_fen_4": {"message": "4 - 4.5 నక్షత్రాలు"}, "zong_he_ti_yan_fen_4_5": {"message": "4.5 - 5.0 నక్షత్రాలు"}, "zong_he_ti_yan_fen_5": {"message": "5 నక్షత్రాలు"}, "zong_ku_cun": {"message": "మొత్తం జాబితా"}, "zong_xiao_liang": {"message": "అమ్మకాల మొత్తం"}, "zui_jin_30D_3Min_xiang_ying_lv": {"message": "గత 30 రోజుల్లో 3 నిమిషాల ప్రతిస్పందన రేటు"}, "zui_jin_30D_48H_lan_shou_lv": {"message": "గత 30 రోజుల్లో 48H రికవరీ రేటు"}, "zui_jin_30D_48H_lv_yue_lv": {"message": "గత 30 రోజుల్లో 48H పనితీరు రేటు"}, "zui_jin_30D_jiao_yi_ji_lu": {"message": "ట్రేడింగ్ రికార్డ్ (30 రోజులు)"}, "zui_jin_30D_jiao_yi_ji_lu__desc": {"message": "ట్రేడింగ్ రికార్డ్ (30 రోజులు)"}, "zui_jin_30D_jiu_fen_lv": {"message": "గత 30 రోజులలో వివాద రేటు"}, "zui_jin_30D_pin_zhi_tui_kuan_lv": {"message": "గత 30 రోజుల్లో నాణ్యమైన వాపసు రేటు"}, "zui_jin_30D_zhi_fu_ding_dan_shu": {"message": "గత 30 రోజుల్లో చెల్లింపు ఆర్డర్‌ల సంఖ్య"}}