/**
 * @description Build Script V2 类型定义
 *
 * 重构后的构建脚本类型系统，遵循 KISS 原则，简化复杂的嵌套逻辑
 */

import type { ProcessedVariantConfig } from '../extension-config-manager/types.js';

/**
 * @description 构建选项
 */
export interface BuildOptions {
  /** @description 构建模式，'dev' 或 'release' */
  mode: 'dev' | 'release';
  /** @description 是否使用缓存，可以覆盖默认行为 */
  useCache?: boolean | 'auto';
}

/**
 * @description 构建队列项 - 表示需要构建的单个插件及其渠道包
 */
export interface BuildQueueItem {
  /** @description 插件名称 */
  extensionName: string;
  /** @description 渠道包列表，空数组表示构建所有渠道包 */
  variantTargets: string[];
}

/**
 * @description 构建计划项 - 表示经过配置处理后的构建项
 */
export interface BuildPlanItem {
  /** @description 插件名称 */
  extensionName: string;
  /** @description 渠道包名称 */
  variantTarget: string;
  /** @description 处理后的配置对象 */
  config: ProcessedVariantConfig;
  /** @description 是否使用了缓存 */
  usedCache: boolean;
}

/**
 * @description 构建结果统计
 */
export interface BuildSummary {
  /** @description 总插件数 */
  totalExtensions: number;
  /** @description 总渠道包数 */
  totalVariantTargets: number;
  /** @description 使用缓存的数量 */
  cachedCount: number;
  /** @description 重新生成的数量 */
  regeneratedCount: number;
  /** @description 构建开始时间 */
  startTime: Date;
  /** @description 构建结束时间 */
  endTime?: Date;
}
