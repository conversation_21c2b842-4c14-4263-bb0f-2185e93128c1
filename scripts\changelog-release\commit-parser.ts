import { createLogger } from '../helpers/logger.js';
import type { ParsedCommit } from './types.js';

const logger = createLogger('Commit解析');

/**
 * 将原始的 git commit 字符串解析为结构化对象
 *
 * 该函数解析 git log 输出的提交信息，提取关键字段：
 * - 解析提交哈希、主题行和正文内容
 * - 使用正则表达式匹配 Conventional Commits 格式的类型和主题
 * - 从正文中提取 Issue-ID 和 Applies-To 信息
 * - 如果缺少 Applies-To 字段，跳过该提交（无法关联到插件）
 * - 支持可选的作用域（如 feat(scope): message）
 * - 识别破坏性变更（通过 '!' 或 'BREAKING CHANGE' 关键字）
 *
 * @param {string} commitString - 来自 git log 的原始提交字符串，包含哈希、主题和正文
 * @returns {ParsedCommit | null} 解析后的提交对象，如果解析失败或缺少必要字段则返回 null
 *   - hash: 提交哈希值
 *   - subject: 提交主题（去除类型前缀）
 *   - body: 提交正文内容
 *   - type: 提交类型（feat、fix、chore等）
 *   - issueId: Issue-ID 字段值，可能为 null
 *   - appliesTo: 适用的插件列表
 *   - isBreaking: 是否为破坏性变更
 *
 * @example
 * // 带 '!' 的破坏性变更
 * parseCommit('abc123\nfeat!: 移除旧 API\nApplies-To: my-plugin')
 * // 返回: { ..., isBreaking: true }
 */
export function parseCommit(commitString: string): ParsedCommit | null {
  const lines = commitString.trim().split('\n');
  const hash = lines.shift() || '';
  const subjectLine = lines.shift() || '';
  const bodyLines = lines;

  // 匹配 conventional commit 的 type, scope, breaking change indicator (!) 和 subject
  const subjectMatch = subjectLine.match(
    /^(?<type>\w+)(?:\((?<scope>[\w-]+)\))?(?<breaking>!)?:\s*(?<subject>.*)$/,
  );
  const type = subjectMatch?.groups?.type || 'chore';
  const subject = subjectMatch?.groups?.subject || subjectLine;
  const isBreakingFromHeader = subjectMatch?.groups?.breaking === '!';

  const body = bodyLines.join('\n');
  const isBreakingFromBody = body.includes('BREAKING CHANGE');
  const isBreaking = isBreakingFromHeader || isBreakingFromBody;

  const issueIdMatch = body.match(/Issue-ID:\s*(.*)/);
  const appliesToMatch = body.match(/Applies-To:\s*(.*)/);

  const issueId = issueIdMatch ? issueIdMatch[1].trim() : null;
  const appliesTo = appliesToMatch ? appliesToMatch[1].split(',').map((s) => s.trim()) : [];

  // 如果缺少 Applies-To，则跳过此提交，因为无法关联到任何插件
  if (appliesTo.length === 0) {
    logger.debug(`跳过缺少 Applies-To 的提交: ${subject}`);
    return null;
  }

  return {
    hash,
    subject,
    body,
    type,
    issueId,
    appliesTo,
    isBreaking,
  };
}
