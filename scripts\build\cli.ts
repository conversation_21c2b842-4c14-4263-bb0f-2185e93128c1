#!/usr/bin/env node
/**
 * @fileoverview 构建工具命令行接口
 * @description 提供开发和生产模式的浏览器插件构建命令
 */

import fs from 'fs-extra';
import path from 'path';
import yargs from 'yargs';
import { hideBin } from 'yargs/helpers';
import { Logger, projectPaths } from '../helpers/index.js';
import { runBuildWorkflow } from './index.js';
import type { BuildQueueItem } from './types.js';

/**
 * @description 命令行主入口函数
 * @returns Promise<void>
 *
 * 支持的命令：
 * - dev <extension> [variantTarget] - 开发模式构建
 * - build [options] - 生产模式构建
 *
 * 全局选项：
 * - --verbose, -V - 显示详细输出
 * - --no-cache - 强制不使用缓存
 *
 * 功能说明：
 * 1. 提供开发和生产两种构建模式
 * 2. 开发模式支持单个插件的快速构建和热重载
 * 3. 生产模式支持批量构建多个插件和渠道包
 * 4. 支持缓存策略以提高构建效率
 * 5. 提供详细的日志输出选项
 */
yargs(hideBin(process.argv))
  .scriptName('build')
  .usage('用法: $0 <command> [options]\n\n浏览器插件构建工具')
  .option('verbose', {
    alias: 'V',
    type: 'boolean',
    describe: '显示详细输出信息',
    default: false,
    global: true,
  })
  .command(
    'dev <extension> [variantTarget]',
    '开发模式构建指定插件和渠道包',
    (yargs) => {
      yargs
        .positional('extension', {
          describe: '插件名（如 cookies_manager）',
          type: 'string',
          demandOption: true,
        })
        .positional('variantTarget', {
          describe: '渠道包名（如 chrome-mv3-master）',
          type: 'string',
          demandOption: false,
          default: 'chrome-mv3-master',
        })
        .option('no-cache', {
          type: 'boolean',
          describe: '强制不使用缓存',
          default: false,
        });
    },
    async (argv) => {
      const extensionName = argv.extension as string;
      const variantTarget = (argv.variantTarget as string) || 'chrome-mv3-master';
      const noCache = argv['no-cache'] as boolean;
      const verbose = argv.verbose as boolean;

      Logger.setVerbose(verbose);

      const buildQueue: BuildQueueItem[] = [
        {
          extensionName,
          variantTargets: [variantTarget],
        },
      ];

      await runBuildWorkflow(buildQueue, {
        mode: 'dev',
        useCache: noCache ? false : 'auto',
      });
    },
  )
  .command(
    'build',
    '生产模式批量构建',
    (yargs) => {
      yargs
        .option('extension', {
          type: 'string',
          describe: '仅构建指定插件',
        })
        .option('no-cache', {
          type: 'boolean',
          describe: '强制不使用缓存',
          default: false,
        })
        .array('variantTargets')
        .describe('variantTargets', '指定渠道包列表');
    },
    async (argv) => {
      const noCache = argv['no-cache'] as boolean;
      const verbose = argv.verbose as boolean;

      Logger.setVerbose(verbose);

      let buildQueue: BuildQueueItem[] = [];

      if (argv.extension) {
        // 构建指定插件
        const extensionName = argv.extension as string;
        let variantTargets: string[] = [];

        if (argv.variantTargets && Array.isArray(argv.variantTargets)) {
          variantTargets = argv.variantTargets.map(String);
        }

        buildQueue = [{ extensionName, variantTargets }];
      } else {
        // 从配置文件读取构建列表
        const buildListPath = path.resolve(projectPaths.scripts, './build/release-extensions.json');
        if (!(await fs.pathExists(buildListPath))) {
          throw new Error(
            `未找到 release-extensions.json: ${path.relative(projectPaths.workspace, buildListPath)}`,
          );
        }

        const buildList: [string, string[]?][] = await fs.readJson(buildListPath);
        buildQueue = buildList.map(([extensionName, variantTargets]) => ({
          extensionName,
          variantTargets: variantTargets || [],
        }));
      }

      await runBuildWorkflow(buildQueue, {
        mode: 'release',
        useCache: noCache ? false : 'auto',
      });
    },
  )
  .example('$0 dev cookies_manager', '开发模式构建 cookies_manager 插件（默认渠道包）')
  .example('$0 dev cookies_manager chrome-mv3-tm', '开发模式构建指定渠道包')
  .example('$0 build', '生产模式构建所有插件（从配置文件读取）')
  .example('$0 build --extension cookies_manager', '生产模式构建指定插件的所有渠道包')
  .demandCommand(1, '请指定一个命令（dev、build）')
  .strict()
  .help()
  .version('2.0.0')
  .epilog(
    `说明：
  • dev 命令用于开发模式，支持缓存以提高构建速度
  • build 命令用于生产模式，确保构建结果的一致性
  • 使用 --verbose 可以查看详细的构建过程信息`,
  )
  .parse();
