{"EXTENSION_NAME": {"message": "TODO: EXTENSION_NAME"}, "EXTENSION_DESCRIPTION": {"message": "TODO: EXTENSION_DESCRIPTION"}, "1688_cross_border_hot_selling": {"message": "1688 Grenzüberschreitender Hot Selling Spot"}, "1688_shi_li_ren_zheng": {"message": "1688 Festigkeitszertifikat"}, "1_jian_qi_pi": {"message": "MOQ: 1"}, "1year_yi_shang": {"message": "Mehr als 1 Jahr"}, "24H": {"message": "24H"}, "24H_fa_huo": {"message": "Lieferung innerhalb von 24 Stunden"}, "24H_lan_shou_lv": {"message": "24-Stunden-Verpackungstarif"}, "30D_shang_xin": {"message": "Monatliche Neuankömmlinge"}, "30d_sales": {"message": "$amount$ in 30 Tagen verkauft", "placeholders": {"amount": {"content": "$1"}}}, "3Min_xiang_ying_lv": {"message": "Antwort innerhalb von 3 Min."}, "3Min_xiang_ying_lv__desc": {"message": "Der Anteil von Wangwangs effektiven Antworten auf Käuferanfragenachrichten innerhalb von 3 Minuten in den letzten 30 Tagen"}, "48H": {"message": "48H"}, "48H_fa_huo": {"message": "Lieferung innerhalb von 48 Stunden"}, "48H_lan_shou_lv": {"message": "48-Stunden-Verpackungstarif"}, "48H_lan_shou_lv__desc": {"message": "Verhältnis der abgeholten Bestellanzahl innerhalb von 48 Stunden zur Gesamtanzahl der Bestellungen"}, "48H_lv_yue_lv": {"message": "48-<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ungssatz"}, "48H_lv_yue_lv__desc": {"message": "Verhältnis der abgeholten bzw. gelieferten Bestellanzahl innerhalb von 48 Stunden zur Gesamtzahl der Bestellungen"}, "72H": {"message": "72H"}, "7D_shang_xin": {"message": "Wöchentliche Neuankömmlinge"}, "7D_wu_li_you": {"message": "7 Tage sorgenfrei"}, "ABS_title_text": {"message": "<PERSON><PERSON> enthält eine Markengeschichte"}, "AC_title_text": {"message": "<PERSON><PERSON> hat das Amazon's Choice-Abzeichen"}, "A_title_text": {"message": "<PERSON><PERSON> hat eine A+-Inhaltsseite"}, "BS_title_text": {"message": "Dieses <PERSON> ist als $num$ Bestseller in der Kategorie $type$ eingestuft", "placeholders": {"type": {"content": "$1"}, "num": {"content": "$2"}}}, "LTD_title_text": {"message": "LTD (Limited Time Deal) bedeutet, dass dieses Angebot Teil einer „7-tägigen Aktion“ ist"}, "NR_title_text": {"message": "Dieses <PERSON> ist als $num$ Neuerscheinung in der Kategorie $type$ eingestuft", "placeholders": {"type": {"content": "$1"}, "num": {"content": "$2"}}}, "SBV_title_text": {"message": "<PERSON><PERSON> enthält eine Videoanzeige, eine Art PPC-Anzeige, die normalerweise in der Mitte der Suchergebnisse erscheint"}, "SB_title_text": {"message": "<PERSON><PERSON> enthält eine Markenanzeige, eine Art PPC-Anzeige, die normalerweise oben oder unten in den Suchergebnissen erscheint"}, "SP_title_text": {"message": "<PERSON><PERSON> enthält eine gesponserte Produktanzeige"}, "V_title_text": {"message": "<PERSON><PERSON> enthält eine Videoeinführung"}, "advanced_research": {"message": "Erweiterte Recherche"}, "agent_ds1688___my_order": {"message": "<PERSON><PERSON>"}, "agent_ds1688__add_to_cart": {"message": "Kauf im Ausland"}, "agent_ds1688__cart": {"message": "Einkaufswagen"}, "agent_ds1688__desc": {"message": "Angeboten durch 1688. Unterstützt den Direktkauf aus Übersee, die Zahlung in USD und die Lieferung an Ihr Transitlager in China."}, "agent_ds1688__freight": {"message": "Versandkostenrechner"}, "agent_ds1688__help": {"message": "<PERSON><PERSON><PERSON>"}, "agent_ds1688__packages": {"message": "Frachtbrief"}, "agent_ds1688__profile": {"message": "Persönliches Zentrum"}, "agent_ds1688__warehouse": {"message": "<PERSON><PERSON>"}, "ai_comment_analysis_advantage": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "ai_comment_analysis_ai": {"message": "KI-Bewertungsanalyse"}, "ai_comment_analysis_available": {"message": "Verfügbar"}, "ai_comment_analysis_balance": {"message": "Nicht genügend Münzen, bitte aufladen"}, "ai_comment_analysis_behavior": {"message": "Verhalten"}, "ai_comment_analysis_characteristic": {"message": "Crowd-Merkmale"}, "ai_comment_analysis_comment": {"message": "Das Produkt hat nicht genügend Bewertungen, um genaue Schlussfolgerungen zu ziehen. Bitte wählen Sie ein Produkt mit mehr Bewertungen aus."}, "ai_comment_analysis_consume": {"message": "Geschätzter Verbrauch"}, "ai_comment_analysis_default": {"message": "Standardbewertungen"}, "ai_comment_analysis_desire": {"message": "Kundenerwartungen"}, "ai_comment_analysis_disadvantage": {"message": "Nachteil<PERSON>"}, "ai_comment_analysis_free": {"message": "<PERSON><PERSON><PERSON>"}, "ai_comment_analysis_freeNum": {"message": "1 kostenloses Guthaben wird verwendet"}, "ai_comment_analysis_go_recharge": {"message": "Zur Aufladung gehen"}, "ai_comment_analysis_intelligence": {"message": "Intelligente Bewertungsanalyse"}, "ai_comment_analysis_location": {"message": "<PERSON><PERSON>"}, "ai_comment_analysis_motive": {"message": "Kaufmotivation"}, "ai_comment_analysis_network_error": {"message": "Netzwerkfehler, bitte versuchen Sie es erneut"}, "ai_comment_analysis_normal": {"message": "Fotobewertungen"}, "ai_comment_analysis_number_reviews": {"message": "Anzahl der Bewertungen: $num$, Geschätzter Verbrauch: $consume$", "placeholders": {"num": {"content": "$1"}, "consume": {"content": "$2"}}}, "ai_comment_analysis_ordinary": {"message": "Allgemeine Kommentare"}, "ai_comment_analysis_percentage": {"message": "Prozentsatz"}, "ai_comment_analysis_problem": {"message": "Probleme mit der Zahlung"}, "ai_comment_analysis_reanalysis": {"message": "Erneut analysieren"}, "ai_comment_analysis_reason": {"message": "<PERSON><PERSON><PERSON>"}, "ai_comment_analysis_recharge": {"message": "Aufladen"}, "ai_comment_analysis_recharged": {"message": "Ich habe aufgeladen"}, "ai_comment_analysis_retry": {"message": "<PERSON><PERSON><PERSON> versuchen"}, "ai_comment_analysis_scene": {"message": "Nutzungsszenario"}, "ai_comment_analysis_start": {"message": "Analyse starten"}, "ai_comment_analysis_subject": {"message": "Themen"}, "ai_comment_analysis_time": {"message": "Nutzungszeit"}, "ai_comment_analysis_tool": {"message": "KI-Tool"}, "ai_comment_analysis_user_portrait": {"message": "Benutzerprofil"}, "ai_comment_analysis_welcome": {"message": "Willkommen bei der KI-Bewertungsanalyse"}, "ai_comment_analysis_year": {"message": "Kommentare aus dem letzten Jahr"}, "ai_listing_Exclude_keywords": {"message": "Schlüsselwörter ausschließen"}, "ai_listing_Login_the_feature": {"message": "<PERSON><PERSON>r die Funktion ist eine Anmeldung erforderlich"}, "ai_listing_aI_generation": {"message": "KI-Generierung"}, "ai_listing_add_automatic": {"message": "Automatisch"}, "ai_listing_add_dictionary_new": {"message": "Neue Bibliothek erstellen"}, "ai_listing_add_enter_keywords": {"message": "Schlüsselwörter eingeben"}, "ai_listing_add_inputkey_selling": {"message": "Einen Verkaufspunkt eingeben und $key$ drücken, um das Hinzufügen abzuschließen", "placeholders": {"key": {"content": "$1"}}}, "ai_listing_add_inputkey_warning": {"message": "Limit ü<PERSON>ch<PERSON>ten, bis zu $amount$ Verkaufspunkte", "placeholders": {"amount": {"content": "$1"}}}, "ai_listing_add_keywords": {"message": "Schlüsselwörter hinzufügen"}, "ai_listing_add_manually": {"message": "<PERSON><PERSON>"}, "ai_listing_add_selling": {"message": "Verkaufspunkte hinzufügen"}, "ai_listing_added_keywords": {"message": "Hinzugefügte Schlüsselwörter"}, "ai_listing_added_successfully": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> hi<PERSON>"}, "ai_listing_addexcluded_keywords": {"message": "Geben Sie die ausgeschlossenen Schlüsselwörter ein und drücken Sie Eingabetaste, um das Hinzufügen abzuschließen."}, "ai_listing_adding_selling": {"message": "Hinzugefügte Verkaufspunkte"}, "ai_listing_addkeyword_enter": {"message": "Schlüsselworte eingeben und Eingabetaste drücken, um das Hinzufügen abzuschließen"}, "ai_listing_ai_description": {"message": "AI-Beschreibungs-Wortbibliothek"}, "ai_listing_ai_dictionary": {"message": "AI-Titel-Wortbibliothek"}, "ai_listing_ai_title": {"message": "AI-Titel"}, "ai_listing_aidescription_repeated": {"message": "Name der AI-Beschreibungs-Wortbibliothek darf nicht wiederholt werden"}, "ai_listing_aititle_repeated": {"message": "Name der AI-Titel-Wortbibliothek darf nicht wiederholt werden"}, "ai_listing_data_comes_from": {"message": "<PERSON><PERSON> Daten stammen von:"}, "ai_listing_deleted_successfully": {"message": "Erfolg<PERSON><PERSON>"}, "ai_listing_dictionary_name": {"message": "Bibliotheksname"}, "ai_listing_edit_dictionary": {"message": "Bibliothek ändern..."}, "ai_listing_edit_word_library": {"message": "Wortbibliothek bearbeiten"}, "ai_listing_enter_keywords": {"message": "Schlüsselwörter eingeben und $key$ drücken, um das Hinzufügen abzuschließen", "placeholders": {"key": {"content": "$1"}}}, "ai_listing_exceeded_maximum": {"message": "Das Limit wurde überschritten, maximal $amount$ Schlüsselwörter", "placeholders": {"amount": {"content": "$1"}}}, "ai_listing_excluded_word_library": {"message": "Ausgeschlossene Wortbibliothek"}, "ai_listing_generate_characters": {"message": "<PERSON><PERSON><PERSON> gene<PERSON>"}, "ai_listing_generation_platform": {"message": "Generierungsplattform"}, "ai_listing_help_optimize": {"message": "<PERSON><PERSON><PERSON> mir, den Produkttitel zu optimieren, der Originaltitel ist"}, "ai_listing_include_selling": {"message": "Weitere Verkaufspunkte enthalten:"}, "ai_listing_included_keyword": {"message": "Eingeschlossene Schlüsselwörter"}, "ai_listing_included_keywords": {"message": "Eingeschlossene Schlüsselwörter"}, "ai_listing_input_selling": {"message": "Einen Verkaufspunkt eingeben"}, "ai_listing_input_selling_fit": {"message": "Verkaufspunkte passend zum Titel eingeben"}, "ai_listing_input_selling_please": {"message": "Bitte Verkaufspunkte eingeben"}, "ai_listing_intelligently_title": {"message": "<PERSON><PERSON><PERSON> Si<PERSON> oben den erforderlichen Inhalt ein, um den Titel intelligent zu generieren"}, "ai_listing_keyword_product_title": {"message": "Schlüsselwort-Produkttitel"}, "ai_listing_keywords_repeated": {"message": "Schlüsselwörter dürfen nicht wiederholt werden"}, "ai_listing_listed_selling_points": {"message": "Eingeschlossene Verkaufspunkte"}, "ai_listing_long_title_1": {"message": "Enthält grundlegende Informationen wie Markenname, Produkttyp, Produktmerkmale usw."}, "ai_listing_long_title_2": {"message": "Auf der Grundlage des Standard-Produkttitels werden SEO-förderliche Schlüsselwörter hinzugefügt."}, "ai_listing_long_title_3": {"message": "<PERSON>eben <PERSON>, Produkttyp, Produktmerkmalen und Schlüsselwörtern werden auch Longtail-Schlüsselwörter aufgenommen, um bei spezifischen, segmentierten Suchanfragen höhere Rankings zu erzielen."}, "ai_listing_longtail_keyword_product_title": {"message": "Longtail-Schlüsselwort-Produkttitel"}, "ai_listing_manually_enter": {"message": "<PERSON><PERSON> e<PERSON> ..."}, "ai_listing_network_not_working": {"message": "Internet ist nicht verfügbar, VPN ist erford<PERSON>lich, um auf ChatGPT zuzugreifen"}, "ai_listing_new_dictionary": {"message": "Neue Wortbibliothek erstellen ..."}, "ai_listing_new_generate": {"message": "<PERSON><PERSON><PERSON>"}, "ai_listing_optional_words": {"message": "Optionale Wörter"}, "ai_listing_original_title": {"message": "Originaltitel"}, "ai_listing_other_keywords_included": {"message": "Weitere enthaltene Schlüsselwörter:"}, "ai_listing_please_again": {"message": "Bitte versuche es erneut"}, "ai_listing_please_select": {"message": "Die folgenden Titel wurden für Sie generiert, bitte wählen Sie:"}, "ai_listing_product_category": {"message": "Produktkategorie"}, "ai_listing_product_category_is": {"message": "Die Produktkategorie ist"}, "ai_listing_product_category_to": {"message": "Zu welcher Kategorie gehört das Produkt?"}, "ai_listing_random_keywords": {"message": "Zufällige $amount$ Schlüsselwörter", "placeholders": {"amount": {"content": "$1"}}}, "ai_listing_random_selling": {"message": "Zufällige $amount$ Verkaufspunkte", "placeholders": {"amount": {"content": "$1"}}}, "ai_listing_randomize_keywords": {"message": "Aus der Wortbibliothek zufällig auswählen"}, "ai_listing_search_selling": {"message": "Nach Verkaufspunkt suchen"}, "ai_listing_select_product_categories": {"message": "Produktkategorien automatisch auswählen."}, "ai_listing_select_product_selling_points": {"message": "Produktverkaufspunkte automatisch auswählen"}, "ai_listing_select_word_library": {"message": "Wortbibliothek auswählen"}, "ai_listing_selling": {"message": "Verkaufspunkte"}, "ai_listing_selling_ask": {"message": "Welche weiteren Verkaufspunktanforderungen gibt es für den Titel?"}, "ai_listing_selling_optional": {"message": "Optionale Verkaufsargumente"}, "ai_listing_selling_repeat": {"message": "Punkte können nicht dupliziert werden"}, "ai_listing_set_excluded": {"message": "Als ausgeschlossene Wortbibliothek festlegen"}, "ai_listing_set_include_selling_points": {"message": "Verkaufspunkte einschließen"}, "ai_listing_set_included": {"message": "Als eingeschlossene Wortbibliothek festlegen"}, "ai_listing_set_selling_dictionary": {"message": "Als Verkaufspunktbibliothek festlegen"}, "ai_listing_standard_product_title": {"message": "Standard-Produkttitel"}, "ai_listing_translated_title": {"message": "Übersetzter Titel"}, "ai_listing_visit_chatGPT": {"message": "Besuchen Sie ChatGPT"}, "ai_listing_what_other_keywords": {"message": "<PERSON>e anderen Schlüsselwörter werden für den Titel benötigt?"}, "aliprice_coupons_apply_again": {"message": "Bewerben Sie sich erneut"}, "aliprice_coupons_apply_coupons": {"message": "Gutscheine anwenden"}, "aliprice_coupons_apply_success": {"message": "Coupon gefunden: <PERSON><PERSON> $amount$", "placeholders": {"amount": {"content": "$1"}}}, "aliprice_coupons_applying": {"message": "Testcodes für die besten Angebote..."}, "aliprice_coupons_applying_desc": {"message": "Auschecken: $code$", "placeholders": {"code": {"content": "$1"}}}, "aliprice_coupons_back_to_checkout": {"message": "<PERSON><PERSON> zur Kasse"}, "aliprice_coupons_found_coupons": {"message": "Wir haben Gutscheine im Wert von $amount$ gefunden", "placeholders": {"amount": {"content": "$1"}}}, "aliprice_coupons_found_coupons_desc": {"message": "Bereit zur Kasse? <PERSON>ellen wir sicher\nSie erhalten den besten Preis!"}, "aliprice_coupons_no_coupon_aviable": {"message": "Diese Codes haben nicht funktioniert.\nKein Problem – du bist es bereits\nden besten Preis bekommen."}, "aliprice_coupons_toolbar_btn": {"message": "Gutscheine erhalten"}, "aliww_translate": {"message": "Aliwangwang Chat-Übersetzer"}, "aliww_translate_supports": {"message": "Unterstützung: 1688 & Taobao"}, "amazon_extended_keywords_Keywords": {"message": "Stichwörter"}, "amazon_extended_keywords_copy_all": {"message": "Alle kopieren"}, "amazon_extended_keywords_more": {"message": "<PERSON><PERSON>"}, "an_lei_ji_xiao_liang_pai_xu": {"message": "Sortieren Sie nach kumulierten Verkäufen"}, "an_lei_xing_cha_kan": {"message": "<PERSON><PERSON>"}, "an_yue_dai_xiao_pai_xu": {"message": "Ranking nach Dropshipping-Verkäufen"}, "apra_btn__cat_name": {"message": "Bewertungsanalyse"}, "apra_chart__name": {"message": "Prozentsatz der Produktverkäufe nach Land"}, "apra_chart__update_at": {"message": "Aktualisierungszeit $time$", "placeholders": {"time": {"content": "$1"}}}, "apra_name": {"message": "Verkaufsstatistiken der Länder"}, "auto_opening": {"message": "Automatisches Öffnen in $num$ Sekunden", "placeholders": {"num": {"content": "$1"}}}, "auto_page_next_x_pages": {}, "average_days_listed": {"message": "Durchschnitt an Regaltagen"}, "average_hui_fu_lv": {"message": "Durchschnittliche Antwortrate"}, "average_ping_gong_ying_shang_deng_ji": {"message": "Durchschnittliches Lieferantenlevel"}, "average_price": {"message": "Durchschnittlicher Preis"}, "average_qi_ding_liang": {"message": "Durchschnittliche Mindestbestellmenge"}, "average_rating": {"message": "Durchschnittliche Bewertung"}, "average_ren_zheng_gong_ying_nian_chang": {"message": "Durchschnittliche Zertifizierungsdauer"}, "average_revenue": {"message": "Durchschnittsumsatz"}, "average_revenue_per_product": {"message": "Gesamtumsatz ÷ Anzahl der Produkte"}, "average_sales": {"message": "Durchschnittlicher Umsatz"}, "average_sales_per_product": {"message": "Gesamtumsatz ÷ Anzahl der Produkte"}, "bao_han": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "bao_zheng_jin": {"message": "Rand"}, "bian_ti_shu": {"message": "Variationen"}, "biao_ti": {"message": "Titel"}, "blacklist_add_blacklist": {"message": "Diesen Shop blockieren"}, "blacklist_address_incorrect": {"message": "Die Adresse ist falsch. Überprüfen Sie bitte das."}, "blacklist_blacked_out": {"message": "Der Shop wurde blockiert"}, "blacklist_blacklist": {"message": "Schwarze Liste"}, "blacklist_no_records_yet": {"message": "Noch kein Rekord!"}, "blue_rocket": {"message": "로켓배송"}, "brand_name": {"message": "<PERSON><PERSON>"}, "btn_aliprice_agent__daigou": {"message": "Kaufvermittler"}, "btn_aliprice_agent__dropshipping": {"message": "Direktversand"}, "btn_have_a_try": {"message": "Versuchen Sie es jetzt"}, "btn_refresh": {"message": "Aktualisierung"}, "btn_try_it_now": {"message": "Versuchen Sie es jetzt"}, "btn_txt_view_on_aliprice": {"message": "Ansicht auf AliPrice"}, "bu_bao_han": {"message": "Beinhaltet nicht"}, "bulk_copy_links": {"message": "Massenkopie<PERSON> von Links"}, "bulk_copy_products": {"message": "Massenkopien von Produkten"}, "cai_gou_zi_xun": {"message": "Kundendienst"}, "cai_gou_zi_xun__desc": {"message": "Drei-Minuten-Antwortrate des Verkäufers"}, "can_ping_lei_xing": {"message": "<PERSON><PERSON>"}, "cao_zuo": {"message": "<PERSON><PERSON><PERSON>"}, "chan_pin_ID": {"message": "Produkt ID"}, "chan_pin_e_wai_xin_xi": {"message": "Produkt zusätzliche Informationen"}, "chan_pin_lian_jie": {"message": "Produktlink"}, "cheng_li_shi_jian": {"message": "Gründungszeit"}, "city__aba": {"message": "Aba"}, "city__aksu": {"message": "<PERSON><PERSON><PERSON>"}, "city__alaer": {"message": "<PERSON><PERSON><PERSON>"}, "city__altai": {"message": "Altai"}, "city__alxaleague": {"message": "Alxa League"}, "city__ankang": {"message": "Ankan<PERSON>"}, "city__anqing": {"message": "Anqing"}, "city__anshan": {"message": "<PERSON><PERSON>"}, "city__anshun": {"message": "<PERSON><PERSON><PERSON>"}, "city__anyang": {"message": "Anyang"}, "city__baicheng": {"message": "Baicheng"}, "city__baise": {"message": "Baise"}, "city__baisha": {"message": "Baisha"}, "city__baishan": {"message": "Baishan"}, "city__baiyin": {"message": "<PERSON><PERSON><PERSON>"}, "city__baoding": {"message": "<PERSON>od<PERSON>"}, "city__baoji": {"message": "<PERSON><PERSON><PERSON>"}, "city__baoshan": {"message": "<PERSON><PERSON><PERSON>"}, "city__baoting": {"message": "Baoting"}, "city__baotou": {"message": "<PERSON><PERSON><PERSON>"}, "city__bayannur": {"message": "Bayannur"}, "city__bayinguoleng": {"message": "Bayinguoleng"}, "city__bazhong": {"message": "Bazhong"}, "city__beihai": {"message": "Beihai"}, "city__bengbu": {"message": "<PERSON><PERSON><PERSON>"}, "city__benxi": {"message": "Benxi"}, "city__bijie": {"message": "Bijie"}, "city__binzhou": {"message": "Binzhou"}, "city__bortala": {"message": "<PERSON><PERSON><PERSON>"}, "city__bozhou": {"message": "Bozhou"}, "city__cangzhou": {"message": "Cangzhou"}, "city__chamdo": {"message": "<PERSON><PERSON><PERSON>"}, "city__changchun": {"message": "<PERSON><PERSON><PERSON>"}, "city__changde": {"message": "<PERSON><PERSON>"}, "city__changhua": {"message": "Changhua"}, "city__changji": {"message": "Changji"}, "city__changjiang": {"message": "Changjiang"}, "city__changsha": {"message": "Changsha"}, "city__changzhi": {"message": "Changzhi"}, "city__changzhou": {"message": "Changzhou"}, "city__chaohu": {"message": "<PERSON><PERSON>"}, "city__chaoyang": {"message": "Chaoyang"}, "city__chaozhou": {"message": "Chaozhou"}, "city__chengde": {"message": "Chengde"}, "city__chengdu": {"message": "Chengdu"}, "city__chengmai": {"message": "<PERSON><PERSON><PERSON>"}, "city__chenzhou": {"message": "Chenzhou"}, "city__chiayi": {"message": "<PERSON><PERSON><PERSON>"}, "city__chifeng": {"message": "<PERSON><PERSON>"}, "city__chizhou": {"message": "Chizhou"}, "city__chongzuo": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__chuxiong": {"message": "Chuxiong"}, "city__chuzhou": {"message": "Chuzhou"}, "city__dali": {"message": "<PERSON><PERSON>"}, "city__dalian": {"message": "<PERSON><PERSON>"}, "city__dandong": {"message": "Dandong"}, "city__danzhou": {"message": "Danzhou"}, "city__daqing": {"message": "Daqing"}, "city__datong": {"message": "<PERSON><PERSON><PERSON>"}, "city__daxinganling": {"message": "<PERSON><PERSON>'<PERSON>ling"}, "city__dazhou": {"message": "Dazhou"}, "city__dehong": {"message": "<PERSON><PERSON>"}, "city__deyang": {"message": "Deyang"}, "city__dezhou": {"message": "Dezhou"}, "city__dingan": {"message": "Ding'an"}, "city__dingxi": {"message": "Dingxi"}, "city__diqing": {"message": "Diqing"}, "city__dongfang": {"message": "<PERSON><PERSON>g"}, "city__dongguan": {"message": "Dongguan"}, "city__dongying": {"message": "<PERSON><PERSON>"}, "city__enshi": {"message": "<PERSON><PERSON>"}, "city__ezhou": {"message": "Ezhou"}, "city__fangchenggang": {"message": "Fangchenggang"}, "city__foshan": {"message": "<PERSON><PERSON><PERSON>"}, "city__fushun": {"message": "<PERSON><PERSON><PERSON>"}, "city__fuxin": {"message": "Fuxin"}, "city__fuyang": {"message": "Fuyang"}, "city__fuzhou": {"message": "Fuzhou"}, "city__gannan": {"message": "<PERSON><PERSON><PERSON>"}, "city__ganzhou": {"message": "Ganzhou"}, "city__ganzi": {"message": "Ganzi"}, "city__guangan": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__guangyuan": {"message": "Guangyuan"}, "city__guangzhou": {"message": "Guangzhou"}, "city__guigang": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__guilin": {"message": "<PERSON><PERSON><PERSON>"}, "city__guiyang": {"message": "Guiyang"}, "city__guolok": {"message": "Guolok"}, "city__guyuan": {"message": "<PERSON><PERSON>"}, "city__haibei": {"message": "Haibei"}, "city__haidong": {"message": "Haidong"}, "city__haikou": {"message": "Hai<PERSON><PERSON>"}, "city__hainan": {"message": "Hainan"}, "city__haixi": {"message": "Haixi"}, "city__hami": {"message": "<PERSON><PERSON>"}, "city__handan": {"message": "<PERSON><PERSON>"}, "city__hangzhou": {"message": "Hangzhou"}, "city__hanzhong": {"message": "Hanzhong"}, "city__harbin": {"message": "<PERSON><PERSON><PERSON>"}, "city__hebi": {"message": "<PERSON><PERSON>"}, "city__hechi": {"message": "<PERSON><PERSON>"}, "city__hefei": {"message": "<PERSON><PERSON><PERSON>"}, "city__hegang": {"message": "<PERSON><PERSON><PERSON>"}, "city__heihe": {"message": "<PERSON><PERSON><PERSON>"}, "city__hengshui": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__hengyang": {"message": "Hengyang"}, "city__heyuan": {"message": "<PERSON><PERSON>"}, "city__heze": {"message": "<PERSON><PERSON>"}, "city__hezhou": {"message": "Hezhou"}, "city__hohhot": {"message": "Hohhot"}, "city__honghe": {"message": "<PERSON><PERSON>"}, "city__hongkongisland": {"message": "Hong Kong Island"}, "city__hotan": {"message": "<PERSON><PERSON>"}, "city__hsinchu": {"message": "<PERSON><PERSON><PERSON>"}, "city__huaian": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__huaibei": {"message": "<PERSON><PERSON><PERSON>"}, "city__huaihua": {"message": "Huaihua"}, "city__huaisouth": {"message": "Huai South"}, "city__hualien": {"message": "<PERSON><PERSON><PERSON>"}, "city__huanggang": {"message": "<PERSON><PERSON><PERSON>"}, "city__huangnan": {"message": "Huangnan"}, "city__huangshan": {"message": "<PERSON><PERSON>"}, "city__huangshi": {"message": "<PERSON><PERSON>"}, "city__huizhou": {"message": "Huizhou"}, "city__huludao": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__hulunbuir": {"message": "Hulunbuir"}, "city__huzhou": {"message": "Huzhou"}, "city__jiamusi": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__jian": {"message": "<PERSON><PERSON><PERSON>"}, "city__jiangmen": {"message": "Jiangmen"}, "city__jiaozuo": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__jiaxing": {"message": "Jiaxing"}, "city__jiayuguan": {"message": "Jiayuguan"}, "city__jieyang": {"message": "<PERSON><PERSON><PERSON>"}, "city__jilin": {"message": "<PERSON><PERSON>"}, "city__jinan": {"message": "<PERSON><PERSON>"}, "city__jinchang": {"message": "Jinchang"}, "city__jincheng": {"message": "Jincheng"}, "city__jingdezhen": {"message": "Jingdez<PERSON>"}, "city__jingmen": {"message": "Jingmen"}, "city__jingzhou": {"message": "Jingzhou"}, "city__jinhua": {"message": "Jinhua"}, "city__jining": {"message": "Jining"}, "city__jinzhong": {"message": "Jinzhong"}, "city__jinzhou": {"message": "Jinzhou"}, "city__jiujiang": {"message": "Jiujiang"}, "city__jiuquan": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__jixi": {"message": "Ji<PERSON>"}, "city__kaifeng": {"message": "Kaifeng"}, "city__kaohsiung": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__karamay": {"message": "<PERSON><PERSON><PERSON>"}, "city__kashgar": {"message": "Ka<PERSON><PERSON>"}, "city__keelung": {"message": "Keelung"}, "city__kinmen": {"message": "Kinmen"}, "city__kizilsu": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__kowloon": {"message": "Kowloon"}, "city__kunming": {"message": "Ku<PERSON><PERSON>"}, "city__laibin": {"message": "<PERSON><PERSON>"}, "city__laiwu": {"message": "<PERSON><PERSON>"}, "city__langfang": {"message": "<PERSON><PERSON><PERSON>"}, "city__lanzhou": {"message": "Lanzhou"}, "city__ledong": {"message": "<PERSON><PERSON>"}, "city__leshan": {"message": "<PERSON><PERSON>"}, "city__lhasa": {"message": "Lhasa"}, "city__liangshan": {"message": "Liangshan"}, "city__lianyungang": {"message": "Lianyungang"}, "city__liaocheng": {"message": "<PERSON><PERSON><PERSON>"}, "city__liaoyang": {"message": "<PERSON><PERSON><PERSON>"}, "city__liaoyuan": {"message": "<PERSON><PERSON><PERSON>"}, "city__lienchiang": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__lijiang": {"message": "Lijiang"}, "city__lincang": {"message": "Lincang"}, "city__linfen": {"message": "Linfen"}, "city__lingao": {"message": "Lingao"}, "city__lingshui": {"message": "<PERSON><PERSON><PERSON>"}, "city__linxia": {"message": "Lin<PERSON>"}, "city__linyi": {"message": "<PERSON><PERSON>"}, "city__lishui": {"message": "Li<PERSON><PERSON>"}, "city__liupanshui": {"message": "Liupanshu<PERSON>"}, "city__liuzhou": {"message": "Liuzhou"}, "city__longnan": {"message": "<PERSON><PERSON>"}, "city__longyan": {"message": "<PERSON><PERSON>"}, "city__loudi": {"message": "<PERSON><PERSON>"}, "city__luan": {"message": "<PERSON><PERSON><PERSON>"}, "city__luohe": {"message": "<PERSON><PERSON><PERSON>"}, "city__luoyang": {"message": "<PERSON><PERSON><PERSON>"}, "city__luzhou": {"message": "Luzhou"}, "city__lüliang": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__maanshan": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__macauoutlyingislands": {"message": "Macau Outlying Islands"}, "city__macaupeninsula": {"message": "Macau Peninsula"}, "city__maoming": {"message": "<PERSON><PERSON>"}, "city__meishan": {"message": "<PERSON><PERSON>"}, "city__meizhou": {"message": "Meizhou"}, "city__mianyang": {"message": "Mianyang"}, "city__miaoli": {"message": "<PERSON><PERSON>"}, "city__mudanjiang": {"message": "Mudanjiang"}, "city__nagqu": {"message": "<PERSON>g<PERSON>u"}, "city__nanchang": {"message": "Nanchang"}, "city__nanchong": {"message": "<PERSON><PERSON><PERSON>"}, "city__nanjing": {"message": "Nanjing"}, "city__nanning": {"message": "Nanning"}, "city__nanping": {"message": "<PERSON><PERSON>"}, "city__nantong": {"message": "Nantong"}, "city__nantou": {"message": "<PERSON><PERSON><PERSON>"}, "city__nanyang": {"message": "Nanyang"}, "city__neijiang": {"message": "Neijiang"}, "city__newterritories": {"message": "New Territories"}, "city__ngari": {"message": "<PERSON><PERSON>"}, "city__ningbo": {"message": "Ningbo"}, "city__ningde": {"message": "<PERSON><PERSON><PERSON>"}, "city__nujiang": {"message": "Nujiang"}, "city__nyingchi": {"message": "Nyingchi"}, "city__ordos": {"message": "Ordos"}, "city__panjin": {"message": "Panjin"}, "city__panzhihua": {"message": "Pan Zhihua"}, "city__penghu": {"message": "<PERSON><PERSON><PERSON>"}, "city__pingdingshan": {"message": "Pingdingshan"}, "city__pingliang": {"message": "<PERSON><PERSON><PERSON>"}, "city__pingtung": {"message": "<PERSON><PERSON><PERSON>"}, "city__pingxiang": {"message": "<PERSON><PERSON><PERSON>"}, "city__puer": {"message": "<PERSON>u'er"}, "city__putian": {"message": "<PERSON><PERSON>"}, "city__puyang": {"message": "P<PERSON><PERSON>"}, "city__qiandongnan": {"message": "Qiandongnan"}, "city__qianjiang": {"message": "Qianjiang"}, "city__qiannan": {"message": "<PERSON><PERSON><PERSON>"}, "city__qianxinan": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__qingdao": {"message": "Qingdao"}, "city__qingyang": {"message": "Qingyang"}, "city__qingyuan": {"message": "Qingyuan"}, "city__qinhuangdao": {"message": "Qinhuangdao"}, "city__qinzhou": {"message": "Qinzhou"}, "city__qionghai": {"message": "Qionghai"}, "city__qiongzhong": {"message": "Qiongzhong"}, "city__qiqihar": {"message": "Qiqihar"}, "city__qitaihe": {"message": "<PERSON><PERSON><PERSON>"}, "city__quanzhou": {"message": "Quanzhou"}, "city__qujing": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__quzhou": {"message": "Quzhou"}, "city__rizhao": {"message": "<PERSON><PERSON><PERSON>"}, "city__sanmenxia": {"message": "Sanmenxia"}, "city__sanming": {"message": "Sanming"}, "city__sanya": {"message": "Sanya"}, "city__shangluo": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__shangqiu": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__shangrao": {"message": "<PERSON><PERSON><PERSON>"}, "city__shannan": {"message": "Shannan"}, "city__shantou": {"message": "<PERSON><PERSON><PERSON>"}, "city__shanwei": {"message": "Shanwei"}, "city__shaoguan": {"message": "Shaoguan"}, "city__shaoxing": {"message": "Shaoxing"}, "city__shaoyang": {"message": "Shaoyang"}, "city__shennongjiaforestarea": {"message": "Shennongjia Forest Area"}, "city__shenyang": {"message": "Shenyang"}, "city__shenzhen": {"message": "Shenzhen"}, "city__shigatse": {"message": "Shigat<PERSON>"}, "city__shihezi": {"message": "<PERSON><PERSON><PERSON>"}, "city__shijiazhuang": {"message": "Shijiazhuang"}, "city__shiyan": {"message": "<PERSON><PERSON>"}, "city__shizuishan": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__shuangyashan": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__shuozhou": {"message": "Shuozhou"}, "city__siping": {"message": "<PERSON><PERSON>"}, "city__songyuan": {"message": "Songyuan"}, "city__suihua": {"message": "Su<PERSON>ua"}, "city__suining": {"message": "Suining"}, "city__suizhou": {"message": "<PERSON><PERSON><PERSON>"}, "city__suqian": {"message": "<PERSON><PERSON><PERSON>"}, "city__suzhou": {"message": "Suzhou"}, "city__tacheng": {"message": "Tacheng"}, "city__taian": {"message": "Tai'an"}, "city__taichung": {"message": "Taichung"}, "city__tainan": {"message": "Tainan"}, "city__taipei": {"message": "Taipei"}, "city__taitung": {"message": "<PERSON><PERSON><PERSON>"}, "city__taiyuan": {"message": "Taiyuan"}, "city__taizhou": {"message": "Taizhou"}, "city__tangshan": {"message": "Tangshan"}, "city__taoyuan": {"message": "Taoyuan"}, "city__tianmen": {"message": "Tianmen"}, "city__tianshui": {"message": "<PERSON><PERSON><PERSON>"}, "city__tieling": {"message": "Tieling"}, "city__tongchuan": {"message": "<PERSON><PERSON><PERSON>"}, "city__tonghua": {"message": "Tonghua"}, "city__tongliao": {"message": "<PERSON><PERSON><PERSON>"}, "city__tongling": {"message": "Tongling"}, "city__tongren": {"message": "<PERSON><PERSON>"}, "city__tumushuke": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__tunchang": {"message": "Tunchang"}, "city__turpan": {"message": "<PERSON><PERSON><PERSON>"}, "city__ulanqab": {"message": "Ulanqab"}, "city__urumqi": {"message": "Urumqi"}, "city__wanning": {"message": "Wanning"}, "city__weifang": {"message": "<PERSON><PERSON><PERSON>"}, "city__weihai": {"message": "Weihai"}, "city__weinan": {"message": "Weinan"}, "city__wenchang": {"message": "Wenchang"}, "city__wenshan": {"message": "<PERSON><PERSON>"}, "city__wenzhou": {"message": "Wenzhou"}, "city__wuhai": {"message": "Wu<PERSON>"}, "city__wuhan": {"message": "<PERSON><PERSON>"}, "city__wuhu": {"message": "<PERSON><PERSON>"}, "city__wujiaqu": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__wuwei": {"message": "<PERSON><PERSON>"}, "city__wuxi": {"message": "Wuxi"}, "city__wuzhishan": {"message": "Wuzhishan"}, "city__wuzhong": {"message": "Wuzhong"}, "city__wuzhou": {"message": "Wuzhou"}, "city__xiamen": {"message": "Xiamen"}, "city__xian": {"message": "Xi'<PERSON>"}, "city__xiangfan": {"message": "<PERSON><PERSON><PERSON>"}, "city__xiangtan": {"message": "Xiangtan"}, "city__xiangxi": {"message": "Xiangxi"}, "city__xianning": {"message": "Xianning"}, "city__xiantao": {"message": "Xiantao"}, "city__xianyang": {"message": "<PERSON><PERSON><PERSON>"}, "city__xiaogan": {"message": "<PERSON><PERSON>"}, "city__xilingol": {"message": "Xilingol"}, "city__xinganleague": {"message": "Xing'an League"}, "city__xingtai": {"message": "Xingtai"}, "city__xining": {"message": "Xining"}, "city__xinxiang": {"message": "Xinxiang"}, "city__xinyang": {"message": "Xinyang"}, "city__xinyu": {"message": "Xinyu"}, "city__xinzhou": {"message": "Xinzhou"}, "city__xishuangbanna": {"message": "Xishuangbanna"}, "city__xuancheng": {"message": "Xuancheng"}, "city__xuchang": {"message": "Xuchang"}, "city__xuzhou": {"message": "Xuzhou"}, "city__yaan": {"message": "Ya'an"}, "city__yanan": {"message": "Yan'<PERSON>"}, "city__yanbian": {"message": "Yanbian"}, "city__yancheng": {"message": "Yancheng"}, "city__yangjiang": {"message": "Yangjiang"}, "city__yangquan": {"message": "<PERSON><PERSON><PERSON>"}, "city__yangzhou": {"message": "Yangzhou"}, "city__yantai": {"message": "Yantai"}, "city__yibin": {"message": "<PERSON><PERSON>"}, "city__yichang": {"message": "Yichang"}, "city__yichun": {"message": "<PERSON><PERSON><PERSON>"}, "city__yilan": {"message": "Yilan"}, "city__yili": {"message": "<PERSON><PERSON>"}, "city__yinchuan": {"message": "<PERSON><PERSON><PERSON>"}, "city__yingkou": {"message": "<PERSON><PERSON><PERSON>"}, "city__yingtan": {"message": "Yingtan"}, "city__yiwu": {"message": "<PERSON><PERSON>"}, "city__yiyang": {"message": "Yiyang"}, "city__yongzhou": {"message": "Yongzhou"}, "city__yueyang": {"message": "<PERSON><PERSON><PERSON>"}, "city__yulin": {"message": "Yulin"}, "city__yuncheng": {"message": "Yuncheng"}, "city__yunfu": {"message": "<PERSON><PERSON>"}, "city__yunlin": {"message": "<PERSON><PERSON>"}, "city__yushu": {"message": "Yushu"}, "city__yuxi": {"message": "Yuxi"}, "city__zaozhuang": {"message": "Zaozhuang"}, "city__zhangjiajie": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__zhangjiakou": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "city__zhangye": {"message": "<PERSON><PERSON>"}, "city__zhangzhou": {"message": "Zhangzhou"}, "city__zhanjiang": {"message": "Zhanjiang"}, "city__zhaoqing": {"message": "Zhaoqing"}, "city__zhaotong": {"message": "Zhaotong"}, "city__zhengzhou": {"message": "Zhengzhou"}, "city__zhenjiang": {"message": "Zhenjiang"}, "city__zhongshan": {"message": "Zhongshan"}, "city__zhongwei": {"message": "Zhongwei"}, "city__zhoukou": {"message": "<PERSON><PERSON><PERSON>"}, "city__zhoushan": {"message": "Zhoushan"}, "city__zhuhai": {"message": "Zhu<PERSON>"}, "city__zhumadian": {"message": "Zhumadian"}, "city__zhuzhou": {"message": "Zhuzhou"}, "city__zibo": {"message": "Zibo"}, "city__zigong": {"message": "Zigong"}, "city__ziyang": {"message": "<PERSON><PERSON><PERSON>"}, "city__zunyi": {"message": "<PERSON><PERSON><PERSON>"}, "click_copy_product_info": {"message": "Klicken Sie auf „Produktinformationen kopieren“"}, "commmon_txt_expired": {"message": "Abgelaufen"}, "common__date_range_12m": {"message": "1 Jahr"}, "common__date_range_1m": {"message": "1 Monat"}, "common__date_range_1w": {"message": "1 Woche"}, "common__date_range_2w": {"message": "2 Wochen"}, "common__date_range_3m": {"message": "3 Monate"}, "common__date_range_3w": {"message": "3 Wochen"}, "common__date_range_6m": {"message": "6 Monate"}, "common_btn_cancel": {"message": "Abbrechen"}, "common_btn_close": {"message": "Schließen"}, "common_btn_save": {"message": "Speichern"}, "common_btn_setting": {"message": "Setup"}, "common_email": {"message": "Email"}, "common_error_msg_no_data": {"message": "<PERSON><PERSON>"}, "common_error_msg_no_result": {"message": "Leider kein Ergebnis gefunden."}, "common_favorites": {"message": "<PERSON><PERSON>"}, "common_feedback": {"message": "<PERSON><PERSON><PERSON>"}, "common_help": {"message": "<PERSON><PERSON><PERSON>"}, "common_loading": {"message": "Wird geladen"}, "common_login": {"message": "Anmelden"}, "common_logout": {"message": "Ausloggen"}, "common_no": {"message": "<PERSON><PERSON>"}, "common_powered_by_aliprice": {"message": "Unterstützt von AliPrice.com"}, "common_setting": {"message": "<PERSON><PERSON><PERSON>"}, "common_sign_up": {"message": "Einloggen"}, "common_system_upgrading_title": {"message": "Systemaktualisierung"}, "common_system_upgrading_txt": {"message": "Bitte versuchen Sie es später"}, "common_txt__currency": {"message": "Währung"}, "common_txt__video_tutorial": {"message": "Videoanleitung"}, "common_txt_ago_time": {"message": "vor $time$ Tagen", "placeholders": {"time": {"content": "$1"}}}, "common_txt_all_product": {"message": "alle"}, "common_txt_analysis": {"message": "Analyse"}, "common_txt_basically_used": {"message": "Fast nie benutzt"}, "common_txt_biaoti_link": {"message": "Titel + Link"}, "common_txt_biaoti_link_dian_pu": {"message": "Titel + Link + Shopname"}, "common_txt_blacklist": {"message": "Blockliste"}, "common_txt_cancel": {"message": "Stornieren"}, "common_txt_category": {"message": "<PERSON><PERSON><PERSON>"}, "common_txt_chakan": {"message": "Überprüfen"}, "common_txt_colors": {"message": "<PERSON><PERSON>"}, "common_txt_confirm": {"message": "Bestätigen Sie"}, "common_txt_copied": {"message": "<PERSON><PERSON><PERSON>"}, "common_txt_copy": {"message": "<PERSON><PERSON><PERSON>"}, "common_txt_copy_link": {"message": "<PERSON>"}, "common_txt_copy_title": {"message": "Titel kopieren"}, "common_txt_copy_title__link": {"message": "Titel und Link kopieren"}, "common_txt_day": {"message": "<PERSON><PERSON>"}, "common_txt_delete": {"message": "Löschen"}, "common_txt_dian_pu_link": {"message": "Shopnamen + Link kopieren"}, "common_txt_download": {"message": "herunt<PERSON><PERSON>n"}, "common_txt_downloaded": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "common_txt_export_as_csv": {"message": "Excel exportieren"}, "common_txt_export_as_txt": {"message": "Txt exportieren"}, "common_txt_fail": {"message": "Sc<PERSON>itern"}, "common_txt_format": {"message": "Format"}, "common_txt_get": {"message": "erhalten"}, "common_txt_incert_selection": {"message": "Auswahl umkehren"}, "common_txt_install": {"message": "Installieren"}, "common_txt_load_failed": {"message": "Laden fehlgeschlagen"}, "common_txt_month": {"message": "<PERSON><PERSON>"}, "common_txt_more": {"message": "<PERSON><PERSON>"}, "common_txt_new_unused": {"message": "Nagelneu, unbenutzt"}, "common_txt_next": {"message": "Nächster"}, "common_txt_no_limit": {"message": "Unbegrenzt"}, "common_txt_no_noticeable": {"message": "<PERSON><PERSON> sichtbaren Kratzer oder Schmutz"}, "common_txt_on_sale": {"message": "Verfügbar"}, "common_txt_opt_in_out": {"message": "An aus"}, "common_txt_order": {"message": "Auftrag"}, "common_txt_others": {"message": "<PERSON><PERSON>"}, "common_txt_overall_poor_condition": {"message": "Insgesamt schlechter Zustand"}, "common_txt_patterns": {"message": "Muster"}, "common_txt_platform": {"message": "Plattformen"}, "common_txt_please_select": {"message": "Bitte auswählen"}, "common_txt_prev": {"message": "Zurück"}, "common_txt_price": {"message": "Pre<PERSON>"}, "common_txt_privacy_policy": {"message": "Datenschutz-Bestimmungen"}, "common_txt_product_condition": {"message": "Produktstatus"}, "common_txt_rating": {"message": "Bewertung"}, "common_txt_ratings": {"message": "Bewertungen"}, "common_txt_reload": {"message": "Neu laden"}, "common_txt_reset": {"message": "Z<PERSON>ücksetzen"}, "common_txt_retail": {"message": "Einzelhandel"}, "common_txt_review": {"message": "Rezension"}, "common_txt_sale": {"message": "Verfügbar"}, "common_txt_same": {"message": "<PERSON><PERSON><PERSON>"}, "common_txt_scratches_and_dirt": {"message": "<PERSON><PERSON> Krat<PERSON>n und Schmutz"}, "common_txt_search_title": {"message": "Suchtitel"}, "common_txt_select_all": {"message": "Wählen Sie Alle"}, "common_txt_selected": {"message": "Ausgewählt"}, "common_txt_share": {"message": "Aktie"}, "common_txt_sold": {"message": "verkauft"}, "common_txt_sold_out": {"message": "Ausverkauft"}, "common_txt_some_scratches": {"message": "Einige Kratzer und Schmutz"}, "common_txt_sort_by": {"message": "Sortiere nach"}, "common_txt_state": {"message": "Status"}, "common_txt_success": {"message": "Erfolg"}, "common_txt_sys_err": {"message": "<PERSON><PERSON><PERSON>"}, "common_txt_today": {"message": "<PERSON><PERSON>"}, "common_txt_total": {"message": "alle"}, "common_txt_unselect_all": {"message": "Auswahl umkehren"}, "common_txt_upload_image": {"message": "Bild hochladen"}, "common_txt_visit": {"message": "Be<PERSON><PERSON>"}, "common_txt_whitelist": {"message": "Weiße Liste"}, "common_txt_wholesale": {"message": "G<PERSON>ßhandel"}, "common_txt_wildberries": {"message": "Wildberries"}, "common_txt_year": {"message": "<PERSON><PERSON><PERSON>"}, "common_yes": {"message": "<PERSON>a"}, "compare_tool_btn_clear_all": {"message": "Alles löschen"}, "compare_tool_btn_compare": {"message": "Vergleichen Sie"}, "compare_tool_btn_contact": {"message": "Kontakt"}, "compare_tool_tips_max_compared": {"message": "Addiere bis zu $maxComparedCount$", "placeholders": {"maxComparedCount": {"content": "$1"}}}, "configure_notifiactions": {"message": "Benachrichtigungen konfigurieren"}, "contact_us": {"message": "Kontaktiere uns"}, "context_menu_screenshot_search": {"message": "Capture to search by image"}, "context_menus_aliprice_search_by_image": {"message": "<PERSON>t AliPrice nach Bild suchen"}, "context_menus_goote_trans": {"message": "Seite übersetzen/Original anzeigen"}, "context_menus_search_by_image": {"message": "Suche nach Bild auf $storeName$", "placeholders": {"storeName": {"content": "$1"}}}, "context_menus_search_by_image_capture": {"message": "Aufnahme in $storeName$", "placeholders": {"storeName": {"content": "$1"}}}, "context_menus_translator": {"message": "Zum Übersetzen aufnehmen"}, "converter_modal_amount_placeholder": {"message": "<PERSON><PERSON><PERSON> hier den Betrag ein"}, "converter_modal_btn_convert": {"message": "Konvertieren"}, "converter_modal_exchange_rate_source": {"message": "Die Daten stammen aus dem Wechselkurs von $boc$ Aktualisierungszeit: $updatedAt$", "placeholders": {"boc": {"content": "$1"}, "updatedAt": {"content": "$2"}}}, "converter_modal_name": {"message": "Währungsumrechnung"}, "converter_modal_search_placeholder": {"message": "Währung suchen"}, "copy_all_contact_us_notice": {"message": "Diese Site wird derzeit nicht unterstützt. Bitte kontaktieren Sie uns"}, "copy_product_info": {"message": "Produktinformationen kopieren"}, "copy_suggest_search_kw": {"message": "Dropdown-<PERSON><PERSON> kop<PERSON>en"}, "country__han_gou": {"message": "Südkorea"}, "country__ri_ben": {"message": "Japan"}, "country__yue_nan": {"message": "Vietnam"}, "currency_convert__custom": {"message": "Kundenspezifischer Wechselkurs"}, "currency_convert__sync_server": {"message": "Server synchronisieren"}, "dang_ri_fa_huo": {"message": "Versand am selben Tag"}, "dao_chu_quan_dian_shang_pin": {"message": "Alle Shop-Produkte exportieren"}, "dao_chu_wei_CSV": {"message": "Export"}, "dao_chu_zi_duan": {"message": "Felder exportieren"}, "delivery_address": {"message": "Versandadresse"}, "delivery_company": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "di_zhi": {"message": "<PERSON><PERSON><PERSON>"}, "dian_ji_cha_xun": {"message": "<PERSON><PERSON><PERSON> hier, um eine Anfrage zu stellen"}, "dian_pu_ID": {"message": "Store-ID"}, "dian_pu_di_zhi": {"message": "Geschäftsadresse"}, "dian_pu_lian_jie": {"message": "Store-Link"}, "dian_pu_ming": {"message": "Shopname"}, "dian_pu_ming_cheng": {"message": "Geschäftsname"}, "dian_pu_shang_pin_zong_hsu": {"message": "Gesamtanzahl der Produkte im Shop: $num$", "placeholders": {"num": {"content": "$1"}}}, "dian_pu_xin_xi": {"message": "Information speichern"}, "ding_zai_zuo_ce": {"message": "nach links genagelt"}, "download_image__SKU_variant_images": {"message": "SKU-Variantenbilder"}, "download_image__assume": {"message": "Beispiel: Wir haben 2 Bilder, product1.jpg und product2.gif.\nimg_{$no$} wird umbenannt in img_01.jpg, img_02.gif;\n{$group$}_{$no$} wird umbenannt in main_image_01.jpg, main_image_02.gif;", "placeholders": {"no": {"content": "$3"}, "group": {"content": "$2"}}}, "download_image__batch_download": {"message": "Batch-Download"}, "download_image__combined_image": {"message": "Kombiniertes Produktdetailbild"}, "download_image__continue_downloading": {"message": "<PERSON><PERSON>"}, "download_image__description_images": {"message": "Beschreibungsbilder"}, "download_image__download_combined_image": {"message": "Kombiniertes Produktdetailbild herunterladen"}, "download_image__download_zip": {"message": "<PERSON> herunterladen"}, "download_image__enlarge_check": {"message": "Unterstützt nur JPEG-, JPG-, GIF- und PNG-Bilder, maximale Größe eines einzelnen Bildes: 1600 * 1600"}, "download_image__enlarge_image": {"message": "Bild vergrößern"}, "download_image__export": {"message": "Export"}, "download_image__height": {"message": "<PERSON><PERSON><PERSON>"}, "download_image__ignore_videos": {"message": "Das Video wurde ignoriert, da es nicht exportiert werden kann"}, "download_image__img_translate": {"message": "Bild übersetzen"}, "download_image__main_image": {"message": "Hauptbild"}, "download_image__multi_folder": {"message": "<PERSON><PERSON><PERSON>"}, "download_image__name": {"message": "Bild her<PERSON><PERSON><PERSON>n"}, "download_image__notice_content": {"message": "Bitte aktivieren Sie in den Download-Einstellungen Ihres Browsers nicht die Option „Vor dem Herunterladen fragen, wo jede Datei gespeichert werden soll“!!! Andernfalls werden viele Dialogfelder angezeigt."}, "download_image__notice_ignore": {"message": "Fordern Sie diese Nachricht nicht erneut an"}, "download_image__order_number": {"message": "{$no$} Seriennummer; {$group$} Gruppenname; {$date$} Zeitstempel", "placeholders": {"no": {"content": "$1"}, "group": {"content": "$2"}, "date": {"content": "$3"}}}, "download_image__overview": {"message": "Überblick"}, "download_image__prompt_download_zip": {"message": "<PERSON><PERSON> gibt zu viele Bilder, <PERSON>e sollten sie besser als Zip-Ordner herunterladen."}, "download_image__rename": {"message": "Umbenennen"}, "download_image__rule": {"message": "Benennungsregeln"}, "download_image__single_folder": {"message": "Einzelner Ordner"}, "download_image__sku_image": {"message": "SKU-Bilder"}, "download_image__video": {"message": "Video"}, "download_image__width": {"message": "Breite"}, "download_reviews__download_images": {"message": "Bewertungsbild herunterladen"}, "download_reviews__dropdown_title": {"message": "Bewertungsbild herunterladen"}, "download_reviews__export_csv": {"message": "CSV exportieren"}, "download_reviews__no_images": {"message": "Probeexemplar: 0 Bilder zum Download"}, "download_reviews__no_reviews": {"message": "Es sind keine Kommentare zum Download verfügbar!"}, "download_reviews__notice": {"message": "Spitze:"}, "download_reviews__notice__chrome_settings": {"message": "<PERSON><PERSON><PERSON> den Chrome-Browser so ein, dass er vor dem Herunterladen fragt, wo jede <PERSON>i gespeichert werden soll, und stellen Sie sie auf „Aus“."}, "download_reviews__notice__wait": {"message": "Je nach Anzahl der Bewertungen kann die Wartezeit länger sein"}, "download_reviews__pages_list__all": {"message": "Alle"}, "download_reviews__pages_list__page": {"message": "Vorherige $page$ Seiten", "placeholders": {"page": {"content": "$1"}}}, "download_reviews__pages_list_title": {"message": "Auswahlbereich"}, "export_shopping_cart__csv_filed__details_url": {"message": "Produktlink"}, "export_shopping_cart__csv_filed__details_url_with_sku": {"message": "Echo-SKU-Link"}, "export_shopping_cart__csv_filed__images": {"message": "Bildlink"}, "export_shopping_cart__csv_filed__quantity": {"message": "<PERSON><PERSON>"}, "export_shopping_cart__csv_filed__sale_price": {"message": "Pre<PERSON>"}, "export_shopping_cart__csv_filed__specs": {"message": "Spezifikationen"}, "export_shopping_cart__csv_filed__store_name": {"message": "Geschäftsname"}, "export_shopping_cart__csv_filed__store_url": {"message": "Shop-Link"}, "export_shopping_cart__csv_filed__title": {"message": "Produktname"}, "export_shopping_cart__export_btn": {"message": "Export"}, "export_shopping_cart__export_empty": {"message": "<PERSON>te wählen Si<PERSON> ein Produkt aus!"}, "fa_huo_shi_jian": {"message": "<PERSON>ers<PERSON>"}, "favorite_add_email": {"message": "E-Mail hinzufügen"}, "favorite_add_favorites": {"message": "<PERSON><PERSON> <PERSON><PERSON> hinzufügen"}, "favorite_added": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "favorite_btn_add": {"message": "Preisrückgang Alarm."}, "favorite_btn_notify": {"message": "Preis verfolgen"}, "favorite_cate_name_all": {"message": "Alle Produkte"}, "favorite_current_price": {"message": "Aktueller Preis"}, "favorite_due_date": {"message": "Fälligkeitsdatum"}, "favorite_enable_notification": {"message": "Bitte aktivieren Sie die E-Mail-Benachrichtigung"}, "favorite_expired": {"message": "Abgelaufen"}, "favorite_go_to_enable": {"message": "<PERSON><PERSON> zum Aktivieren"}, "favorite_msg_add_success": {"message": "<PERSON><PERSON> den <PERSON> hinzufügen"}, "favorite_msg_del_success": {"message": "<PERSON><PERSON><PERSON>"}, "favorite_msg_failure": {"message": "Ausfallen! Aktualisieren Seite und versuchen Sie es erneut."}, "favorite_please_add_email": {"message": "Bitte E-Mail hinzufügen"}, "favorite_price_drop": {"message": "<PERSON>ter"}, "favorite_price_rise": {"message": "Hoch"}, "favorite_price_untracked": {"message": "<PERSON><PERSON> nicht verfolgt"}, "favorite_saved_price": {"message": "Gespeicherter Preis"}, "favorite_stop_tracking": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> beenden"}, "favorite_sub_email_address": {"message": "E-Mail-Adresse für Abonnement"}, "favorite_tracking_period": {"message": "Trackingzeitraum"}, "favorite_tracking_prices": {"message": "Preise verfolgen"}, "favorite_verify_email": {"message": "E-Mail-Adresse bestätigen"}, "favorites_list_remove_prompt_msg": {"message": "Sind Si<PERSON> sicher, es zu l<PERSON>schen?"}, "favorites_update_button": {"message": "Jetzt Preise aktualisieren"}, "fen_lei": {"message": "<PERSON><PERSON><PERSON>"}, "fen_xia_yan_xuan": {"message": "Auswahl des Händlers"}, "find_similar": {"message": "Ähnliches finden"}, "first_ali_price_date": {"message": "Das Datum der ersten Erfassung durch den AliPrice-Crawler"}, "fooview_coupons_modal_no_data": {"message": "<PERSON><PERSON>"}, "fooview_coupons_modal_title": {"message": "Gutscheine"}, "fooview_favorites__product_sub__tooltip_l1": {"message": "Preis < $lowerPrice$", "placeholders": {"lowerPrice": {"content": "$1"}}}, "fooview_favorites__product_sub__tooltip_l2": {"message": "oder Preis > $higherPrice$", "placeholders": {"higherPrice": {"content": "$1"}}}, "fooview_favorites__product_sub__tooltip_l3": {"message": "<PERSON><PERSON><PERSON>"}, "fooview_favorites_error_msg_no_favorites": {"message": "Addieren Sie Lieblingsprodukte hier, um Preisfallwarnung zu erhalten."}, "fooview_favorites_filter_latest": {"message": "Spätestens"}, "fooview_favorites_filter_price_drop": {"message": "NIEDER"}, "fooview_favorites_filter_price_up": {"message": "HOCH"}, "fooview_favorites_modal_title": {"message": "<PERSON><PERSON>"}, "fooview_favorites_modal_title_title": {"message": "<PERSON><PERSON><PERSON> zu AliPrice <PERSON>"}, "fooview_favorites_track_price": {"message": "Um den Preis zu verfolgen"}, "fooview_price_history_app_price": {"message": "APP Preis :"}, "fooview_price_history_title": {"message": "Preis-Tracking"}, "fooview_product_list_feedback": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "fooview_product_list_orders": {"message": "Auftrag"}, "fooview_product_list_price": {"message": "Pre<PERSON>"}, "fooview_reviews_error_msg_no_review": {"message": "Wir fanden keine Bewertung zu diesem Produkt."}, "fooview_reviews_filter_buyer_reviews": {"message": "Käufer fotos"}, "fooview_reviews_modal_title": {"message": "Bewertungen"}, "fooview_same_product_choose_category": {"message": "Kategorie auswählen"}, "fooview_same_product_filter_feedback": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "fooview_same_product_filter_orders": {"message": "Auftrag"}, "fooview_same_product_filter_price": {"message": "Pre<PERSON>"}, "fooview_same_product_filter_rating": {"message": "Bewertung"}, "fooview_same_product_modal_title": {"message": "Finden Sie das gleiche Produkt"}, "fooview_same_product_search_by_image": {"message": "Suchen nach Bilder"}, "fooview_seller_analysis_modal_title": {"message": "Verkäufer Analyse"}, "for_12_months": {"message": "1 Jahr lang"}, "for_12_months_list_pro": {"message": "12 Monate"}, "for_12_months_nei": {"message": "Innerhalb von 12 Monaten"}, "for_1_months": {"message": "1 Monat"}, "for_1_months_nei": {"message": "Innerhalb von 1 Monat"}, "for_3_months": {"message": "Für 3 Monate"}, "for_3_months_nei": {"message": "Innerhalb von 3 Monaten"}, "for_6_months": {"message": "Für 6 Monate"}, "for_6_months_nei": {"message": "Innerhalb von 6 Monaten"}, "for_9_months": {"message": "9 Monate"}, "for_9_months_nei": {"message": "Innerhalb von 9 Monaten"}, "fu_gou_lv": {"message": "Wiederkaufrate"}, "gao_liang_bu_tong_dian": {"message": "Unterschiede hervorheben"}, "gao_liang_guang_gao_chan_pin": {"message": "Werbeprodukte hervor<PERSON>ben"}, "geng_duo_xin_xi": {"message": "Mehr Info"}, "geng_xin_shi_jian": {"message": "Letztes Update"}, "get_store_products_fail_tip": {"message": "Klicken Sie auf „OK“, um zur Überprüfung zu gelangen und den normalen Zugriff sicherzustellen."}, "gong_x_kuan_shang_pin": {"message": "Insgesamt $amount$ Produkte", "placeholders": {"amount": {"content": "$1"}}}, "gong_ying_shang": {"message": "<PERSON><PERSON><PERSON>"}, "gong_ying_shang_ID": {"message": "Lieferanten ID"}, "gong_ying_shang_deng_ji": {"message": "Lieferantenbewertung"}, "gong_ying_shang_nian_zhan": {"message": "Lieferant ist älter"}, "gong_ying_shang_xin_xi": {"message": "Lieferanteninformationen"}, "gong_ying_shang_zhu_ye_lian_jie": {"message": "Link zur Lieferanten-Homepage"}, "green_rocket": {"message": "로켓프레시"}, "grey_rocket": {"message": "로켓설치"}, "gu_ji_shou_jia": {"message": "Geschätzter Verkaufspreis"}, "guan_jian_zi": {"message": "Stichwort"}, "guang_gao_chan_pin": {"message": "Anzeigenprodukte"}, "guang_gao_zhan_bi": {"message": "Anzeigenverhältnis"}, "guo_ji_wu_liu_yun_fei": {"message": "Internationale Versandkosten"}, "guo_lv_tiao_jian": {"message": "Filter"}, "hao_ping_lv": {"message": "Positive Bewertung"}, "highest_price": {"message": "Hoch"}, "historical_trend": {"message": "Historischer Trend"}, "how_to_screenshot": {"message": "Halten Sie die linke Maustaste gedrückt, um den Bereich auszuwählen, und tippen Sie auf die rechte Maustaste oder die Esc-Taste, um den Screenshot zu verlassen"}, "howt_it_works": {"message": "Wie es funktioniert"}, "hui_fu_lv": {"message": "<PERSON><PERSON><PERSON><PERSON>q<PERSON><PERSON>"}, "hui_tou_lv": {"message": "<PERSON><PERSON><PERSON><PERSON>q<PERSON><PERSON>"}, "inquire_freightFee": {"message": "Frachtanfrage"}, "inquire_freightFee_Yuan": {"message": "Fracht/Yuan"}, "inquire_freightFee_province": {"message": "<PERSON><PERSON><PERSON>"}, "inquire_freightFee_the": {"message": "Die Fracht beträgt $num$, was bedeutet, dass die Region versandkostenfrei ist.", "placeholders": {"num": {"content": "$1"}}}, "is_ad": {"message": "Anzeige."}, "jia_ge": {"message": "Pre<PERSON>"}, "jia_ge_dan_wei": {"message": "Einheit"}, "jia_ge_qu_shi": {"message": "Trends"}, "jia_zai_n_ge_shang_pin": {"message": "$num$ Produkte laden", "placeholders": {"num": {"content": "$1"}}}, "jin_30_tian_xiao_liang_zhan_bi": {"message": "Prozentsatz des Verkaufsvolumens in den letzten 30 Tagen"}, "jin_30_tian_xiao_shou_e_zhan_bi": {"message": "Prozentsatz des Umsatzes in den letzten 30 Tagen"}, "jin_90_tian_mai_jia_shu": {"message": "Käufer in den letzten 90 Tagen"}, "jin_90_tian_xiao_shou_liang": {"message": "Verkäufe in den letzten 90 Tagen"}, "jing_xuan_huo_yuan": {"message": "Ausgewählte Quellen"}, "jing_ying_mo_shi": {"message": "Geschäftsmodell"}, "jiu_fen_jie_jue": {"message": "Streitbeilegung"}, "jiu_fen_jie_jue__desc": {"message": "Abrech<PERSON>ng von Streitigkeiten über Ladenrechte von Verkäufern"}, "jiu_fen_lv": {"message": "Streitquote"}, "jiu_fen_lv__desc": {"message": "Anteil der Bestellungen, bei denen in den letzten 30 Tagen Reklamationen abgeschlossen wurden und die in der Verantwortung des Verkäufers oder beider Parteien liegen"}, "kai_dian_ri_qi": {"message": "Eröffnungsdatum"}, "keywords": {"message": "Schlüsselwörter"}, "kua_jin_Select_pan_huo": {"message": "Grenzüberschreitende Auswahl"}, "last15_days": {"message": "Letzte 15 Tage"}, "last180_days": {"message": "Letzte 180 Tage"}, "last30_days": {"message": "In den letzten 30 Tagen"}, "last360_days": {"message": "Letzte 360 ​​Tage"}, "last45_days": {"message": "Letzte 45 Tage"}, "last60_days": {"message": "Letzte 60 Tage"}, "last7_days": {"message": "Letzte 7 Tage"}, "last90_days": {"message": "Letzte 90 Tage"}, "last_30d_sales": {"message": "Angebote der letzten 30 Tage"}, "lei_ji": {"message": "Kumulativ"}, "lei_ji_xiao_liang": {"message": "Gesamt"}, "lei_ji_xiao_liang__desc": {"message": "Alle Verkäufe nach dem Produkt im Regal"}, "lei_ji_xiao_liang_pai_xu__desc": {"message": "Kumuliertes Verkaufsvolumen der letzten 30 Tage, sortiert von hoch nach niedrig"}, "lian_xi_fang_shi": {"message": "Kontaktinformationen"}, "list_time": {"message": "Auf <PERSON>"}, "load_more": {"message": "<PERSON><PERSON> <PERSON>"}, "login_to_aliprice": {"message": "Melden Sie sich bei AliPrice an"}, "long_link": {"message": "<PERSON><PERSON>"}, "lowest_price": {"message": "<PERSON><PERSON><PERSON>"}, "mai_jia_shu": {"message": "Verkäufer"}, "mao_li_lv": {"message": "Bruttomarge"}, "mobile_view__dkxbqy": {"message": "Neuen Tab <PERSON>"}, "mobile_view__sjdxq": {"message": "Details in der App"}, "mobile_view__sjdxqy": {"message": "Detailseite in der App"}, "mobile_view__smck": {"message": "Zum Anzeigen scannen"}, "mobile_view__smckms": {"message": "Bitte verwenden Sie zum Scannen und Anzeigen die Kamera oder die App"}, "modified_failed": {"message": "Die Änderung ist fehlgeschlagen"}, "modified_successfully": {"message": "Erfolgreich geändert"}, "nav_btn_favorites": {"message": "<PERSON><PERSON>"}, "nav_btn_package": {"message": "<PERSON><PERSON>"}, "nav_btn_product_info": {"message": "Über das Produkt"}, "nav_btn_viewed": {"message": "<PERSON><PERSON><PERSON>"}, "no_suggest_search_kw": {"message": "Loading..."}, "none": {"message": "<PERSON><PERSON>"}, "normal_link": {"message": "Normaler Link"}, "notice": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "number_reviews": {"message": "Bewertungen"}, "only_show_num": {"message": "Produkte gesamt: $allnum$, Versteckt: $hidenum2$", "placeholders": {"allnum": {"content": "$1"}, "hidenum2": {"content": "$2"}}}, "only_show_selected": {"message": "Entfernen deaktiviert"}, "open": {"message": "<PERSON><PERSON><PERSON>"}, "open_links": {"message": "<PERSON><PERSON>"}, "options_page_tab_check_links": {"message": "Überprüfen Sie die Links"}, "options_page_tab_gernal": {"message": "Allgemeines"}, "options_page_tab_notifications": {"message": "Benachrichtigungen"}, "options_page_tab_others": {"message": "<PERSON><PERSON>"}, "options_page_tab_sbi": {"message": "Suchen nach Bilder"}, "options_page_tab_shortcuts": {"message": "Verknüpfungen"}, "options_page_tab_shortcuts_title": {"message": "Schriftgröße für Verknüpfungen"}, "options_page_tab_similar_products": {"message": "<PERSON><PERSON><PERSON>"}, "orange_rocket": {"message": "판매자로켓"}, "order_list_open_links_tip": {"message": "Mehrere Produktlinks werden gerade geöffnet"}, "order_list_sku_show_title": {"message": "Ausgewählte Varianten in den freigegebenen Links anzeigen"}, "orders_last30_days": {"message": "<PERSON>za<PERSON> der Bestellungen in den letzten 30 Tagen"}, "pTutorial_favorites_block1_desc1": {"message": "Die Produkte, die Si<PERSON> verfolgt haben, sind hier aufgelistet"}, "pTutorial_favorites_block1_title": {"message": "<PERSON><PERSON>"}, "pTutorial_popup_block1_desc1": {"message": "Ein grünes Etikett bedeutet, dass die Preise für Produkte gesunken sind"}, "pTutorial_popup_block1_title": {"message": "Verknüpfungen und Favoriten"}, "pTutorial_price_history_block1_desc1": {"message": "<PERSON>lick<PERSON> Sie auf \"Preis verfolgen\", um Produkte zu Ihren Favoriten hinzuzufügen. <PERSON><PERSON><PERSON> ihre Preise fallen, erhalten Sie Benachrichtigungen"}, "pTutorial_price_history_block1_title": {"message": "Preis verfolgen"}, "pTutorial_reviews_block1_desc1": {"message": "Käufer Bewertungen von Itao und echte Fotos aus dem AliExpress Feedback"}, "pTutorial_reviews_block1_title": {"message": "Bewertungen"}, "pTutorial_reviews_block2_desc1": {"message": "<PERSON>s ist immer hilfreich, die Bewertungen anderer Käufer zu überprüfen"}, "pTutorial_same_products_block1_desc1": {"message": "Sie können sie vergleichen, um die beste Wahl zu treffen"}, "pTutorial_same_products_block1_desc2": {"message": "<PERSON>lick<PERSON> Sie auf \"Mehr\", um nach Bild zu suchen."}, "pTutorial_same_products_block1_title": {"message": "Gleiche Produkte"}, "pTutorial_same_products_by_image_search_block1_desc1": {"message": "Legen Sie das Produktbild dort ab und wählen Sie eine Kategorie aus"}, "pTutorial_same_products_by_image_search_block1_title": {"message": "Suche nach Bild"}, "pTutorial_seller_analysis_block1_desc1": {"message": "Positive Feedbackrate des Verkäufers, Bewertungspunkte und wie lange der Verkäufer auf dem Markt ist"}, "pTutorial_seller_analysis_block1_title": {"message": "Verkäufer-Bewertung"}, "pTutorial_seller_analysis_block2_desc2": {"message": "Verkäuferbewertung basiert auf 3 Indizes: Artikel wie beschrieben, Kommunikation Versandgeschwindigkeit"}, "pTutorial_seller_analysis_block3_desc3": {"message": "Wir verwenden 3 Farben und Symbole, um das Vertrauensniveau der Verkäufer anzuzeigen"}, "page_count": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "pai_chu": {"message": "Ausgeschlossen"}, "pai_chu_HK_bu_yi_xia_xiaoshou": {"message": "Hongkong-Einschränkungen ausschließen"}, "pai_chu_JP_bu_yi_xia_xiaoshou": {"message": "Japan-Einschränkungen ausschließen"}, "pai_chu_KR_bu_yi_xia_xiaoshou": {"message": "Korea-Einschränkungen ausschließen"}, "pai_chu_KZ_bu_yi_xia_xiaoshou": {"message": "Kasachstan-Einschränkungen ausschließen"}, "pai_chu_MO_bu_yi_xia_xiaoshou": {"message": "Macau-Einschränkungen ausschließen"}, "pai_chu_RU_bu_yi_xia_xiaoshou": {"message": "Osteuropa-Einschränkungen ausschließen"}, "pai_chu_SA_bu_yi_xia_xiaoshou": {"message": "Saudi-Arabien-Einschränkungen ausschließen"}, "pai_chu_TW_bu_yi_xia_xiaoshou": {"message": "Taiwan-Einschränkungen ausschließen"}, "pai_chu_USA_bu_yi_xia_xiaoshou": {"message": "USA-Einschränkungen ausschließen"}, "pai_chu_VN_bu_yi_xia_xiaoshou": {"message": "Vietnam-Einschränkungen ausschließen"}, "pai_chu_bu_yi_xia_xiaoshou": {"message": "Eingeschränkte Artikel ausschließen"}, "payable_price_formula": {"message": "Preis + Versand + <PERSON><PERSON><PERSON>"}, "pdd_check_retail_btn_txt": {"message": "<PERSON>"}, "pdd_pifa_to_retail_btn_txt": {"message": "<PERSON><PERSON> kaufen"}, "pdp_copy_fail": {"message": "Kopieren fehlgeschlagen!"}, "pdp_copy_success": {"message": "<PERSON><PERSON><PERSON> erfolgreich!"}, "pdp_share_modal_subtitle": {"message": "Screenshot teilen, er/sie wird Ihre Auswahl sehen."}, "pdp_share_modal_title": {"message": "<PERSON><PERSON> dei<PERSON>"}, "pdp_share_screenshot": {"message": "Screenshot teilen"}, "pei_song": {"message": "Versandmethode"}, "pin_lei": {"message": "<PERSON><PERSON><PERSON>"}, "pin_zhi_ti_yan": {"message": "Produktqualität"}, "pin_zhi_ti_yan__desc": {"message": "Hochwertige Rückerstattungsrate des Verkäufergeschäfts"}, "pin_zhi_tui_kuan_lv": {"message": "Rückerstattungssatz"}, "pin_zhi_tui_kuan_lv__desc": {"message": "Anteil der Bestellungen, die in den letzten 30 Tagen nur erstattet und zurückgegeben wurden"}, "ping_fen": {"message": "Bewertung"}, "ping_jun_fa_huo_su_du": {"message": "Durchschnittliche Versandgeschwindigkeit"}, "pkgInfo_hide": {"message": "Logistikinfo: ein/aus"}, "pkgInfo_no_trace": {"message": "<PERSON><PERSON>"}, "platform_name__1688": {"message": "1688"}, "platform_name__17zwd": {"message": "17zwd.com"}, "platform_name__51taoyang": {"message": "51taoyang.com"}, "platform_name__5ts": {"message": "5ts.com"}, "platform_name__91jf": {"message": "91jf.com"}, "platform_name__alibaba": {"message": "Alibaba.com"}, "platform_name__aliexpress": {"message": "AliExpress"}, "platform_name__amazon": {"message": "Amazon"}, "platform_name__bao66": {"message": "bao66.cn"}, "platform_name__chinagoods": {"message": "chinagoods.com"}, "platform_name__dhgate": {"message": "DHgate"}, "platform_name__e3hui": {"message": "e3hui.com"}, "platform_name__ebay": {"message": "Ebay"}, "platform_name__hznzcn": {"message": "hznzcn.com"}, "platform_name__jd": {"message": "JD"}, "platform_name__juyi5": {"message": "juyi5.cn"}, "platform_name__naver_shopping": {"message": "Naver Shopping"}, "platform_name__pinduoduo": {"message": "<PERSON><PERSON><PERSON>"}, "platform_name__pinduoduo_pifa": {"message": "pin<PERSON><PERSON>"}, "platform_name__shopee": {"message": "Einkaufen"}, "platform_name__sjxzw": {"message": "571xz.com"}, "platform_name__sooxie": {"message": "sooxie.com"}, "platform_name__taobao": {"message": "Taobao"}, "platform_name__taobao_lite": {"message": "Taobao Lite"}, "platform_name__vvic": {"message": "vvic.com"}, "platform_name__walmart": {"message": "Walmart"}, "platform_name__wsy": {"message": "wsy.com"}, "platform_name__xingfujie": {"message": "xingfujie.cn"}, "platform_name__yiwugo": {"message": "Yiwugo.com"}, "platform_name__yunchepin": {"message": "yunchepin.cn"}, "platform_name__zhaojiafang": {"message": "zhaojiafang.com"}, "popup_go_to_home": {"message": "homepage"}, "popup_go_to_platform": {"message": "zum $name$", "placeholders": {"name": {"content": "$1"}}}, "popup_search_placeholder": {"message": "Ich kaufe jetzt..."}, "popup_track_package_btn_track": {"message": "Spur"}, "popup_track_package_desc": {"message": "ALL-IN-ONE Paketverfolgung"}, "popup_track_package_search_placeholder": {"message": "Tracking-<PERSON><PERSON><PERSON>"}, "popup_translate_search_placeholder": {"message": "Übersetzen und suchen Sie auf $searchOn$", "placeholders": {"searchOn": {"content": "$1"}}}, "price_history": {"message": "Preis-Tracking"}, "price_history_chart_tip_ae": {"message": "Tipp: Die Anzahl der Bestellungen ist die kumulierte Anzahl der Bestellungen seit dem Start"}, "price_history_chart_tip_coupang": {"message": "Tipp: Coupang löscht den Bestellzähler betrügerischer Bestellungen"}, "price_history_inm_1688_l1": {"message": "Bitte installiere"}, "price_history_inm_1688_l2": {"message": "<PERSON><PERSON><PERSON>ufsassistent für 1688"}, "price_history_panel_lowest_price": {"message": "Der niedrigste Preis: "}, "price_history_panel_tab_price_tracking": {"message": "Preis-Tracking"}, "price_history_panel_tab_seller_analysis": {"message": "Verkäufer Analyse"}, "price_history_pro_modal_title": {"message": "Preishistorie & Bestellhistorie"}, "privacy_consent__btn_agree": {"message": "Überprüfung der Einwilligung zur Datenerfassung"}, "privacy_consent__btn_disable_all": {"message": "<PERSON><PERSON> a<PERSON><PERSON>"}, "privacy_consent__btn_enable_all": {"message": "Alles aktivieren"}, "privacy_consent__btn_uninstall": {"message": "Entfernen"}, "privacy_consent__desc_privacy": {"message": "<PERSON><PERSON>, dass ohne Daten oder Cookies einige Funktionen deaktiviert sind, da für diese Funktionen die Erklärung von Daten oder Cookies erforderlich ist. Sie können jedoch auch die anderen Funktionen verwenden."}, "privacy_consent__desc_privacy_L1": {"message": "Leider funktioniert es ohne Daten oder Cookies nicht, da wir die Erklärung von Daten oder Cookies benötigen."}, "privacy_consent__desc_privacy_L2": {"message": "<PERSON>n Si<PERSON> uns nicht erlauben, diese Information<PERSON> zu sammeln, entfernen <PERSON> sie bitte."}, "privacy_consent__item_cookies_desc": {"message": "<PERSON><PERSON>, wir erhalten Ihre Währungsdaten nur beim Online-Einkauf in Cookies, um den Preisverlauf anzuzeigen."}, "privacy_consent__item_cookies_title": {"message": "Erforderliche Cookies"}, "privacy_consent__item_functional_desc_L1": {"message": "1. <PERSON>ügen Sie im Browser Cookies hinzu, um Ihren Computer oder Ihr Gerät anonym zu identifizieren."}, "privacy_consent__item_functional_desc_L2": {"message": "2. <PERSON><PERSON><PERSON> Sie im Add-On Funktionsdaten hinzu, um mit der Funktion zu arbeiten."}, "privacy_consent__item_functional_title": {"message": "Funktions- und Analyse-Cookies"}, "privacy_consent__more_desc": {"message": "<PERSON>te beach<PERSON> Si<PERSON>, dass wir Ihre persönlichen Daten nicht an andere Unternehmen weitergeben und keine Werbefirmen Daten über unseren Service sammeln."}, "privacy_consent__options__btn__desc": {"message": "Um alle Funktionen nutzen zu könne<PERSON>, müssen Sie sie aktivieren."}, "privacy_consent__options__btn__label": {"message": "Mach es an"}, "privacy_consent__options__desc_L1": {"message": "Wir erfassen folgende Daten, die Sie persönlich identifizieren:"}, "privacy_consent__options__desc_L2": {"message": "- Cookies, wir erhalten Ihre Währungsdaten nur dann in Cookies, wenn Sie online einkaufen, um den Preisverlauf anzuzeigen."}, "privacy_consent__options__desc_L3": {"message": "- und fügen Sie Cookies im Browser hinzu, um Ihren computer oder Ihr Gerät anonym zu identifizieren."}, "privacy_consent__options__desc_L4": {"message": "- Andere anonyme Daten machen diese Erweiterung bequemer."}, "privacy_consent__options__desc_L5": {"message": "<PERSON>te <PERSON> Si<PERSON>, dass wir Ihre persönlichen Daten nicht mit anderen Unternehmen teilen und keine Werbefirmen Daten über unseren Service sammeln."}, "privacy_consent__privacy_preferences": {"message": "Datenschutzeinstellungen"}, "privacy_consent__read_more": {"message": "<PERSON><PERSON> mehr >>"}, "privacy_consent__title_privacy": {"message": "Privatsphäre"}, "product_info": {"message": "Produktinfo"}, "product_recommend__name": {"message": "<PERSON><PERSON><PERSON>"}, "product_research": {"message": "Produktforschung"}, "product_sub__email_desc": {"message": "Preisbenachrichtigungs-E-Mail"}, "product_sub__email_edit": {"message": "bearbeiten"}, "product_sub__email_not_verified": {"message": "Bitte E-Mail bestätigen"}, "product_sub__email_required": {"message": "Bitte E-Mail angeben"}, "product_sub__form_countdown": {"message": "Automatisches Schließen nach $seconds$ Sekunden", "placeholders": {"seconds": {"content": "$1"}}}, "product_sub__form_fail": {"message": "Erinnerung konnte nicht hinzugefügt werden!"}, "product_sub__form_input_price": {"message": "Eingangspreis"}, "product_sub__form_item_country": {"message": "Nation"}, "product_sub__form_item_current_price": {"message": "Derzeitiger <PERSON>"}, "product_sub__form_item_duration": {"message": "Spur"}, "product_sub__form_item_higher_price": {"message": "Oder Preis>"}, "product_sub__form_item_invalid_higher_price": {"message": "Der Preis muss größer als $price$ . sein", "placeholders": {"price": {"content": "$1"}}}, "product_sub__form_item_invalid_lower_price": {"message": "Der Preis muss niedriger sein als $price$", "placeholders": {"price": {"content": "$1"}}}, "product_sub__form_item_lower_price": {"message": "<PERSON><PERSON> <"}, "product_sub__form_submit": {"message": "e<PERSON>re<PERSON>n"}, "product_sub__form_success": {"message": "Das Hinzufügen einer Erinnerung ist gelungen!"}, "product_sub__high_price_notify": {"message": "Benachrichtigen Sie mich über Preiserhöhungen"}, "product_sub__low_price_notify": {"message": "Benachrichtigen Sie mich über Preissenkungen"}, "product_sub__modal_title": {"message": "Erinnerung an Abonnementpreisänderung"}, "province__an_hui": {"message": "<PERSON><PERSON>"}, "province__ao_men": {"message": "Macau"}, "province__bei_jing": {"message": "Beijing"}, "province__chong_qing": {"message": "Chongqing"}, "province__fu_jian": {"message": "Fujian"}, "province__gan_su": {"message": "Gansu"}, "province__guang_dong": {"message": "Guangdong"}, "province__guang_xi": {"message": "Guangxi"}, "province__gui_zhou": {"message": "Guizhou"}, "province__hai_nan": {"message": "Hainan"}, "province__he_bei": {"message": "Hebei"}, "province__he_nan": {"message": "<PERSON><PERSON>"}, "province__hei_long_jiang": {"message": "Heilongjiang"}, "province__hu_bei": {"message": "Hubei"}, "province__hu_nan": {"message": "Hunan"}, "province__ji_lin": {"message": "<PERSON><PERSON>"}, "province__jiang_su": {"message": "Jiangsu"}, "province__jiang_xi": {"message": "Jiangxi"}, "province__liao_ning": {"message": "Liaoning"}, "province__nei_meng_gu": {"message": "Inner Mongolia"}, "province__ning_xia": {"message": "Ningxia"}, "province__qing_hai": {"message": "Qinghai"}, "province__shan_dong": {"message": "Shandong"}, "province__shan_xi": {"message": "Shaanxi"}, "province__shang_hai": {"message": "Shanghai"}, "province__si_chuan": {"message": "Sichuan"}, "province__tai_wan": {"message": "Taiwan"}, "province__tian_jin": {"message": "Tianjin"}, "province__xi_zhang": {"message": "Tibet"}, "province__xiang_gang": {"message": "Hong Kong"}, "province__xin_jiang": {"message": "Xinjiang"}, "province__yun_nan": {"message": "Yunnan"}, "province__zhe_jiang": {"message": "Zhejiang"}, "purple_rocket": {"message": "로켓직구"}, "qi_ding_liang": {"message": "Mindestbestellmenge"}, "qi_ding_liang_qi_ding_jia": {"message": "MOQ & MOP"}, "qi_ye_mian_ji": {"message": "Unternehmensbereich"}, "qing_zhi_shao_xuan_ze_yi_ge_chan_pin": {"message": "Bitte wählen Sie mindestens ein Produkt aus"}, "qing_zhi_shao_xuan_ze_yi_ge_zi_duan": {"message": "Bitte wählen Sie mindestens ein Feld aus"}, "qu_deng_lu": {"message": "Anmelden"}, "quan_guo_yan_xuan": {"message": "Globale Auswahl"}, "recommendation_popup_banner_btn_install": {"message": "Es installieren"}, "recommendation_popup_banner_desc": {"message": "Anzeigen des Preisverlaufs innerhalb von 3/6 Monaten und Benachrichtigung über Preissenkungen"}, "region__all": {"message": "Alle Regionen"}, "region__hai_wai": {"message": "Overseas"}, "region__hua_bei_qu": {"message": "North China"}, "region__hua_dong_qu": {"message": "East China"}, "region__hua_nan_qu": {"message": "South China"}, "region__hua_zhong_qu": {"message": "Central China"}, "region__jiang_zhe_lu": {"message": "Jiangsu, Zhejiang and Shanghai"}, "remove_web_limits": {"message": "Rechtsklick aktivieren"}, "ren_zheng_gong_chang": {"message": "Zertifizierte Fabrik"}, "ren_zheng_gong_ying_shang_nian_zhan": {"message": "Jahre als zertifizierter Lieferant"}, "required_to_aliprice_login": {"message": "<PERSON><PERSON> müssen sich bei AliPrice anmelden"}, "revenue_last30_days": {"message": "Umsatzbetrag in den letzten 30 Tagen"}, "review_counts": {"message": "<PERSON><PERSON><PERSON> der Sammler"}, "rocket": {"message": "로켓"}, "ru_zhu_nian_xian": {"message": "Eintrittszeitraum"}, "sales_amount_last30_days": {"message": "Gesamtumsatz in den letzten 30 Tagen"}, "sales_last30_days": {"message": "Verkäufe in den letzten 30 Tagen"}, "sbi_alibaba_cate__accessories": {"message": "Zubehör"}, "sbi_alibaba_cate__aqfk": {"message": "Sicherheit"}, "sbi_alibaba_cate__bags_cases": {"message": "Taschen & Koffer"}, "sbi_alibaba_cate__beauty": {"message": "Schönheit"}, "sbi_alibaba_cate__beverage": {"message": "<PERSON>rä<PERSON>"}, "sbi_alibaba_cate__bgwh": {"message": "Bürokultur"}, "sbi_alibaba_cate__bz": {"message": "<PERSON><PERSON>"}, "sbi_alibaba_cate__ccyj": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "sbi_alibaba_cate__clothes": {"message": "Bekleidung"}, "sbi_alibaba_cate__cmgd": {"message": "Medienübertragung"}, "sbi_alibaba_cate__coat_jacket": {"message": "Manteljacke"}, "sbi_alibaba_cate__consumer_electronics": {"message": "Unterhaltungselektronik"}, "sbi_alibaba_cate__cryp": {"message": "Produkte für Erwachsene"}, "sbi_alibaba_cate__csyp": {"message": "Bettwäsche"}, "sbi_alibaba_cate__cwyy": {"message": "Tiergartenarbeit"}, "sbi_alibaba_cate__cysx": {"message": "Catering frisch"}, "sbi_alibaba_cate__dgdq": {"message": "Elektriker"}, "sbi_alibaba_cate__dl": {"message": "Schauspielkunst"}, "sbi_alibaba_cate__dress_suits": {"message": "Kleid & Anzüge"}, "sbi_alibaba_cate__dszm": {"message": "Beleuchtung"}, "sbi_alibaba_cate__dzqj": {"message": "Elektronisches Gerät"}, "sbi_alibaba_cate__essb": {"message": "Gebrauchte Ausrüstung"}, "sbi_alibaba_cate__food": {"message": "Essen"}, "sbi_alibaba_cate__fspj": {"message": "Kleidung"}, "sbi_alibaba_cate__furniture": {"message": "<PERSON><PERSON><PERSON>"}, "sbi_alibaba_cate__fzpg": {"message": "Textilleder"}, "sbi_alibaba_cate__ghjq": {"message": "K<PERSON><PERSON>erpflege"}, "sbi_alibaba_cate__gt": {"message": "<PERSON><PERSON>"}, "sbi_alibaba_cate__gyp": {"message": "Kunsthandwerk"}, "sbi_alibaba_cate__hb": {"message": "Umweltfreundlich"}, "sbi_alibaba_cate__hfcz": {"message": "Make-up für die Hautpflege"}, "sbi_alibaba_cate__hg": {"message": "Chemieindustrie"}, "sbi_alibaba_cate__jg": {"message": "wird bearbeitet"}, "sbi_alibaba_cate__jianccai": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "sbi_alibaba_cate__jichuang": {"message": "Werkzeugmaschine"}, "sbi_alibaba_cate__jjry": {"message": "Täglicher Gebrauch im Haushalt"}, "sbi_alibaba_cate__jtys": {"message": "Transport"}, "sbi_alibaba_cate__jxsb": {"message": "Ausrüstung"}, "sbi_alibaba_cate__jxwj": {"message": "Mechanische Hardware"}, "sbi_alibaba_cate__jydq": {"message": "Haushaltsgeräte"}, "sbi_alibaba_cate__jzjc": {"message": "Baustoffe für Heimwerker"}, "sbi_alibaba_cate__jzjf": {"message": "Heimtextilien"}, "sbi_alibaba_cate__mj": {"message": "Handtuch"}, "sbi_alibaba_cate__myyp": {"message": "Baby Produkte"}, "sbi_alibaba_cate__nanz": {"message": "<PERSON><PERSON>"}, "sbi_alibaba_cate__nvz": {"message": "Frauenkleidung"}, "sbi_alibaba_cate__ny": {"message": "Energie"}, "sbi_alibaba_cate__others": {"message": "<PERSON><PERSON>"}, "sbi_alibaba_cate__qcyp": {"message": "Autozubehör"}, "sbi_alibaba_cate__qmpj": {"message": "Autoteile"}, "sbi_alibaba_cate__shoes": {"message": "<PERSON><PERSON><PERSON>"}, "sbi_alibaba_cate__smdn": {"message": "Digitaler Computer"}, "sbi_alibaba_cate__snqj": {"message": "Lagerung und Reinigung"}, "sbi_alibaba_cate__spjs": {"message": "<PERSON><PERSON> trinken"}, "sbi_alibaba_cate__swfw": {"message": "Geschäftsdienstleistungen"}, "sbi_alibaba_cate__toys_hobbies": {"message": "Spielzeug"}, "sbi_alibaba_cate__trousers_skirt": {"message": "Hose & Rock"}, "sbi_alibaba_cate__txcp": {"message": "Kommunikationsprodukte"}, "sbi_alibaba_cate__tz": {"message": "Kinderkleidung"}, "sbi_alibaba_cate__underwear": {"message": "Unterwäsche"}, "sbi_alibaba_cate__wjgj": {"message": "Mechanische Werkzeuge"}, "sbi_alibaba_cate__xgpi": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "sbi_alibaba_cate__xmhz": {"message": "Projekt Zusammenarbeit"}, "sbi_alibaba_cate__xs": {"message": "<PERSON><PERSON><PERSON>"}, "sbi_alibaba_cate__ydfs": {"message": "Sportbekleidung"}, "sbi_alibaba_cate__ydhw": {"message": "Sport im Freien"}, "sbi_alibaba_cate__yjkc": {"message": "Metallurgische Mineralien"}, "sbi_alibaba_cate__yqyb": {"message": "Instrumentierung"}, "sbi_alibaba_cate__ys": {"message": "<PERSON><PERSON><PERSON>"}, "sbi_alibaba_cate__yyby": {"message": "Medizinische Versorgung"}, "sbi_alibaba_cn_kj_90mjs": {"message": "Anzahl der Käufer in den letzten 90 Tagen"}, "sbi_alibaba_cn_kj_90xsl": {"message": "Verkaufsvolumen in den letzten 90 Tagen"}, "sbi_alibaba_cn_kj_gjsj": {"message": "Schätzpreis"}, "sbi_alibaba_cn_kj_gjwlyf": {"message": "Internationale Versandgebühr"}, "sbi_alibaba_cn_kj_gjyf": {"message": "Versandgebühr"}, "sbi_alibaba_cn_kj_gssj": {"message": "Schätzpreis"}, "sbi_alibaba_cn_kj_lr": {"message": "Profitieren"}, "sbi_alibaba_cn_kj_lrgs": {"message": "Gewinn = geschätzter Preis x Gewinnspanne"}, "sbi_alibaba_cn_kj_pjfhsd": {"message": "Durchschnittliche Liefergeschwindigkeit"}, "sbi_alibaba_cn_kj_qtfy": {"message": "<PERSON><PERSON>"}, "sbi_alibaba_cn_kj_qtfygs": {"message": "Sonstige Kosten = geschätzter Preis x sonstiges Kostenverhältnis"}, "sbi_alibaba_cn_kj_spjg": {"message": "Pre<PERSON>"}, "sbi_alibaba_cn_kj_spzl": {"message": "Gewicht"}, "sbi_alibaba_cn_kj_szd": {"message": "Ort"}, "sbi_alibaba_cn_kj_unit_ge": {"message": "<PERSON><PERSON><PERSON>"}, "sbi_alibaba_cn_kj_unit_jian": {"message": "<PERSON><PERSON><PERSON>"}, "sbi_alibaba_cn_kj_unit_ke": {"message": "Gramm"}, "sbi_alibaba_cn_kj_unit_ren": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "sbi_alibaba_cn_kj_unit_tai": {"message": "<PERSON><PERSON><PERSON>"}, "sbi_alibaba_cn_kj_unit_tao": {"message": "S<PERSON><PERSON>"}, "sbi_alibaba_cn_kj_unit_tian": {"message": "Tage"}, "sbi_alibaba_cn_kj_zwbj": {"message": "<PERSON><PERSON>"}, "sbi_aliprice_alibaba_cn__jiage": {"message": "Pre<PERSON>"}, "sbi_aliprice_alibaba_cn__keshou": {"message": "Zum Verkauf verfügbar"}, "sbi_aliprice_alibaba_cn__moren": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "sbi_aliprice_alibaba_cn__queding": {"message": "<PERSON><PERSON>"}, "sbi_aliprice_alibaba_cn__xiaoliang": {"message": "Der Umsatz"}, "sbi_aliprice_alibaba_cn_cate__jiaju": {"message": "<PERSON><PERSON><PERSON>"}, "sbi_aliprice_alibaba_cn_cate__linghsi": {"message": "Snack"}, "sbi_aliprice_alibaba_cn_cate__meizhuang": {"message": "Make-ups"}, "sbi_aliprice_alibaba_cn_cate__neiyi": {"message": "Unterwäsche"}, "sbi_aliprice_alibaba_cn_cate__peishi": {"message": "Zubehör"}, "sbi_aliprice_alibaba_cn_cate__pinchui": {"message": "Getränke in Flaschen"}, "sbi_aliprice_alibaba_cn_cate__qita": {"message": "<PERSON><PERSON>"}, "sbi_aliprice_alibaba_cn_cate__qunzhuang": {"message": "Rock"}, "sbi_aliprice_alibaba_cn_cate__shangyi": {"message": "<PERSON><PERSON>"}, "sbi_aliprice_alibaba_cn_cate__shuma": {"message": "Elektronik"}, "sbi_aliprice_alibaba_cn_cate__wanju": {"message": "Spielzeug"}, "sbi_aliprice_alibaba_cn_cate__xiangbao": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "sbi_aliprice_alibaba_cn_cate__xiazhuang": {"message": "Unterteile"}, "sbi_aliprice_alibaba_cn_cate__xiezi": {"message": "<PERSON><PERSON><PERSON>"}, "sbi_aliprice_cate__apparel": {"message": "Bekleidung"}, "sbi_aliprice_cate__automobiles_motorcycles": {"message": "Autos & Motorräder"}, "sbi_aliprice_cate__beauty_health": {"message": "Schönheit und Gesundheit"}, "sbi_aliprice_cate__cellphones_telecommunications": {"message": "Handys & Telekommunikation"}, "sbi_aliprice_cate__computer_office": {"message": "Computer & Büro"}, "sbi_aliprice_cate__consumer_electronics": {"message": "Unterhaltungselektronik"}, "sbi_aliprice_cate__education_office_supplies": {"message": "Bildung & Büromaterial"}, "sbi_aliprice_cate__electronic_components_supplies": {"message": "Elektronische Komponenten & Zubehör"}, "sbi_aliprice_cate__furniture": {"message": "<PERSON><PERSON><PERSON>"}, "sbi_aliprice_cate__hair_extensions_wigs": {"message": "Haarverlängerungen & Perücken"}, "sbi_aliprice_cate__home_garden": {"message": "Haus & Garten"}, "sbi_aliprice_cate__home_improvement": {"message": "Heimwerker"}, "sbi_aliprice_cate__jewelry_accessories": {"message": "Schmuck & Accessoires"}, "sbi_aliprice_cate__luggage_bags": {"message": "Gepäck und Taschen"}, "sbi_aliprice_cate__mother_kids": {"message": "Mutter & Kinder"}, "sbi_aliprice_cate__novelty_special_use": {"message": "Neuheit & besondere Verwendung"}, "sbi_aliprice_cate__security_protection": {"message": "Sicherheitsschutz"}, "sbi_aliprice_cate__shoes": {"message": "<PERSON><PERSON><PERSON>"}, "sbi_aliprice_cate__sports_entertainment": {"message": "Sport und Unterhaltung"}, "sbi_aliprice_cate__toys_hobbies": {"message": "Spielzeug & Hobbys"}, "sbi_aliprice_cate__watches": {"message": "<PERSON><PERSON>"}, "sbi_aliprice_cate__weddings_events": {"message": "Hochzeiten & Events"}, "sbi_btn_capture_txt": {"message": "Erfassung"}, "sbi_btn_source_now_txt": {"message": "<PERSON><PERSON> jet<PERSON>t"}, "sbi_button__chat_with_me": {"message": "Chatte mit mir"}, "sbi_button__contact_supplier": {"message": "Kontakt"}, "sbi_button__hide_on_this_site": {"message": "<PERSON>cht auf dieser Seite anzeigen"}, "sbi_button__open_settings": {"message": "Konfigurieren Sie die Suche nach Bild"}, "sbi_capture_shortcut_tip": {"message": "oder drücken Sie die Eingabetaste auf der Tastatur"}, "sbi_capturing_tip": {"message": "Erfassen"}, "sbi_composed_rating_45": {"message": "4,5 - 5,0 <PERSON><PERSON>"}, "sbi_crop_and_search": {"message": "<PERSON><PERSON>"}, "sbi_crop_start": {"message": "Screenshot verwenden"}, "sbi_err_captcha_action": {"message": "Bestätigen"}, "sbi_err_captcha_for_alibaba_cn": {"message": "Benötigt eine Bestätigung. Bitte laden Sie zur Bestätigung ein Bild hoch. ($video_tutorial$ ansehen oder versuchen, <PERSON><PERSON> zu löschen)", "placeholders": {"feedback": {"content": "$1"}, "video_tutorial": {"content": "$1"}}}, "sbi_err_captcha_for_aliprice": {"message": "Ungewöhnlicher Verkehr, bitte überprüfen"}, "sbi_err_captcha_for_taobao": {"message": "Taobao fordert Sie zur Überprüfung auf. Bitte laden Si<PERSON> ein Bild manuell hoch und suchen Si<PERSON> nach, um es zu überprüfen. Dieser Fehler ist auf die neue Überprüfungsrichtlinie \"TaoBao-Suche nach Bild\" zurückzuführen. Wir empfehlen Ihnen, dass Beschwerden auf Taobao $feedback$ zu häufig überprüft werden.", "placeholders": {"feedback": {"content": "$1"}}}, "sbi_err_captcha_for_taobao_feedback": {"message": "<PERSON><PERSON><PERSON>"}, "sbi_err_captcha_msg": {"message": "$platform$ er<PERSON><PERSON>, dass Sie zum Suchen ein Bild hochladen oder eine Sicherheitsüberprüfung durchführen, um die Sucheinschränkungen aufzuheben", "placeholders": {"platform": {"content": "$1"}}}, "sbi_err_checking_if_low_version": {"message": "Überprüfen Sie, ob es die neueste Version ist"}, "sbi_err_cookie_btn_clear": {"message": "<PERSON><PERSON><PERSON>"}, "sbi_err_cookie_for_alibaba_cn": {"message": "<PERSON><PERSON><PERSON><PERSON> Si<PERSON>, die <PERSON><PERSON> von 1688 zu löschen? (<PERSON><PERSON> müssen sich erneut anmelden)"}, "sbi_err_desperate_feature_pdd": {"message": "Die Bildsuchfunktion wurde in die Pinduoduo-Erweiterung „Suche nach Bild“ verschoben."}, "sbi_err_hidden_skill_of_improve_search_rate": {"message": "Wie kann die Erfolgsrate der Bildersuche verbessert werden?"}, "sbi_err_img_undersize": {"message": "Bild > $imgMinimumSize$", "placeholders": {"imgMinimumSize": {"content": "$1"}}}, "sbi_err_login_required": {"message": "Melden Sie sich an $loginSite$", "placeholders": {"loginSite": {"content": "$1"}}}, "sbi_err_login_required_action": {"message": "Einloggen"}, "sbi_err_low_version": {"message": "Installieren Sie die neueste Version ($latestVersion$)", "placeholders": {"latestVersion": {"content": "$1"}}}, "sbi_err_low_version_action": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "sbi_err_need_help": {"message": "Benötigen Sie Hilfe?"}, "sbi_err_network": {"message": "Netzwerkfehler. <PERSON><PERSON><PERSON>, dass Sie die Website besuchen können"}, "sbi_err_not_low_version": {"message": "Die neueste Version wurde installiert ($latestVersion$)", "placeholders": {"latestVersion": {"content": "$1"}}}, "sbi_err_try_again": {"message": "Versuch es noch einmal"}, "sbi_err_try_again_action": {"message": "Versuch es noch einmal"}, "sbi_err_visit_and_try": {"message": "Versuchen Sie es noch einmal oder besuchen Sie $website$, um es noch einmal zu versuchen", "placeholders": {"website": {"content": "$1"}}}, "sbi_err_visit_site": {"message": "Besuchen Sie die Startseite von $siteName$", "placeholders": {"siteName": {"content": "$1"}}}, "sbi_fail_tip": {"message": "Laden fehlgeschlagen, aktualisieren Sie die Seite und versuchen Sie es erneut."}, "sbi_kuajing_filter_area": {"message": "<PERSON><PERSON><PERSON>"}, "sbi_kuajing_filter_au": {"message": "Australien"}, "sbi_kuajing_filter_btn_confirm": {"message": "Bestätigen Sie"}, "sbi_kuajing_filter_de": {"message": "Deutschland"}, "sbi_kuajing_filter_destination_country": {"message": "Zielland"}, "sbi_kuajing_filter_es": {"message": "Spanien"}, "sbi_kuajing_filter_estimate": {"message": "Schätzen"}, "sbi_kuajing_filter_estimate_price": {"message": "Ungefährer Preis"}, "sbi_kuajing_filter_estimate_price_formula": {"message": "Geschätzte Preisformel = (Warenpreis + internationale Logistikfracht)/(1 - Gewinnspanne - sonstiges Kostenverhältnis)"}, "sbi_kuajing_filter_fr": {"message": "<PERSON><PERSON><PERSON>"}, "sbi_kuajing_filter_kw_placeholder": {"message": "<PERSON><PERSON><PERSON> Sie Schlüsselwörter ein, die dem Titel entsprechen"}, "sbi_kuajing_filter_logistics": {"message": "Logistikvorlage"}, "sbi_kuajing_filter_logistics_china_post": {"message": "China Post Luftweg"}, "sbi_kuajing_filter_logistics_discount": {"message": "Logistikrabatt"}, "sbi_kuajing_filter_logistics_epacket": {"message": "epacket"}, "sbi_kuajing_filter_logistics_price_formula": {"message": "Internationale Logistikfracht = (Gewicht x Versandpreis + Anmeldegebühr) x (1 - Ra<PERSON><PERSON>)"}, "sbi_kuajing_filter_others_fee": {"message": "Andere Gebühr"}, "sbi_kuajing_filter_profit_percent": {"message": "Gewinnspanne"}, "sbi_kuajing_filter_prop": {"message": "Attribute"}, "sbi_kuajing_filter_ru": {"message": "Russland"}, "sbi_kuajing_filter_total": {"message": "Übereinstimmung mit $count$ ähnliche Artikel", "placeholders": {"count": {"content": "$1"}}}, "sbi_kuajing_filter_uk": {"message": "VEREINIGTES KÖNIGREICH"}, "sbi_kuajing_filter_usa": {"message": "Amerika"}, "sbi_login_punish_title__pdd_pifa": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "sbi_msg_no_result": {"message": "<PERSON><PERSON> gefunden,bitte melden <PERSON> sich bei $loginSite$ an oder versuchen Sie es mit einem anderen Bild", "placeholders": {"loginSite": {"content": "$1"}}}, "sbi_msg_no_result_check_support_page_l1": {"message": "Für Safari vorübergehend nicht verfügbar, verwenden Sie bitte $supportPage$.", "placeholders": {"supportPage": {"content": "$1"}}}, "sbi_msg_no_result_check_support_page_l2": {"message": "Chrome-Browser und seine Erweiterungen"}, "sbi_msg_no_result_reinstall_l1": {"message": "<PERSON><PERSON>se gefunden, melden <PERSON> sich bei $loginSite$ an oder versuchen Sie es mit einem anderen Bild oder installieren $latestExtUrl$ neu", "placeholders": {"loginSite": {"content": "$1"}, "latestExtUrl": {"content": "$2"}}}, "sbi_msg_no_result_reinstall_l2": {"message": "Letzte Version", "placeholders": {}}, "sbi_search_by_selected_area": {"message": "Ausgewählter Bereich"}, "sbi_shipping_": {"message": "Versand am selben Tag"}, "sbi_specify_category": {"message": "<PERSON><PERSON><PERSON>:"}, "sbi_start_crop": {"message": "<PERSON><PERSON><PERSON> auswählen"}, "sbi_store_name_alibabaCNKuaJing": {"message": "1688 Übersee"}, "sbi_tutorial_btn_more": {"message": "Mehr Nutzung"}, "sbi_tutorial_find_coupons_on_taobao": {"message": "Taobao-Gutscheine finden"}, "sbi_txt__empty_retry": {"message": "<PERSON>s wurden leider keine Ergebnisse gefunden. Bitte versuchen Sie es erneut."}, "sbi_txt__min_order": {"message": "Mindest. Auftrag"}, "sbi_visiting": {"message": "Durchsuchen"}, "sbi_yiwugo__jiagexiangtan": {"message": "Kontaktieren Sie den Verkäufer für den Preis"}, "sbi_yiwugo__qigou": {"message": "$num$ Stück (MOQ)", "placeholders": {"num": {"content": "$1"}}}, "sbi_yiwugo__xing": {"message": "<PERSON><PERSON>"}, "searchByImage_screenshot": {"message": "Screenshot mit einem Klick"}, "searchByImage_search": {"message": "<PERSON>e mit einem Klick nach denselben Elementen"}, "searchByImage_size_type": {"message": "Dateigrö<PERSON> darf nicht größer als $num$ MB sein, nur $type$", "placeholders": {"num": {"content": "$1"}, "type": {"content": "$2"}}}, "search_by_image_progress_analysing": {"message": "Bild analysieren"}, "search_by_image_progress_searching": {"message": "Suche nach Produkten"}, "search_by_image_progress_sending": {"message": "Bild senden"}, "search_by_image_response_rate": {"message": "Rücklaufquote: $responseRate$ der Käufer, die diesen Lieferanten kontaktiert haben, haben innerhalb von $responseInHour$ Stunden eine Antwort erhalten.", "placeholders": {"responseRate": {"content": "$1"}, "responseInHour": {"content": "$2"}}}, "search_by_keyword": {"message": "Über Schlüsselwort suchen:"}, "select_country_language_modal_title_country": {"message": "Land"}, "select_country_language_modal_title_language": {"message": "<PERSON><PERSON><PERSON>"}, "select_country_region_modal_title": {"message": "Wählen Sie ein Land / eine Region aus"}, "select_language_modal_title": {"message": "<PERSON><PERSON>hle eine Sprache:"}, "select_shop": {"message": "Geschäft auswählen"}, "sellers_count": {"message": "Anzahl der Verkäufer auf der aktuellen Seite"}, "sellers_count_per_page": {"message": "Anzahl der Verkäufer auf der aktuellen Seite"}, "service_score": {"message": "Umfassende Servicebewertung"}, "set_shortcut_keys": {"message": "Legen Sie Tastenkombinationen fest"}, "setting_logo_title": {"message": "Einkaufsassistent"}, "setting_modal_options_position_title": {"message": "<PERSON><PERSON>"}, "setting_modal_options_position_value_left": {"message": "Auf der linken Seite"}, "setting_modal_options_position_value_right": {"message": "Au<PERSON> der rechten Seite"}, "setting_modal_options_theme_title": {"message": "Farbe Theme"}, "setting_modal_options_theme_value_dark": {"message": "<PERSON><PERSON><PERSON>"}, "setting_modal_options_theme_value_light": {"message": "Licht"}, "setting_modal_title": {"message": "<PERSON><PERSON><PERSON>"}, "setting_options_country_title": {"message": "Land / Region"}, "setting_options_hover_zoom_desc": {"message": "Zum Hereinzoomen mit der Maus über das Bild fahren"}, "setting_options_hover_zoom_title": {"message": "Schweben und zoomen"}, "setting_options_jd_coupon_desc": {"message": "Gutschein auf JD.com gefunden"}, "setting_options_jd_coupon_title": {"message": "JD.com-Gutschein"}, "setting_options_language_title": {"message": "<PERSON><PERSON><PERSON>"}, "setting_options_price_drop_alert_desc": {"message": "<PERSON>n der Preis der Produkte in Mein Favorit fällt, erhalten Sie eine Push-Benachrichtigung."}, "setting_options_price_drop_alert_title": {"message": "Preisverfall Alarm"}, "setting_options_price_history_on_list_page_desc": {"message": "Preisverlauf auf der Produktsuchseite anzeigen"}, "setting_options_price_history_on_list_page_title": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON> (Listenseite)"}, "setting_options_price_history_on_produt_page_desc": {"message": "Zeigen Sie die Produkthistorie auf der Produktsuchseite an"}, "setting_options_price_history_on_produt_page_title": {"message": "<PERSON><PERSON>verlau<PERSON> (Detailseite)"}, "setting_options_sales_analysis_desc": {"message": "Unterstützen Sie Statistiken zu Preis, <PERSON><PERSON><PERSON><PERSON>svolumen, Anzahl der Verkäufer und Ladenverkaufsquote auf der Produktlistenseite von $platforms$", "placeholders": {"platforms": {"content": "$1"}}}, "setting_options_sales_analysis_title": {"message": "Verkaufsanalyse"}, "setting_options_save_success_msg": {"message": "Erfolg"}, "setting_options_tacking_price_title": {"message": "Preisänderungsalarm"}, "setting_options_value_off": {"message": "Off"}, "setting_options_value_on": {"message": "On"}, "setting_pkg_quick_view_desc": {"message": "Unterstützung: 1688 & Taobao"}, "setting_saved_message": {"message": "Änderungen erfolgreich gespeichert"}, "setting_section_enable_platform_title": {"message": "Ein-Aus"}, "setting_section_setting_title": {"message": "<PERSON><PERSON><PERSON>"}, "setting_section_shortcuts_title": {"message": "Abkürzung :"}, "settings_aliprice_agent__desc": {"message": "Wird auf der Produktdetailseite von $platforms$ angezeigt", "placeholders": {"platforms": {"content": "$1"}}}, "settings_aliprice_agent__title": {"message": "<PERSON><PERSON><PERSON> mich kaufen"}, "settings_copy_link__desc": {"message": "Anzeige auf Produktdetailseite"}, "settings_copy_link__title": {"message": "Ko<PERSON><PERSON>-<PERSON><PERSON> und Titel suchen"}, "settings_currency_desc__for_detail": {"message": "Support 1688 Produktdetailseite"}, "settings_currency_desc__for_list": {"message": "Suche nach Bild(einschließlich 1688/1688 Übersee/Taobao)"}, "settings_currency_desc__for_sbi": {"message": "Wählen Sie den Preis"}, "settings_currency_desc_display_for_list": {"message": "Wird in der Bildersuche angezeigt (einschließlich 1688/1688 in Übersee/Taobao)"}, "settings_currency_rate_desc": {"message": "Aktualisierung des Wechselkurses von \"$currencyRateFrom$\"", "placeholders": {"currencyRateFrom": {"content": "$1"}}}, "settings_currency_rate_from_boc": {"message": "Bank von China"}, "settings_download_images__desc": {"message": "Unterstützung für das Herunterladen von Bildern von $platforms$", "placeholders": {"platforms": {"content": "$1"}}}, "settings_download_images__title": {"message": "Schaltfläche Bild herunterladen"}, "settings_download_reviews__desc": {"message": "Wird auf der Produktdetailseite von $platforms$ angezeigt", "placeholders": {"platforms": {"content": "$1"}}}, "settings_download_reviews__title": {"message": "Rezensionsbilder herunterladen"}, "settings_google_translate_desc": {"message": "<PERSON>licken Sie mit der rechten Maustaste, um die Google Übersetzerleiste zu erhalten"}, "settings_google_translate_title": {"message": "Übersetzung von <PERSON>"}, "settings_historical_trend_desc": {"message": "Anzeige in der unteren rechten Ecke des Bildes auf der Produktlistenseite"}, "settings_modal_btn_more": {"message": "Mehr Einstellungen"}, "settings_productInfo_desc": {"message": "Detailliertere Produktinformationen auf der Produktlistenseite anzeigen. Das Aktivieren dieser Option kann die Computerlast erhöhen und zu Seitenverzögerungen führen. Wenn die Leistung beeinträchtigt wird, wird em<PERSON><PERSON>, diese Option zu deaktivieren."}, "settings_product_recommend__desc": {"message": "Wird unter dem Hauptbild auf der Produktdetailseite von $platforms$ angezeigt", "placeholders": {"platforms": {"content": "$1"}}}, "settings_product_recommend__name": {"message": "Empfohlene Produkte"}, "settings_research_desc": {"message": "Detailliertere Informationen auf der Produktlistenseite abfragen"}, "settings_sbi_add_to_list": {"message": "Zu $listType$ hinzufügen", "placeholders": {"listType": {"content": "$1"}}}, "settings_sbi_detail_page_icon_title_desc": {"message": "Miniaturansicht des Bildsuchergebnisses"}, "settings_sbi_remove_from_list": {"message": "Aus $listType$ entfernen", "placeholders": {"listType": {"content": "$1"}}}, "settings_search_by_image_add_to_blacklist": {"message": "Zur Blacklist hinzufügen"}, "settings_search_by_image_adjust_entrance_entrance": {"message": "Eingangsposition anpassen"}, "settings_search_by_image_blacklist_desc": {"message": "Symbol auf Websites in der Blacklist nicht anzeigen."}, "settings_search_by_image_blacklist_title": {"message": "Schwarze Liste"}, "settings_search_by_image_bottom_left": {"message": "Unten links"}, "settings_search_by_image_bottom_right": {"message": "Unten rechts"}, "settings_search_by_image_clear_blacklist": {"message": "Schwarze Liste löschen"}, "settings_search_by_image_detail_page_icon_title": {"message": "Miniaturansicht"}, "settings_search_by_image_detail_page_icon_val_large": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "settings_search_by_image_detail_page_icon_val_small": {"message": "<PERSON><PERSON>"}, "settings_search_by_image_display_button_desc": {"message": "Ein Klick auf das Symbol, um nach Bildern zu suchen"}, "settings_search_by_image_display_button_title": {"message": "Symbol auf Bildern"}, "settings_search_by_image_sourece_websites_desc": {"message": "Finden Sie das Quellprodukt auf diesen Websites"}, "settings_search_by_image_sourece_websites_title": {"message": "Suche nach Bildergebnis"}, "settings_search_by_image_top_left": {"message": "Oben links"}, "settings_search_by_image_top_right": {"message": "<PERSON><PERSON> rechts"}, "settings_search_keyword_on_x__desc": {"message": "Suchbegriffe auf $platform$ . suchen", "placeholders": {"platform": {"content": "$1"}}}, "settings_search_keyword_on_x__title": {"message": "$platform$-Symbol anzeigen, wenn Wörter ausgewählt werden", "placeholders": {"platform": {"content": "$1"}}}, "settings_similar_products_desc": {"message": "Versuche<PERSON> Sie, auf diesen Websites dasselbe Produkt zu finden (maximal 5)."}, "settings_similar_products_title": {"message": "Finden Sie das gleiche Produkt"}, "settings_toolbar_expand_title": {"message": "Plug-in minimieren"}, "settings_top_toolbar_desc": {"message": "Suchleiste oben auf der Seite"}, "settings_top_toolbar_title": {"message": "Suchleiste"}, "settings_translate_search_desc": {"message": "Ins Chinesische übersetzen und suchen"}, "settings_translate_search_title": {"message": "Mehrsprachige Suche"}, "settings_translator_contextmenu_title": {"message": "Zum Übersetzen aufnehmen"}, "settings_translator_title": {"message": "Übersetzen"}, "shai_xuan_dao_chu": {"message": "Zum Exportieren filtern"}, "shai_xuan_zi_duan": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "shang_jia_shi_jian": {"message": "Auf <PERSON>"}, "shang_pin_biao_ti": {"message": "Produktname"}, "shang_pin_dui_bi": {"message": "Produktvergleich"}, "shang_pin_lian_jie": {"message": "Produktlink"}, "shang_pin_xin_xi": {"message": "Produktinformation"}, "share_modal__content": {"message": "<PERSON><PERSON> mit deinen Freunden"}, "share_modal__disable_for_while": {"message": "Ich möchte nichts teilen"}, "share_modal__title": {"message": "Magst du $extensionName$?", "placeholders": {"extensionName": {"content": "$1"}}}, "sheng_yu_ku_cun": {"message": "Verbleibend"}, "shi_fou_ke_ding_zhi": {"message": "Ist es anpassbar?"}, "shi_fou_ren_zheng_gong_ying_sha": {"message": "Zertifizierter Lieferant"}, "shi_fou_ren_zheng_gong_ying_shang_zong_shu": {"message": "Zertifizierte Lieferanten"}, "shi_fou_you_mao_yi_dan_bao": {"message": "Handelsversicherung"}, "shi_fou_you_mao_yi_dan_bao_zong_shu": {"message": "Handels<PERSON><PERSON><PERSON>"}, "shipping_fee": {"message": "Versandkosten"}, "shop_followers": {"message": "Shop-Follower"}, "shou_qi": {"message": "<PERSON><PERSON>"}, "similar_products_warn_max_platforms": {"message": "Max bis 5"}, "sku_calc_price": {"message": "Berechnet<PERSON>is"}, "sku_calc_price_settings": {"message": "Berechnete Preiseinstellungen"}, "sku_formula": {"message": "Formel"}, "sku_formula_desc": {"message": "Formelbeschreibung"}, "sku_formula_desc_text": {"message": "Unterstützt komplexe mathematische Formeln: Der ursprüngliche Preis wird durch A und die Fracht durch B dargestellt.\n\n<br/>\n\nUnterstützt Klammern (), Plus +, Minus -, Multiplikation * und Division /.\n\n<br/>\n\nBeispiel:\n\n<br/>\n\n1. Um den ursprünglichen Preis um das 1,2-<PERSON><PERSON> zu erhöhen und die Frachtkosten zu addieren, lautet die Formel: A*1,2+B.\n\n<br/>\n\n2. Um den ursprünglichen Preis um 1 Yuan zu erhöhen und mit 1,2 zu multiplizieren, lautet die Formel: (A+1)*1,2.\n\n<br/>\n\n3. Um den ursprünglichen Preis um 10 Yuan zu erhöhen, mit 1,2 zu multiplizieren und 3 Yuan abzuziehen, lautet die Formel: (A+10)*1,2-3."}, "sku_in_stock": {"message": "Lagerbestand"}, "sku_invalid_formula_format": {"message": "Ungültiges Formelformat"}, "sku_inventory": {"message": "Lagerbestand"}, "sku_link_copy_fail": {"message": "Erfolg<PERSON><PERSON>, SKU-Spezifikationen und -Attribute sind nicht ausgewählt"}, "sku_link_copy_success": {"message": "Erfolgre<PERSON>, SKU-Spezifikationen und Attribute ausgewählt"}, "sku_list": {"message": "Artikelnummerliste"}, "sku_min_qrder_qty": {"message": "Mindestbestellmenge"}, "sku_name": {"message": "Artikelnummer"}, "sku_no": {"message": "Nr."}, "sku_original_price": {"message": "Ursprünglicher Preis"}, "sku_price": {"message": "Artikelnummer"}, "stop_track_time_label": {"message": "Verfolgungsschluss:"}, "suo_zai_di_qu": {"message": "<PERSON><PERSON>"}, "tab_pkg_quick_view": {"message": "Logistikmonitor"}, "tab_product_details_price_history": {"message": "Geschichte"}, "tab_product_details_reviews": {"message": "Bewertungen"}, "tab_product_details_seller_analysis": {"message": "Analyse"}, "tab_product_details_similar_products": {"message": "<PERSON><PERSON><PERSON>"}, "total_days_listed_per_product": {"message": "Summe der Lagertage ÷ Anzahl der Produkte"}, "total_items": {"message": "Gesamtzahl der Produkte"}, "total_price_per_product": {"message": "Summe der Preise ÷ Anzahl der Produkte"}, "total_rating_per_product": {"message": "Summe der Bewertungen ÷ Anzahl der Produkte"}, "total_revenue": {"message": "Gesamtumsatz"}, "total_revenue40_items": {"message": "Gesamtumsatz der $amount$ Produkte auf der aktuellen Seite", "placeholders": {"amount": {"content": "$1"}}}, "total_sales": {"message": "Gesamtumsatz"}, "total_sales40_items": {"message": "Gesamtumsatz der $amount$ Produkte auf der aktuellen Seite", "placeholders": {"amount": {"content": "$1"}}}, "track_for_12_months": {"message": "Track für: 1 Jahr"}, "track_for_3_months": {"message": "Track für: 3 Monate"}, "track_for_6_months": {"message": "Track für: 6 Monate"}, "tracking_price_email_add_btn": {"message": "E-Mail hinzufügen"}, "tracking_price_email_edit_btn": {"message": "E-Mail bearbeiten"}, "tracking_price_email_intro": {"message": "Wir werden Sie per E-Mail benachrichtigen."}, "tracking_price_email_invalid": {"message": "Bitte geben Si<PERSON> eine gültige E-Mail-Adresse an"}, "tracking_price_email_verified_desc": {"message": "Sie können jetzt unsere Preissenkungsbenachrichtigung erhalten."}, "tracking_price_email_verified_title": {"message": "Erfolgreich verifiziert"}, "tracking_price_email_verify_desc_line1": {"message": "Wir haben einen Bestätigungslink an Ihre E-Mail-Adresse gesendet."}, "tracking_price_email_verify_desc_line2": {"message": "Bitte überprüfen Sie Ihren E-Mail-Posteingang."}, "tracking_price_email_verify_title": {"message": "E-Mail bestätigen"}, "tracking_price_web_push_notification_intro": {"message": "Auf dem Desktop: <PERSON><PERSON><PERSON> kann jedes Produkt für Sie überwachen und Ihnen eine Web-Push-Benachrichtigung senden, sobald sich der Preis ändert."}, "tracking_price_web_push_notification_title": {"message": "Web-Push-Benachrichtigungen"}, "translate_im__login_required": {"message": "Übers<PERSON><PERSON><PERSON> von Ali<PERSON>, bitte melden Sie sich bei $loginUrl$ an", "placeholders": {"loginUrl": {"content": "$1"}}}, "translate_im__paste_fail": {"message": "Übersetzt und in die Zwischenablage kopiert, aber aufgrund der Einschränkungen von Aliwangwang müssen Sie es manuell einfügen!"}, "translate_im__send": {"message": "Übersetzen & Senden"}, "translate_search": {"message": "Übersetzen und suchen"}, "translation_originals_translated": {"message": "Original und Chinesisch"}, "translation_translated": {"message": "<PERSON><PERSON><PERSON>"}, "translator_btn_capture_txt": {"message": "Übersetzen"}, "translator_language_auto_detect": {"message": "Automatische Erkennung"}, "translator_language_detected": {"message": "Erkannt"}, "translator_language_search_placeholder": {"message": "S<PERSON><PERSON> suchen"}, "try_again": {"message": "Versuch es noch einmal"}, "tu_pian_chi_cun": {"message": "Bildgröße:"}, "tu_pian_lian_jie": {"message": "Bildlink"}, "tui_huan_ti_yan": {"message": "Rückkehrerfahrung"}, "tui_huan_ti_yan__desc": {"message": "Bewerten Sie die After-Sales-Indikatoren der Verkäufer"}, "tutorial__show_all": {"message": "Alle Features"}, "tutorial_ae_popup_title": {"message": "Pinne die Erweiterung, öffne Aliexpress"}, "tutorial_aliexpress_reviews_analysis": {"message": "AliExpress Bewertungsanalyse"}, "tutorial_aliprice_agent_with1688_desc_l1": {"message": "Unterstützen Sie USD"}, "tutorial_aliprice_agent_with1688_desc_l2": {"message": "Versand nach Korea/Japan/Festlandchina"}, "tutorial_aliprice_agent_with1688_title": {"message": "1688 unterstützt den Kauf im Ausland"}, "tutorial_auto_apply_coupon_title": {"message": "Gutschein automatisch anwenden"}, "tutorial_btn_end": {"message": "<PERSON><PERSON>"}, "tutorial_btn_example": {"message": "Beispiel"}, "tutorial_btn_have_a_try": {"message": "Ok, versuch es mal"}, "tutorial_btn_next": {"message": "Nächster"}, "tutorial_btn_see_more": {"message": "<PERSON><PERSON>"}, "tutorial_compare_products": {"message": "Compare Products"}, "tutorial_currency_convert_title": {"message": "Currency Conversion"}, "tutorial_export_shopping_cart": {"message": "Export as CSV, Support Taobao and 1688"}, "tutorial_export_shopping_cart_title": {"message": "Export cart"}, "tutorial_price_history_pro": {"message": "Displayed on product detail page.\nSupport Shopee, Lazada, Amazon, Ebay"}, "tutorial_price_history_pro_title": {"message": "Whole year Price history & Order history"}, "tutorial_sbi_context_menu_screenshot_title": {"message": "Capture to search by image"}, "tutorial_translate_search": {"message": "Zur Suche übersetzen"}, "tutorial_translate_search_and_package_tracking": {"message": "Übersetzungssuche und Paketverfolgung"}, "unit_bao": {"message": "Stck"}, "unit_ben": {"message": "Stck"}, "unit_bi": {"message": "Aufträge"}, "unit_chuang": {"message": "Stck"}, "unit_dai": {"message": "Stck"}, "unit_dui": {"message": "<PERSON><PERSON>"}, "unit_fen": {"message": "Stck"}, "unit_ge": {"message": "Stck"}, "unit_he": {"message": "Stck"}, "unit_jian": {"message": "Stck"}, "unit_li_fang_mi": {"message": "m³"}, "unit_ping": {"message": "Stck"}, "unit_ping_fang_mi": {"message": "㎡"}, "unit_shuang": {"message": "<PERSON><PERSON>"}, "unit_tai": {"message": "Stck"}, "unit_ti": {"message": "Stck"}, "unit_tiao": {"message": "Stck"}, "unit_xiang": {"message": "Stck"}, "unit_zhang": {"message": "Stck"}, "unit_zhi": {"message": "Stck"}, "verify_contact_support": {"message": "Support kontaktieren"}, "verify_human_verification": {"message": "Menschliche Überprüfung"}, "verify_unusual_access": {"message": "Ungewöhnlicher Zugriff erkannt"}, "view_history_clean_all": {"message": "Alles saubermachen"}, "view_history_clean_all_warring": {"message": "Alle angezeigten Datensätze bereinigen?"}, "view_history_clean_all_warring_title": {"message": "<PERSON><PERSON><PERSON>"}, "view_history_viewd": {"message": "<PERSON><PERSON><PERSON>"}, "website": {"message": "Webseite"}, "weight": {"message": "Gewicht"}, "wu_fa_huo_qu_dao_gai_shu_ju": {"message": "Daten konnten nicht abgerufen werden"}, "wu_liu_shi_xiao": {"message": "Pünktlicher Versand"}, "wu_liu_shi_xiao__desc": {"message": "Die 48-Stunden-Abholrate und Erfüllungsrate des Shops des Verkäufers"}, "xia_dan_jia": {"message": "Endpreis"}, "xian_xuan_ze_product_attributes": {"message": "Produktattribute auswählen"}, "xiao_liang": {"message": "Verkaufsvolumen"}, "xiao_liang_zhan_bi": {"message": "Prozentsatz des Verkaufsvolumens"}, "xiao_shi": {"message": "$num$ Stunden", "placeholders": {"num": {"content": "$1"}}}, "xiao_shou_e": {"message": "Umsatz"}, "xiao_shou_e_zhan_bi": {"message": "Prozentsatz des Umsatzes"}, "xuan_zhong_x_tiao_ji_lu": {"message": "Wählen Sie $amount$ Datensätze", "placeholders": {"amoun": {"content": "$1"}, "amount": {"content": "$1"}}}, "yan_xuan": {"message": "Auswahl"}, "yi_ding_zai_zuo_ce": {"message": "Gep<PERSON><PERSON>"}, "yi_jia_zai_wan_suo_you_shang_pin": {"message": "Alle Produkte geladen"}, "yi_nian_xiao_liang": {"message": "Jahresumsatz"}, "yi_nian_xiao_liang_zhan_bi": {"message": "Jahresumsatzanteil"}, "yi_nian_xiao_shou_e": {"message": "Jahresumsatz"}, "yi_nian_xiao_shou_e_zhan_bi": {"message": "Jahresumsatzanteil"}, "yi_shua_xin": {"message": "<PERSON>ktual<PERSON><PERSON>"}, "yin_cang_xiang_tong_dian": {"message": "Ähnlichkeiten verbergen"}, "you_xiao_liang": {"message": "<PERSON><PERSON> V<PERSON>kaufsvolumen"}, "yu_ji_dao_da_shi_jian": {"message": "Geschätzte Ankunftszeit"}, "yuan_gong_ren_shu": {"message": "Anza<PERSON> der Angestellten"}, "yue_cheng_jiao": {"message": "Monatliches Volumen"}, "yue_dai_xiao": {"message": "Dropshipping"}, "yue_dai_xiao__desc": {"message": "Dropshipping-Verkäufe in den letzten 30 Tagen"}, "yue_dai_xiao_pai_xu__desc": {"message": "Dropshipping-Verkäufe in den letzten 30 Tagen, sortiert von hoch nach niedrig"}, "yue_xiao_liang__desc": {"message": "Verkaufsvolumen in den letzten 30 Tagen"}, "zhan_kai": {"message": "<PERSON><PERSON>"}, "zhe_kou": {"message": "<PERSON><PERSON><PERSON>"}, "zhi_chi_yi_jian_dai_fa": {"message": "Dropshipping"}, "zhi_chi_yi_jian_dai_fa_bao_you": {"message": "Kostenloser Versand"}, "zhi_fu_ding_dan_shu": {"message": "Bezahlte Bestellungen"}, "zhi_fu_ding_dan_shu__desc": {"message": "<PERSON>zahl der Bestellungen für dieses Produkt (30 Tage)"}, "zhu_ce_xing_zhi": {"message": "Registrierungscharakter"}, "zi_ding_yi_tiao_jian": {"message": "Benutzerdefinierte Bedingungen"}, "zi_duan": {"message": "<PERSON><PERSON>"}, "zi_ti_xiao_liang": {"message": "<PERSON><PERSON><PERSON> ve<PERSON>t"}, "zong_he_fu_wu_fen": {"message": "Gesamtbewertung"}, "zong_he_fu_wu_fen__desc": {"message": "Gesamtbewertung des Verkäuferservices"}, "zong_he_fu_wu_fen__short": {"message": "Bewertung"}, "zong_he_ti_yan_fen": {"message": "Bewertung"}, "zong_he_ti_yan_fen_3": {"message": "Unter 4 Sternen"}, "zong_he_ti_yan_fen_4": {"message": "4 - 4,5 <PERSON><PERSON>"}, "zong_he_ti_yan_fen_4_5": {"message": "4,5 - 5,0 <PERSON><PERSON>"}, "zong_he_ti_yan_fen_5": {"message": "5 Sterne"}, "zong_ku_cun": {"message": "Gesamtbestand"}, "zong_xiao_liang": {"message": "Gesamtumsatz"}, "zui_jin_30D_3Min_xiang_ying_lv": {"message": "3-Minuten-Antwortrate in den letzten 30 Tagen"}, "zui_jin_30D_48H_lan_shou_lv": {"message": "48-Stunden-Wiederherstellungsrate in den letzten 30 Tagen"}, "zui_jin_30D_48H_lv_yue_lv": {"message": "48-St<PERSON>en-Leistungsrate in den letzten 30 Tagen"}, "zui_jin_30D_jiao_yi_ji_lu": {"message": "Handelsaufzeichnung (30 Tage)"}, "zui_jin_30D_jiao_yi_ji_lu__desc": {"message": "Handelsaufzeichnung (30 Tage)"}, "zui_jin_30D_jiu_fen_lv": {"message": "Streitrate in den letzten 30 Tagen"}, "zui_jin_30D_pin_zhi_tui_kuan_lv": {"message": "Qualitätsrückerstattungsrate in den letzten 30 Tagen"}, "zui_jin_30D_zhi_fu_ding_dan_shu": {"message": "Die Anzahl der Zahlungsaufträge in den letzten 30 Tagen"}}